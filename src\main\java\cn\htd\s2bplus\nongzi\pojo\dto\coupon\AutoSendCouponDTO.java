package cn.htd.s2bplus.nongzi.pojo.dto.coupon;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportCouponRuleDTO;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class AutoSendCouponDTO implements Serializable {
    /**
     * 商家编码
     */
    private String sellerCode;
    /**
     * 导入发券明细
     */
    private List<ImportCouponRuleDTO> importRecordList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
