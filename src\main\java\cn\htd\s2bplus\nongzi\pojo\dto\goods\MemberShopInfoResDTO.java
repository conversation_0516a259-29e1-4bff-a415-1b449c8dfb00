package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MemberShopInfoResDTO {

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "开店时间")
    private Date shopTime;

//    @ApiModelProperty(value = "开店时间")
//    @Excel(name = "开店时间", width = 20, numFormat = "2")
//    private Date shopTimeStr;

//    @ApiModelProperty(value = "店铺状态(1：申请 2:通过 4:关闭 5：开通)")
//    @Excel(name = "店铺状态", width = 20,numFormat = "3")
//    private String storeStatus;

    @ApiModelProperty(value = "店铺状态(1：申请 2:通过 4:关闭 5：开通)")
    private String storeStatusName;
//
//    @ApiModelProperty(value = "店铺编码")
//    @Excel(name = "店铺编码", width = 20,numFormat = "4")
//    private Long shopId;

    @ApiModelProperty(value = "店铺编码", example = "店铺编码")
    private String shopUrl;

    @ApiModelProperty(value = "最近交易时间", example = "最近交易时间")
    private String orderTime;

    @ApiModelProperty(value = "商家名称")
    private String companyName;

    @ApiModelProperty(value = "店铺渠道")
    private String appName;

    @ApiModelProperty(value = "商家编码")
    private String memberCode;

    //    @ApiModelProperty(value = "卖家类型(1 自营  2:pop  3：pop)")
//    @Excel(name = "卖家类型", width = 20,numFormat = "9")
//    private String sellerType;
    @ApiModelProperty(value = "卖家类型(1 自营  2:pop  3：pop)")
    private String sellerTypeName;

    @ApiModelProperty(value = "入驻状态  0:未入驻 1：已入驻", example = "入驻状态")
    private String entryStatusName;

    @ApiModelProperty(value = "经营范围 1、酒水，2、3C数码，3、交通出行，4、建材，5、农资农机，6、厨具卫浴，7、家用电器，")
    private String businessIndustryName;

    @ApiModelProperty(value = "实际经营地址")
    private String actualBusinessAddress;

    @ApiModelProperty(value = "入驻时间")
    private Date joinTime;

    @ApiModelProperty(value = "统一信用代码")
    private String buyerBusinessLicenseId;

    @ApiModelProperty(value = "登记类型/市场主体类型")
    private String customerNature;

    @ApiModelProperty(value = "登记机关", example = "登记机关")
    private String belongOrg;

    @ApiModelProperty(value = "登记状态")
    private String regStatus;

    @ApiModelProperty(value = "法人姓名/负责人姓名")
    private String artificialPersonName;

    @ApiModelProperty(value = "注册资本 0: 0 ~ 100万 1：100万 ~ 500万，2：大于等于500万")
    private String registerCapital;

    @ApiModelProperty(value = "负责人电话号码")
    private String responsiblePersonMobile;

    @ApiModelProperty(value = "成立日期")
    private Date establishTime;

    @ApiModelProperty(value = "营业期限自")
    private Date businessLicenseRegistrationDate;

    @ApiModelProperty(value = "营业期限至")
    private Date businessLicenseEndDate;

    @ApiModelProperty(value = "核准日期")
    private Date approvalTime;

    @ApiModelProperty(value = "详细地址-脱敏", example = "江苏省南京市玄武区孝陵卫街道钟灵街50号")
    private String locationAddr;

    @ApiModelProperty(value = "年度交易额")
    private BigDecimal orderTotalAmount;

    @ApiModelProperty(value = "行政区划代码")
    private String areaCode;

    @ApiModelProperty(value = "营业执照链接")
    private String buyerBusinessLicensePicSrc;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;


}
