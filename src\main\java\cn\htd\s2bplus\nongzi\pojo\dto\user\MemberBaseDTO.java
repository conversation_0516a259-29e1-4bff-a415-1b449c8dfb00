package cn.htd.s2bplus.nongzi.pojo.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ProjectName: s2bplus-oop-api
 * @Package: cn.htd.s2bplus.nongzi.dto.user
 * @ClassName: MemberBaseDTO
 * @Author: 80333
 * @Description:
 * @Date: 2021/1/13 13:37
 */
@Data
@ApiModel
public class MemberBaseDTO implements Serializable {

    @ApiModelProperty(value = " 商家编码", notes = "商家编码", example = "123456")
    private String memberCode;
    @ApiModelProperty(value = " 公司注册地址所在镇", notes = "公司注册地址所在镇", example = "32056312")
    private String locationTown;
    @ApiModelProperty(value = " 法人手机号", notes = "法人手机号", example = "13*********")
    private String artificialPersonMobile;
    @ApiModelProperty(value = " 公司注册地址所在省份", notes = "公司注册地址所在省份", example = "32")
    private String locationProvince;
    @ApiModelProperty(value = "经营人手机号码", notes = "经营人手机号码", example = "***********")
    private String businessPersonMobile;
    @ApiModelProperty(value = " 公司名称", notes = "公司名称", example = "南通乐美学投资管理有限公司")
    private String companyName;
    @ApiModelProperty(value = "经营人姓名", notes = "经营人姓名", example = "张三")
    private String businessPersonName;
    @ApiModelProperty(value = " 公司注册地址所在区", notes = "公司注册地址所在区", example = "320501")
    private String locationCounty;
    @ApiModelProperty(value = " 公司注册详细地址", notes = "公司注册详细地址", example = "江苏省南京市玄武区孝陵卫街道钟灵街50号")
    private String locationDetail;
    @ApiModelProperty(value = "会员类型", notes = "会员类型 1.非会员、2.产业会员、3.标准会员", example = "1")
    private String memberType;
    @ApiModelProperty(value = "商家类型  空:会员店,1:内部供应商，2:外部供应商 ,3:分销商",example = "1")
    private String sellerType;
    @ApiModelProperty(value = " 公司注册地址所在城市", notes = "公司注册地址所在城市", example = "3205")
    private String locationCity;
    @ApiModelProperty(value = " 公司法人/自然人", notes = "公司法人/自然人", example = "公司法人")
    private String artificialPersonName;
    @ApiModelProperty(value = "会员id", notes = "会员ID", example = "10XX")
    private String memberId;
    @ApiModelProperty(value = " 经营范围", notes = "经营范围:1、酒水，2、3C数码，3、交通出行，4、建材，5、农资农机，6、厨具卫浴，7、家用电器，9、家居建材，10、微物流乡村站点，11、新能源，12、种植养殖，13、快消品", example = "1")
    private String businessScope;
    @ApiModelProperty(value = " 会员属性，也是客商属性", notes = "客商属性(vms的会员属性字段)：11、乡镇零售服务商，12、工程客户" +
            "13、代理批发，14、商超客户，15、零售客户", example = "11")
    private String buyerFeature;
    @ApiModelProperty(value = "实际经营地址-省市区镇详细地址",example = "江苏省南京市玄武区孝陵卫街道钟灵街50号")
    private String actualBusinessAddress;
    @ApiModelProperty(value = " 法人身份证号", notes = "法人身份证号", example = "320XXX")
    private String artificialPersonIdcard;
    @ApiModelProperty(value = " 法人身份证电子版图片地址", notes = "法人身份证电子版图片地址", example = "图片地址.jpg")
    private String artificialPersonPicSrc;
    @ApiModelProperty(value = " 法人身份证电子版图片地址(反面)", notes = "法人身份证电子版图片地址(反面)", example = "图片地址.jpg")
    private String artificialPersonPicBackSrc;
    @ApiModelProperty(value = " 会员营业执照电子版图片地址", notes = "会员营业执照电子版图片地址", example = "/图片.jpg")
    private String buyerBusinessLicensePicSrc;
    @ApiModelProperty(value = "业务/财务联系人手机号", notes = "业务/财务联系人手机号", example = "131XXX")
    private String operatingMobile;
    @ApiModelProperty(value = "业务/财务联系人姓名", notes = "业务/财务联系人姓名", example = "范XX")
    private String operatingName;
    @ApiModelProperty(value = "客户经理",example = "zs")
    private String customManagerName;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"memberCode\":\"")
                .append(memberCode).append('\"');
        sb.append(",\"locationTown\":\"")
                .append(locationTown).append('\"');
        sb.append(",\"artificialPersonMobile\":\"")
                .append(artificialPersonMobile).append('\"');
        sb.append(",\"locationProvince\":\"")
                .append(locationProvince).append('\"');
        sb.append(",\"businessPersonMobile\":\"")
                .append(businessPersonMobile).append('\"');
        sb.append(",\"companyName\":\"")
                .append(companyName).append('\"');
        sb.append(",\"businessPersonName\":\"")
                .append(businessPersonName).append('\"');
        sb.append(",\"locationCounty\":\"")
                .append(locationCounty).append('\"');
        sb.append(",\"locationDetail\":\"")
                .append(locationDetail).append('\"');
        sb.append(",\"memberType\":\"")
                .append(memberType).append('\"');
        sb.append(",\"sellerType\":\"")
                .append(sellerType).append('\"');
        sb.append(",\"locationCity\":\"")
                .append(locationCity).append('\"');
        sb.append(",\"artificialPersonName\":\"")
                .append(artificialPersonName).append('\"');
        sb.append(",\"memberId\":\"")
                .append(memberId).append('\"');
        sb.append(",\"businessScope\":\"")
                .append(businessScope).append('\"');
        sb.append(",\"buyerFeature\":\"")
                .append(buyerFeature).append('\"');
        sb.append(",\"actualBusinessAddress\":\"")
                .append(actualBusinessAddress).append('\"');
        sb.append(",\"artificialPersonIdcard\":\"")
                .append(artificialPersonIdcard).append('\"');
        sb.append(",\"artificialPersonPicSrc\":\"")
                .append(artificialPersonPicSrc).append('\"');
        sb.append(",\"artificialPersonPicBackSrc\":\"")
                .append(artificialPersonPicBackSrc).append('\"');
        sb.append(",\"buyerBusinessLicensePicSrc\":\"")
                .append(buyerBusinessLicensePicSrc).append('\"');
        sb.append(",\"operatingMobile\":\"")
                .append(operatingMobile).append('\"');
        sb.append(",\"operatingName\":\"")
                .append(operatingName).append('\"');
        sb.append(",\"customManagerName\":\"")
                .append(customManagerName).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
