package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class QueryPurchaserOrderDetailDTO implements Serializable {

    @ApiModelProperty(value = "采购单唯一键")
    private String orderNum;

    @ApiModelProperty(value = "供应商编码",hidden = true)
    private String supplierCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
