package cn.htd.s2bplus.nongzi.pojo.dto.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * 财务域报表信息表
 *
 * <AUTHOR>

 * @date 2020-11-05 13:40:48
 */
@Data
public class FinanceReportformEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId (value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 类型：0：云场结算单，1：OSS结算单，2oss佣金单
     */
    @ApiModelProperty(value = "类型：0：云场结算单，1：OSS结算单，2:oss佣金单，3：oss商家佣金，4：服务商收货地址")
    private String type;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createId;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String createName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    /**
     * 下载链接
     */
    @ApiModelProperty(value = "下载链接")
    private String downUrl;
    /**
     * 报表生成开始时间
     */
    @ApiModelProperty(value = "报表生成开始时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 报表生成结束时间
     */
    @ApiModelProperty(value = "报表生成结束时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "分页查询条数")
    @NotEmpty(message = "分页查询条数不能为空")
    private int pagesize;

    @ApiModelProperty(value = "分页查询页数")
    @NotEmpty(message = "分页查询页数不能为空")
    private int current;

    @ApiModelProperty(value = "文件名称")
    private String fileName;
}

