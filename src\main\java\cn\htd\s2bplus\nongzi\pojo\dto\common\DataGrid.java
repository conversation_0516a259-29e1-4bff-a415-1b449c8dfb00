package cn.htd.s2bplus.nongzi.pojo.dto.common;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * DataGrid模型
 */
@Data
public class DataGrid<T> implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long total = 0L; // 总记录数
	private List<T> rows = new ArrayList<T>(); // 结果集

	private int pageNum = 0; // 第几页
	private int pageSize = 0; // 每页记录数
	private int pages = 0; // 总页数
	private int size = 0; // 当前页的数量 <= pageSize，该属性来自ArrayList的size属性
	private int startRow; // 起始行
	private int endRow; // 末行

	@Override
	public String toString() {
		return "{\"total\":\"" + total + "\", \"rows\":\"" + rows + "\", \"pageNum\":\"" + pageNum
				+ "\", \"pageSize\":\"" + pageSize + "\", \"pages\":\"" + pages + "\", \"size\":\"" + size
				+ "\", \"startRow\":\"" + startRow + "\", \"endRow\":\"" + endRow + "\"}";
	}

}
