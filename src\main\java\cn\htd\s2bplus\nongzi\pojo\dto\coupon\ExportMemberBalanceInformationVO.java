package cn.htd.s2bplus.nongzi.pojo.dto.coupon;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@ExcelTarget("ExportMemberBalanceInformationVO")
@Data
public class ExportMemberBalanceInformationVO implements Serializable {

    private static final long serialVersionUID = -4559465795596543080L;

    @Excel(name = "会员编码", height = 10, width = 30)
    private String memberCode;

    @Excel(name = "会员名称", height = 10, width = 30)
    private String memberName;

    @Excel(name = "活动名称", height = 10, width = 30)
    private String promotionName;

    @Excel(name = "应发总金额", height = 10, width = 30)
    private BigDecimal planSendAmount;

    @Excel(name = "上次结余", height = 10, width = 30)
    private BigDecimal lastRemainAmount;

    @Excel(name = "实发总金额", height = 10, width = 30)
    private BigDecimal realSendAmount;

    @Excel(name = "结余总金额", height = 10, width = 30)
    private BigDecimal thisTimeRemainAmount;



    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
