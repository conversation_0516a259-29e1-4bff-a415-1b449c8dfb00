package cn.htd.s2bplus.nongzi.feign.goods;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportParticipleDto;
import cn.htd.s2bplus.nongzi.pojo.dto.order.TradeOrderBargainDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wangxuan
 * @description: 搜索分词配置feign接口
 * @date: 2023/3/2 10:57
 */
@FeignClient(name = "s2bplus-goods-service")
public interface SearchParticipleFeignService {

    /**
     * 搜索分词批量导入
     * @return
     */
    @PostMapping(value = "/searchParticiple/importBatchParticiple")
    Result<Boolean> importBatchParticiple(@RequestBody List<ImportParticipleDto> importParticipleDtos);
}
