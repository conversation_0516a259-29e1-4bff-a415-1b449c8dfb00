package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class CloudTradeOrderItemsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(
            value = "订单行ID",
            example = "1"
    )
    private Long id;
    @ApiModelProperty(
            value = "订单号",
            example = "1"
    )
    private String orderNo;
    @ApiModelProperty(
            value = "订单行号",
            example = "1"
    )
    private String orderItemNo;
    @ApiModelProperty(
            value = "渠道编码",
            example = "123456"
    )
    private String channelCode;
    @ApiModelProperty(
            value = "商品编码",
            example = "123456"
    )
    private String itemCode;
    @ApiModelProperty(
            value = "商品名称",
            example = "华为手机"
    )
    private String goodsName;
    @ApiModelProperty(
            value = "商品SKU编码",
            example = "123456"
    )
    private String skuCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
