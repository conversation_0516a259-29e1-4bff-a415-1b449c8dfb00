package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> gm
 * @create 2024/2/29
 */
@Data
@ApiModel
public class CommissionOrderListExportDTO implements Serializable {


    @ApiModelProperty(value = "订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "买家编号")
    @Excel(name = "客户编码")
    private String buyerCode;

    @ApiModelProperty(value = "买家名称")
    @Excel(name = "客户名称")
    private String buyerName;

    @ApiModelProperty(value = "商家编码")
    @Excel(name = "商家编码")
    private String sellerCode;

    @ApiModelProperty(value = "卖家名称")
    @Excel(name = "商家名称")
    private String sellerName;

    @ApiModelProperty(value = "店铺名称")
    @Excel(name = "交易店铺")
    private String shopName;

    @ApiModelProperty(value = "订单商品总数量")
    private String totalGoodsCount;

    @ApiModelProperty(value = "订单实付金额")
    @Excel(name = "订单实付金额(元)")
    private String orderPayAmount;

    @ApiModelProperty(value = "订单创建时间")
    @Excel(name = "下单时间")
    private String createTime;

    @ApiModelProperty(value = "确认收货时间", example = "")
    @Excel(name = "确认收货时间")
    private String orderReceiptTime;

    @ApiModelProperty(value = "渠道名称")
    @Excel(name = "订单来源渠道")
    private String appName;

    @ApiModelProperty(value = "渠道编码")
    private String appCode;

    @ApiModelProperty(value = "商家类型")
    @Excel(name = "商家类型")
    private String sellerTypeName;

    @ApiModelProperty(value = "运营佣金总金额")
    @Excel(name = "运营佣金金额（元）")
    private String totalCommissionAmount;

    @ApiModelProperty(value = "技术佣金金额")
    @Excel(name = "技术佣金金额（元）")
    private String totalTechnologyAmount;


    @ApiModelProperty(value = "账存支付方式 ：账存支付 线下汇款")
    @Excel(name = "支付方式")
    private String depositPayType;

    @ApiModelProperty(value = "线下结算状态描述")
    @Excel(name = "线下结算状态")
    private String offlineSettlementStatusDesc;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
