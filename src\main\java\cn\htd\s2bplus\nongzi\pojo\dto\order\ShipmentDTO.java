package cn.htd.s2bplus.nongzi.pojo.dto.order;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: xqw
 * @date: 2020/10/14
 * @time: 10:41
 */
public class ShipmentDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "阶段单号")
    private String orderStageNo;
    @ApiModelProperty(value = "仓储出库单编号")
    private String deliveryOrderCode;
    @ApiModelProperty(value = "仓储发货单编号")
    private String warehouseDeliverCode;
    @ApiModelProperty(value = "完成发货时间")
    private Date consignmentFinishTime = new Date();
    @ApiModelProperty(value = "发货单明细集合")
    List<ShipmentDetailDTO> detailList;
    @ApiModelProperty(value = "物流信息集合")
    List<LogisticsDTO> logisticsList;

    public ShipmentDTO() {
    }

    public Long getId() {
        return this.id;
    }

    public String getOrderStageNo() {
        return this.orderStageNo;
    }

    public String getDeliveryOrderCode() {
        return this.deliveryOrderCode;
    }

    public String getWarehouseDeliverCode() {
        return this.warehouseDeliverCode;
    }

    public Date getConsignmentFinishTime() {
        return this.consignmentFinishTime;
    }

    public List<ShipmentDetailDTO> getDetailList() {
        return this.detailList;
    }

    public List<LogisticsDTO> getLogisticsList() {
        return this.logisticsList;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setOrderStageNo(String orderStageNo) {
        this.orderStageNo = orderStageNo;
    }

    public void setDeliveryOrderCode(String deliveryOrderCode) {
        this.deliveryOrderCode = deliveryOrderCode;
    }

    public void setWarehouseDeliverCode(String warehouseDeliverCode) {
        this.warehouseDeliverCode = warehouseDeliverCode;
    }

    public void setConsignmentFinishTime(Date consignmentFinishTime) {
        this.consignmentFinishTime = consignmentFinishTime;
    }

    public void setDetailList(List<ShipmentDetailDTO> detailList) {
        this.detailList = detailList;
    }

    public void setLogisticsList(List<LogisticsDTO> logisticsList) {
        this.logisticsList = logisticsList;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ShipmentDTO)) {
            return false;
        } else {
            ShipmentDTO other = (ShipmentDTO)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label95: {
                    Object this$id = this.getId();
                    Object other$id = other.getId();
                    if (this$id == null) {
                        if (other$id == null) {
                            break label95;
                        }
                    } else if (this$id.equals(other$id)) {
                        break label95;
                    }

                    return false;
                }

                Object this$orderStageNo = this.getOrderStageNo();
                Object other$orderStageNo = other.getOrderStageNo();
                if (this$orderStageNo == null) {
                    if (other$orderStageNo != null) {
                        return false;
                    }
                } else if (!this$orderStageNo.equals(other$orderStageNo)) {
                    return false;
                }

                Object this$deliveryOrderCode = this.getDeliveryOrderCode();
                Object other$deliveryOrderCode = other.getDeliveryOrderCode();
                if (this$deliveryOrderCode == null) {
                    if (other$deliveryOrderCode != null) {
                        return false;
                    }
                } else if (!this$deliveryOrderCode.equals(other$deliveryOrderCode)) {
                    return false;
                }

                label74: {
                    Object this$warehouseDeliverCode = this.getWarehouseDeliverCode();
                    Object other$warehouseDeliverCode = other.getWarehouseDeliverCode();
                    if (this$warehouseDeliverCode == null) {
                        if (other$warehouseDeliverCode == null) {
                            break label74;
                        }
                    } else if (this$warehouseDeliverCode.equals(other$warehouseDeliverCode)) {
                        break label74;
                    }

                    return false;
                }

                label67: {
                    Object this$consignmentFinishTime = this.getConsignmentFinishTime();
                    Object other$consignmentFinishTime = other.getConsignmentFinishTime();
                    if (this$consignmentFinishTime == null) {
                        if (other$consignmentFinishTime == null) {
                            break label67;
                        }
                    } else if (this$consignmentFinishTime.equals(other$consignmentFinishTime)) {
                        break label67;
                    }

                    return false;
                }

                Object this$detailList = this.getDetailList();
                Object other$detailList = other.getDetailList();
                if (this$detailList == null) {
                    if (other$detailList != null) {
                        return false;
                    }
                } else if (!this$detailList.equals(other$detailList)) {
                    return false;
                }

                Object this$logisticsList = this.getLogisticsList();
                Object other$logisticsList = other.getLogisticsList();
                if (this$logisticsList == null) {
                    if (other$logisticsList != null) {
                        return false;
                    }
                } else if (!this$logisticsList.equals(other$logisticsList)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof ShipmentDTO;
    }

    public int hashCode() {

        int result = 1;
        Object $id = this.getId();
        result = result * 59 + ($id == null ? 43 : $id.hashCode());
        Object $orderStageNo = this.getOrderStageNo();
        result = result * 59 + ($orderStageNo == null ? 43 : $orderStageNo.hashCode());
        Object $deliveryOrderCode = this.getDeliveryOrderCode();
        result = result * 59 + ($deliveryOrderCode == null ? 43 : $deliveryOrderCode.hashCode());
        Object $warehouseDeliverCode = this.getWarehouseDeliverCode();
        result = result * 59 + ($warehouseDeliverCode == null ? 43 : $warehouseDeliverCode.hashCode());
        Object $consignmentFinishTime = this.getConsignmentFinishTime();
        result = result * 59 + ($consignmentFinishTime == null ? 43 : $consignmentFinishTime.hashCode());
        Object $detailList = this.getDetailList();
        result = result * 59 + ($detailList == null ? 43 : $detailList.hashCode());
        Object $logisticsList = this.getLogisticsList();
        result = result * 59 + ($logisticsList == null ? 43 : $logisticsList.hashCode());
        return result;
    }

    public String toString() {
        return "ShipmentDTO(id=" + this.getId() + ", orderStageNo=" + this.getOrderStageNo() + ", deliveryOrderCode=" + this.getDeliveryOrderCode() + ", warehouseDeliverCode=" + this.getWarehouseDeliverCode() + ", consignmentFinishTime=" + this.getConsignmentFinishTime() + ", detailList=" + this.getDetailList() + ", logisticsList=" + this.getLogisticsList() + ")";
    }
}
