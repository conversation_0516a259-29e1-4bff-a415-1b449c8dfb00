package cn.htd.s2bplus.nongzi.pojo.dto.distribute;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BasePageRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;


@Data
public class CommissionRecordReqDTO extends BasePageRequestDTO implements Serializable {
    @ApiModelProperty(value = "会员Id")
    @NotEmpty(message = "分销员Id不能为空")
    private String memberId;

    @ApiModelProperty(value = "变动类型 1:全部 2:增加 3:减少")
    private String changeType;

    @ApiModelProperty(value = "变动属性 0:全部 11:分销佣金 12:分销奖励 2:退款 3:提现")
    private String changeAttr;

    @ApiModelProperty(value = "关联订单号")
    private String orderNo;

    @ApiModelProperty(value = "结算状态 0 未发放 1 已发放")
    private String provideStatus;

    @ApiModelProperty(value = "更新开始检索范围 开始")
    private Date updateTimeStart;

    @ApiModelProperty(value = "更新结束检索范围 结束")
    private Date updateTimeEnd;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
