package cn.htd.s2bplus.nongzi.service.order.impl;

import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.feign.order.OrderFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.common.SaveOperationLogParamDTO;
import cn.htd.s2bplus.nongzi.service.order.BusinessOperateLogService;
import cn.htd.s2bplus.nongzi.service.order.SaveBusinessRecordLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;


@Service
@Slf4j
public class BusinessOperateLogServiceImpl implements BusinessOperateLogService {

	@Autowired
	private SaveBusinessRecordLogService saveBusinessRecordLogService;

	@Autowired
	private OrderFeignService orderFeignService;

	/**
	 * 记录商家操作日志
	 * @param user
	 * @param businessKey
	 * @param businessType
	 * @param operatorType
	 * @param operationRecord
	 */
	@Async
	@Override
	public void recordBusinessOperateLog(LoginUserDetail user, String businessKey, Integer businessType, Integer operatorType, String operationRecord) {
        log.info("记录商家操作日志,user:{},businessType:{},operatorType:{},operationRecord:{}",user.toString(),businessType,operatorType,operationRecord);
		try {
			if (ObjectUtils.isEmpty(businessType) || ObjectUtils.isEmpty(operatorType)) {
				log.info("记录商家操作日志类型错误,user:{},businessType:{},operatorType:{}",user.toString(),businessType,operatorType);
				return;
			}
			saveBusinessRecordLogService.saveRecord(this.getBusinessRecordLogData(businessKey, operationRecord, businessType, operatorType),user);
		} catch (Exception e) {
			log.error("创建商家操作日志异常,error", e);
		}
    }

	/**
	 * 组装日志记录数据
	 * @param businessKey
	 * @param operationRecord
	 * @param businessType
	 * @param operationType
	 * @return
	 */
	@Override
	public SaveOperationLogParamDTO getBusinessRecordLogData(String businessKey, String operationRecord, Integer businessType, Integer operationType) {
		SaveOperationLogParamDTO saveOperationLogParamDTO = new SaveOperationLogParamDTO();
		saveOperationLogParamDTO.setBusinessKey(businessKey);
		saveOperationLogParamDTO.setOperationRecord(operationRecord);
		saveOperationLogParamDTO.setBusinessType(businessType);
		saveOperationLogParamDTO.setOperatorType(operationType);
		return saveOperationLogParamDTO;
	}


}
