package cn.htd.s2bplus.nongzi.pojo.dto.order;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: xqw
 * @date: 2020/10/14
 * @time: 11:14
 */
public class LogisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    @ApiModelProperty(value = "发货单id")
    private Long consignmentWarehouseId;
    @ApiModelProperty(value = "发货时间")
    private Date consignmentTime = new Date();
    @ApiModelProperty(value = "物流公司名称")
    private String logisticsCompany;
    @ApiModelProperty(value = "快递物流单号")
    private String logisticsNo;
    @ApiModelProperty(value = "物流类别 1 快递公司物流 2 商家自配送物流")
    private String logisticsType = "1";
    @ApiModelProperty(value = "配送人姓名")
    private String sender;
    @ApiModelProperty(value = "发货人手机号")
    private String senderPhone;
    @ApiModelProperty(value = "物流备注")
    private String logisticsRemark;
    @ApiModelProperty(value = "物流状态 902 已确认发货 903 待收货 904 已妥投 905已签收")
    private String status;
    @ApiModelProperty(value = "阶段单号")
    private String orderStageNo;

    public LogisticsDTO() {
    }

    public Long getId() {
        return this.id;
    }

    public Long getConsignmentWarehouseId() {
        return this.consignmentWarehouseId;
    }

    public Date getConsignmentTime() {
        return this.consignmentTime;
    }

    public String getLogisticsCompany() {
        return this.logisticsCompany;
    }

    public String getLogisticsNo() {
        return this.logisticsNo;
    }

    public String getLogisticsType() {
        return this.logisticsType;
    }

    public String getSender() {
        return this.sender;
    }

    public String getSenderPhone() {
        return this.senderPhone;
    }

    public String getLogisticsRemark() {
        return this.logisticsRemark;
    }

    public String getStatus() {
        return this.status;
    }

    public String getOrderStageNo() {
        return this.orderStageNo;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setConsignmentWarehouseId(Long consignmentWarehouseId) {
        this.consignmentWarehouseId = consignmentWarehouseId;
    }

    public void setConsignmentTime(Date consignmentTime) {
        this.consignmentTime = consignmentTime;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }

    public void setLogisticsRemark(String logisticsRemark) {
        this.logisticsRemark = logisticsRemark;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setOrderStageNo(String orderStageNo) {
        this.orderStageNo = orderStageNo;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof LogisticsDTO)) {
            return false;
        } else {
            LogisticsDTO other = (LogisticsDTO)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label143: {
                    Object this$id = this.getId();
                    Object other$id = other.getId();
                    if (this$id == null) {
                        if (other$id == null) {
                            break label143;
                        }
                    } else if (this$id.equals(other$id)) {
                        break label143;
                    }

                    return false;
                }

                Object this$consignmentWarehouseId = this.getConsignmentWarehouseId();
                Object other$consignmentWarehouseId = other.getConsignmentWarehouseId();
                if (this$consignmentWarehouseId == null) {
                    if (other$consignmentWarehouseId != null) {
                        return false;
                    }
                } else if (!this$consignmentWarehouseId.equals(other$consignmentWarehouseId)) {
                    return false;
                }

                Object this$consignmentTime = this.getConsignmentTime();
                Object other$consignmentTime = other.getConsignmentTime();
                if (this$consignmentTime == null) {
                    if (other$consignmentTime != null) {
                        return false;
                    }
                } else if (!this$consignmentTime.equals(other$consignmentTime)) {
                    return false;
                }

                label122: {
                    Object this$logisticsCompany = this.getLogisticsCompany();
                    Object other$logisticsCompany = other.getLogisticsCompany();
                    if (this$logisticsCompany == null) {
                        if (other$logisticsCompany == null) {
                            break label122;
                        }
                    } else if (this$logisticsCompany.equals(other$logisticsCompany)) {
                        break label122;
                    }

                    return false;
                }

                label115: {
                    Object this$logisticsNo = this.getLogisticsNo();
                    Object other$logisticsNo = other.getLogisticsNo();
                    if (this$logisticsNo == null) {
                        if (other$logisticsNo == null) {
                            break label115;
                        }
                    } else if (this$logisticsNo.equals(other$logisticsNo)) {
                        break label115;
                    }

                    return false;
                }

                Object this$logisticsType = this.getLogisticsType();
                Object other$logisticsType = other.getLogisticsType();
                if (this$logisticsType == null) {
                    if (other$logisticsType != null) {
                        return false;
                    }
                } else if (!this$logisticsType.equals(other$logisticsType)) {
                    return false;
                }

                Object this$sender = this.getSender();
                Object other$sender = other.getSender();
                if (this$sender == null) {
                    if (other$sender != null) {
                        return false;
                    }
                } else if (!this$sender.equals(other$sender)) {
                    return false;
                }

                label94: {
                    Object this$senderPhone = this.getSenderPhone();
                    Object other$senderPhone = other.getSenderPhone();
                    if (this$senderPhone == null) {
                        if (other$senderPhone == null) {
                            break label94;
                        }
                    } else if (this$senderPhone.equals(other$senderPhone)) {
                        break label94;
                    }

                    return false;
                }

                label87: {
                    Object this$logisticsRemark = this.getLogisticsRemark();
                    Object other$logisticsRemark = other.getLogisticsRemark();
                    if (this$logisticsRemark == null) {
                        if (other$logisticsRemark == null) {
                            break label87;
                        }
                    } else if (this$logisticsRemark.equals(other$logisticsRemark)) {
                        break label87;
                    }

                    return false;
                }

                Object this$status = this.getStatus();
                Object other$status = other.getStatus();
                if (this$status == null) {
                    if (other$status != null) {
                        return false;
                    }
                } else if (!this$status.equals(other$status)) {
                    return false;
                }

                Object this$orderStageNo = this.getOrderStageNo();
                Object other$orderStageNo = other.getOrderStageNo();
                if (this$orderStageNo == null) {
                    if (other$orderStageNo != null) {
                        return false;
                    }
                } else if (!this$orderStageNo.equals(other$orderStageNo)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof LogisticsDTO;
    }



    public int hashCode() {
        int result = 1;
        Object $id = this.getId();
        result = result * 59 + ($id == null ? 43 : $id.hashCode());
        Object $consignmentWarehouseId = this.getConsignmentWarehouseId();
        result = result * 59 + ($consignmentWarehouseId == null ? 43 : $consignmentWarehouseId.hashCode());
        Object $consignmentTime = this.getConsignmentTime();
        result = result * 59 + ($consignmentTime == null ? 43 : $consignmentTime.hashCode());
        Object $logisticsCompany = this.getLogisticsCompany();
        result = result * 59 + ($logisticsCompany == null ? 43 : $logisticsCompany.hashCode());
        Object $logisticsNo = this.getLogisticsNo();
        result = result * 59 + ($logisticsNo == null ? 43 : $logisticsNo.hashCode());
        Object $logisticsType = this.getLogisticsType();
        result = result * 59 + ($logisticsType == null ? 43 : $logisticsType.hashCode());
        Object $sender = this.getSender();
        result = result * 59 + ($sender == null ? 43 : $sender.hashCode());
        Object $senderPhone = this.getSenderPhone();
        result = result * 59 + ($senderPhone == null ? 43 : $senderPhone.hashCode());
        Object $logisticsRemark = this.getLogisticsRemark();
        result = result * 59 + ($logisticsRemark == null ? 43 : $logisticsRemark.hashCode());
        Object $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : $status.hashCode());
        Object $orderStageNo = this.getOrderStageNo();
        result = result * 59 + ($orderStageNo == null ? 43 : $orderStageNo.hashCode());
        return result;
    }

    public String toString() {
        return "LogisticsDTO(id=" + this.getId() + ", consignmentWarehouseId=" + this.getConsignmentWarehouseId() + ", consignmentTime=" + this.getConsignmentTime() + ", logisticsCompany=" + this.getLogisticsCompany() + ", logisticsNo=" + this.getLogisticsNo() + ", logisticsType=" + this.getLogisticsType() + ", sender=" + this.getSender() + ", senderPhone=" + this.getSenderPhone() + ", logisticsRemark=" + this.getLogisticsRemark() + ", status=" + this.getStatus() + ", orderStageNo=" + this.getOrderStageNo() + ")";
    }

}
