package cn.htd.s2bplus.nongzi.pojo.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class TradeOrderBargainDTO implements Serializable {
    private static final long serialVersionUID = 5817905189327013817L;

    @ApiModelProperty(value = "单据号",required = true)
    @NotBlank(message = "单据号不能为空")
    private String finOrderNo;

    @ApiModelProperty(value = "实际交易金额")
    @NotBlank(message = "实际交易金额不能为空")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "单据商品行信息")
    @NotBlank(message = "单据商品行不能为空")
    private List<TradeOrderItemDTO> items;

    @ApiModelProperty(value = "结算状态：00：默认 10:待确认，30已结算")
    private String settlementStatus;

    @ApiModelProperty(value = "发货时间")
    private Date sendTime;

    @ApiModelProperty(value = "修改人ID",required = true)
    @NotNull(message = "修改人ID不能为空")
    private Long modifyId;

    @ApiModelProperty(value = "修改人名称",required = true)
    @NotBlank(message = "修改人名称不能为空")
    private String modifyName;
}
