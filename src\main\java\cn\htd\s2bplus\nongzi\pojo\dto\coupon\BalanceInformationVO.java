package cn.htd.s2bplus.nongzi.pojo.dto.coupon;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BalanceInformationVO implements Serializable {


    private static final long serialVersionUID = -3661484991038497713L;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "会员名称")
    private String memberName;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "应发金额")
    private BigDecimal planSendAmount;

    @ApiModelProperty(value = "实发金额")
    private BigDecimal realSendAmount;

    @ApiModelProperty(value = "结余金额")
    private BigDecimal thisTimeRemainAmount;

    @ApiModelProperty(value = "上次结余")
    private BigDecimal lastRemainAmount;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
