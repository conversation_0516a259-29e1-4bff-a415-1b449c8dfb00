package cn.htd.s2bplus.nongzi.service.history.impl;

import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.mapper.ReportHistoryMapper;
import cn.htd.s2bplus.nongzi.pojo.dto.common.Pager;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.ReportHistoryVo;
import cn.htd.s2bplus.nongzi.service.history.ReportHistoryService;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class ReportHistoryServiceImpl implements ReportHistoryService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * yyyy-MM-dd HH:mm:ss
     */
    private static final String YYYY_MM_DD_HH_MM_SS_SPLIT = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private ReportHistoryMapper reportHistoryMapper;

    @Override
    public  Result<ReportHistoryVo> queryReportHistoryPage(String finishBeginTime, String finishEndTime, Long memberId, Integer businessType, int page, int rows)  {
        Pager pager = new Pager();
        Date beginTime = null;
        Date endTime = null;
        List<ReportHistory> reportHistories = new ArrayList<>();
        ReportHistoryVo reportHistoryVo = new ReportHistoryVo();
        Result<ReportHistoryVo> result = new Result<>();
        try {
            //默认显示30天
            if (StringUtils.isEmpty(finishBeginTime) && StringUtils.isEmpty(finishEndTime)){
                Date date = DateUtil.rollDay(new Date(), -30);
                finishBeginTime = DateUtil.setDateToString(date);
            }
            pager.setPage(page);
            pager.setRows(rows);
            SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
            if (StringUtils.isNotEmpty(finishBeginTime)) {
                beginTime = sdf.parse(finishBeginTime);
            }
            if (StringUtils.isNotEmpty(finishEndTime)) {
                endTime = sdf.parse(finishEndTime);
            }
            logger.info("分页查询报表生成下载历史 入参 finishBeginTime:{},finishEndTime:{},memberId:{},businessType:{},page:{},rows:{}",finishBeginTime,finishEndTime,memberId,businessType,page,rows);
            Integer integer = reportHistoryMapper.queryReportHistoryCount(beginTime, endTime, memberId,businessType);
            if (integer > 0) {
                reportHistories = reportHistoryMapper.queryReportHistoryPage(beginTime, endTime, memberId,businessType,pager);
            }
            reportHistoryVo.setPages(page);
            reportHistoryVo.setTotal(Long.valueOf(integer));
            reportHistoryVo.setReportHistoryList(reportHistories);
            result.setData(reportHistoryVo);
            result.setCode(ResultEnum.SUCCESS.getCode());
            result.setMsg(ResultEnum.SUCCESS.getMsg());
        }catch (Exception e){
            logger.error("分页查询报表生成下载历史异常 ",e);
            result.setCode(ResultEnum.ERROR.getCode());
            result.setMsg("分页查询报表生成下载历史异常");
        }
        return result;
    }


    /**
     * 保存报表生成下载历史
     * @param downloadUrl
     */
    @Override
    public void saveReportHistory(String downloadUrl,Integer businessType, LoginUserDetail loginUser) {
        ReportHistory reportHistoryDTO = new ReportHistory();
        Date date = new Date();
        Long userId = loginUser.getUserId();
        String userName = loginUser.getUserName();
        Timestamp monthTime = DateUtil.getMonthTime(CommonConstants.THREEMONTH);
        reportHistoryDTO.setBusinessType(businessType.byteValue());
        reportHistoryDTO.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
        reportHistoryDTO.setDownloadUrl(downloadUrl);
        reportHistoryDTO.setFinishTime(date);
        reportHistoryDTO.setBeginTime(monthTime);
        reportHistoryDTO.setEndTime(monthTime);
        reportHistoryDTO.setCreateId(userId);
        reportHistoryDTO.setCreateName(userName);
        reportHistoryDTO.setCreateTime(date);
        reportHistoryDTO.setModifyId(userId);
        reportHistoryDTO.setModifyName(userName);
        reportHistoryDTO.setModifyTime(date);

        // 保存报表生成下载历史
        logger.info("保存报表生成下载历史 入参:{}",reportHistoryDTO);
        boolean insertSelectiveBoolean = reportHistoryMapper.insertSelective(reportHistoryDTO);
        logger.info("保存报表生成下载历史 出参:{}",insertSelectiveBoolean);
        if (!insertSelectiveBoolean){
            logger.info("保存报表生成下载历史出错");
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.REPORT_HISTORY_ERROR.getMsg());
        }
    }

}
