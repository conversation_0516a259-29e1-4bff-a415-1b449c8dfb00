package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/5
 */
@Data
public class WarehouseGoodsDetailApiVO implements Serializable
{

   @ApiModelProperty(name = "sku编码")
    private String skuCode;

   @ApiModelProperty(name = "仓库商品编码")
    private String stockCode;

   @ApiModelProperty(name = "商品名称")
    private String itemName;

   @ApiModelProperty(name = "仓库编码")
    private String warehouseCode;

   @ApiModelProperty(name = "仓库名称")
    private String warehouseName;

   @ApiModelProperty(name = "仓库类型编码")
    private Integer warehouseType;

   @ApiModelProperty(name = "仓库类型")
    private String warehouseTypeStr;

   @ApiModelProperty(name = "可用库存")
    private BigDecimal useNum;

   @ApiModelProperty(name = "订单占用库存")
    private BigDecimal reserveNum;

   @ApiModelProperty(name = "创建时间")
    private Date createTime;

}
