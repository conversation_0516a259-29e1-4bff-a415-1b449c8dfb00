package cn.htd.s2bplus.nongzi.service.goods.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.config.OssNacosConfig;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.BusinessTypeEnum;
import cn.htd.s2bplus.nongzi.enums.DataTagEnum;
import cn.htd.s2bplus.nongzi.enums.ImportBusinessTypeEnum;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.*;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.SellerSku;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.SellerSkuQueryDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.SellerSkuVO;
import cn.htd.s2bplus.nongzi.pojo.vo.ShopAuthSubAccountVO;
import cn.htd.s2bplus.nongzi.service.goods.GoodsService;
import cn.htd.s2bplus.nongzi.service.goods.ImportAndExportGoodsService;
import cn.htd.s2bplus.nongzi.service.order.SubAccountService;
import cn.htd.s2bplus.nongzi.service.purchase.BatchShipmentService;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
@RefreshScope
public class ImportAndExportGoodsServiceImpl implements ImportAndExportGoodsService {

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private MiddleGroundAPI middleGroundAPI;

    @Autowired
    private SubAccountService subAccountService;

    @Autowired
    private OssNacosConfig ossNacosConfig;

    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;

    @Autowired
    private GoodsFeignService goodsFeignService;

    @Autowired
    private BatchShipmentService batchShipmentService;


    /**
     * 导入货盘商品
     * @param file
     */
    public Result<Boolean> importGoodsInfo(MultipartFile file,String businessType,LoginUserDetail user, HttpServletResponse response){
        Result<Boolean> result = new Result<>();
        try{
            // 导入压缩包文件批量发布商品
            if (ImportBusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS.getType().equals(businessType)) {
                return batchShipmentService.handleCompressedFile(file, businessType, user);
            }

            // 校验导入文件格式
            List<ImportGoodsInfoDTO> importGoodsList = new ArrayList<>();
            if (ObjectUtils.isEmpty(file) || StringUtils.isEmpty(file.getOriginalFilename())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
            if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equals(substring) || StrConstant.XLSX.equals(substring))) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_FILE_ERROR.getMsg());
            }
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setStartSheetIndex(0);
            ExcelImportResult<ImportGoodsInfoDTO> resultExcel = ExcelImportUtil.importExcelMore(file.getInputStream(),
                    ImportGoodsInfoDTO.class, params);
            importGoodsList = resultExcel.getList();
            if (CollectionUtils.isEmpty(importGoodsList)) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), "导入文件不能为空，请核对");
            }
            if (importGoodsList.size() > nongZiNacosConfig.getImportGoodsSize()) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), "导入文件数量不能超过" + nongZiNacosConfig.getImportGoodsSize() + "条,请核对");
            }

            // 校验导入商品数据，将校验结果生成excel文件上传并保存，同时保存校验通过的货盘商品
            goodsService.importGoodsList(importGoodsList,user,file.getOriginalFilename(),response);
        }catch (BusinessException be){
            log.info("导入商品业务异常:{}", be.getMessage());
            result.setCode(be.getCode());
            result.setMsg(be.getMessage());
            return result;
        }catch (Exception e){
            log.error("导入商品异常",e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("导入商品异常");
            return result;
        }
        return CommonResultUtil.success(true);
    }

    /**
     * 	导出商品
     *
     */
    @Override
    @Async
    public void exportGoodsInfo(ExportGoodsInfoReqVO exportGoodsInfoDTO, LoginUserDetail user,HttpServletResponse response){
        // 组装查询商品列表入参
        SellerSkuQueryDTO queryReqDTO = this.buildQueryGoodsReqParam(exportGoodsInfoDTO,user);
        log.info("导出商品-查询商品列表 入参：{}", queryReqDTO);
        int initSize = 10;
        Result<SellerSkuVO> executeResult = middleGroundAPI.querySellerGoodsList(queryReqDTO,1, initSize);
        if (!executeResult.isSuccess() || executeResult.getData() == null
                || CollectionUtils.isEmpty(executeResult.getData().getSellerSkuList())) {
            return;
        }
        log.info("导出商品-查询商品列表条数 出参：{}", executeResult.getData().getTotal());
        long total = executeResult.getData().getTotal();
        // 组装查询商品列表出参，生成导出记录文件
        List<SellerSku> sellerSkuList = new ArrayList<>();
        if (total <= initSize) {
            sellerSkuList = executeResult.getData().getSellerSkuList();
        }else {
            int pageSize = 50;
            for (int tempPage = 1; sellerSkuList.size() < total; tempPage++) {
                Result<SellerSkuVO> tempResult = middleGroundAPI.querySellerGoodsList(queryReqDTO, tempPage, pageSize);
                if (!tempResult.isSuccess() || tempResult.getData() == null
                        || CollectionUtils.isEmpty(tempResult.getData().getSellerSkuList())) {
                   continue;
                }
                sellerSkuList.addAll(tempResult.getData().getSellerSkuList());
            }
        }
        List<ExportGoodsInfoRespDTO> exportGoodsDTOS = this.buildExportGoodsResp(sellerSkuList);
        OssUtils ossUtils = new OssUtils();
        String downloadUrl = ossUtils.getDownloadUrl(exportGoodsDTOS, ExportGoodsInfoRespDTO.class, "货盘商品列表", ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        if (StringUtils.isBlank(downloadUrl)) {
            log.info("上传货盘商品导出文件至阿里云失败");
            return;
        }
        //保存报表生成下载历史
        goodsService.saveReportHistoryToGoodsService(downloadUrl, BusinessTypeEnum.GOODS_SERVICE_EXPORT_GOODS_INFO_RECORD.getCode(),user);
    }

    /**
     * 组装查询商品列表参数
     */
    private SellerSkuQueryDTO buildQueryGoodsReqParam(ExportGoodsInfoReqVO exportGoodsInfoDTO, LoginUserDetail user) {
        SellerSkuQueryDTO queryReqDTO = new SellerSkuQueryDTO();
        BeanUtils.copyProperties(exportGoodsInfoDTO, queryReqDTO);
        queryReqDTO.setCreateItemScene(CommonConstants.CREATE_ITEM_SCENE);
        queryReqDTO.setSellerId(user.getMemberId());
        // 设置上下架状态
        this.setStatusList(queryReqDTO);
        queryReqDTO.setDataTag(DataTagEnum.NATIVE_CLOUD_CHANNEL.getCode());
        // 判断是否为子账号
        if (ObjectUtils.isNotEmpty(user.getParentAccount())) {
            queryReqDTO.setSellerId(user.getParentAccount().getMemberId());
            this.setShopIds(queryReqDTO, user);
        }
        return queryReqDTO;
    }

    /**
     * 组装导出的商品列表出参
     */
    private List<ExportGoodsInfoRespDTO> buildExportGoodsResp(List<SellerSku> sellerSkuList){
        List<ExportGoodsInfoRespDTO> exportGoodsDTOS = new ArrayList<>();
        sellerSkuList.forEach(sellerSku -> {
            ExportGoodsInfoRespDTO exportGoodsDTO = new ExportGoodsInfoRespDTO();
            BeanUtils.copyProperties(sellerSku,exportGoodsDTO);
            if(ObjectUtils.isNotEmpty(sellerSku.getItemExtendDTO())){
                exportGoodsDTO.setSupplierName(sellerSku.getItemExtendDTO().getSupplierName());
            }
            exportGoodsDTOS.add(exportGoodsDTO);
        });
        return exportGoodsDTOS;
    }

    /**
     * 设置上下架状态
     * @param queryReqDTO
     */
    private void setStatusList(SellerSkuQueryDTO queryReqDTO){
        List<Integer> statusList = new ArrayList<>(2);
        if (null == queryReqDTO.getStatus()){
            statusList.add(0);
            statusList.add(1);
        }else {
            statusList.add(queryReqDTO.getStatus());
        }
        queryReqDTO.setStatusList(statusList);
    }


    /**
     * 设置店铺
     */
    private void setShopIds(SellerSkuQueryDTO skuQueryDTO, LoginUserDetail user) {
        Result<List<ShopAuthSubAccountVO>> listResult = subAccountService.querySubAllocateShopByLoginId(user.getSubAccountLoginId(),"",user.getMemberCode());
        if(listResult.isSuccess()){
            if(!CollectionUtils.isEmpty(listResult.getData())){
                Map<Long,String> allocateShopList = listResult.getData().stream().
                        collect(Collectors.toMap(ShopAuthSubAccountVO::getShopId,ShopAuthSubAccountVO::getShopName));
                skuQueryDTO.setShopIds(new ArrayList<>(allocateShopList.keySet()));
            }else{
                skuQueryDTO.setFlag(true);
            }
        }else{
            throw new BusinessException(listResult.getCode(),listResult.getMsg());
        }
    }

    /**
     * 	导出发布失败商品-三个月以内
     *
     */
    @Async
    @Override
    public void exportPublishFailureRecord(LoginUserDetail user,HttpServletResponse response){
        String sellerCode = "";
        // 判断是否为子账号
        if (ObjectUtils.isNotEmpty(user.getParentAccount())){
            sellerCode = user.getParentAccount().getLoginId();
        }else {
            sellerCode = user.getLoginId();
        }
        log.info("查询发布失败商品 入参：{}",sellerCode);
        Result<List<ExportPublishFailureGoodsDTO>> listResult = goodsFeignService.queryPublishFailureGoods(CommonConstants.PUBLISH_IMPORT_GOODS_TYPE,sellerCode);
        log.info("查询发布失败商品 出参：{}",listResult);
        if(!listResult.isSuccess() || CollectionUtils.isEmpty(listResult.getData())){
            return;
        }
        OssUtils ossUtils = new OssUtils();
        String downloadUrl = ossUtils.getDownloadUrl(listResult.getData(), ExportPublishFailureGoodsDTO.class, sellerCode + "发布商品失败记录", ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        if (StringUtils.isBlank(downloadUrl)) {
            log.info("上传阿里云发布商品失败记录异常");
            return;
        }
        //保存报表生成下载历史
        goodsService.saveReportHistoryToGoodsService(downloadUrl, BusinessTypeEnum.GOODS_SERVICE_PUBLISH_GOODS_FAILURE_RECORD.getCode(),user);
    }

}
