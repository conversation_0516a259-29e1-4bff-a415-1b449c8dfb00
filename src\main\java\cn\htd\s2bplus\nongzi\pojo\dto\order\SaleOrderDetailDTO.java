package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * date: 2020/5/9
 */
@ExcelTarget("SaleOrderDetailDTO")
public class SaleOrderDetailDTO implements Serializable {



    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @Excel(name = "订单编号", height = 10, width = 20)
    private String orderNo;


    /**
     * 子订单号
     */
    @ApiModelProperty(value = "子订单号")
    @Excel(name = "子订单号", height = 10, width = 22)
    private String orderItemNo;


    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @Excel(name = "下单时间", height = 10, width = 20)
    private String createTime;


    /**
     * 会员店编码
     */
    @ApiModelProperty(value = "会员店编码")
    @Excel(name = "会员店编码", height = 10, width = 20)
    private String buyerCode;

    /**
     * 会员店名称
     */
    @ApiModelProperty(value = "会员店名称")
    @Excel(name = "会员店名称", height = 10, width = 20)
    private String buyerName;


    /**
     * sku编码
     */
    @ApiModelProperty(value = "sku编码")
    @Excel(name = "sku编码", height = 10, width = 20)
    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @Excel(name = "商品名称", height = 10, width = 20)
    private String goodsName;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    @Excel(name = "品牌", height = 10, width = 20)
    private String brandName;

    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位")
    @Excel(name = "销售单位", height = 10, width = 20)
    private String salesUnit;

    /**
     * 商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价
     */
    @ApiModelProperty(value = "商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价")
    @Excel(name = "销售单价", height = 10, width = 20)
    private BigDecimal goodsPrice;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    @Excel(name = "销售数量", height = 10, width = 20)
    private Integer goodsCount;


    /**
     * 运费金额
     */
    @ApiModelProperty(value = "运费金额")
    @Excel(name = "运费", height = 10, width = 20)
    private BigDecimal goodsFreight;

    /**
     * 优惠总金额  包含店铺优惠、平台优惠和使用返利的合计
     */
    @ApiModelProperty(value = "优惠总金额  包含店铺优惠、平台优惠和使用返利的合计")
    @Excel(name = "优惠总金额", height = 10, width = 20)
    private BigDecimal totalDiscountAmount;


    /**
     * 平台优惠金额   分担优惠券金额中，平台优惠金额
     */
    @ApiModelProperty(value = "平台优惠金额   分担优惠券金额中，平台优惠金额")
    @Excel(name = "平台补贴金额", height = 10, width = 20)
    private BigDecimal platformDiscountAmount;

    /**
     * 店铺优惠金额   分担优惠券金额中，店铺优惠金额
     */
    @ApiModelProperty(value = "店铺优惠金额   分担优惠券金额中，店铺优惠金额")
    @Excel(name = "商家补贴金额", height = 10, width = 20)
    private BigDecimal shopDiscountAmount;

    /**
     * 订单行实付金额
     */
    @ApiModelProperty(value = "订单行实付金额")
    @Excel(name = "实付金额", height = 10, width = 20)
    private BigDecimal orderItemPayAmount;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    @Excel(name = "收货人姓名", height = 10, width = 20)
    private String consigneeName;

    /**
     * 收货人联系电话
     */
    @ApiModelProperty(value = "收货人联系电话")
    @Excel(name = "收货人联系方式", height = 10, width = 20)
    private String consigneePhoneNum;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    @Excel(name = "收货地址", height = 10, width = 20)
    private String consigneeAddress;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    @Excel(name = "支付方式", height = 10, width = 20)
    private String  payType;

    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    @Excel(name = "支付渠道", height = 10, width = 20)
    private String  payChannel;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    @Excel(name = "订单类型", height = 10, width = 20)
    private String orderType;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    @Excel(name = "订单状态", height = 10, width = 20)
    private String orderStatus;


    /**
     * 订单备注
     */
    @ApiModelProperty(value = "订单备注")
    @Excel(name = "商家备注", height = 10, width = 20)
    private String  orderRemarks;

    /**
     * 买家留言
     */
    @ApiModelProperty(value = "买家留言")
    @Excel(name = "会员备注", height = 10, width = 20)
    private String  buyerRemarks;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderItemNo() {
        return orderItemNo;
    }

    public void setOrderItemNo(String orderItemNo) {
        this.orderItemNo = orderItemNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getBuyerCode() {
        return buyerCode;
    }

    public void setBuyerCode(String buyerCode) {
        this.buyerCode = buyerCode;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSalesUnit() {
        return salesUnit;
    }

    public void setSalesUnit(String salesUnit) {
        this.salesUnit = salesUnit;
    }

    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public Integer getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(Integer goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsFreight() {
        return goodsFreight;
    }

    public void setGoodsFreight(BigDecimal goodsFreight) {
        this.goodsFreight = goodsFreight;
    }

    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public BigDecimal getPlatformDiscountAmount() {
        return platformDiscountAmount;
    }

    public void setPlatformDiscountAmount(BigDecimal platformDiscountAmount) {
        this.platformDiscountAmount = platformDiscountAmount;
    }

    public BigDecimal getShopDiscountAmount() {
        return shopDiscountAmount;
    }

    public void setShopDiscountAmount(BigDecimal shopDiscountAmount) {
        this.shopDiscountAmount = shopDiscountAmount;
    }

    public BigDecimal getOrderItemPayAmount() {
        return orderItemPayAmount;
    }

    public void setOrderItemPayAmount(BigDecimal orderItemPayAmount) {
        this.orderItemPayAmount = orderItemPayAmount;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getConsigneePhoneNum() {
        return consigneePhoneNum;
    }

    public void setConsigneePhoneNum(String consigneePhoneNum) {
        this.consigneePhoneNum = consigneePhoneNum;
    }

    public String getConsigneeAddress() {
        return consigneeAddress;
    }

    public void setConsigneeAddress(String consigneeAddress) {
        this.consigneeAddress = consigneeAddress;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderRemarks() {
        return orderRemarks;
    }

    public void setOrderRemarks(String orderRemarks) {
        this.orderRemarks = orderRemarks;
    }

    public String getBuyerRemarks() {
        return buyerRemarks;
    }

    public void setBuyerRemarks(String buyerRemarks) {
        this.buyerRemarks = buyerRemarks;
    }


    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"orderNo\":\"")
                .append(orderNo).append('\"');
        sb.append(",\"orderItemNo\":\"")
                .append(orderItemNo).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append(",\"buyerCode\":\"")
                .append(buyerCode).append('\"');
        sb.append(",\"buyerName\":\"")
                .append(buyerName).append('\"');
        sb.append(",\"skuCode\":\"")
                .append(skuCode).append('\"');
        sb.append(",\"goodsName\":\"")
                .append(goodsName).append('\"');
        sb.append(",\"brandName\":\"")
                .append(brandName).append('\"');
        sb.append(",\"salesUnit\":\"")
                .append(salesUnit).append('\"');
        sb.append(",\"goodsPrice\":")
                .append(goodsPrice);
        sb.append(",\"goodsCount\":")
                .append(goodsCount);
        sb.append(",\"goodsFreight\":")
                .append(goodsFreight);
        sb.append(",\"totalDiscountAmount\":")
                .append(totalDiscountAmount);
        sb.append(",\"platformDiscountAmount\":")
                .append(platformDiscountAmount);
        sb.append(",\"shopDiscountAmount\":")
                .append(shopDiscountAmount);
        sb.append(",\"orderItemPayAmount\":")
                .append(orderItemPayAmount);
        sb.append(",\"consigneeName\":\"")
                .append(consigneeName).append('\"');
        sb.append(",\"consigneePhoneNum\":\"")
                .append(consigneePhoneNum).append('\"');
        sb.append(",\"consigneeAddress\":\"")
                .append(consigneeAddress).append('\"');
        sb.append(",\"payType\":\"")
                .append(payType).append('\"');
        sb.append(",\"payChannel\":\"")
                .append(payChannel).append('\"');
        sb.append(",\"orderType\":\"")
                .append(orderType).append('\"');
        sb.append(",\"orderStatus\":\"")
                .append(orderStatus).append('\"');
        sb.append(",\"orderRemarks\":\"")
                .append(orderRemarks).append('\"');
        sb.append(",\"buyerRemarks\":\"")
                .append(buyerRemarks).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
