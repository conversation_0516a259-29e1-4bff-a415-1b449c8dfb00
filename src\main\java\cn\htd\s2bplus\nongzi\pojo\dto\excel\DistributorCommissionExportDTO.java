package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class DistributorCommissionExportDTO implements Serializable {
    @Excel(name = "分销员Id")
    private Long memberId;
    @Excel(name = "分销账号")
    private String memberCode;
    @Excel(name = "分销账户名称",width = 50)
    private String businessPersonName;
    @Excel(name = "分销账户角色",width = 30)
    private String distributeType;

    @Excel(name = "累计佣金")
    private BigDecimal totalCommission;
    @Excel(name = "待结算佣金")
    private BigDecimal unsettledCommission;
    @Excel(name = "已结算佣金")
    private BigDecimal settledCommission;
    @Excel(name = "可提现佣金")
    private BigDecimal canWithdrawCommission;
    /**
     * 品牌分销:分销佣金等于累计佣金
     */
    @Excel(name = "分销佣金")
    private BigDecimal distributeCommission;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
