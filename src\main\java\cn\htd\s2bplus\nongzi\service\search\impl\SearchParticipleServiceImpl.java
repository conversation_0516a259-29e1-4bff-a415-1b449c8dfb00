package cn.htd.s2bplus.nongzi.service.search.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.feign.goods.SearchParticipleFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportParticipleDto;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ParticipleExcelDto;
import cn.htd.s2bplus.nongzi.service.search.SearchParticipleService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/**
 * @author: wangxuan
 * @description: TODO
 * @date: 2023/2/22 15:51
 */
@Service
@Slf4j
public class SearchParticipleServiceImpl implements SearchParticipleService {


    @Autowired
    SearchParticipleFeignService searchParticipleApi;

    HashMap<String,Integer> participleTypeDict =new HashMap<>();

    private static final Integer MAX_BATCH_SIZE=5000;
    private static final Integer MAX_FILE_SIZE=20000;

    @Override
    public Result<Boolean> importBatchParticiple (
            MultipartFile file, BaseDTO baseDTO, HttpServletResponse response) throws Exception {

        Result<Boolean> result=new Result<>();
        //校验文件是否为空
        if (file == null || file.isEmpty()) {
            log.info("上传文件为空");
            return ResultUtil.error(ResultEnum.FAILURE.getCode(),"上传文件为空");
        }
        //校验文件是否为excel文件
        String originalFilename = file.getOriginalFilename();
        String substring = originalFilename.substring(originalFilename.lastIndexOf(".") + 1, originalFilename.length());
        if (StringUtils.isEmpty(substring)) {
            log.info("仅支持Excel格式文件");
            return ResultUtil.error(ResultEnum.FAILURE.getCode(),"仅支持Excel格式文件");
        }
        if (ObjectUtils.isEmpty(baseDTO)) {
            log.info("登录人信息为空");
            return ResultUtil.error(ResultEnum.FAILURE.getCode(),"登录人信息为空");
        }
        if(CommonConstants.EXCEL_XLSX.contains(substring) || CommonConstants.EXCEL_XLS.contains(substring)){
            //ImportParams参数组装
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setHeadRows(1);
            List<String> list = new ArrayList<>();
            list.add("分词");
            list.add("同义词");
            list.add("分词类型");
            participleTypeDict.put("普通分词",1);
            participleTypeDict.put("同义词",2);
            participleTypeDict.put("过滤词",3);
            String[] strings = new String[list.size()];
            params.setImportFields(list.toArray(strings));
            //读取excel文件
            List<ParticipleExcelDto> fileListImport = ExcelImportUtil.importExcel(file.getInputStream(), ParticipleExcelDto.class, params);
            //校验一次导入不能超过20000条
            if(fileListImport.size()>MAX_FILE_SIZE){
                return CommonResultUtil.error(ResultEnum.FAILURE.getCode(),"采集词库一次导入不得超过20000条");
            }
            log.info("导入文件:{}",fileListImport);
            //校验空值(excel只有一条时，解析出的集合会有两个空对象)
            Iterator<ParticipleExcelDto> iterator= fileListImport.iterator();
            ParticipleExcelDto i;
            while (iterator.hasNext()){
                i=iterator.next();
                if(StringUtils.isBlank(i.getWord())&&StringUtils.isBlank(i.getParticipleType())){
                    iterator.remove();
                }
            }
            //对数据进行校验
            StringBuilder sb=new StringBuilder();
            this.checkImportExcel(fileListImport,sb);
            //数据封装
            List<ImportParticipleDto> importParticipleDtos=new ArrayList<>();
            for(ParticipleExcelDto participleExcelDto:fileListImport){
                ImportParticipleDto importParticipleDto=new ImportParticipleDto();
                importParticipleDto.setWord(participleExcelDto.getWord());
                Integer type = participleTypeDict.get(participleExcelDto.getParticipleType());
                importParticipleDto.setParticipleType(type);
                importParticipleDto.setConversionWord(participleExcelDto.getConversionWord());
                if(type!=2){
                    importParticipleDto.setConversionWord("");
                }
                importParticipleDto.setTrade(participleExcelDto.getTrade());
                importParticipleDto.setCreateId(baseDTO.getUser().getUserId());
                importParticipleDtos.add(importParticipleDto);
            }
            //记录批量调用失败条数
            int failCount=0;
            //分批调用
            List<List<ImportParticipleDto>> batchImportParticiples = ListUtils.partition(importParticipleDtos, MAX_BATCH_SIZE);
            for(List<ImportParticipleDto> importParticiples:batchImportParticiples){
                log.info("importBatchParticiple调用 调用入参:{}",importParticipleDtos);
                result= searchParticipleApi.importBatchParticiple(importParticiples);
                if(!result.isSuccess()){
                    failCount+=importParticiples.size();
                    continue;
                }
                log.info("importBatchParticiple调用 出参:{}",result);
            }
            if(failCount>0){
                result.setMsg("导入词库导入失败条数:"+failCount);
                result.setCode(ResultEnum.FAILURE.getCode());
                result.setData(false);
            }
        }else {
            result.setData(false);
            result.setSuccess(false);
            result.setCode(ResultEnum.FAILURE.getCode());
            result.setMsg("仅支持Excel格式文件！");
        }
        return result;
    }

    /**
     * 校验导入的excel模板
     * @param fileListImport
     * @param sb
     */
    private void checkImportExcel(List<ParticipleExcelDto> fileListImport, StringBuilder sb){
        if(CollectionUtils.isEmpty(fileListImport)){
            throw new BusinessException(ResultEnum.PARAM_VALID_ERROR.getCode(),"导入模板数据为空");
        }
        int rows = 2;
        int failureCount = 0;
        for(ParticipleExcelDto participleExcelDto:fileListImport){
            //校验每行分词是否为空
            String word= participleExcelDto.getWord();
            if(StringUtils.isEmpty(word)){
                sb.append("第").append(rows).append("行:").append("分词为空");
                failureCount++;
            }
            if(failureCount>0){
                throw new BusinessException(ResultEnum.PARAM_VALID_ERROR.getCode(), sb.toString());
            }
            //校验每行分词类型是否为空
            String participleType = participleExcelDto.getParticipleType();
            if(StringUtils.isBlank(participleType)){
                sb.append("第").append(rows).append("行:").append("分词类型为空");
                failureCount++;
            }
            if(failureCount>0){
                throw new BusinessException(ResultEnum.PARAM_VALID_ERROR.getCode(), sb.toString());
            }
            //校验如果分词类型为同义词时同义词项是否为空
            String conversionWord = participleExcelDto.getConversionWord();
            if("同义词".equals(participleType)){
                if(StringUtils.isBlank(conversionWord)){
                    sb.append("第").append(rows).append("行:").append("同义词为空");
                    failureCount++;
                    throw new BusinessException(ResultEnum.PARAM_VALID_ERROR.getCode(), sb.toString());
                }
            }
            if(failureCount>0){
                throw new BusinessException(ResultEnum.PARAM_VALID_ERROR.getCode(), sb.toString());
            }
            //校验分词是否重复
            if(!checkWordRepetition(fileListImport,word,participleType,conversionWord)){
                sb.append("第").append(rows).append("行:").append("分词重复");
                failureCount++;
            }
            if(failureCount>0){
                throw new BusinessException(ResultEnum.PARAM_VALID_ERROR.getCode(), sb.toString());
            }
            rows++;
        }
        if(failureCount>0){
            throw new BusinessException(ResultEnum.PARAM_VALID_ERROR.getCode(), sb.toString());
        }
    }

    /**
     * 校验分词是否重复
     * @param fileListImport
     * @param word
     * @return
     */
    private boolean checkWordRepetition(List<ParticipleExcelDto> fileListImport,String word,String participleType,String conversionWord){
        if(null == fileListImport || null == word){
            return true;
        }
        int count = 0;
        for(ParticipleExcelDto participleExcelDto:fileListImport){
            if(StringUtils.isBlank(participleExcelDto.getWord())||StringUtils.isBlank(word)){
                continue;
            }
            if(StringUtils.isBlank(participleExcelDto.getParticipleType())||StringUtils.isBlank(participleType)){
                continue;
            }
            if(participleExcelDto.getWord().equals(word)){
                if(participleExcelDto.getParticipleType().equals(participleType)){
                    if("同义词".equals(participleExcelDto.getParticipleType())){
                        if(StringUtils.isBlank(conversionWord)||StringUtils.isBlank(participleExcelDto.getConversionWord())){
                            continue;
                        }
                        if(participleExcelDto.getConversionWord().equals(conversionWord)){
                            count++;
                        }
                    }else{
                        count++;
                    }
                }
            }
        }
        if(count==1){
            return true;
        }
        return false;
    }


}
