package cn.htd.s2bplus.nongzi.pojo.dto.coupon;


import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 自动发券记录
 */
@Data
public class AutoSendCouponRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;



    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id 有锁编码时必传")
    private Long id;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String memberName;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String memberCode;

    /**
     * 计划发券(应发)
     */
    @ApiModelProperty(value = "计划发券(应发)")
    private BigDecimal planSendAmount;

    /**
     * 上次发券结余
     */
    @ApiModelProperty(value = "上次发券结余")
    private BigDecimal lastRemainAmount;

    /**
     * 单笔订单用券比例
     */
    @ApiModelProperty(value = "单笔订单用券比例")
    private BigDecimal singleOrderLimit;

    /**
     * 实发金额
     */
    @ApiModelProperty(value = "实发金额")
    private BigDecimal realSendAmount;

    /**
     * 本次发券结余
     */
    @ApiModelProperty(value = "本次发券结余")
    private BigDecimal thisTimeRemainAmount;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long promotionId;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    /**
     * 拆券类型 0:按照521面额拆 1:不拆分
     */
    @ApiModelProperty(value = "拆券类型 0:按照521面额拆 1:不拆分")
    private Integer splitType;

    /**
     * 发券商家编码
     */
    @ApiModelProperty(value = "发券商家编码")
    private String sellerCode;

    /**
     * 发券商家名称
     */
    @ApiModelProperty(value = "发券商家名称")
    private String sellerName;


    /**
     * 结余金额清算状态 0:未清算 1:已清算
     */
    @ApiModelProperty(value = "结余金额清算状态 0:未清算 1:已清算")
    private Integer remainStatus;


    /**
     * 锁编号
     */
    @ApiModelProperty(value = "锁编号")
    private Integer version;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
