package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class IntentionAddressRecordAddDTO implements Serializable {
    private static final long serialVersionUID = 216036445720855774L;

    @ApiModelProperty(value = "服务商编码")
    private String serviceProviderCode;

    @ApiModelProperty(value = "卖家编号")
    private String sellerCode;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "appleId编码")
    private String appleId;

    @ApiModelProperty(value = "收货人名称")
    private String consigneeName;

    @ApiModelProperty(value = "收货人电话")
    private String consigneeMobile;

    @ApiModelProperty(value = "采购部门编码")
    private String departmentCode;

    @ApiModelProperty(value = "采购部门名称")
    private String departmentName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "买家编码")
    private String buyerCode;

    @ApiModelProperty(value = "买家名称")
    private String buyerName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsCount;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "店铺ID")
    private String shopId;

    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Long modifyId;

    @ApiModelProperty(value = "更新人名称")
    private String modifyName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
