package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GoodsStockDTO implements Serializable {

    private static final long serialVersionUID = -2076582446468540751L;

    @ApiModelProperty(value = "id",hidden = true)
    private Long id;

    /**
     * 卖家编码
     */
    @ApiModelProperty(value = "卖家编码",hidden = true)
    private String sellerCode;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id",notes = "店铺id",example = "1")
    private Long shopId;

    /**
     * item编码
     */
    @NotBlank(message = "item编码不可为空")
    @ApiModelProperty(value = "item编码",required = true)
    private String itemCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称",notes = "商品名称",example = "1")
    private String itemName;

    /**
     * itemid
     */
    @NotNull(message = "商品id不可为空")
    @ApiModelProperty(value = "商品id",required = true)
    private Long itemId;

    /**
     * sku编码
     */
    @NotBlank(message = "sku编码不可为空")
    @ApiModelProperty(value = "sku编码",required = true)
    private String skuCode;

    /**
     * 可卖库存
     */
    @NotNull(message = "可卖库存不可为空")
    @ApiModelProperty(value = "可卖库存",required = true)
    @Range(max = 99999999,message = "虚拟库存数量:大于0的整数，最多99999999")
    private BigDecimal saleStockNum;

    @ApiModelProperty(value = "虚拟库存总数")
    private BigDecimal stockTotalNum;

    /**
     * 删除标记  0 未删除 1 已删除
     */
    @ApiModelProperty(value = "删除标记",hidden = true)
    private Integer deleteFlag;

    /**
     * 有效开始时间
     */
    @NotNull(message = "有效开始时间不可为空")
    @ApiModelProperty(value = "有效开始时间",required = true)
    private Date effectiveStartTime;

    /**
     * 有效结束时间
     */
    @NotNull(message = "有效结束时间不可为空")
    @ApiModelProperty(value = "有效结束时间",required = true)
    private Date effectiveEndTime;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID",hidden = true)
    private Long createId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createTime;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID",hidden = true)
    private Long modifyId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间",hidden = true)
    private Date modifyTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
