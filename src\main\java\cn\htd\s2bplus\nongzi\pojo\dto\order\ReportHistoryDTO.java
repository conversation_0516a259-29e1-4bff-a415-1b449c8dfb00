package cn.htd.s2bplus.nongzi.pojo.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

public class ReportHistoryDTO implements Serializable {


    private static final long serialVersionUID = -7403427845076706318L;
    /**
     * ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 业务类型，0=未知，1=销售单报表，2=结算单报表
     */
    private Byte businessType;

    /**
     * 报表状态,0=生成中，1=已生成，2=生成异常
     */
    private Byte reportStatus;

    /**
     * 文件下载路径
     */
    private String downloadUrl;

    /**
     * 报表生成完成时间
     */
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    /**
     * 查询条件开始时间
     */
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 查询条件结束时间
     */
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    private Long modifyId;

    /**
     * 更新人名称
     */
    private String modifyName;

    /**
     * 更新时间
     */
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * ID
     * @return id ID
     */
    public Long getId() {
        return id;
    }

    /**
     * ID
     * @param id ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 业务类型，0=未知，1=销售单报表，2=结算单报表
     * @return business_type 业务类型，0=未知，1=销售单报表，2=结算单报表
     */
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     * 业务类型，0=未知，1=销售单报表，2=结算单报表
     * @param businessType 业务类型，0=未知，1=销售单报表，2=结算单报表
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 报表状态,0=生成中，1=已生成，2=生成异常
     * @return report_status 报表状态,0=生成中，1=已生成，2=生成异常
     */
    public Byte getReportStatus() {
        return reportStatus;
    }

    /**
     * 报表状态,0=生成中，1=已生成，2=生成异常
     * @param reportStatus 报表状态,0=生成中，1=已生成，2=生成异常
     */
    public void setReportStatus(Byte reportStatus) {
        this.reportStatus = reportStatus;
    }

    /**
     * 文件下载路径
     * @return download_url 文件下载路径
     */
    public String getDownloadUrl() {
        return downloadUrl;
    }

    /**
     * 文件下载路径
     * @param downloadUrl 文件下载路径
     */
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl == null ? null : downloadUrl.trim();
    }

    /**
     * 报表生成完成时间
     * @return finish_time 报表生成完成时间
     */
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getFinishTime() {
        return finishTime;
    }

    /**
     * 报表生成完成时间
     * @param finishTime 报表生成完成时间
     */
    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    /**
     * 查询条件开始时间
     * @return begin_time 查询条件开始时间
     */
    public Date getBeginTime() {
        return beginTime;
    }

    /**
     * 查询条件开始时间
     * @param beginTime 查询条件开始时间
     */
    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    /**
     * 查询条件结束时间
     * @return end_time 查询条件结束时间
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * 查询条件结束时间
     * @param endTime 查询条件结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 创建人ID
     * @return create_id 创建人ID
     */
    public Long getCreateId() {
        return createId;
    }

    /**
     * 创建人ID
     * @param createId 创建人ID
     */
    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    /**
     * 创建人名称
     * @return create_name 创建人名称
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人名称
     * @param createName 创建人名称
     */
    public void setCreateName(String createName) {
        this.createName = createName == null ? null : createName.trim();
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新人ID
     * @return modify_id 更新人ID
     */
    public Long getModifyId() {
        return modifyId;
    }

    /**
     * 更新人ID
     * @param modifyId 更新人ID
     */
    public void setModifyId(Long modifyId) {
        this.modifyId = modifyId;
    }

    /**
     * 更新人名称
     * @return modify_name 更新人名称
     */
    public String getModifyName() {
        return modifyName;
    }

    /**
     * 更新人名称
     * @param modifyName 更新人名称
     */
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName == null ? null : modifyName.trim();
    }

    /**
     * 更新时间
     * @return modify_time 更新时间
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 更新时间
     * @param modifyTime 更新时间
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }


    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"id\":")
                .append(id);
        sb.append(",\"businessType\":")
                .append(businessType);
        sb.append(",\"reportStatus\":")
                .append(reportStatus);
        sb.append(",\"downloadUrl\":\"")
                .append(downloadUrl).append('\"');
        sb.append(",\"finishTime\":\"")
                .append(finishTime).append('\"');
        sb.append(",\"beginTime\":\"")
                .append(beginTime).append('\"');
        sb.append(",\"endTime\":\"")
                .append(endTime).append('\"');
        sb.append(",\"createId\":")
                .append(createId);
        sb.append(",\"createName\":\"")
                .append(createName).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append(",\"modifyId\":")
                .append(modifyId);
        sb.append(",\"modifyName\":\"")
                .append(modifyName).append('\"');
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
