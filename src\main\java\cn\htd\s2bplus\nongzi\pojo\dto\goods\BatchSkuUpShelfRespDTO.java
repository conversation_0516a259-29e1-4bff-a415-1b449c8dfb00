package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchSkuUpShelfRespDTO implements Serializable {
    private static final long serialVersionUID = -2706309139281296392L;

    @ApiModelProperty(value = "sku导入失败文件地址")
    private String fileUrl;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
