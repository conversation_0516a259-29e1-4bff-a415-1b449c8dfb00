package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@EqualsAndHashCode
public class ImportCouponRuleDTO implements Serializable {

	private static final long serialVersionUID = 9093076679579831694L;
	//会员编码
	private String memberCode;

	//待发券面额
	private BigDecimal waitRemainAmount;

	//单笔订单用券比例
	private BigDecimal singleOrderLimit;


	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
	}
}
