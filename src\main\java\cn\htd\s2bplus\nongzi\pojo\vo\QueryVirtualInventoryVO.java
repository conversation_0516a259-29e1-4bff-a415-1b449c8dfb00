package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class QueryVirtualInventoryVO implements Serializable {

    /**
     * 店铺ID
     */
    @ApiModelProperty(value = "店铺ID",notes = "店铺ID",example = "1")
    private Long shopId;

    /**
     * sku编码
     */
    @ApiModelProperty(value = "sku编码",notes = "sku编码",example = "1")
    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称",notes = "商品名称",example = "1")
    private String itemName;

    /**
     * 规格(销售属性集合名称)
     */
    @ApiModelProperty(value = "规格(销售属性集合名称)",notes = "规格(销售属性集合名称)",example = "1")
    private String attributesName;

    /**
     * 虚拟库存总数
     */
    @ApiModelProperty(value = "虚拟库存总数",notes = "虚拟库存总数",example = "1")
    private BigDecimal totalVirtualInventory;

    /**
     * 剩余可卖虚拟库存
     */
    @ApiModelProperty(value = "剩余可卖虚拟库存",notes = "剩余可卖虚拟库存",example = "1")
    private BigDecimal saleVirtualInventory;

    /**
     * 已售数量
     */
    @ApiModelProperty(value = "已售数量",notes = "已售数量",example = "1")
    private BigDecimal soldVirtualInventory;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间",notes = "生效时间",example = "1")
    private String effectiveTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间",notes = "创建时间",example = "1")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 库存状态 0:未开始,1:生效中,2:已过期
     */
    @ApiModelProperty(value = "库存状态 0:未开始,1:生效中,2:已过期",notes = "库存状态 0:未开始,1:生效中,2:已过期",example = "1")
    private String inventoryStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
