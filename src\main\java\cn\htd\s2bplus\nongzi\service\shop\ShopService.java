package cn.htd.s2bplus.nongzi.service.shop;

import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.SellerSku;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.SellerSkuQuery;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;

/**
 * 商品服务
 *
 */
public interface ShopService {

    void importGoodsList(BaseDTO baseDto, SellerSkuQuery sellerSkuQuery, HttpServletResponse response) throws ParseException;
    void queryGoodsStock(List<SellerSku> sellerSkus, String sellerCode);
}

