package cn.htd.s2bplus.nongzi.pojo.dto.membergroup;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QueryMemberGroupRelationDTO implements Serializable {

    @ApiModelProperty(value = "商家编码", example = "926388")
    private String sellerId;

    @ApiModelProperty(value = "新增删除会员编码拼接字符串", example = "1")
    private List<String> buyerIdList;

    @ApiModelProperty(value = "分组类型", example = "空默认、1指定人、2按行业")
    private Integer groupType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
