package cn.htd.s2bplus.nongzi.service.distribute.impl;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.rdc.base.development.framework.core.util.StringUtil;
import cn.htd.rdc.base.development.framework.core.util.date.DateUtil;
import cn.htd.s2bplus.common.AppNacosConfig;
import cn.htd.s2bplus.common.enums.PlatformEnum;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.config.OssNacosConfig;
import cn.htd.s2bplus.nongzi.enums.BusinessTypeEnum;
import cn.htd.s2bplus.nongzi.enums.MemberFromEnum;
import cn.htd.s2bplus.nongzi.feign.base.BaseFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.CommissionRecordDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.CommissionRecordReqDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.DistributorCommissionDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.DistributorCommissionReqDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.CommissionRecordExportDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.DistributorCommissionExportDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.BuyerUserExportDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.BuyerUserRespDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.dto.user.PlatformUserQueryDTO;
import cn.htd.s2bplus.nongzi.service.distribute.DistributeService;
import cn.htd.s2bplus.nongzi.service.history.ReportHistoryService;
import cn.htd.s2bplus.nongzi.service.security.BaseService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class DistributeServiceImpl implements DistributeService {

    @Autowired
    private BaseFeignService baseFeignService;
    @Autowired
    private BaseService baseService;
    @Autowired
    private ReportHistoryService reportHistoryService;
    @Autowired
    private OssNacosConfig ossNacosConfig;
    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;
    @Autowired
    private AppNacosConfig appNacosConfig;

    @Autowired
    private MiddleGroundAPI middleGroundAPI;

    @Value("${cipher.common.saleOrderReportExportSceneId}")
    private String saleOrderReportExportSceneId;

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    @Async
    public void exportDistributorCommission(DistributorCommissionReqDTO req, HttpServletResponse response) {
        LoginUserDetail loginUser = req.getUser();

        Result<List<DistributorCommissionDTO>> queryResult = baseFeignService.queryDistributorCommission(req);
        log.info("查询分销员佣金汇总 总数:{}", ObjectUtils.isEmpty(queryResult.getData()) ? null : queryResult.getData().size());
        if (!queryResult.isSuccess() || ObjectUtils.isEmpty(queryResult.getData())) {
            log.info("查询分销员佣金汇总 出错:{}", queryResult);
            return;
        }
        List<DistributorCommissionDTO> dataList = queryResult.getData();
        List<DistributorCommissionExportDTO> dataExportList = new ArrayList<>(dataList.size());
        for (DistributorCommissionDTO dto : dataList) {
            DistributorCommissionExportDTO exportDTO = new DistributorCommissionExportDTO();
            BeanUtil.copy(dto, exportDTO);
            if (dto.getDistributeType() == 1) {
                if (PlatformEnum.CYC.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())) {
                    exportDTO.setDistributeType("推客");
                } else if (PlatformEnum.BRAND_POP.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())) {
                    exportDTO.setDistributeType("分销员");
                }
            } else {
                exportDTO.setDistributeType("合伙人");
            }
            dataExportList.add(exportDTO);
        }

        OssUtils ossUtils = new OssUtils();
        String fileName = BusinessTypeEnum.EXPORT_DISTRIBUTOR_COMMISSION.getMsg();
        //生成excel文件，并上传的阿里云服务器返回文件地址路径
        String downloadUrl = ossUtils.getDownloadUrl(dataExportList, DistributorCommissionExportDTO.class, fileName, ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        if (StringUtils.isBlank(downloadUrl)) {
            log.info("获取上传文件地址出错:{}", req);
            return;
        }
        //保存报表生成下载历史
        reportHistoryService.saveReportHistory(downloadUrl, BusinessTypeEnum.EXPORT_DISTRIBUTOR_COMMISSION.getCode(),loginUser);
    }

    @Override
    @Async
    public void exportCommissionRecord(CommissionRecordReqDTO req, HttpServletResponse response) {
        LoginUserDetail loginUser = req.getUser();
        Result<List<CommissionRecordDTO>> queryResult = baseFeignService.queryCommissionRecord(req);
        log.info("查询分销员佣金明细 总数:{}", ObjectUtils.isEmpty(queryResult.getData()) ? null : queryResult.getData().size());
        if (!queryResult.isSuccess() || ObjectUtils.isEmpty(queryResult.getData())) {
            log.info("查询分销员佣金明细 出错:{}", queryResult);
            return;
        }
        List<CommissionRecordDTO> dataList = queryResult.getData();
        List<CommissionRecordExportDTO> dataExportList = new ArrayList<>(dataList.size());
        for (CommissionRecordDTO dto : dataList) {
            CommissionRecordExportDTO exportDTO = new CommissionRecordExportDTO();
            BeanUtil.copy(dto, exportDTO);
            if (dto.getDistributeType() == 1) {
                if (PlatformEnum.CYC.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())) {
                    exportDTO.setDistributeType("推客");
                } else if (PlatformEnum.BRAND_POP.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())) {
                    exportDTO.setDistributeType("分销员");
                }
            } else {
                exportDTO.setDistributeType("合伙人");
            }
            exportDTO.setUpdateTime(sdf.format(dto.getUpdateTime()));
            exportDTO.setChangeType("2".equals(dto.getChangeType())?"增加":"减少");
            // 11:分销佣金 12:分销奖励 2:退款 3:提现
            String changeAttr = dto.getChangeAttr();
            if ("2".equals(changeAttr)) {
                exportDTO.setChangeAttr("提现");
            } else if ("3".equals(changeAttr)) {
                exportDTO.setChangeAttr("退款");
            } else if ("11".equals(changeAttr)) {
                exportDTO.setChangeAttr("分销佣金");
            } else if ("12".equals(changeAttr)) {
                exportDTO.setChangeAttr("分销奖励");
            }
            exportDTO.setProvideStatus("0".equals(dto.getProvideStatus())?"未发放":"已发放");
            dataExportList.add(exportDTO);
        }

        OssUtils ossUtils = new OssUtils();
        String fileName = BusinessTypeEnum.EXPORT_COMMISSION_RECORD.getMsg();
        //生成excel文件，并上传的阿里云服务器返回文件地址路径
        String downloadUrl = ossUtils.getDownloadUrl(dataExportList, CommissionRecordExportDTO.class, fileName, ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        if (StringUtils.isBlank(downloadUrl)) {
            log.info("获取上传文件地址出错:{}", req);
            return;
        }
        //保存报表生成下载历史
        reportHistoryService.saveReportHistory(downloadUrl, BusinessTypeEnum.EXPORT_COMMISSION_RECORD.getCode(),loginUser);
    }

    @Override
    @Async
    public Result<String> exportUserList(PlatformUserQueryDTO platformUserQueryDTO, LoginUserDetail loginUser, HttpServletResponse response) {
        if(StringUtil.isNotBlank(platformUserQueryDTO.getFirstClassCode())){
            platformUserQueryDTO.setCustomerClassCode(platformUserQueryDTO.getFirstClassCode());
        }
        if(StringUtil.isNotBlank(platformUserQueryDTO.getSecondClassCode())){
            platformUserQueryDTO.setCustomerClassCode(platformUserQueryDTO.getSecondClassCode());
        }
        if(StringUtil.isNotBlank(platformUserQueryDTO.getFirstClassCode())
                && StringUtil.isNotBlank(platformUserQueryDTO.getSecondClassCode())){
            platformUserQueryDTO.setCustomerClassCode(platformUserQueryDTO.getFirstClassCode() + "-" + platformUserQueryDTO.getSecondClassCode());
        }
        platformUserQueryDTO.setPage(1);
        Integer exportUserListPageSize = nongZiNacosConfig.getExportUserListPageSize();
        platformUserQueryDTO.setSize(exportUserListPageSize);
        log.info("查询平台用户列表 req:{}",platformUserQueryDTO);
        Result<DataGrid<BuyerUserRespDTO>> buyerUserRespDTOResult = middleGroundAPI.selectBuyerUserListPage(platformUserQueryDTO);
        log.info("查询平台用户列表 resp:{}",buyerUserRespDTOResult);
        if(!buyerUserRespDTOResult.isSuccess()){
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),"查询平台用户列表失败");
        }
        Long total = buyerUserRespDTOResult.getData().getTotal();
        if(total == null || total == 0){
            return CommonResultUtil.success("无数据");
        }
        List<BuyerUserExportDTO> dataExportList = new ArrayList<>();
        this.convertExportList(buyerUserRespDTOResult.getData().getRows(), dataExportList);
        if(total > exportUserListPageSize){
            // 100条数据分页查询 从第二页开始补充查询
            for (int i = exportUserListPageSize; i < total; i = i + exportUserListPageSize) {
                platformUserQueryDTO.setPage(i/exportUserListPageSize + 1);
                Result<DataGrid<BuyerUserRespDTO>> buyerUserRespDTOResult1 = middleGroundAPI.selectBuyerUserListPage(platformUserQueryDTO);
                if(!buyerUserRespDTOResult1.isSuccess()){
                    return CommonResultUtil.error(ResultEnum.ERROR.getCode(),"查询平台用户列表失败");
                }
                this.convertExportList(buyerUserRespDTOResult1.getData().getRows(), dataExportList);
            }
        }
        OssUtils ossUtils = new OssUtils();
        String fileName = BusinessTypeEnum.EXPORT_CYC_USER_LIST.getMsg();
        //生成excel文件，并上传的阿里云服务器返回文件地址路径
        String downloadUrl = ossUtils.getDownloadUrl(dataExportList, BuyerUserExportDTO.class, fileName, ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        if (StringUtils.isBlank(downloadUrl)) {
            log.info("获取上传文件地址出错:{}", platformUserQueryDTO);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),"获取上传文件地址出错");
        }
        //保存报表生成下载历史
        reportHistoryService.saveReportHistory(downloadUrl, BusinessTypeEnum.EXPORT_CYC_USER_LIST.getCode(),loginUser);
        Result<String> successResult = CommonResultUtil.success(downloadUrl);
        log.info("导出平台用户列表成功:{}", successResult);
        return successResult;
    }

    @Override
    public Result<String> exportUserListSync(PlatformUserQueryDTO requestDTO, LoginUserDetail loginUser, HttpServletResponse response) {
        return this.exportUserList(requestDTO, loginUser, response);
    }

    private void convertExportList(List<BuyerUserRespDTO> dataList, List<BuyerUserExportDTO> dataExportList) {
        for (BuyerUserRespDTO dto : dataList) {
            // 设置用户身份
            this.settingUserClassCode(dto);
            BuyerUserExportDTO exportDTO = new BuyerUserExportDTO();
            BeanUtil.copy(dto, exportDTO);
            if (nongZiNacosConfig.getBuyerMobileDecryptSwitch() == 1) {
                exportDTO.setDsBusinessPersonMobile(baseService.decryptSingle(dto.getDsBusinessPersonMobile(),saleOrderReportExportSceneId));
                // 实际经营地址
                exportDTO.setDsActualBusinessAddress(baseService.decryptSingle(dto.getDsActualBusinessAddress(),saleOrderReportExportSceneId));
            }
            // 会员来源
            exportDTO.setMemberFromStr(MemberFromEnum.getName(dto.getMemberFrom()));
            if (dto.getDistributorRoleFlag() == 1) {
                exportDTO.setDistributorRoleFlag("是");
            } else {
                exportDTO.setDistributorRoleFlag("否");
            }
            if ("1".equals(dto.getIsRealNameAuthenticated())) {
                exportDTO.setIsRealNameAuthenticated("是");
            } else {
                exportDTO.setIsRealNameAuthenticated("否");
            }
            if ("1".equals(dto.getRealNameStatus())) {
                exportDTO.setRealNameStatus("个人实名");
            } else if ("2".equals(dto.getRealNameStatus())) {
                exportDTO.setRealNameStatus("企业单位实名");
            } else {
                exportDTO.setRealNameStatus("未实名");
            }
            if ("2".equals(dto.getVerifyStatus())) {
                exportDTO.setVerifyStatus("审核通过");
            } else if ("3".equals(dto.getVerifyStatus())) {
                exportDTO.setVerifyStatus("审核驳回");
            } else {
                exportDTO.setVerifyStatus("审核中");
            }
            if (dto.getCustomerLabelSource() == 2) {
                exportDTO.setCustomerLabelSource("未确认");
            } else {
                exportDTO.setCustomerLabelSource("已确认");
            }
            // 一级分类 门店会员：MD、云店会员：YD、政企客户：ZQ
            if ("MD".equals(dto.getFirstClassCode())) {
                exportDTO.setFirstClassCode("门店会员");
            } else if ("YD".equals(dto.getFirstClassCode())) {
                exportDTO.setFirstClassCode("云店会员");
            } else if ("ZQ".equals(dto.getFirstClassCode())) {
                exportDTO.setFirstClassCode("政企客户");
            }
            //二级分类 会员门店：A1、授权门店：A2、经销代理：A3网店店主:B1、社群团长：B2、主播达人：B3政府/中大型企业:C1、普通企业:C2
            if ("A1".equals(dto.getSecondClassCode())) {
                exportDTO.setSecondClassCode("会员门店");
            } else if ("A2".equals(dto.getSecondClassCode())) {
                exportDTO.setSecondClassCode("授权门店");
            } else if ("A3".equals(dto.getSecondClassCode())) {
                exportDTO.setSecondClassCode("经销代理");
            } else if ("B1".equals(dto.getSecondClassCode())) {
                exportDTO.setSecondClassCode("网店店主");
            } else if ("B2".equals(dto.getSecondClassCode())) {
                exportDTO.setSecondClassCode("社群团长");
            } else if ("B3".equals(dto.getSecondClassCode())) {
                exportDTO.setSecondClassCode("主播达人");
            } else if ("C1".equals(dto.getSecondClassCode())) {
                exportDTO.setSecondClassCode("政府/中大型企业");
            } else if ("C2".equals(dto.getSecondClassCode())){
                exportDTO.setSecondClassCode("普通企业");
            }
            // 客户性质分类：MD-A1：门店会员-会员门店，MD-A2：门店会员-授权门店，MD-A2：门店会员-经销代理，YD-B1：云店会员-网店店主，YD-B2：云店客户-社群团长，YD-B3：云店客户-主播达人，ZQ-C1：政企客户-政府/中大型企业，ZQ-C2：政企客户-普通企业
            if ("MD-A1".equals(dto.getCustomerClassCode())) {
                exportDTO.setCustomerClassCode("门店会员-会员门店");
            } else if ("MD-A2".equals(dto.getCustomerClassCode())) {
                exportDTO.setCustomerClassCode("门店会员-授权门店");
            } else if ("MD-A3".equals(dto.getCustomerClassCode())) {
                exportDTO.setCustomerClassCode("门店会员-经销代理");
            } else if ("YD-B1".equals( dto.getCustomerClassCode()))
                exportDTO.setCustomerClassCode("云店会员-网店店主");
            else if ("YD-B2".equals(dto.getCustomerClassCode())) {
                exportDTO.setCustomerClassCode("云店客户-社群团长");
            } else if ("YD-B3".equals(dto.getCustomerClassCode())) {
                exportDTO.setCustomerClassCode("云店客户-主播达人");
            } else if ("ZQ-C1".equals(dto.getCustomerClassCode())) {
                exportDTO.setCustomerClassCode("政企客户-政府/中大型企业");
            } else if ("ZQ-C2".equals(dto.getCustomerClassCode())) {
                exportDTO.setCustomerClassCode("政企客户-普通企业");
            }
            // 注册时间
            if (dto.getRegisterTime() != null) {
                exportDTO.setRegisterTime(DateUtil.format(dto.getRegisterTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            dataExportList.add(exportDTO);
        }
    }

    /**
     * 设置用户身份
     */
    private void settingUserClassCode(BuyerUserRespDTO buyerUserRespDTO){
        if(StringUtil.isBlank(buyerUserRespDTO.getCustomerClassCode())){
            return;
        }
        String[] parts = buyerUserRespDTO.getCustomerClassCode().split("-");
        //判断数组大小
        if(parts.length != 2){
            log.error("客户性质分类格式不正确，请检查数据:{}", buyerUserRespDTO.getCustomerClassCode());
            return;
        }
        buyerUserRespDTO.setFirstClassCode(parts[0]);
        buyerUserRespDTO.setSecondClassCode(parts[1]);
    }
}
