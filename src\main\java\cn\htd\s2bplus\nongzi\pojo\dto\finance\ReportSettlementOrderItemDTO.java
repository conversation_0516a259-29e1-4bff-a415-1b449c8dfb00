package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2020/11/9 15:30
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class ReportSettlementOrderItemDTO implements Serializable {


    private static final long serialVersionUID = -8971928539451847299L;
    @ApiModelProperty(value = "订单编号")
    private String finOrderNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;


    @ApiModelProperty(value = "下单时间")
    private Date orderBeginTime;


    @ApiModelProperty(value = "商品编码")
    private String itemCode;


    @ApiModelProperty(value = "商品名称")
    private String itemName;


    @ApiModelProperty(value = "会员code")
    private String memberCode;


    @ApiModelProperty(value = "会员名称")
    private String memberName;


    @ApiModelProperty(value = "销售价格")
    private BigDecimal price;


    @ApiModelProperty(value = "商品数量")
    private BigDecimal quantity;


    @ApiModelProperty(value = "销售金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal realAmount;


    @ApiModelProperty(value = "其他费用")
    private BigDecimal otherPrice;


    @ApiModelProperty(value = "优惠金额")
    private BigDecimal preferentialAmount;

    @ApiModelProperty(value = "结算金额")
    private BigDecimal settlementAmount;


    @ApiModelProperty(value = "分佣方式 1：固定 2 百分比")
    private String distributionType;

    @ApiModelProperty(value = "分佣系数 对应百分比0.1,固定值：对应金额")
    private BigDecimal distributionValue;


    @ApiModelProperty(value = "确认收货时间")
    private Date acceptTime;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * 运费总金额
     */
    @ApiModelProperty(value = "运费总金额")
    private BigDecimal totalFreight;

    @ApiModelProperty(value = "佣金")
    private BigDecimal statementAmount;

    @ApiModelProperty(
            value = "手续费",
            notes = "手续费",
            example = "100.00"
    )
    private BigDecimal chargeAmount;

    @ApiModelProperty(value = "付款方式 1现金 2微信 3支付宝 4余额 5大额转账 6其他",position = 6)
    private String payType;

    @ApiModelProperty(
            value = "体现金额",
            notes = "体现金额",
            example = "100.00"
    )
    private BigDecimal withdrawAmount;

    @ApiModelProperty(value = "平台分佣金额",notes="平台分佣金额",example="100.00")
    private BigDecimal platformAmount;

    @ApiModelProperty(value = "商家分佣金额",notes="商家分佣金额",example="100.00")
    private BigDecimal sellerAmount;

    @ApiModelProperty(value = "运营商分佣金额",notes="运营商分佣金额",example="100.00")
    private BigDecimal operatorAmount;
}
