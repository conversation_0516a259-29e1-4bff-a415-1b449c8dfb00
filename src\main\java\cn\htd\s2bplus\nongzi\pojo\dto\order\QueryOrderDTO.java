package cn.htd.s2bplus.nongzi.pojo.dto.order;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * date: 2020/3/9
 */
public class QueryOrderDTO implements Serializable {

    private static final long serialVersionUID = 2213829110437296610L;


    @ApiModelProperty(value = "订单ID")
    private Long id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "会员ID")
    private Long buyerId;

    @ApiModelProperty(value = "会员编号")
    private String buyerCode;

    @ApiModelProperty(value = "会员类型0=未知， 1:非会员，2：担保会员，3：正式会员")
    private Byte buyerType;

    @ApiModelProperty(value = "会员名称")
    private String buyerName;

    @ApiModelProperty(value = "卖家Id")
    private Long sellerId;

    @ApiModelProperty(value = "卖家编号")
    private String sellerCode;

    @ApiModelProperty(value = "卖家类型,0=未知")
    private Byte sellerType;

    @ApiModelProperty(value = "卖家名称")
    private String sellerName;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "订单渠道0=S2B平台,1=江苏移动")
    private Byte orderFromChannel;

    @ApiModelProperty(value = "订单终端来源0=web,1=app,2=H5,3=小程序，4=公众号，5=其他")
    private Byte orderFromTerminal;

    @ApiModelProperty(value = "订单商品总数量")
    private Integer totalGoodsCount;

    @ApiModelProperty(value = "订单类型：0、普通订单，1、预售订单")
    private Byte orderType;

    @ApiModelProperty(value = "订单商品总金额")
    private BigDecimal totalGoodsAmount;

    @ApiModelProperty(value = "运费总金额")
    private BigDecimal totalFreight;

    @ApiModelProperty(value = "优惠总金额")
    private BigDecimal totalDiscountAmount;

    @ApiModelProperty(value = "店铺用券优惠总金额   分担优惠券总金额中，店铺优惠总金额")
    private BigDecimal shopDiscountAmount;

    @ApiModelProperty(value = "平台用券优惠总金额   分担优惠券总金额中，平台优惠总金额")
    private BigDecimal platformDiscountAmount;

    @ApiModelProperty(value = "议价后商品总价")
    private BigDecimal bargainingOrderAmount;

    @ApiModelProperty(value = "议价后运费金额")
    private BigDecimal bargainingOrderFreight;

    @ApiModelProperty(value = "订单总价")
    private BigDecimal orderTotalAmount;

    @ApiModelProperty(value = "订单实付金额=商品总金额-用券优惠总金额-议价优惠总金额")
    private BigDecimal orderPayAmount;

    @ApiModelProperty(value = "是否参与优惠 0：否，1：是")
    private Byte hasUsedCoupon;

    @ApiModelProperty(value = "是否议价(1:是，0:否)")
    private Byte changePrice;

    @ApiModelProperty(value = "订单状态 0：初始化,1=已取消,10:待审核,20:待支付,21:审核通过待支付,31:已支付待拆单，32：已支付已拆单待下行ERP,40:待发货,50:已发货,61:买家收货,62:到期自动收货")
    private String orderStatus;

    @ApiModelProperty(value = "订单收货时间")
    private Date orderReceiptTime;

    @ApiModelProperty(value = "支付方式：1：余额帐支付，2：平台账户支付，3：在线支付")
    private String payType;

    @ApiModelProperty(value = "支付时间")
    private Date payOrderTime;

    @ApiModelProperty(value = "支付状态 0-待支付,1-已支付,2-支付失败,3-支付异常（超时）")
    private String payStatus;

    @ApiModelProperty(value = "是否超出配送范围 0:否,1:-是")
    private Byte outDistribution;

    @ApiModelProperty(value = "是否为秒杀订单 0-否，1-是")
    private Byte timeLimitedOrder;

    @ApiModelProperty(value = "秒杀活动编码")
    private String promotionId;

    @ApiModelProperty(value = "订单备注")
    private String orderRemarks;

    @ApiModelProperty(value = "是否要发票(0:不要，1:要)")
    private Byte needInvoice;

    @ApiModelProperty(value = "配送方式1:供应商配送  2:自提")
    private String deliveryType;

    @ApiModelProperty(value = "物流总状态：00-待发货，01-已发货，02-配送中，03-已接收")
    private String logisticsStatus;

    @ApiModelProperty(value = "订单删除状态 0:未删除，1：已删除（可还原），2：彻底删除（不可还原）")
    private Byte orderDeleteStatus;

    @ApiModelProperty(value = "YJF：易极付 QT：钱途")
    private String payChannel;

    @ApiModelProperty(value = "确认时间")
    private Date confirmTime;

    @ApiModelProperty(value = "拆单类型 (1:手动拆单, 2:自动拆单)")
    private String seperateBillType;

    @ApiModelProperty(value = "商家用券优惠总金额")
    private BigDecimal sellerDiscountAmount;

    @ApiModelProperty(value = "支付回调状态 0，未支付，1，已支付未回调，2，已回调")
    private String payCallbackStatus;

    @ApiModelProperty(value = "是否为预售订单 0-否，1-是")
    private Byte prePay;

    @ApiModelProperty(value = "VIP折扣活动编号")
    private String vipDiscountPromotionId;

    @ApiModelProperty(value = "VIP折扣活动订单标识 0 不是 1 是")
    private String vipDiscount;

    @ApiModelProperty(value = "VIP折扣活动优惠总金额")
    private BigDecimal totalVipDiscountAmount;

    @ApiModelProperty(value = "VIP折扣订单使用的套餐订单编号")
    private String dealsNo;

    @ApiModelProperty(value = "额度扣减的总值")
    private BigDecimal totalVipDisGoodsAmount;

    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人ID")
    private Long modifyId;

    @ApiModelProperty(value = "更新人名称")
    private String modifyName;

    @ApiModelProperty(value = "更新时间")
    private Date modifyTime;

    /**
     * 订单渠道0=S2B平台,1=江苏移动
     */
    @ApiModelProperty(value = "订单渠道0=S2B平台,1=江苏移动")
    private String orderFromChannle;

    /**
     * 第三方订单号
     */
    @ApiModelProperty(value = "第三方订单号")
    private String thirdOrderNo;

    /**
     * 第三方渠道
     */
    @ApiModelProperty(value = "第三方渠道")
    private Integer thirdChannel;
    /**
     * 是否超出配送范围 0:否,1:-是
     */
    @ApiModelProperty(value = "是否超出配送范围 0:否,1:-是")
    private Integer outDistribtion;

    /**
     * 是否为秒杀订单 0-否，1-是
     */
    @ApiModelProperty(value = "是否为秒杀订单 0-否，1-是")
    private  Integer timelimitedOrder;

    /**
     * 父级订单编号
     */
    @ApiModelProperty(value = "父级订单编号")
    private String parentOrderNo;

    /**
     * 佣金单编号
     */
    private String finOrderNo;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerCode() {
        return buyerCode;
    }

    public void setBuyerCode(String buyerCode) {
        this.buyerCode = buyerCode;
    }

    public Byte getBuyerType() {
        return buyerType;
    }

    public void setBuyerType(Byte buyerType) {
        this.buyerType = buyerType;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getSellerCode() {
        return sellerCode;
    }

    public void setSellerCode(String sellerCode) {
        this.sellerCode = sellerCode;
    }

    public Byte getSellerType() {
        return sellerType;
    }

    public void setSellerType(Byte sellerType) {
        this.sellerType = sellerType;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Byte getOrderFromChannel() {
        return orderFromChannel;
    }

    public void setOrderFromChannel(Byte orderFromChannel) {
        this.orderFromChannel = orderFromChannel;
    }

    public Byte getOrderFromTerminal() {
        return orderFromTerminal;
    }

    public void setOrderFromTerminal(Byte orderFromTerminal) {
        this.orderFromTerminal = orderFromTerminal;
    }

    public Integer getTotalGoodsCount() {
        return totalGoodsCount;
    }

    public void setTotalGoodsCount(Integer totalGoodsCount) {
        this.totalGoodsCount = totalGoodsCount;
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getTotalGoodsAmount() {
        return totalGoodsAmount;
    }

    public void setTotalGoodsAmount(BigDecimal totalGoodsAmount) {
        this.totalGoodsAmount = totalGoodsAmount;
    }

    public BigDecimal getTotalFreight() {
        return totalFreight;
    }

    public void setTotalFreight(BigDecimal totalFreight) {
        this.totalFreight = totalFreight;
    }

    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }

    public BigDecimal getShopDiscountAmount() {
        return shopDiscountAmount;
    }

    public void setShopDiscountAmount(BigDecimal shopDiscountAmount) {
        this.shopDiscountAmount = shopDiscountAmount;
    }

    public BigDecimal getPlatformDiscountAmount() {
        return platformDiscountAmount;
    }

    public void setPlatformDiscountAmount(BigDecimal platformDiscountAmount) {
        this.platformDiscountAmount = platformDiscountAmount;
    }

    public BigDecimal getBargainingOrderAmount() {
        return bargainingOrderAmount;
    }

    public void setBargainingOrderAmount(BigDecimal bargainingOrderAmount) {
        this.bargainingOrderAmount = bargainingOrderAmount;
    }

    public BigDecimal getBargainingOrderFreight() {
        return bargainingOrderFreight;
    }

    public void setBargainingOrderFreight(BigDecimal bargainingOrderFreight) {
        this.bargainingOrderFreight = bargainingOrderFreight;
    }

    public BigDecimal getOrderTotalAmount() {
        return orderTotalAmount;
    }

    public void setOrderTotalAmount(BigDecimal orderTotalAmount) {
        this.orderTotalAmount = orderTotalAmount;
    }

    public BigDecimal getOrderPayAmount() {
        return orderPayAmount;
    }

    public void setOrderPayAmount(BigDecimal orderPayAmount) {
        this.orderPayAmount = orderPayAmount;
    }

    public Byte getHasUsedCoupon() {
        return hasUsedCoupon;
    }

    public void setHasUsedCoupon(Byte hasUsedCoupon) {
        this.hasUsedCoupon = hasUsedCoupon;
    }

    public Byte getChangePrice() {
        return changePrice;
    }

    public void setChangePrice(Byte changePrice) {
        this.changePrice = changePrice;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Date getOrderReceiptTime() {
        return orderReceiptTime;
    }

    public void setOrderReceiptTime(Date orderReceiptTime) {
        this.orderReceiptTime = orderReceiptTime;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public Date getPayOrderTime() {
        return payOrderTime;
    }

    public void setPayOrderTime(Date payOrderTime) {
        this.payOrderTime = payOrderTime;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public Byte getOutDistribution() {
        return outDistribution;
    }

    public void setOutDistribution(Byte outDistribution) {
        this.outDistribution = outDistribution;
    }

    public Byte getTimeLimitedOrder() {
        return timeLimitedOrder;
    }

    public void setTimeLimitedOrder(Byte timeLimitedOrder) {
        this.timeLimitedOrder = timeLimitedOrder;
    }

    public String getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(String promotionId) {
        this.promotionId = promotionId;
    }

    public String getOrderRemarks() {
        return orderRemarks;
    }

    public void setOrderRemarks(String orderRemarks) {
        this.orderRemarks = orderRemarks;
    }

    public Byte getNeedInvoice() {
        return needInvoice;
    }

    public void setNeedInvoice(Byte needInvoice) {
        this.needInvoice = needInvoice;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(String logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public Byte getOrderDeleteStatus() {
        return orderDeleteStatus;
    }

    public void setOrderDeleteStatus(Byte orderDeleteStatus) {
        this.orderDeleteStatus = orderDeleteStatus;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getSeperateBillType() {
        return seperateBillType;
    }

    public void setSeperateBillType(String seperateBillType) {
        this.seperateBillType = seperateBillType;
    }

    public BigDecimal getSellerDiscountAmount() {
        return sellerDiscountAmount;
    }

    public void setSellerDiscountAmount(BigDecimal sellerDiscountAmount) {
        this.sellerDiscountAmount = sellerDiscountAmount;
    }

    public String getPayCallbackStatus() {
        return payCallbackStatus;
    }

    public void setPayCallbackStatus(String payCallbackStatus) {
        this.payCallbackStatus = payCallbackStatus;
    }

    public Byte getPrePay() {
        return prePay;
    }

    public void setPrePay(Byte prePay) {
        this.prePay = prePay;
    }

    public String getVipDiscountPromotionId() {
        return vipDiscountPromotionId;
    }

    public void setVipDiscountPromotionId(String vipDiscountPromotionId) {
        this.vipDiscountPromotionId = vipDiscountPromotionId;
    }

    public String getVipDiscount() {
        return vipDiscount;
    }

    public void setVipDiscount(String vipDiscount) {
        this.vipDiscount = vipDiscount;
    }

    public BigDecimal getTotalVipDiscountAmount() {
        return totalVipDiscountAmount;
    }

    public void setTotalVipDiscountAmount(BigDecimal totalVipDiscountAmount) {
        this.totalVipDiscountAmount = totalVipDiscountAmount;
    }

    public String getDealsNo() {
        return dealsNo;
    }

    public void setDealsNo(String dealsNo) {
        this.dealsNo = dealsNo;
    }

    public BigDecimal getTotalVipDisGoodsAmount() {
        return totalVipDisGoodsAmount;
    }

    public void setTotalVipDisGoodsAmount(BigDecimal totalVipDisGoodsAmount) {
        this.totalVipDisGoodsAmount = totalVipDisGoodsAmount;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getModifyId() {
        return modifyId;
    }

    public void setModifyId(Long modifyId) {
        this.modifyId = modifyId;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getOrderFromChannle() {
        return orderFromChannle;
    }

    public void setOrderFromChannle(String orderFromChannle) {
        this.orderFromChannle = orderFromChannle;
    }

    public String getThirdOrderNo() {
        return thirdOrderNo;
    }

    public void setThirdOrderNo(String thirdOrderNo) {
        this.thirdOrderNo = thirdOrderNo;
    }

    public Integer getThirdChannel() {
        return thirdChannel;
    }

    public void setThirdChannel(Integer thirdChannel) {
        this.thirdChannel = thirdChannel;
    }

    public Integer getOutDistribtion() {
        return outDistribtion;
    }

    public void setOutDistribtion(Integer outDistribtion) {
        this.outDistribtion = outDistribtion;
    }

    public Integer getTimelimitedOrder() {
        return timelimitedOrder;
    }

    public void setTimelimitedOrder(Integer timelimitedOrder) {
        this.timelimitedOrder = timelimitedOrder;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getFinOrderNo() {
        return finOrderNo;
    }

    public void setFinOrderNo(String finOrderNo) {
        this.finOrderNo = finOrderNo;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"id\":")
                .append(id);
        sb.append(",\"orderNo\":\"")
                .append(orderNo).append('\"');
        sb.append(",\"cityCode\":\"")
                .append(cityCode).append('\"');
        sb.append(",\"buyerId\":")
                .append(buyerId);
        sb.append(",\"buyerCode\":\"")
                .append(buyerCode).append('\"');
        sb.append(",\"buyerType\":")
                .append(buyerType);
        sb.append(",\"buyerName\":\"")
                .append(buyerName).append('\"');
        sb.append(",\"sellerId\":")
                .append(sellerId);
        sb.append(",\"sellerCode\":\"")
                .append(sellerCode).append('\"');
        sb.append(",\"sellerType\":")
                .append(sellerType);
        sb.append(",\"sellerName\":\"")
                .append(sellerName).append('\"');
        sb.append(",\"shopId\":")
                .append(shopId);
        sb.append(",\"shopName\":\"")
                .append(shopName).append('\"');
        sb.append(",\"orderFromChannel\":")
                .append(orderFromChannel);
        sb.append(",\"orderFromTerminal\":")
                .append(orderFromTerminal);
        sb.append(",\"totalGoodsCount\":")
                .append(totalGoodsCount);
        sb.append(",\"orderType\":")
                .append(orderType);
        sb.append(",\"totalGoodsAmount\":")
                .append(totalGoodsAmount);
        sb.append(",\"totalFreight\":")
                .append(totalFreight);
        sb.append(",\"totalDiscountAmount\":")
                .append(totalDiscountAmount);
        sb.append(",\"shopDiscountAmount\":")
                .append(shopDiscountAmount);
        sb.append(",\"platformDiscountAmount\":")
                .append(platformDiscountAmount);
        sb.append(",\"bargainingOrderAmount\":")
                .append(bargainingOrderAmount);
        sb.append(",\"bargainingOrderFreight\":")
                .append(bargainingOrderFreight);
        sb.append(",\"orderTotalAmount\":")
                .append(orderTotalAmount);
        sb.append(",\"orderPayAmount\":")
                .append(orderPayAmount);
        sb.append(",\"hasUsedCoupon\":")
                .append(hasUsedCoupon);
        sb.append(",\"changePrice\":")
                .append(changePrice);
        sb.append(",\"orderStatus\":\"")
                .append(orderStatus).append('\"');
        sb.append(",\"orderReceiptTime\":\"")
                .append(orderReceiptTime).append('\"');
        sb.append(",\"payType\":\"")
                .append(payType).append('\"');
        sb.append(",\"payOrderTime\":\"")
                .append(payOrderTime).append('\"');
        sb.append(",\"payStatus\":\"")
                .append(payStatus).append('\"');
        sb.append(",\"outDistribution\":")
                .append(outDistribution);
        sb.append(",\"timeLimitedOrder\":")
                .append(timeLimitedOrder);
        sb.append(",\"promotionId\":\"")
                .append(promotionId).append('\"');
        sb.append(",\"orderRemarks\":\"")
                .append(orderRemarks).append('\"');
        sb.append(",\"needInvoice\":")
                .append(needInvoice);
        sb.append(",\"deliveryType\":\"")
                .append(deliveryType).append('\"');
        sb.append(",\"logisticsStatus\":\"")
                .append(logisticsStatus).append('\"');
        sb.append(",\"orderDeleteStatus\":")
                .append(orderDeleteStatus);
        sb.append(",\"payChannel\":\"")
                .append(payChannel).append('\"');
        sb.append(",\"confirmTime\":\"")
                .append(confirmTime).append('\"');
        sb.append(",\"seperateBillType\":\"")
                .append(seperateBillType).append('\"');
        sb.append(",\"sellerDiscountAmount\":")
                .append(sellerDiscountAmount);
        sb.append(",\"payCallbackStatus\":\"")
                .append(payCallbackStatus).append('\"');
        sb.append(",\"prePay\":")
                .append(prePay);
        sb.append(",\"vipDiscountPromotionId\":\"")
                .append(vipDiscountPromotionId).append('\"');
        sb.append(",\"vipDiscount\":\"")
                .append(vipDiscount).append('\"');
        sb.append(",\"totalVipDiscountAmount\":")
                .append(totalVipDiscountAmount);
        sb.append(",\"dealsNo\":\"")
                .append(dealsNo).append('\"');
        sb.append(",\"totalVipDisGoodsAmount\":")
                .append(totalVipDisGoodsAmount);
        sb.append(",\"createId\":")
                .append(createId);
        sb.append(",\"createName\":\"")
                .append(createName).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append(",\"modifyId\":")
                .append(modifyId);
        sb.append(",\"modifyName\":\"")
                .append(modifyName).append('\"');
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append(",\"orderFromChannle\":\"")
                .append(orderFromChannle).append('\"');
        sb.append(",\"thirdOrderNo\":\"")
                .append(thirdOrderNo).append('\"');
        sb.append(",\"thirdChannel\":")
                .append(thirdChannel);
        sb.append(",\"outDistribtion\":")
                .append(outDistribtion);
        sb.append(",\"timelimitedOrder\":")
                .append(timelimitedOrder);
        sb.append(",\"parentOrderNo\":\"")
                .append(parentOrderNo).append('\"');
        sb.append(",\"finOrderNo\":\"")
                .append(finOrderNo).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
