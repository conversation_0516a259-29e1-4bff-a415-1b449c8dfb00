package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class QueryIntentionAddressPageDTO implements Serializable {
    private static final long serialVersionUID = 6735618337220858121L;

    @ApiModelProperty(value = "服务商编号")
    private String serviceProviderCode;

    @ApiModelProperty(value = "买家编码")
    private String buyerCode;

    @ApiModelProperty(value = "卖家编码",hidden = true)
    private String sellerCode;

    @ApiModelProperty(value = "1:待处理 2:成功")
    private Integer status;

    @ApiModelProperty(value = "地址数据类型 0:试导入 1:苹果宝尊",required = true)
    private Integer consigneeType;

    @ApiModelProperty("当前页")
    @NotNull(message = "当前页不能为空")
    private Integer page;

    @ApiModelProperty("每页记录数")
    @NotNull(message = "每页记录数不能为空")
    private Integer size;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
