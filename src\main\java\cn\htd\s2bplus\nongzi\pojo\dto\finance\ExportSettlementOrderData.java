package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.nongzi.utils.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ExportSettlementOrderData implements Serializable {

    @ApiModelProperty(
            value = "结算单编号",
            notes = "结算单编号"
    )
    @Excel(name = "结算单编号", width = 20,orderNum = "1")
    private String settlementNo;

    @ApiModelProperty(
            value = "订单来源",
            notes = "订单来源"
    )
    @Excel(name = "订单来源", width = 20,orderNum = "2")
    private String orderFrom;

    @ApiModelProperty(
            value = "店铺id",
            notes = "店铺id"
    )
    @Excel(name = "店铺id", width = 20,orderNum = "3")
    private Long shopId;

    @ApiModelProperty(
            value = "店铺名称",
            notes = "店铺名称"
    )
    @Excel(name = "店铺名称", width = 20,orderNum = "4")
    private String shopName;

    @ApiModelProperty(
            value = "卖家id",
            notes = "卖家id"
    )
    @Excel(name = "卖家id", width = 20,orderNum = "5")
    private String sellerId;

    @ApiModelProperty(
            value = "卖家名称",
            notes = "卖家名称"
    )
    @Excel(name = "卖家名称", width = 20,orderNum = "6")
    private String sellerName;

    @ApiModelProperty(
            value = "账单日",
            notes = "账单日"
    )
    @Excel(name = "账单日", width = 20,orderNum = "7",exportFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date periodTime;

    @ApiModelProperty(
            value = "结算单完成时间",
            notes = "结算单完成时间"
    )
    @Excel(name = "结算单完成时间", width = 20,orderNum = "8",exportFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date settleCompleteTime;

    @ApiModelProperty(
            value = "结算单生成时间",
            notes = "结算单生成时间"
    )
    @Excel(name = "结算单生成时间", width = 20,orderNum = "9",exportFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date settleCreateTime;

    @ApiModelProperty(
            value = "结算单状态",
            notes = "结算单状态"
    )
    @Excel(name = "结算单状态", width = 20,orderNum = "10")
    private String settleStatus;

    @ApiModelProperty(
            value = "结算金额",
            notes = "结算金额"
    )
    @Excel(name = "结算单金额", width = 20,orderNum = "11")
    private BigDecimal settleAmount;

    @ApiModelProperty(
            value = "订单数",
            notes = "订单数"
    )
    @Excel(name = "订单数", width = 20,orderNum = "12")
    private Integer settleOrderNum;

    @ApiModelProperty(
            value = "订单编号",
            notes = "订单编号"
    )
    @Excel(name = "订单编号", width = 20,orderNum = "13")
    private String orderNo;

    @ApiModelProperty(
            value = "会员名称",
            notes = "会员名称"
    )
    @Excel(name = "会员名称", width = 20,orderNum = "14")
    private String memberName;

    @ApiModelProperty(
            value = "会员ID",
            notes = "会员ID"
    )
    @Excel(name = "会员ID", width = 20,orderNum = "15")
    private String memberCode;

    @ApiModelProperty(value = "实付金额")
    @Excel(name = "订单支付金额", width = 20,orderNum = "16")
    private BigDecimal realAmount;

    @ApiModelProperty(
            value = "佣金总额",
            notes = "佣金总额"
    )
    @Excel(name = "佣金金额", width = 20,orderNum = "17")
    private BigDecimal statementAmount;

    @ApiModelProperty(
            value = "手续费",
            notes = "手续费",
            example = "100.00"
    )
    @Excel(name = "渠道手续费金额", width = 20,orderNum = "18")
    private BigDecimal chargeAmount;

//    @ApiModelProperty(value = "结算金额")
//    @Excel(name = "订单实际结算金额", width = 20,orderNum = "19")
//    private BigDecimal settlementAmount;

    @ApiModelProperty(value = "付款方式 1现金 2微信 3支付宝 4余额 5大额转账 6其他")
    @Excel(name = "订单支付方式", width = 20,orderNum = "20")
    private String payType;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    @ApiModelProperty(value = "渠道名称")
    @Excel(name = "经营渠道")
    private String channelName;

    @ApiModelProperty(value = "平台分佣金额",notes="平台分佣金额",example="100.00")
    @Excel(name = "平台分佣金额", width = 20,orderNum = "21")
    private BigDecimal platformAmount;

    @ApiModelProperty(value = "商家分佣金额",notes="商家分佣金额",example="100.00")
    @Excel(name = "商家分佣金额", width = 20,orderNum = "22")
    private BigDecimal sellerAmount;

    @ApiModelProperty(value = "运营商分佣金额",notes="运营商分佣金额",example="100.00")
    @Excel(name = "运营商分佣金额", width = 20,orderNum = "23")
    private BigDecimal operatorAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
