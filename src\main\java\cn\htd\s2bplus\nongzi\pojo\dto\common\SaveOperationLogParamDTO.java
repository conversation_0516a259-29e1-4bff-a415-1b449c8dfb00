package cn.htd.s2bplus.nongzi.pojo.dto.common;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class SaveOperationLogParamDTO implements Serializable {
    private static final long serialVersionUID = 6750745581297813324L;

    public SaveOperationLogParamDTO() {

    }

    public SaveOperationLogParamDTO(Integer businessType, Integer operatorType, String businessKey, String operationRecord) {
        this.businessType = businessType;
        this.operatorType = operatorType;
        this.businessKey = businessKey;
        this.operationRecord = operationRecord;
    }

    @ApiModelProperty(value = "日志业务类型")
    private Integer businessType;

    @ApiModelProperty(value = "操作类型")
    private Integer operatorType;

    @ApiModelProperty(value = "业务关键字")
    private String businessKey;

    @ApiModelProperty(value = "操作记录")
    private String operationRecord;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
