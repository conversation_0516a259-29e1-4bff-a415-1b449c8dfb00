package cn.htd.s2bplus.nongzi.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class DateUtil {
	private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);


	/**
	 * yyyy-MM-dd HH:mm:ss
	 */
	public static final String YYYY_MM_DD_HH_MM_SS_SPLIT = "yyyy-MM-dd HH:mm:ss";
	/**
	 * yyyy-MM-dd-HH-mm-ss
	 */
	public static final String YYYY_MM_DD_HH_MM_SS_FORMAT = "yyyy-MM-dd-HH-mm-ss";
	/**
	 *
	 */
	public static final String YYYY_MM_DD_FORMAT = "yyyy-MM-dd";

	/**
	 * yyyyMMdd
	 */
	private static final String YYYY_MM_DD = "yyyyMMdd";

	/**
	 * yyyyMM
	 */
	private static final String YYYY_MM =  "yyyyMM";

	/**
	 * yyyyMMddHHmmss
	 */
	private static final String YYYY_MM_DD_HH_MM_SS = "yyyyMMddHHmmss";

	/**
	 * yyyy-MM-dd HH:mm:ss.SSS
	 */
	private static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

	private static final Map<String, DateFormat> DFS = new HashMap();

	/**
	 * 获取系统当前时间
	 */
	public static Timestamp getSystemTime() {

		Date dt = new Date();
		DateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		String nowTime = df.format(dt);
		Timestamp buydate = Timestamp.valueOf(nowTime);
		return buydate;
	}

	/**
	 * 获取当前时间的n天后时间
	 */
	public static Timestamp getDaysTime(int date) {
		Calendar now = Calendar.getInstance();
		now.setTime(new Date());
		now.add(Calendar.DAY_OF_MONTH, date);
		DateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		String nowTime = df.format(now.getTime());
		Timestamp buydate = Timestamp.valueOf(nowTime);
		return buydate;
	}

	/**
	 * 获取当前时间的n分钟后的时间
	 */
	public static Timestamp getMinutesLaterTime(int minutes) {
		Calendar now = Calendar.getInstance();
		now.setTime(new Date());
		now.add(Calendar.MINUTE, minutes);
		DateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		String nowTime = df.format(now.getTime());
		Timestamp buydate = Timestamp.valueOf(nowTime);
		return buydate;
	}

	/**
	 * 获取n个月后时间
	 */
	public static Timestamp getMonthTime(int month) {
		Date dt = new Date();
		Calendar now = Calendar.getInstance();
		now.setTime(dt);
		now.add(Calendar.MONTH, month);
		Date threeMonthAgoDate = now.getTime();
		DateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		String nowTime = df.format(threeMonthAgoDate);
		Timestamp buydate = Timestamp.valueOf(nowTime);
		return buydate;
	}

	public static String getDay() {
		Date dt = new Date();
		DateFormat dfInt = new SimpleDateFormat(YYYY_MM_DD);
		String nowTime = dfInt.format(dt);
		return nowTime;
	}

	/**
	 * 获取当前年月
	 */
	public static String getYearMonth() {
		Date dt = new Date();
		DateFormat dfIntYearMonth = new SimpleDateFormat(YYYY_MM);
		String nowTime = dfIntYearMonth.format(dt);
		return nowTime;
	}

	/**
	 * 获取当前日期-到秒
	 * @return
	 */
	public static String getDaySS() {
		Date dt = new Date();
		DateFormat dfIntSS = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
		String nowTime = dfIntSS.format(dt);
		return nowTime;
	}


	/**
	 * 判断时间是否超过24小时
	 * @param date1
	 * @param date2
	 * @return
	 * @throws Exception
	 */
	public static boolean judgmentDate(String date1, String date2) throws Exception {
		DateFormat dfIntSS = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
		Date start = dfIntSS.parse(date1);
		Date end = dfIntSS.parse(date2);
		long cha = end.getTime() - start.getTime();
		double result = cha * 1.0 / (1000 * 60 * 60);
		System.out.println(result);
		if (result <= 24) {
			return true;
		} else {
			return false;
		}
	}

	public static boolean judgment24Date(String date1) throws Exception {
		Calendar now = Calendar.getInstance();
		String year = String.valueOf(now.get(Calendar.YEAR));
		String month = String.valueOf(now.get(Calendar.MONTH) + 1);
		String day = String.valueOf(now.get(Calendar.DAY_OF_MONTH));
		if (month.length() == 1) {
			month = "0" + month;
		}
		if (day.length() == 1) {
			day = "0" + day;
		}
		String nowday = year + month + day;
		LOGGER.info("页面传进来的时间内容:" + date1 + " 当前时间:" + nowday);
		if (date1.substring(0, 8).equals(nowday)) {
			return true;
		}
		return false;
	}

	public static boolean isValidDate(String str) {
		boolean convertSuccess = true;
		SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
		try {
			format.setLenient(false);
			format.parse(str);
		} catch (ParseException e) {
			convertSuccess = false;
		}
		return convertSuccess;
	}

	public static String getCurrentDate() {
		Date d = new Date();
		DateFormat dfsss = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SSS);
		String dateNowStr = dfsss.format(d);
		return dateNowStr;
	}

	public static String getCurrentDateFull() {
		Calendar now = Calendar.getInstance();
		Date d = new Date();
		DateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		String dateNowStr = df.format(d);
		return dateNowStr;
	}

	/**
	 * 将Date转成java.sql.Timestamp 入库用
	 */
	public static Timestamp getDaysSqlTime(Date date) {
		DateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		String nowTime = df.format(date.getTime());
		Timestamp buydate = Timestamp.valueOf(nowTime);
		return buydate;
	}

	/**
	 * date 转成 String
	 * @param date
	 * @return
	 */
	public static String dateToString(Date date){
		DateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		return df.format(date);
	}


	/**
	 * 相差几天
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static int dayDiff(Date date1, Date date2) {
		long diff = date1.getTime() - date2.getTime();
		return (int) (diff / 86400000L);
	}

	/**
	 *
	 * @param time
	 *           时间
	 * @param num
	 *           加的数，-num就是减去
	 * @return
	 *          减去相应的数量的月份的日期小时
	 * @throws ParseException Date
	 */
	public static Date hourAddNum(Date time, Integer num){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(time);
		calendar.add(Calendar.HOUR, num);
		Date newTime = calendar.getTime();
		return newTime;
	}

	/**
	 * 前/后?天
	 *
	 * @param d
	 * @param
	 * @return
	 */
	public static Date rollDay(Date d, int day) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(d);
		cal.add(Calendar.DAY_OF_MONTH, day);
		return cal.getTime();
	}

	public static Date parse(String source, String pattern) {
		if (source == null) {
			return null;
		} else {
			try {
				Date date = getFormat(pattern).parse(source);
				return date;
			} catch (ParseException var4) {
				return null;
			}
		}
	}

	public static DateFormat getFormat(String pattern) {
		DateFormat format = (DateFormat)DFS.get(pattern);
		if (format == null) {
			format = new SimpleDateFormat(pattern);
			DFS.put(pattern, format);
		}

		return (DateFormat)format;
	}

	public static String setDateToString(Date date) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		String format = simpleDateFormat.format(date);
		return format;
	}

	/**
	 * 获取当前时间距离第二天凌晨的秒数
	 * @return 返回值单位为秒
	 */
	public static Long getSecondsNextEarlyMorning() {
		Calendar cal = Calendar.getInstance();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		cal.add(Calendar.DAY_OF_YEAR, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MINUTE, 0);
		System.out.println(format.format(cal.getTimeInMillis()) + " " + cal.getTimeInMillis());
		System.out.println(format.format(System.currentTimeMillis()) + " " + System.currentTimeMillis());
		return (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
	}

	/**
	 * 获取当前时间指定天数之前的日期
	 * @param month 指定月份
	 * @return 指定时间
	 */
	public static Date getDateBeforeTheSpecifiedDay(Integer month) {
		int day = month * 30;
		LocalDateTime systemTime = LocalDateTime.now();
		LocalDateTime specifiedDay = systemTime.minus(day, ChronoUnit.DAYS);
		return Date.from(specifiedDay.atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * 时间字符串按照指定类型转时间格式数据
	 * @param dateString 时间字符串
	 * @param format 时间格式
	 * @return 时间格式数据
	 */
	public static Date setStringToDate(String dateString,String format) {
		Date date = null;
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
			date = simpleDateFormat.parse(dateString);
		} catch (Exception e) {
			LOGGER.error("时间类型字符串转时间格式失败,error:",e);
		}
		return date;
	}

	/**
	 * 字符串格式转date
	 * @param strDate
	 * @return
	 */
	public static Date strToDateLong(String strDate) {
		SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SPLIT);
		ParsePosition pos = new ParsePosition(0);
		return formatter.parse(strDate, pos);
	}

	/**
	 * 判断一个入参是否为既定格式
	 * @param dateString
	 * @param pattern
	 * @return
	 */
	public static boolean isValidDate(String dateString, String pattern) {
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
			LocalDate date = LocalDate.parse(dateString, formatter);
			return true;
		} catch (DateTimeParseException e) {
			return false;
		}
	}
}
