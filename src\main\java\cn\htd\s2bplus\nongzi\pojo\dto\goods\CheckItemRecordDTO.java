package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class CheckItemRecordDTO implements Serializable {
    private static final long serialVersionUID = -6827437290671842113L;

    // 商品维度是否校验标识 true:已校验
    private boolean itemFlag;

    // 商品维度校验不通过的原因
    private List<String> itemErrorReasonList;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
