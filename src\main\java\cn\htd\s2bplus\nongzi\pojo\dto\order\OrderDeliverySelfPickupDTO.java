package cn.htd.s2bplus.nongzi.pojo.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class OrderDeliverySelfPickupDTO implements Serializable {
    private static final long serialVersionUID = -7127268681357033472L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /**
     * 提货人姓名
     */
    @ApiModelProperty(value = "提货人姓名")
    private String pickupName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 联系电话加密
     */
    @ApiModelProperty(value = "联系电话加密")
    private String dsContactPhone;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String identityNo;

    /**
     * 身份证号加密
     */
    @ApiModelProperty(value = "身份证号加密")
    private String dsIdentityNo;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String carNo;

    /**
     * 提货地址
     */
    @ApiModelProperty(value = "提货地址")
    private String pickupAddress;

    /**
     * 提货地址加密
     */
    @ApiModelProperty(value = "提货地址加密")
    private String dsPickupAddress;

    /**
     * 身份证正面照
     */
    @ApiModelProperty(value = "身份证正面照")
    private String pictureIdFrontUrl;

    /**
     * 身份证正面照
     */
    @ApiModelProperty(value = "身份证正面照")
    private String pictureIdReverseUrl;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long modifyId;

    /**
     * 更新人名称
     */
    @ApiModelProperty(value = "更新人名称")
    private String modifyName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
/*
    *//**
     * ID
     * @return id ID
     *//*
    public Long getId() {
        return id;
    }

    *//**
     * ID
     * @param id ID
     *//*
    public void setId(Long id) {
        this.id = id;
    }

    *//**
     * 订单号
     * @return order_no 订单号
     *//*
    public String getOrderNo() {
        return orderNo;
    }

    *//**
     * 订单号
     * @param orderNo 订单号
     *//*
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    *//**
     * 提货人姓名
     * @return pickup_name 提货人姓名
     *//*
    public String getPickupName() {
        return pickupName;
    }

    *//**
     * 提货人姓名
     * @param pickupName 提货人姓名
     *//*
    public void setPickupName(String pickupName) {
        this.pickupName = pickupName == null ? null : pickupName.trim();
    }

    *//**
     * 联系电话
     * @return contact_phone 联系电话
     *//*
    public String getContactPhone() {
        return contactPhone;
    }

    *//**
     * 联系电话
     * @param contactPhone 联系电话
     *//*
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    *//**
     * 身份证号
     * @return identity_no 身份证号
     *//*
    public String getIdentityNo() {
        return identityNo;
    }

    *//**
     * 身份证号
     * @param identityNo 身份证号
     *//*
    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo == null ? null : identityNo.trim();
    }

    *//**
     * 车牌号
     * @return car_no 车牌号
     *//*
    public String getCarNo() {
        return carNo;
    }

    *//**
     * 车牌号
     * @param carNo 车牌号
     *//*
    public void setCarNo(String carNo) {
        this.carNo = carNo == null ? null : carNo.trim();
    }

    *//**
     * 提货地址
     * @return pickup_address 提货地址
     *//*
    public String getPickupAddress() {
        return pickupAddress;
    }

    *//**
     * 提货地址
     * @param pickupAddress 提货地址
     *//*
    public void setPickupAddress(String pickupAddress) {
        this.pickupAddress = pickupAddress == null ? null : pickupAddress.trim();
    }

    *//**
     * 身份证正面照
     * @return picture_id_front_url 身份证正面照
     *//*
    public String getPictureIdFrontUrl() {
        return pictureIdFrontUrl;
    }

    *//**
     * 身份证正面照
     * @param pictureIdFrontUrl 身份证正面照
     *//*
    public void setPictureIdFrontUrl(String pictureIdFrontUrl) {
        this.pictureIdFrontUrl = pictureIdFrontUrl == null ? null : pictureIdFrontUrl.trim();
    }

    *//**
     * 身份证正面照
     * @return picture_id_reverse_url 身份证正面照
     *//*
    public String getPictureIdReverseUrl() {
        return pictureIdReverseUrl;
    }

    *//**
     * 身份证正面照
     * @param pictureIdReverseUrl 身份证正面照
     *//*
    public void setPictureIdReverseUrl(String pictureIdReverseUrl) {
        this.pictureIdReverseUrl = pictureIdReverseUrl == null ? null : pictureIdReverseUrl.trim();
    }

    *//**
     * 创建人ID
     * @return create_id 创建人ID
     *//*
    public Long getCreateId() {
        return createId;
    }

    *//**
     * 创建人ID
     * @param createId 创建人ID
     *//*
    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    *//**
     * 创建人名称
     * @return create_name 创建人名称
     *//*
    public String getCreateName() {
        return createName;
    }

    *//**
     * 创建人名称
     * @param createName 创建人名称
     *//*
    public void setCreateName(String createName) {
        this.createName = createName == null ? null : createName.trim();
    }

    *//**
     * 创建时间
     * @return create_time 创建时间
     *//*
    public Date getCreateTime() {
        return createTime;
    }

    *//**
     * 创建时间
     * @param createTime 创建时间
     *//*
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    *//**
     * 更新人ID
     * @return modify_id 更新人ID
     *//*
    public Long getModifyId() {
        return modifyId;
    }

    *//**
     * 更新人ID
     * @param modifyId 更新人ID
     *//*
    public void setModifyId(Long modifyId) {
        this.modifyId = modifyId;
    }

    *//**
     * 更新人名称
     * @return modify_name 更新人名称
     *//*
    public String getModifyName() {
        return modifyName;
    }

    *//**
     * 更新人名称
     * @param modifyName 更新人名称
     *//*
    public void setModifyName(String modifyName) {
        this.modifyName = modifyName == null ? null : modifyName.trim();
    }

    *//**
     * 更新时间
     * @return modify_time 更新时间
     *//*
    public Date getModifyTime() {
        return modifyTime;
    }

    *//**
     * 更新时间
     * @param modifyTime 更新时间
     *//*
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }*/




    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"id\":")
                .append(id);
        sb.append(",\"orderNo\":\"")
                .append(orderNo).append('\"');
        sb.append(",\"pickupName\":\"")
                .append(pickupName).append('\"');
        sb.append(",\"contactPhone\":\"")
                .append(contactPhone).append('\"');
        sb.append(",\"identityNo\":\"")
                .append(identityNo).append('\"');
        sb.append(",\"carNo\":\"")
                .append(carNo).append('\"');
        sb.append(",\"pickupAddress\":\"")
                .append(pickupAddress).append('\"');
        sb.append(",\"pictureIdFrontUrl\":\"")
                .append(pictureIdFrontUrl).append('\"');
        sb.append(",\"pictureIdReverseUrl\":\"")
                .append(pictureIdReverseUrl).append('\"');
        sb.append(",\"createId\":")
                .append(createId);
        sb.append(",\"createName\":\"")
                .append(createName).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append(",\"modifyId\":")
                .append(modifyId);
        sb.append(",\"modifyName\":\"")
                .append(modifyName).append('\"');
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
