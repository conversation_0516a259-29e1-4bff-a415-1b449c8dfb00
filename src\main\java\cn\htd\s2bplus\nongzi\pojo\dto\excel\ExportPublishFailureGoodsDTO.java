package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExportPublishFailureGoodsDTO  implements Serializable {
    private static final long serialVersionUID = 628767088396731758L;

    @Excel(name = "条形码",fixedIndex = 0,width = 30,orderNum = "1")
    private String eanCode;

    @Excel(name = "*商品名称",fixedIndex = 1,width = 30,orderNum = "2")
    private String itemName;

    @Excel(name = "*单位",fixedIndex = 2,width = 30,orderNum = "3")
    private String unit;

    @Excel(name = "*成本价",fixedIndex = 3,width = 30,orderNum = "4")
    private BigDecimal cost;

    @Excel(name = "*销售价",fixedIndex = 4,width = 30,orderNum = "5")
    private BigDecimal retailPrice;

    @Excel(name = "供应商",fixedIndex = 5,width = 30,orderNum = "6")
    private String supplierName;

    @Excel(name = "备注",fixedIndex = 6,width = 30,orderNum = "6")
    private String remark;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
