package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class ItemsUpShelfDTO implements Serializable{

	private static final long serialVersionUID = -3793348805286660998L;
	
    @ApiModelProperty(value = "商品id集合")
    @NotNull(message = "商品ids不能为空")
    private List<Long> itemIds;

    @ApiModelProperty(value = "商品id")
    @NotNull(message = "商品id不能为空")
    private Long itemId;
	
    @ApiModelProperty(value = "店铺id")
    @NotNull(message = "店铺id不能为空")
	private Long shopId;

    @ApiModelProperty(value = "上下架状态")
	@NotNull(message = "isVisable上下架状态不能为空")
	private Integer isVisable;
	
    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    /**
     * 创建人ID
     */
    @NotNull(message = "operateId不能为null")
    @ApiModelProperty(value = "操作人id")
    private Long operateId;

    /**
     * 创建人名称
     */
    @NotNull(message = "operateName不能为空")
    @ApiModelProperty(value = "操作人名称")
    private String operateName;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "应用渠道ID")
    private Long appId;

    @ApiModelProperty(value = "卖家id", required = true)
    @NotNull(message = "卖家id不能为空")
    private Long sellerId;

    @ApiModelProperty(value = "卖家编码")
    private String sellerCode;

    @ApiModelProperty(value = "下架类型，1：商家下架 2：运营下架")
    private String itemActionType;

    @ApiModelProperty(value = "下架原因")
    private String delistingRemark;

    @ApiModelProperty(value = "下架枚举 1:经营资质证书未上传 2:价格异常 3:商品信息不符合标准 4:其他 ")
    private Integer delistingType;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
