package cn.htd.s2bplus.nongzi.service.goods.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.ResultEnum;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.SellerSkuExport;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.*;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.service.goods.SubmitAssignmentService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class SubmitAssignmentServiceImpl implements SubmitAssignmentService {

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;


    @Autowired
    private GoodsFeignService goodsFeignService;

    @Override
    public Result<List<SubmitAssignmentExecutorVO>> importBatchAssignmentExecutor(MultipartFile file) {
        Result<List<SubmitAssignmentExecutorVO>> result = new Result();
        List<SubmitAssignmentExecutorVO> list = new ArrayList<>();
        List<String> codeList = new ArrayList();
        try {
            //校验文件类型
            if (ObjectUtils.isEmpty(file) || StringUtils.isEmpty(file.getOriginalFilename())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
            if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equals(substring) || StrConstant.XLSX.equals(substring))) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_FILE_ERROR.getMsg());
            }
            long start = System.currentTimeMillis();
            EasyExcel.read(file.getInputStream(), SubmitAssignmentExecutorVO.class, new ReadListener<SubmitAssignmentExecutorVO>() {
                public static final int BATCH_COUNT = 100;
                private final List<SubmitAssignmentExecutorVO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

                @Override
                public void invoke(SubmitAssignmentExecutorVO data, AnalysisContext context) {
                    int rowNumber = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
                    int rowIndex = context.readSheetHolder().getRowIndex() + 1;
                    if (rowNumber == 0) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
                    }
                    if (StringUtils.isEmpty(data.getEmployeeId())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.EMPLOYEE_ID_NULL.getMsg());
                    }
                    if (StringUtils.isEmpty(data.getEmployeeName())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.EMPLOYEE_NAME_NULL.getMsg());
                    }
                    if (codeList.contains(data.getEmployeeId())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "员工:" + data.getEmployeeId() + ApiResultEnum.EMPLOYEE_NAME_REPEAT.getMsg());
                    }
                    codeList.add(data.getEmployeeId());
                    cachedDataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (!CollectionUtils.isEmpty(cachedDataList)) {
                        list.addAll(cachedDataList);
                    }
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) {
                    log.info("解析任务执行人失败 :{}", exception.getMessage());
                    if (exception instanceof BusinessException) {
                        throw new BusinessException(cn.htd.s2bplus.nongzi.enums.ResultEnum.ERROR.getCode(), exception.getMessage());
                    } else if (exception instanceof ExcelDataConvertException) {
                        ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                        String errorMsg = ApiResultEnum.IMPORT_ERROR_LINE_COLUMN.getMsg() + "解析异常";
                        Integer rowIndex = excelDataConvertException.getRowIndex() + 1;
                        Integer columnIndex = excelDataConvertException.getColumnIndex() + 1;
                        log.info("解析任务执行人,第{}行，第{}列解析异常，数据为:{}", rowIndex, columnIndex, JSON.toJSONString(excelDataConvertException.getCellData()));
                        throw new BusinessException(ResultEnum.ERROR.getCode(), String.format(errorMsg, rowIndex, columnIndex));
                    }
                }
            }).sheet().doRead();
            log.info("解析任务执行人的数据量：{}", list.size());
            log.info("解析任务执行人excel时间占用 :{}ms", System.currentTimeMillis() - start);
            log.info("解析任务执行人完毕数据:{}", list);
            if (list.size() == 0) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            result.setData(list);
            result.setCode(ApiResultEnum.SUCCESS.getCode());
            result.setMsg("任务执行人导入会员成功");
        } catch (BusinessException e) {
            log.info("任务执行人导入失败:{}", e.getMessage());
            result.setCode(e.getCode());
            result.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("任务执行人导入异常", e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("任务执行人导入异常");
        }
        return result;
    }

    @Override
    public Result<String> deriveSubmitAssignmentGood(Long submitAssignmentId,HttpServletResponse response) {
        String employeeId = BaseContextHandler.getLoginUser().getLoginId();
        log.info("查询提报商品入参：{},{}",submitAssignmentId,employeeId);
        Result<List<SubmitAssignmentGoodVO>> listResult = goodsFeignService.getGoodsByAssignmentId(submitAssignmentId,employeeId);
        log.info("查询提报商品出参：{}",listResult);
        if (listResult.isSuccess() && null != listResult && !CollectionUtils.isEmpty(listResult.getData())){

            List<SubmitAssignmentGoodsDTO> submitAssignmentGoodsDTOList = new ArrayList<>();
            List<SubmitAssignmentGoodVO> data = listResult.getData();
            List<Long> itemIds = new ArrayList<>();
            List<ItemSkuPublishStatusDTO> itemSkuInfoList = new ArrayList<>();
            for (SubmitAssignmentGoodVO datum : data) {
                SubmitAssignmentGoodsDTO submitAssignmentGoodsDTO = new SubmitAssignmentGoodsDTO();
                BeanUtils.copyProperties(datum,submitAssignmentGoodsDTO);
                submitAssignmentGoodsDTOList.add(submitAssignmentGoodsDTO);
                ItemSkuPublishStatusDTO itemSkuPublishStatusDTO = new ItemSkuPublishStatusDTO();
                itemSkuPublishStatusDTO.setItemCode(datum.getItemCode());
                itemSkuPublishStatusDTO.setShopId(datum.getShopId());
                itemSkuInfoList.add(itemSkuPublishStatusDTO);
                itemIds.add(datum.getItemId());
            }

            Map<Long,ItemDTO> itemDTOMap = new HashMap<>();
            log.info("批量查询商品信息入参：{}",itemIds);
            Result<List<ItemDTO>> itemResult = goodsFeignService.queryItemListByItemIds(itemIds);
            log.info("批量查询商品信息出参：{}",itemResult);
            if (itemResult.isSuccess() && null != itemResult && !CollectionUtils.isEmpty(itemResult.getData())){
                List<ItemDTO> itemDTOList = itemResult.getData();
                for (ItemDTO itemDTO : itemDTOList) {
                    itemDTOMap.put(itemDTO.getItemId(),itemDTO);
                }
            }

            if(!itemDTOMap.isEmpty()){
                for (SubmitAssignmentGoodsDTO submitAssignmentGoodsDTO : submitAssignmentGoodsDTOList) {
                    ItemDTO itemDTO = itemDTOMap.get(submitAssignmentGoodsDTO.getItemId());
                    submitAssignmentGoodsDTO.setSellerCode(itemDTO.getSellerCode());
                    submitAssignmentGoodsDTO.setSellerName(itemDTO.getCompanyName());
                    submitAssignmentGoodsDTO.setItemName(itemDTO.getItemName());
                }
            }

            ItemShelfStatusQryDTO itemShelfStatusQryDTO = new ItemShelfStatusQryDTO();
            itemShelfStatusQryDTO.setQueryFlag(CommonConstants.QUERY_FLAG);
            itemShelfStatusQryDTO.setItemSkuInfoList(itemSkuInfoList);
            Map<String,String> shelfStatusMap = new HashMap<>();
            log.info("批量获取商品上下架状态入参：{}",itemShelfStatusQryDTO);
            Result<List<ShelfData>> shelfResult = goodsFeignService.batchItemShelfStatus(itemShelfStatusQryDTO);
            log.info("批量获取商品上下架状态出参：{}",shelfResult);
            if (shelfResult.isSuccess() && null !=shelfResult && !CollectionUtils.isEmpty(shelfResult.getData())){
                List<ShelfData> shelfDataList = shelfResult.getData();

                for (ShelfData shelfData : shelfDataList) {
                    shelfStatusMap.put(shelfData.getItemCode()+shelfData.getShopId(),shelfData.getShelfStatus());
                }
            }

            if (!shelfStatusMap.isEmpty()){
                for (SubmitAssignmentGoodsDTO submitAssignmentGoodsDTO : submitAssignmentGoodsDTOList) {
                    String shelfStatus = shelfStatusMap.get(submitAssignmentGoodsDTO.getItemCode() + submitAssignmentGoodsDTO.getShopId());
                    submitAssignmentGoodsDTO.setShelfStatus("0".equals(shelfStatus)?"已下架":"已上架");
                    submitAssignmentGoodsDTO.setCreateName(submitAssignmentGoodsDTO.getCreateId()+"/"+submitAssignmentGoodsDTO.getCreateName());
                }
            }
            String sheetNameStart = "提报商品";
            String downloadUrl = this.getDownloadUrl(submitAssignmentGoodsDTOList, response, sheetNameStart);
            return ResultUtil.success(downloadUrl);

        }
        return ResultUtil.error(ResultEnum.FAILURE.getCode(),"提报商品导出失败");
    }


    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     */
    private String  getDownloadUrl(List<SubmitAssignmentGoodsDTO> list, HttpServletResponse response, String sheetNameStart){
        String downloadUrl = "";
        try{
            //文件名样式
            SimpleDateFormat simpleDateFormatName = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
            String sheetName = simpleDateFormatName.format(new Date());
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);

            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", SubmitAssignmentGoodsDTO.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(list) ?
                    new ArrayList<SubmitAssignmentGoodsDTO>() : list);

            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            // 改成输出excel文件
            response.setContentType("application/msexcel");
            String fileName = sheetNameStart +"_"+ sheetName;
            // 03版本后缀xls，之后的xlsx
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);


            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            OssUtils ossUtils = new OssUtils();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = sdf.format(new Date());
            String ossFileName = "提报商品明细信息"+format+".xls";
            downloadUrl = ossUtils.upload(excelStream,ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        }catch (Exception e){
            log.info("reportExportHandle error:",e);
        }
        return downloadUrl;
    }

}
