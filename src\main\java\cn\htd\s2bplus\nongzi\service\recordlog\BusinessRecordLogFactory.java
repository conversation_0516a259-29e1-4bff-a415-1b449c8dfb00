package cn.htd.s2bplus.nongzi.service.recordlog;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @title BusinessRecordLogFactory
 * @description 业务记录处理日志工厂
 * @Date: 2022/4/21 10:10
 */
@Component
public class BusinessRecordLogFactory implements InitializingBean {
    @Autowired
    private List<BusinessRecordLogService> dealLogInterfaceList;

    /**
     * 存放实现该接口
     */
    private Map<Integer, BusinessRecordLogService> logInterfaceMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        if (CollectionUtils.isEmpty(dealLogInterfaceList)) {
            return;
        }

        dealLogInterfaceList.forEach(log -> logInterfaceMap.put(log.operationType(), log));
    }

    /**
     * 通过类型获取接口
     *
     * @param operationType 操作类型
     * @return 响应
     */
    public BusinessRecordLogService getLogInterfaceByOperationType(Integer operationType) {
        return logInterfaceMap.get(operationType);
    }
}
