package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Date 2020/3/318:22
 **/
@ApiModel("品牌信息实体")
public class Brand extends BaseDTO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("品牌id")
    private Long id;
    @ApiModelProperty("品牌名称")
    private String brandName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

}
