package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class MemberBaseInfoDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -2582063848418925222L;

   @ApiModelProperty(value = "平台公司/金立编码集合",example = "\"0801\", \"8067\"")
   List<String> companyCodes;

   @ApiModelProperty(value = "会员ID",example = "1",required = true)
    private Long id;

   @ApiModelProperty(value = "参与人唯一识别码",example = "htd000001")
    private String uniqueCode;

   @ApiModelProperty(value = "法人",example = "0")
    private String companyLeagalPersionFlag;

   @ApiModelProperty(value = "是否是买家",example = "1")
    private Integer isBuyer;

   @ApiModelProperty(value = "是否是商家",example = "1")
    private Integer isSeller;

   @ApiModelProperty(value = "是否登陆商城",example = "1")
    private Integer canMallLogin;

   @ApiModelProperty(value = "是否有担保证明",example = "1")
    private Integer hasGuaranteeLicense;

   @ApiModelProperty(value = "是否有营业执照",example = "1")
    private Integer hasBusinessLicense;

   @ApiModelProperty(value = "注册时间",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date registTime;

   @ApiModelProperty(value = "是否中心店",example = "0")
    private Integer isCenterStore;

   @ApiModelProperty(value = "升级为中心店时间",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date upgradeCenterStoreTime;

   @ApiModelProperty(value = "商家类型:空:会员店,1:内部供应商，2:外部供应商 ,3:分销商",example = "1")
    private String sellerType;

   @ApiModelProperty(value = "是否是二代",example = "1")
    private Integer isGeneration;

   @ApiModelProperty(value = "发展行业",example = "1")
    private String industryCategory;

   @ApiModelProperty(value = "是否异业",example = "1")
    private Integer isDiffIndustry;

   @ApiModelProperty(value = "联系人姓名",example = "张三")
    private String contactName;

   @ApiModelProperty(value = "联系人手机号码",example = "13100000001")
    private String contactMobile;

   @ApiModelProperty(value = "联系人邮箱",example = "<EMAIL>")
    private String contactEmail;

   @ApiModelProperty(value = "联系人身份证号",example = "320102199003079791")
    private String contactIdcard;

   @ApiModelProperty(value = "联系人身份证电子版图片地址",example = "/img/xx.png")
    private String contactPicSrc;

   @ApiModelProperty(value = "联系人身份证电子版图片地址(反面)",example = "/img/xx.png")
    private String contactPicBackSrc;

   @ApiModelProperty(value = "是否手机号已验证",example = "1")
    private Integer isPhoneAuthenticated;

   @ApiModelProperty(value = "是否已实名验证",example = "1")
    private Integer isRealNameAuthenticated;

   @ApiModelProperty(value = "合作供应商",example = "汇通达网络有限公司")
    private String cooperateVendor;

   @ApiModelProperty(value = "注册来源",example = "3")
    private String registFrom;

   @ApiModelProperty(value = "推广人编号",example = "13100000001")
    private String promotionPerson;

   @ApiModelProperty(value = "状态",example = "1")
    private String status;

   @ApiModelProperty(value = "创建人ID",example = "1")
    private Long createId;

   @ApiModelProperty(value = "创建人名称",example = "张三")
    private String createName;

   @ApiModelProperty(value = "创建时间",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date createTime;

   @ApiModelProperty(value = "更新人ID",example = "1")
    private Long modifyId;

   @ApiModelProperty(value = "更新人名称",example = "张三")
    private String modifyName;

   @ApiModelProperty(value = "更新时间",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date modifyTime;

   @ApiModelProperty(value = "所在地-省",example = "江苏省")
    private String locationProvince;

   @ApiModelProperty(value = "所在地-市",example = "南京市")
    private String locationCity;

   @ApiModelProperty(value = "所在地-区",example = "玄武区")
    private String locationCounty;

   @ApiModelProperty(value = "所在地-镇",example = "麒麟镇")
    private String locationTown;

   @ApiModelProperty(value = "所在地-详细",example = "柳营西路50号汇通达大厦")
    private String locationDetail;

   @ApiModelProperty(value = "法人姓名",example = "张三")
    private String artificialPersonName;

   @ApiModelProperty(value = "法人手机号码",example = "13100000001")
    private String artificialPersonMobile;

   @ApiModelProperty(value = "会员/商家类型",example = "1")
    private String buyerSellerType;

   @ApiModelProperty(value = "注册开始时间",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date startDate;

   @ApiModelProperty(value = "注册结束时间",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date endDate;

   @ApiModelProperty(value = "商城账号",example = "htd20204531")
    private String mallAccount;

   @ApiModelProperty(value = "会员状态信息类型",example = "1")
    private String infoType;

   @ApiModelProperty(value = "客户类别",example = "暂无参考值")
    private String custType;

   @ApiModelProperty(value = "当前商家客户经理名称",example = "李四")
    private String curBelongManagerName;

   @ApiModelProperty(value = "成为会员时间",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date becomeMemberTime;

   @ApiModelProperty(value = "归属商家",example = "1")
    private Long belongSellerId;

   @ApiModelProperty(value = "当前归属商家",example = "1")
    private Long curBelongSellerId;

   @ApiModelProperty(value = "生日",example = "2019-08-21")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
   private Date birthday;

   @ApiModelProperty(value = "会员营业执照号码",example = "91341200686878001G")
    private String buyerBusinessLicenseId;

   @ApiModelProperty(value = "客商属性（会员属性）",example = "11")
    private String buyerFeature;

   @ApiModelProperty(value = "公司编码，金力代码",example = "HTD2000001")
    private String companyCode;

   @ApiModelProperty(value = "发票地址",example = "江苏省南京市玄武区柳营西路50号")
    private String invoiceAddress;

   @ApiModelProperty(value = "营业执照号",example = "91341200686878001G")
    private String businessLicenseId;

   @ApiModelProperty(value = "会员id集合",example = "[1,2]")
    private List<Long> ids;

   @ApiModelProperty(value = "当前归属公司id集合",example = "[1,2]")
    private List<Long> curBelongCompanyIds;

   @ApiModelProperty(value = "归属公司id集合",example = "[1,2]")
    private List<Long> belongCompanyIds;

   @ApiModelProperty(value = "备注",example = "备注")
    private String remark;

   @ApiModelProperty(value = "同步key",example = "224DE617-0921-4147-9518-74A59EA2C5C5")
    private String syncKey;

   @ApiModelProperty(value = "升级为商家时间（会员升级为商家时间）",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date upgradeSellerTime;

   @ApiModelProperty(value = "实名认证状态",example = "3")
    private String realNameStatus;

   @ApiModelProperty(value = "支付账号",example = "17042004542113900057")
    private String accountNo;

   @ApiModelProperty(value = "详细地址",example = "江苏省南京市玄武区柳营路50号")
    private String locationAddr;

   @ApiModelProperty(value = "银行卡绑定id",example = "1")
    private String bindId;

   @ApiModelProperty(value = "会员/商家公司ID",example = "1")
    private Long companyId;

   @ApiModelProperty(value = "审核信息ID",example = "1")
    private Long verifyId;

   @ApiModelProperty(value = "修改后的内容",example = "吕亚玲")
    private String afterChange;

   @ApiModelProperty(value = "修改前的内容",example = "担保会员")
    private String beforeChange;

   @ApiModelProperty(value = "企业类型",example = "1")
    private String businessType;

   @ApiModelProperty(value = "推荐人编码",example = "********")
    private String recommenderCode;

   @ApiModelProperty(value = "超级经理人展示状态",example = "1")
    private String managerStatus;

   @ApiModelProperty(value = "上级公司编码",example = "htd516668")
    private String parentComCode;

   @ApiModelProperty(value = "会员店位置",example = "0")
    private String storePosition;

   @ApiModelProperty(value = "注册资本",example = "0")
    private Integer registerCapital;

   @ApiModelProperty(value = "门头照片",example = "/img/xx.jpg")
    private String storeOutsidePicSrc;

   @ApiModelProperty(value = "店内照片",example = "/img/xx.jpg")
    private String storeInsidePicSrc;

   @ApiModelProperty(value = "会员账号类型",example = "0")
    private String memberAccountType;

   @ApiModelProperty(value = "会员账号所属人",example = "张三")
    private String memberAccountOwner;

   @ApiModelProperty(value = "合作分销商",example = "南京紫金阳光公司")
    private String distributorCooperateVendor;

   @ApiModelProperty(value = "业务审核展示时间",example = "2019-08-21 00:00:00")
   @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
   private Date businessShowTime;

   @ApiModelProperty(value = "平台公司编码",example = "htd516668")
    private String sellerCompanyCode;

   @ApiModelProperty(value = "商家标签",example = "0")
    private String sellerLabel;

   @ApiModelProperty(value = "经营范围",example = "1")
    private String businessScope;

   @ApiModelProperty(value = "经理人编号",example = "765235")
    private String managerCode;

   @ApiModelProperty(value = "会员入驻类型",example = "1")
    private String commingType;

   @ApiModelProperty(value = "标准会员标识",example = "1")
    private String standMemberFlag;

   @ApiModelProperty(value = "区域经理审核状态/系统同步状态",example = "1")
    private String businessVerifyStatus;

   @ApiModelProperty(value = "审核状态/系统同步状态",example = "1")
    private String verifyStatus;

   @ApiModelProperty(value = "供应商审核状态/系统同步状态",example = "1")
    private String cooperateVerifyStatus;

   @ApiModelProperty(value = "所属公司名称",example = "汇通达网络股份有限公司")
    private String belongCompanyName;

   @ApiModelProperty(value = "所属公司ID",example = "1")
    private Long belongCompanyId;

   @ApiModelProperty(value = "当前所属公司名称",example = "汇通达网络股份有限公司")
    private String curBelongCompanyName;

   @ApiModelProperty(value = "当前所属公司ID",example = "1")
    private Long curBelongCompanyId;

   @ApiModelProperty(value = "公司名称",example = "汇通达网络股份有限公司")
    private String companyName;

   @ApiModelProperty(value = "纳税人编号",example = "***************")
    private String taxpayerIDNumber;

   @ApiModelProperty(value = "企业名称",example = "汇通达网络股份有限公司")
    private String vendorName;

   @ApiModelProperty(value = "会员状态",example = "1")
    private String memberType;

   @ApiModelProperty(value = "会员账号类型",example = "0")
    private String accountType;

   @ApiModelProperty(value = "会员编码",example = "htd0000001")
    private String memberCode;

   @ApiModelProperty(value = "归属客户经理",example = "张三")
    private String belongManagerName;

   @ApiModelProperty(value = "当前归属客户经理id",example = "********")
    private String curBelongManagerId;

   @ApiModelProperty(value = "会员等级",example = "1")
    private String buyerGrade;

   @ApiModelProperty(value = "身份证号",example = "320102199003079791")
    private String artificialPersonIdcard;

   @ApiModelProperty(value = "身份证图片地址",example = "/img/xx.png")
    private String artificialPersonPicSrc;

   @ApiModelProperty(value = "手持身份证图片地址",example = "/img/xx.png")
    private String artificialPersonIdcardPicSrc;

   @ApiModelProperty(value = "身份证反面",example = "/img/xx.jpg")
    private String artificialPersonPicBackSrc;

   @ApiModelProperty(value = "JL编码（公司编码）",example = "907036")
    private String JFCode;

   @ApiModelProperty(value = "担保证明照片地址",example = "/img/xx.jpg")
    private String buyerGuaranteeLicensePicSrc;

   @ApiModelProperty(value = "营业执照照片地址",example = "/img/xx.jpg")
    private String buyerBusinessLicensePicSrc;

   @ApiModelProperty(value = "会员审核状态",example = "1")
    private String memberVerifyStatus;

   @ApiModelProperty(value = "归属商家客户经理ID",example = "********")
    private String belongManagerId;

   @ApiModelProperty(value = "开户行名称",example = "工商银行")
    private String bankName;

   @ApiModelProperty(value = "银行账号",example = "**************")
    private String bankAccount;

   @ApiModelProperty(value = "纳税人识别号",example = "***************")
    private String taxManId;

   @ApiModelProperty(value = "状态信息ID",example = "1")
    private String statusId;

   @ApiModelProperty(value = "同步错误信息(驳回原因)",example = "营业执照不合格")
    private String syncErrorMsg;

   @ApiModelProperty(value = "超威大区编码",example = "134212")
    private String districtNo;

   @ApiModelProperty(value = "汇超代理商类型",example = "1")
    private Integer k3SellerType;

   @ApiModelProperty(value = "超威会员编码",example = "123431")
    private String k3MemberCode;

   @ApiModelProperty(value = "公司名称",example = "汇通达网络股份有限公司")
    private String k3CompanyName;

   @ApiModelProperty(value = "经营人姓名",example = "张三")
    private String businessPersonName;

   @ApiModelProperty(value = "经营人手机号码",example = "***********")
    private String businessPersonMobile;

   @ApiModelProperty(value = "开户许可证",example = "/********/img/8096a8585340425288c8570c6a1002a6.jpg")
    private String accountLicence;

   @ApiModelProperty(value = "审核状态",example = "1")
    private String auditStatus;

   @ApiModelProperty(value = "数据标记",example = "1")
    private Integer dataTag;

    @ApiModelProperty(
            value = "会员类型 1-非会员 2-交易会员 3-标准会员",
            notes = "会员类型 1-非会员 2-交易会员 3-标准会员",
            example = "1"
    )
    private String memberFlag;
    @ApiModelProperty(value = "会员所在省", example = "32")
    private String actualBusinessProvince;

    @ApiModelProperty(value = "会员所在城市", example = "3202")
    private String actualBusinessCity;

    @ApiModelProperty(value = "会员所在区", example = "320201")
    private String actualBusinessCounty;

   /**
    * 事业部编码
    */
	@ApiModelProperty(value = "事业部编码", example = "s101")
   private String divisionCode;

    private String companyNameLikeStr;

    /**
     * 会员编码集合
     */
    @ApiModelProperty(value = "会员编码集合")
    private List<String> memberCodes;

    @ApiModelProperty(value = "100零售门店（临时状态），101核心会员店，102其他会员店，111原料商，112品牌商，113渠道商，114新型经营体，115商超连锁平台，116其他产业会员，201其他会员")
    private String memberFlagDetail;

    @ApiModelProperty(value = "一级分类")
    private String oneLevel;

    @ApiModelProperty(value = "二级分类")
    private String twoLevel;

    @ApiModelProperty(value = "三级分类")
    private String threeLevel;

    @ApiModelProperty(value = "企业类型")
    private String businessTypeDetail;

    @ApiModelProperty(
            value = "成立日期",
            notes = "成立日期"
    )
    //@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date establishTime;

    @ApiModelProperty(
            value = "主营品类",
            example = "1,2,3"
    )
    private String majorBusinessCategory;

    @ApiModelProperty(
            value = "税率",
            example = "1,2,3"
    )
    private String taxRate;

    @ApiModelProperty(
            value = "客户性质（系统天眼查对接）",
            notes = "有限公司；股份公司；个体工商户；合伙企业；外资企业；个人（无营业执照）",
            example = "有限公司"
    )
    private String customerNature;

    @ApiModelProperty(
            value = "发展行业",
            notes = "1、酒水，2、3C数码，3、交通出行，4、建材，5、农资农机，6、厨具卫浴，7、家用电器，9、家居建材，10、微物流乡村站点，11、新能源，12、种植养殖，13、快消品",
            example = "1"
    )
    private String industryCategoryMuti;

    @ApiModelProperty(value = "商家类型（复合）1:内部供应商，2:外部供应商 ,3:分销商，4:采购商", example = "1")
    private String sellerTypeMulti;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(
            value = "统一社会信用代码",
            example = "9876598765"
    )
    private String unifiedSocialCreditCode;

    @ApiModelProperty(value = "评级")
    private Integer level;

    @ApiModelProperty(
            value = "实际经营地址-省市区镇详细地址",hidden = true
    )
    private String actualBusinessAddress;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"companyCodes\":")
                .append(companyCodes);
        sb.append(",\"id\":")
                .append(id);
        sb.append(",\"uniqueCode\":\"")
                .append(uniqueCode).append('\"');
        sb.append(",\"companyLeagalPersionFlag\":\"")
                .append(companyLeagalPersionFlag).append('\"');
        sb.append(",\"isBuyer\":")
                .append(isBuyer);
        sb.append(",\"isSeller\":")
                .append(isSeller);
        sb.append(",\"canMallLogin\":")
                .append(canMallLogin);
        sb.append(",\"hasGuaranteeLicense\":")
                .append(hasGuaranteeLicense);
        sb.append(",\"hasBusinessLicense\":")
                .append(hasBusinessLicense);
        sb.append(",\"registTime\":\"")
                .append(registTime).append('\"');
        sb.append(",\"isCenterStore\":")
                .append(isCenterStore);
        sb.append(",\"upgradeCenterStoreTime\":\"")
                .append(upgradeCenterStoreTime).append('\"');
        sb.append(",\"sellerType\":\"")
                .append(sellerType).append('\"');
        sb.append(",\"isGeneration\":")
                .append(isGeneration);
        sb.append(",\"industryCategory\":\"")
                .append(industryCategory).append('\"');
        sb.append(",\"isDiffIndustry\":")
                .append(isDiffIndustry);
        sb.append(",\"contactName\":\"")
                .append(contactName).append('\"');
        sb.append(",\"contactMobile\":\"")
                .append(contactMobile).append('\"');
        sb.append(",\"contactEmail\":\"")
                .append(contactEmail).append('\"');
        sb.append(",\"contactIdcard\":\"")
                .append(contactIdcard).append('\"');
        sb.append(",\"contactPicSrc\":\"")
                .append(contactPicSrc).append('\"');
        sb.append(",\"contactPicBackSrc\":\"")
                .append(contactPicBackSrc).append('\"');
        sb.append(",\"isPhoneAuthenticated\":")
                .append(isPhoneAuthenticated);
        sb.append(",\"isRealNameAuthenticated\":")
                .append(isRealNameAuthenticated);
        sb.append(",\"cooperateVendor\":\"")
                .append(cooperateVendor).append('\"');
        sb.append(",\"registFrom\":\"")
                .append(registFrom).append('\"');
        sb.append(",\"promotionPerson\":\"")
                .append(promotionPerson).append('\"');
        sb.append(",\"status\":\"")
                .append(status).append('\"');
        sb.append(",\"createId\":")
                .append(createId);
        sb.append(",\"createName\":\"")
                .append(createName).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append(",\"modifyId\":")
                .append(modifyId);
        sb.append(",\"modifyName\":\"")
                .append(modifyName).append('\"');
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append(",\"locationProvince\":\"")
                .append(locationProvince).append('\"');
        sb.append(",\"locationCity\":\"")
                .append(locationCity).append('\"');
        sb.append(",\"locationCounty\":\"")
                .append(locationCounty).append('\"');
        sb.append(",\"locationTown\":\"")
                .append(locationTown).append('\"');
        sb.append(",\"locationDetail\":\"")
                .append(locationDetail).append('\"');
        sb.append(",\"artificialPersonName\":\"")
                .append(artificialPersonName).append('\"');
        sb.append(",\"artificialPersonMobile\":\"")
                .append(artificialPersonMobile).append('\"');
        sb.append(",\"buyerSellerType\":\"")
                .append(buyerSellerType).append('\"');
        sb.append(",\"startDate\":\"")
                .append(startDate).append('\"');
        sb.append(",\"endDate\":\"")
                .append(endDate).append('\"');
        sb.append(",\"mallAccount\":\"")
                .append(mallAccount).append('\"');
        sb.append(",\"infoType\":\"")
                .append(infoType).append('\"');
        sb.append(",\"custType\":\"")
                .append(custType).append('\"');
        sb.append(",\"curBelongManagerName\":\"")
                .append(curBelongManagerName).append('\"');
        sb.append(",\"becomeMemberTime\":\"")
                .append(becomeMemberTime).append('\"');
        sb.append(",\"belongSellerId\":")
                .append(belongSellerId);
        sb.append(",\"curBelongSellerId\":")
                .append(curBelongSellerId);
        sb.append(",\"birthday\":\"")
                .append(birthday).append('\"');
        sb.append(",\"buyerBusinessLicenseId\":\"")
                .append(buyerBusinessLicenseId).append('\"');
        sb.append(",\"buyerFeature\":\"")
                .append(buyerFeature).append('\"');
        sb.append(",\"companyCode\":\"")
                .append(companyCode).append('\"');
        sb.append(",\"invoiceAddress\":\"")
                .append(invoiceAddress).append('\"');
        sb.append(",\"businessLicenseId\":\"")
                .append(businessLicenseId).append('\"');
        sb.append(",\"ids\":")
                .append(ids);
        sb.append(",\"curBelongCompanyIds\":")
                .append(curBelongCompanyIds);
        sb.append(",\"belongCompanyIds\":")
                .append(belongCompanyIds);
        sb.append(",\"remark\":\"")
                .append(remark).append('\"');
        sb.append(",\"syncKey\":\"")
                .append(syncKey).append('\"');
        sb.append(",\"upgradeSellerTime\":\"")
                .append(upgradeSellerTime).append('\"');
        sb.append(",\"realNameStatus\":\"")
                .append(realNameStatus).append('\"');
        sb.append(",\"accountNo\":\"")
                .append(accountNo).append('\"');
        sb.append(",\"locationAddr\":\"")
                .append(locationAddr).append('\"');
        sb.append(",\"bindId\":\"")
                .append(bindId).append('\"');
        sb.append(",\"companyId\":")
                .append(companyId);
        sb.append(",\"verifyId\":")
                .append(verifyId);
        sb.append(",\"afterChange\":\"")
                .append(afterChange).append('\"');
        sb.append(",\"beforeChange\":\"")
                .append(beforeChange).append('\"');
        sb.append(",\"businessType\":\"")
                .append(businessType).append('\"');
        sb.append(",\"recommenderCode\":\"")
                .append(recommenderCode).append('\"');
        sb.append(",\"managerStatus\":\"")
                .append(managerStatus).append('\"');
        sb.append(",\"parentComCode\":\"")
                .append(parentComCode).append('\"');
        sb.append(",\"storePosition\":\"")
                .append(storePosition).append('\"');
        sb.append(",\"registerCapital\":")
                .append(registerCapital);
        sb.append(",\"storeOutsidePicSrc\":\"")
                .append(storeOutsidePicSrc).append('\"');
        sb.append(",\"storeInsidePicSrc\":\"")
                .append(storeInsidePicSrc).append('\"');
        sb.append(",\"memberAccountType\":\"")
                .append(memberAccountType).append('\"');
        sb.append(",\"memberAccountOwner\":\"")
                .append(memberAccountOwner).append('\"');
        sb.append(",\"distributorCooperateVendor\":\"")
                .append(distributorCooperateVendor).append('\"');
        sb.append(",\"businessShowTime\":\"")
                .append(businessShowTime).append('\"');
        sb.append(",\"sellerCompanyCode\":\"")
                .append(sellerCompanyCode).append('\"');
        sb.append(",\"sellerLabel\":\"")
                .append(sellerLabel).append('\"');
        sb.append(",\"businessScope\":\"")
                .append(businessScope).append('\"');
        sb.append(",\"managerCode\":\"")
                .append(managerCode).append('\"');
        sb.append(",\"commingType\":\"")
                .append(commingType).append('\"');
        sb.append(",\"standMemberFlag\":\"")
                .append(standMemberFlag).append('\"');
        sb.append(",\"businessVerifyStatus\":\"")
                .append(businessVerifyStatus).append('\"');
        sb.append(",\"verifyStatus\":\"")
                .append(verifyStatus).append('\"');
        sb.append(",\"cooperateVerifyStatus\":\"")
                .append(cooperateVerifyStatus).append('\"');
        sb.append(",\"belongCompanyName\":\"")
                .append(belongCompanyName).append('\"');
        sb.append(",\"belongCompanyId\":")
                .append(belongCompanyId);
        sb.append(",\"curBelongCompanyName\":\"")
                .append(curBelongCompanyName).append('\"');
        sb.append(",\"curBelongCompanyId\":")
                .append(curBelongCompanyId);
        sb.append(",\"companyName\":\"")
                .append(companyName).append('\"');
        sb.append(",\"taxpayerIDNumber\":\"")
                .append(taxpayerIDNumber).append('\"');
        sb.append(",\"vendorName\":\"")
                .append(vendorName).append('\"');
        sb.append(",\"memberType\":\"")
                .append(memberType).append('\"');
        sb.append(",\"accountType\":\"")
                .append(accountType).append('\"');
        sb.append(",\"memberCode\":\"")
                .append(memberCode).append('\"');
        sb.append(",\"belongManagerName\":\"")
                .append(belongManagerName).append('\"');
        sb.append(",\"curBelongManagerId\":\"")
                .append(curBelongManagerId).append('\"');
        sb.append(",\"buyerGrade\":\"")
                .append(buyerGrade).append('\"');
        sb.append(",\"artificialPersonIdcard\":\"")
                .append(artificialPersonIdcard).append('\"');
        sb.append(",\"artificialPersonPicSrc\":\"")
                .append(artificialPersonPicSrc).append('\"');
        sb.append(",\"artificialPersonIdcardPicSrc\":\"")
                .append(artificialPersonIdcardPicSrc).append('\"');
        sb.append(",\"artificialPersonPicBackSrc\":\"")
                .append(artificialPersonPicBackSrc).append('\"');
        sb.append(",\"JFCode\":\"")
                .append(JFCode).append('\"');
        sb.append(",\"buyerGuaranteeLicensePicSrc\":\"")
                .append(buyerGuaranteeLicensePicSrc).append('\"');
        sb.append(",\"buyerBusinessLicensePicSrc\":\"")
                .append(buyerBusinessLicensePicSrc).append('\"');
        sb.append(",\"memberVerifyStatus\":\"")
                .append(memberVerifyStatus).append('\"');
        sb.append(",\"belongManagerId\":\"")
                .append(belongManagerId).append('\"');
        sb.append(",\"bankName\":\"")
                .append(bankName).append('\"');
        sb.append(",\"bankAccount\":\"")
                .append(bankAccount).append('\"');
        sb.append(",\"taxManId\":\"")
                .append(taxManId).append('\"');
        sb.append(",\"statusId\":\"")
                .append(statusId).append('\"');
        sb.append(",\"syncErrorMsg\":\"")
                .append(syncErrorMsg).append('\"');
        sb.append(",\"districtNo\":\"")
                .append(districtNo).append('\"');
        sb.append(",\"k3SellerType\":")
                .append(k3SellerType);
        sb.append(",\"k3MemberCode\":\"")
                .append(k3MemberCode).append('\"');
        sb.append(",\"k3CompanyName\":\"")
                .append(k3CompanyName).append('\"');
        sb.append(",\"businessPersonName\":\"")
                .append(businessPersonName).append('\"');
        sb.append(",\"businessPersonMobile\":\"")
                .append(businessPersonMobile).append('\"');
        sb.append(",\"accountLicence\":\"")
                .append(accountLicence).append('\"');
        sb.append(",\"auditStatus\":\"")
                .append(auditStatus).append('\"');
        sb.append(",\"dataTag\":")
                .append(dataTag);
        sb.append(",\"memberFlag\":\"")
                .append(memberFlag).append('\"');
        sb.append(",\"actualBusinessProvince\":\"")
                .append(actualBusinessProvince).append('\"');
        sb.append(",\"actualBusinessCity\":\"")
                .append(actualBusinessCity).append('\"');
        sb.append(",\"actualBusinessCounty\":\"")
                .append(actualBusinessCounty).append('\"');
        sb.append(",\"divisionCode\":\"")
                .append(divisionCode).append('\"');
        sb.append(",\"companyNameLikeStr\":\"")
                .append(companyNameLikeStr).append('\"');
        sb.append(",\"memberCodes\":")
                .append(memberCodes);
        sb.append(",\"memberFlagDetail\":\"")
                .append(memberFlagDetail).append('\"');
        sb.append(",\"oneLevel\":\"")
                .append(oneLevel).append('\"');
        sb.append(",\"twoLevel\":\"")
                .append(twoLevel).append('\"');
        sb.append(",\"threeLevel\":\"")
                .append(threeLevel).append('\"');
        sb.append(",\"industryCategoryMuti\":\"")
                .append(industryCategoryMuti).append('\"');
        sb.append(",\"businessTypeDetail\":\"")
                .append(businessTypeDetail).append('\"');
        sb.append(",\"establishTime\":\"")
                .append(establishTime).append('\"');
        sb.append(",\"majorBusinessCategory\":\"")
                .append(majorBusinessCategory).append('\"');
        sb.append(",\"taxRate\":\"")
                .append(taxRate).append('\"');
        sb.append(",\"customerNature\":\"")
                .append(customerNature).append('\"');
        sb.append(",\"sellerTypeMulti\":\"")
                .append(sellerTypeMulti).append('\"');
        sb.append(",\"supplierCode\":\"")
                .append(supplierCode).append('\"');
        sb.append(",\"unifiedSocialCreditCode\":\"")
                .append(unifiedSocialCreditCode).append('\"');
        sb.append(",\"level\":\"")
                .append(level).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
