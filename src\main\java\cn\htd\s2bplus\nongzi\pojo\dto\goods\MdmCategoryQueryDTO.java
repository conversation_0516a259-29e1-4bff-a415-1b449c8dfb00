package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class MdmCategoryQueryDTO implements Serializable {
    private static final long serialVersionUID = 6670241488285705725L;
    @ApiModelProperty("类目ID")
    private String categoryId;

    @ApiModelProperty("类目编码")
    private String categoryCode;

    @ApiModelProperty("类目名称")
    private String categoryName;

    @ApiModelProperty("类目级次")
    private String categoryLevel;

    @ApiModelProperty("当前页")
    @NotNull(message = "当前页不能为空")
    private Integer current;

    @ApiModelProperty("每页的数量")
    @NotNull(message = "每页的数量不能为空")
    private Integer size;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
