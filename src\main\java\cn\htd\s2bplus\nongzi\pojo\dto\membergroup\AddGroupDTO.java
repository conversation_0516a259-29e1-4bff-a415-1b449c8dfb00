package cn.htd.s2bplus.nongzi.pojo.dto.membergroup;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.user.MemberBaseInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;

@Data
public class AddGroupDTO implements Serializable {


    @ApiModelProperty(value = "分组ID",example = "1",hidden = true)
    private Long groupId;

    @ApiModelProperty(value = "商家编码",example = "1",hidden = true)
    private String sellerCode;

    @Size(min = 0,max = 20,message = "分组名称长度不符合要求,要求在20个字符之内")
    @ApiModelProperty(value = "分组名称",example = "美的组",required = true)
    @NotBlank(message = "分组名称不能为空")
    private String name;

    @ApiModelProperty(value = "分组类型",example = "1指定人、2按行业、3按白名单行业、4按标签",required = true)
    private Integer groupType;

    @Size(min = 0,max = 30,message = "备注字符长度不符合要求,要求在30个字符之内")
    @ApiModelProperty(value = "备注",example = "备注",required = false)
    private String comment;

    @ApiModelProperty(value = "商家编码",example = "926388",required = false)
    private String sellerId;

    @ApiModelProperty(value = "商家名称",example = "926388",required = false)
    private String sellerName;

    @ApiModelProperty(value = "新增删除会员编码拼接字符串",example = "1,2,3,4",required = false)
    private String buyerIds;

    @ApiModelProperty(value = "操作人编码",example = "操作人编码",required = false,hidden = true)
    public String operateId;

    @ApiModelProperty(value = "操作人姓名",example = "操作人姓名",required = false,hidden = true)
    public String operateName;

    @ApiModelProperty(value = "登录运营人员Id",example = "登录运营人员Id",required = false,hidden = true)
    public String operationId;

    @ApiModelProperty(value = "会员信息集合")
    private Map<Long,MemberBaseInfoDTO> buyerInfoMap;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
