package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ImportServiceAddressDTO implements Serializable {
    @Excel(name = "服务商编码(htd)",width = 20, orderNum = "1")
    @ApiModelProperty(value = "服务商编码")
    private String serviceProviderCode;

    @Excel(name = "代收客户编码(htd)",width = 20, orderNum = "2")
    @ApiModelProperty(value = "代收客户编码")
    private String buyerCode;

    @Excel(name = "代收客户Apple ID",width = 20, orderNum = "3")
    @ApiModelProperty(value = "代收客户代收客户Apple ID")
    private String appleId;

    @Excel(name = "收货人姓名",width = 20, orderNum = "4")
    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;

    @Excel(name = "收货人电话",width = 20, orderNum = "5")
    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;

    @Excel(name = "省",width = 20, orderNum = "6")
    @ApiModelProperty(value = "省")
    private String provinceName;

    @ApiModelProperty(value = "市")
    @Excel(name = "市",width = 20, orderNum = "7")
    private String cityName;

    @Excel(name = "区/县/市",width = 20, orderNum = "8")
    @ApiModelProperty(value = "区/县/市")
    private String areaName;

    @Excel(name = "镇/街道/乡",width = 20, orderNum = "9")
    @ApiModelProperty(value = "镇/街道/乡")
    private String townName;

    @Excel(name = "详细地址",width = 20, orderNum = "10")
    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
