package cn.htd.s2bplus.nongzi.pojo.dto.order;


import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel
public class OrderItemsDTO implements Serializable {

    private static final long serialVersionUID = 3176871964623315720L;
    /**
     * 订单行ID
     */
    @ApiModelProperty(value = "订单行ID")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /**
     * 订单行号
     */
    @ApiModelProperty(value = "订单行号")
    private String orderItemNo;

    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    /**
     * 商品编码 10：HTD商品，20：外部供应商商品，3010：JD+商品
     */
    @ApiModelProperty(value = "商品编码 10：HTD商品，20：外部供应商商品，3010：JD+商品")
    private String itemCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品SKU编码
     */
    @ApiModelProperty(value = "商品SKU编码")
    private String skuCode;

    /**
     * 外部供应商商品SKU编码
     */
    @ApiModelProperty(value = "外部供应商商品SKU编码")
    private String outerSkuCode;

    /**
     * 商品SKUERP编码
     */
    @ApiModelProperty(value = "商品SKUERP编码")
    private String skuErpCode;

    /**
     * 商品SKUEAN码
     */
    @ApiModelProperty(value = "商品SKUEAN码")
    private String skuEanCode;

    /**
     * 商品图片地址
     */
    @ApiModelProperty(value = "商品图片地址")
    private String skuPictureUrl;

    /**
     * 商品模板编码
     */
    @ApiModelProperty(value = "商品模板编码")
    private String itemSpuCode;

    /**
     * 一级类目ID
     */
    @ApiModelProperty(value = "一级类目ID")
    private Long firstCategoryId;

    /**
     * 一级类目名称
     */
    @ApiModelProperty(value = "一级类目名称")
    private String firstCategoryName;

    /**
     * 二级类目ID
     */
    @ApiModelProperty(value = "二级类目ID")
    private Long secondCategoryId;

    /**
     * 二级类目名称
     */
    @ApiModelProperty(value = "二级类目名称")
    private String secondCategoryName;

    /**
     * 三级类目ID
     */
    @ApiModelProperty(value = "三级类目ID")
    private Long thirdCategoryId;

    /**
     * 三级类目名称
     */
    @ApiModelProperty(value = "三级类目名称")
    private String thirdCategoryName;

    /**
     * 类目id集合 该此段存储类目集合用，分割如：一级类目编码，二级类目编码，三级类目编码
     */
    @ApiModelProperty(value = "类目id集合 该此段存储类目集合用，分割如：一级类目编码，二级类目编码，三级类目编码")
    private String categoryIdList;

    /**
     * 类目名称集合 该此段存储类目集合用，分割如：一级类目名称，二级类目名称，三级类目名称
     */
    @ApiModelProperty(value = "类目名称集合 该此段存储类目集合用，分割如：一级类目名称，二级类目名称，三级类目名称")
    private String categoryNameList;

    /**
     * ERP一级类目编码
     */
    @ApiModelProperty(value = "ERP一级类目编码")
    private String erpFirstCategoryCode;

    /**
     *ERP五级类目编码
     */
    @ApiModelProperty(value = "ERP五级类目编码")
    private String erpFiveCategoryCode;

    /**
     * 品牌ID
     */
    @ApiModelProperty(value = "品牌ID")
    private Long brandId;

    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 客户经理编号
     */
    @ApiModelProperty(value = "客户经理编号")
    private String customerManagerCode;

    /**
     * 客户经理名称
     */
    @ApiModelProperty(value = "客户经理名称")
    private String customerManagerName;

    /**
     * 套餐商品标识
     */
    @ApiModelProperty(value = "套餐商品标识")
    private Integer isVipItem;

    /**
     * VIP商品类型
     */
    @ApiModelProperty(value = "VIP商品类型")
    private String vipItemType;

    /**
     * 同步VIP会员标记
     */
    @ApiModelProperty(value = "同步VIP会员标记")
    private Integer vipSyncFlag;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsCount;

    /**
     * 外部供货价/分销限价
     */
    @ApiModelProperty(value = "外部供货价/分销限价")
    private BigDecimal costPrice;

    /**
     * 价格浮动比例
     */
    @ApiModelProperty(value = "价格浮动比例")
    private BigDecimal priceFloatingRatio;

    /**
     * 佣金比例
     */
    @ApiModelProperty(value = "佣金比例")
    private BigDecimal commissionRatio;

    /**
     * 佣金金额
     */
    @ApiModelProperty(value = "佣金金额")
    private BigDecimal commissionAmount;

    /**
     * 包厢标记 1：包厢, 0：大厅
     */
    @ApiModelProperty(value = "包厢标记 1：包厢, 0：大厅")
    private Integer isBoxFlag;

    /**
     * 销售单价(区域时保存区域价格，包厢时保存包厢价格，外部供应商商品时本字段为0，商品+商品时为区域销售价）
     */
    @ApiModelProperty(value = "销售单价(区域时保存区域价格，包厢时保存包厢价格，外部供应商商品时本字段为0，商品+商品时为区域销售价）")
    private BigDecimal salePrice;

    /**
     * 商品单价种类：销售价，阶梯价，区域价，会员分组价，会员等级价，自定义价格（VMS开单时）等
     */
    @ApiModelProperty(value = "商品单价种类：销售价，阶梯价，区域价，会员分组价，会员等级价，自定义价格（VMS开单时）等")
    private String goodsPriceType;

    /**
     * 商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价
     */
    @ApiModelProperty(value = "商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价")
    private BigDecimal goodsPrice;

    /**
     * 议价单价(VMS专用）
     */
    @ApiModelProperty(value = "议价单价(VMS专用）")
    private BigDecimal bargainingGoodsPrice;

    /**
     * 议价数量(VMS专用）
     */
    @ApiModelProperty(value = "议价数量(VMS专用）")
    private Integer bargainingGoodsCount;

    /**
     * 运费模版ID
     */
    @ApiModelProperty(value = "运费模版ID")
    private Long shopFreightTemplateId;

    /**
     * 配送区域编码
     */
    @ApiModelProperty(value = "配送区域编码")
    private String deliveryAreaCode;

    /**
     * 商品总价
     */
    @ApiModelProperty(value = "商品总价")
    private BigDecimal goodsAmount;

    /**
     * 运费金额
     */
    @ApiModelProperty(value = "运费金额")
    private BigDecimal goodsFreight;

    /**
     * 优惠总金额  包含店铺优惠、平台优惠和使用返利的合计
     */
    @ApiModelProperty(value = "优惠总金额  包含店铺优惠、平台优惠和使用返利的合计")
    private BigDecimal totalDiscountAmount;

    /**
     * 店铺优惠金额   分担优惠券金额中，店铺优惠金额
     */
    @ApiModelProperty(value = "店铺优惠金额   分担优惠券金额中，店铺优惠金额")
    private BigDecimal shopDiscountAmount;

    /**
     * 平台优惠金额   分担优惠券金额中，平台优惠金额
     */
    @ApiModelProperty(value = "平台优惠金额   分担优惠券金额中，平台优惠金额")
    private BigDecimal platformDiscountAmount;

    /**
     * 使用返利金额 (VMS开单时才会输入）
     */
    @ApiModelProperty(value = "使用返利金额 (VMS开单时才会输入）")
    private BigDecimal usedRebateAmount;

    /**
     * 议价后商品总价
     */
    @ApiModelProperty(value = "议价后商品总价")
    private BigDecimal bargainingGoodsAmount;

    /**
     * 议价后运费金额
     */
    @ApiModelProperty(value = "议价后运费金额")
    private BigDecimal bargainingGoodsFreight;

    /**
     * 订单行总价
     */
    @ApiModelProperty(value = "订单行总价")
    private BigDecimal orderItemTotalAmount;

    /**
     * 订单行实付金额
     */
    @ApiModelProperty(value = "订单行实付金额")
    private BigDecimal orderItemPayAmount;

    /**
     * 商品实际单价 商品总金额-用券优惠总金额-议价优惠总金额（不含运费）
     */
    @ApiModelProperty(value = "商品实际单价 商品总金额-用券优惠总金额-议价优惠总金额（不含运费）")
    private BigDecimal goodsRealPrice;

    /**
     * 是否参与优惠 0：否，1：是
     */
    @ApiModelProperty(value = "是否参与优惠 0：否，1：是")
    private Integer hasUsedCoupon;

    /**
     * 是否议价(1:是，0:否)
     */
    @ApiModelProperty(value = "是否议价(1:是，0:否)")
    private Integer isChangePrice;

    /**
     * 商品+外部供应商订单号
     */
    @ApiModelProperty(value = "商品+外部供应商订单号")
    private String outerChannelOrderNo;

    /**
     * 商品+采购订单编号
     */
    @ApiModelProperty(value = "商品+采购订单编号")
    private String outerChannelPuchaseNo;

    /**
     * 商品+采购订单状态
     */
    @ApiModelProperty(value = "商品+采购订单状态")
    private String outerChannelPuchaseStatus;

    /**
     * 平台公司状态
     */
    @ApiModelProperty(value = "平台公司状态")
    private String outerChannelStatus;

    /**
     * 订单行状态
     */
    @ApiModelProperty(value = "订单行状态")
    private String orderItemStatus;

    /**
     * 订单行收货时间
     */
    @ApiModelProperty(value = "订单行收货时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date orderItemReceiptTime;

    /**
     * 订单行异常状态
     */
    @ApiModelProperty(value = "订单行异常状态")
    private String orderItemErrorStatus;

    /**
     * 订单行异常时间
     */
    @ApiModelProperty(value = "订单行异常时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date orderItemErrorTime;

    /**
     * 订单行异常原因
     */
    @ApiModelProperty(value = "订单行异常原因")
    private String orderItemErrorReason;

    /**
     * 是否添加订单行
     */
    @ApiModelProperty(value = "是否添加订单行")
    private Integer isAddOrderItem;

    /**
     * 是否取消订单行 0：未取消，1：已取消，2：VMS删除订单行
     */
    @ApiModelProperty(value = "是否取消订单行 0：未取消，1：已取消，2：VMS删除订单行")
    private Integer isCancelOrderItem;

    /**
     * 订单行取消时间
     */
    @ApiModelProperty(value = "订单行取消时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date orderItemCancelTime;

    /**
     * 订单行取消人ID
     */
    @ApiModelProperty(value = "订单行取消人ID")
    private Long orderItemCancelOperatorId;

    /**
     * 订单行取消人名称
     */
    @ApiModelProperty(value = "订单行取消人名称")
    private String orderItemCancelOperatorName;

    /**
     * 订单行取消原因
     */
    @ApiModelProperty(value = "订单行取消原因")
    private String orderItemCancelReason;

    /**
     * 退货/退款状态 0：未退货/退款，1：已退款，2：已退货
     */
    @ApiModelProperty(value = "退货/退款状态 0：未退货/退款，1：已退款，2：已退货")
    private String refundStatus;

    /**
     * 是否超出配送范围
     */
    @ApiModelProperty(value = "是否超出配送范围")
    private Integer isOutDistribtion;

    /**
     * 分销单ID
     */
    @ApiModelProperty(value = "分销单ID")
    private Long erpDistributionId;

    /**
     * ERP分销单号
     */
    @ApiModelProperty(value = "ERP分销单号")
    private String erpSholesalerCode;

    /**
     * 返利单号 erp制作的返利单
     */
    @ApiModelProperty(value = "返利单号 erp制作的返利单")
    private String erpRebateNo;

    /**
     * 返利代码
     */
    @ApiModelProperty(value = "返利代码")
    private String erpRebateCode;

    /**
     * 是否已结算
     */
    @ApiModelProperty(value = "是否已结算")
    private Integer isSettled;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long modifyId;

    /**
     * 更新人名称
     */
    @ApiModelProperty(value = "更新人名称")
    private String modifyName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 商品属性keyId:valueId 外部商家商品保存销售属性，内部商品保存Item的类目属性
     */
    @ApiModelProperty(value = "商品属性keyId:valueId 外部商家商品保存销售属性，内部商品保存Item的类目属性")
    private String skuAttributes;

    /**
     * 商家用券优惠金额
     */
    @ApiModelProperty(value = "商家用券优惠金额")
    private BigDecimal sellerDiscountAmount;

    /**
     * 套餐编码
     */
    @ApiModelProperty(value = "套餐编码")
    private String assembledCode;

    /**
     * 套餐名称
     */
    @ApiModelProperty(value = "套餐名称")
    private String assembledName;

    /**
     * 使用下家返利金额
     */
    @ApiModelProperty(value = "使用下家返利金额")
    private BigDecimal usedRebatePriceAmount;

    /**
     * 使用总部促销池金额
     */
    @ApiModelProperty(value = "使用总部促销池金额")
    private BigDecimal usedPromotionPoolAmount;

    /**
     * 是否自营商品订单
     */
    @ApiModelProperty(value = "是否自营商品订单")
    private Integer isSelfFlag;

    /**
     * 下家计提控制 1为不控制
     */
    @ApiModelProperty(value = "下家计提控制 1为不控制")
    private Integer ctlRebate;

    /**
     * 上家收入控制 1为不控制
     */
    @ApiModelProperty(value = "上家收入控制 1为不控制")
    private Integer ctlRevenue;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private Long tenementId;

    /**
     * 商品快照ID
     */
    @ApiModelProperty(value = "商品快照ID")
    private Long frontItemSnapshotId;

    /**
     * 商品版本号
     */
    @ApiModelProperty(value = "商品版本号")
    private String version;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String itemAttributes;

    @ApiModelProperty(value = "规格属性")
    private String specificationAttribute;

    @ApiModelProperty(value = "商品数量转换小数")
    private String convertGoodsCount;

    @ApiModelProperty(
            value = "推荐人",
            notes = "推荐人,存到商品行",
            example = "张三"
    )
    private String recommender;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
