package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SellerSkuQuery implements Serializable {
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码",example = "xx",required = false)
    private String itemCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称",example = "xx",required = false)
    private String itemName;
    /**
     * sku编码
     */
    @ApiModelProperty(value = "sku编码",example = "xx",required = false)
    private String skuCode;
    /**
     * 上下架状态
     */
    @ApiModelProperty(value = "上下架状态",example = "0:下架，1：上架",required = false,hidden = false)
    private Integer status;
    /**
     * 上下架状态
     */
    @ApiModelProperty(value = "上下架状态",example = "0:下架，1：上架",required = false,hidden = true)
    private List<Integer> statusList;
    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称",example = "xx",required = false)
    private String shopName;
    /**
     * 商家id
     */
    @ApiModelProperty(value = "商品编码",example = "xx",required = false,hidden = true)
    private Long sellerId;
    /**
     * 类目id
     */
    @ApiModelProperty(value = "类目id",example = "xx",required = false,hidden = true)
    private Long categoryId;
    /**
     * 类目名称
     */
    @ApiModelProperty(value = "类目名称",example = "xx",required = false)
    private String categoryName;
    /**
     * 品牌id
     */
    @ApiModelProperty(value = "品牌id",example = "xx",required = false,hidden = true)
    private Long brandId;
    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称",example = "xx",required = false)
    private String brandName;
    /**
     * 销售价格开始
     */
    @ApiModelProperty(value = "销售价格开始",example = "xx",required = false)
    private BigDecimal baseMinPrice;
    /**
     * 销售价格结束
     */
    @ApiModelProperty(value = "销售价格结束",example = "xx",required = false)
    private BigDecimal baseMaxPrice;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间",example = "xx",required = false)
    private String releaseTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间",example = "xx",required = false)
    private String endTime;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"itemCode\":\"")
                .append(itemCode).append('\"');
        sb.append(",\"itemName\":\"")
                .append(itemName).append('\"');
        sb.append(",\"skuCode\":\"")
                .append(skuCode).append('\"');
        sb.append(",\"status\":")
                .append(status);
        sb.append(",\"statusList\":")
                .append(statusList);
        sb.append(",\"shopName\":\"")
                .append(shopName).append('\"');
        sb.append(",\"sellerId\":")
                .append(sellerId);
        sb.append(",\"categoryId\":")
                .append(categoryId);
        sb.append(",\"categoryName\":\"")
                .append(categoryName).append('\"');
        sb.append(",\"brandId\":")
                .append(brandId);
        sb.append(",\"brandName\":\"")
                .append(brandName).append('\"');
        sb.append(",\"baseMinPrice\":\"")
                .append(baseMinPrice).append('\"');
        sb.append(",\"baseMaxPrice\":\"")
                .append(baseMaxPrice).append('\"');
        sb.append(",\"releaseTime\":\"")
                .append(releaseTime).append('\"');
        sb.append(",\"endTime\":\"")
                .append(endTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
