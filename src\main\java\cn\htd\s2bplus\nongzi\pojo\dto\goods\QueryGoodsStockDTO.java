package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QueryGoodsStockDTO implements Serializable {
    @ApiModelProperty(value = "店铺id")
    private Long shopId;
    @NotBlank(message = "卖家编码不能为空")
    private String sellerCode;
    @NotEmpty(message = "sku编码不能为空")
    private List<String> skuCodeList;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
