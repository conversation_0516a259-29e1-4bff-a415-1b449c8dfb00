package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class ItemAttributeDTO implements Serializable {
    private static final long serialVersionUID = -3655840102085448831L;
    @ApiModelProperty(value = "商家属性ID")
    private Long sellerAttrid;

    @ApiModelProperty(value = "属性值ID")
    private Long attrId;

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "属性名称")
    private String attrName;

    @ApiModelProperty(value = "删除标记")
    private Integer status;

    @ApiModelProperty(value = "属性值列表")
    private List<ItemAttributeValueDTO> itemAttributeValueList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
