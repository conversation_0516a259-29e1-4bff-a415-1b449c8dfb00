package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MemberShopInfoReqDTO extends BaseDTO {

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "店铺状态")
    private Integer storeStatus;

    @ApiModelProperty(value = "商家名称")
    private String companyName;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "经营范围(1、酒水，2、3C数码，3、交通出行，4、建材，5、农资农机，6、厨具卫浴，7、家用电器，9、家居建材，10、微物流乡村站点，11、新能源，12、种植养殖，13、快消品)")
    private String businessScope;

    @ApiModelProperty(value = "分页参数")
    private Integer page;

    @ApiModelProperty(value = "分页参数")
    private Integer rows;



}
