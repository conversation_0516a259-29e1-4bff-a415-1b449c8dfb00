package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class QueryPurchaserOrderListDTO implements Serializable {

    private static final long serialVersionUID = -298552800810486253L;

    @ApiModelProperty(value = "订单编号")
    private String purchaserOrderNumber;

    @ApiModelProperty(value = "订单状态")
    private String purchaserOrderStatus;

    @ApiModelProperty(value = "下单开始时间")
    private String createStartTime;

    @ApiModelProperty(value = "下单结束时间")
    private String createEndTime;

    @ApiModelProperty(value = "买家名称")
    private String buyerName;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "当前页",hidden = true)
    private Integer page;

    @ApiModelProperty(value = "每页显示记录数",hidden = true)
    private Integer row;

    @ApiModelProperty(value = "供应商编码",hidden = true)
    private String supplierCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
