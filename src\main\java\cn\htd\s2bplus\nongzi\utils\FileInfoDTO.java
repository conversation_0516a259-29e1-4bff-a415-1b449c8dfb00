package cn.htd.s2bplus.nongzi.utils;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

@Data
public class FileInfoDTO {
    @ApiModelProperty(value = "绝对路径",example = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\e64c986f-fb33-4f56-ae69-4fecb6ad3237\\一个规格商品\\11\\大福1.jpg")
    private String absolutePath;

    @ApiModelProperty(value = "文件名，带后缀",example = "大福1.jpg")
    private String fileName;

    @ApiModelProperty(value = "父文件夹路径名",example = "11")
    private String parentFolders;

    @ApiModelProperty(value = "文件类型",example = "image")
    private String fileType;

    @ApiModelProperty(value = "图片在阿里云的后缀地址",example = "/b2b/batchImportPublishGoods/20250605144518_大福1.jpg")
    private String imageOssUrl;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
