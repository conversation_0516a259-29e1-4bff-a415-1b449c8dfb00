package cn.htd.s2bplus.nongzi.service.order;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.vo.ShopAuthSubAccountVO;

import java.util.List;


/**
 * @title SubAccountService
 * @description 查询子账号已分配的店铺信息
 * @Date: 2022/5/11 13:57
 */
public interface SubAccountService {

    /**
     * 查询子账号已分配的店铺信息
     *
     * @return 响应
     */
    Result<List<ShopAuthSubAccountVO>> querySubAllocateShop(String shopName);

    Result<List<ShopAuthSubAccountVO>> querySubAllocateShopByLoginId(String subAccountLoginId,String shopName,String memberCode);
}
