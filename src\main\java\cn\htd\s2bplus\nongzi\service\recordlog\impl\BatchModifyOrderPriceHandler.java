package cn.htd.s2bplus.nongzi.service.recordlog.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.LogBusinessTypeEnum;
import cn.htd.s2bplus.nongzi.contants.LogOperationTypeEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.Express.UpdatePriceExcelDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.dto.common.SaveOperationLogParamDTO;
import cn.htd.s2bplus.nongzi.service.order.SaveBusinessRecordLogService;
import cn.htd.s2bplus.nongzi.service.recordlog.BusinessRecordLogService;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @title BatchModifyOrderPriceHandler
 * @description 批量改下
 * @Date: 2022/4/28 17:40
 */
@Service
public class BatchModifyOrderPriceHandler implements BusinessRecordLogService {
    /**
     * 日志
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());



    @Autowired
    private SaveBusinessRecordLogService saveBusinessRecordLogService;

    @Override
    public Integer operationType() {
        return LogOperationTypeEnum.BATCH_MODIFY_PRICE.getOperationType();
    }

    @Async
    @Override
    public void saveBusinessRecordLog(Object[] object, LoginUserDetail loginUser) {
        try {
            MultipartFile file = (MultipartFile) object[0];
            if (ObjectUtils.isEmpty(file)) {
                logger.info("保存日志：批量改价获取参数信息为null");
                return;
            }
            // 获取批量改价的模板
            List<UpdatePriceExcelDTO> updatePriceExcel = getUpdatePriceExcel(file);

            if (CollectionUtils.isEmpty(updatePriceExcel)) {
                logger.info("BatchModifyOrderPriceHandler：导入的批量改价模板为空");
                return;
            }

            // 入参整合
            String businessKey = loginUser.getLoginId();
            String operationRecord = JSONObject.toJSONString(updatePriceExcel);
            Integer businessType = LogBusinessTypeEnum.TAKE_DOWN_LIST.getBusinessType();
            Integer operationType = LogOperationTypeEnum.BATCH_MODIFY_PRICE.getOperationType();

            // 整合日志信息
            String printLog = saveBusinessRecordLogService.getPrintLog(businessKey, businessType, operationType);

            logger.info("BatchModifyOrderPriceHandler req={}", printLog);
            SaveOperationLogParamDTO saveOperationLogParamDTO  = new SaveOperationLogParamDTO(businessType, operationType,
                    businessKey, operationRecord);
            saveBusinessRecordLogService.saveRecord(saveOperationLogParamDTO,loginUser);
        } catch (Exception e) {
            logger.error("BatchModifyOrderPriceHandler error", e);
        }
    }

    /**
     * 获取批量导入的内容
     *
     * @param file 文件
     * @return 响应
     * @throws Exception 异常
     */
    private List<UpdatePriceExcelDTO> getUpdatePriceExcel(MultipartFile file) throws Exception {
        List<String> list = Arrays.asList(CommonConstants.SKU_CODE, CommonConstants.SHOP_ID, CommonConstants.SALE_PRICE);

        String[] strings = new String[list.size()];
        // 获取excel内容
        ImportParams params = new ImportParams();
        params.setTitleRows(CommonConstants.EXCEL_TITLE_ROWS);
        params.setHeadRows(CommonConstants.EXCEL_HEAD_ROWS);
        params.setImportFields(list.toArray(strings));

        InputStream inputStream = file.getInputStream();
        List<UpdatePriceExcelDTO> fileListImport =
                ExcelImportUtil.importExcel(inputStream, UpdatePriceExcelDTO.class, params);

        return fileListImport;
    }
}
