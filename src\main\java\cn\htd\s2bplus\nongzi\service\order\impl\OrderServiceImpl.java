package cn.htd.s2bplus.nongzi.service.order.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.rdc.base.development.framework.core.util.CollectionUtil;
import cn.htd.rdc.base.development.framework.core.util.StringUtil;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.config.OssNacosConfig;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.*;
import cn.htd.s2bplus.nongzi.feign.auth.AuthServiceAPI;
import cn.htd.s2bplus.nongzi.feign.finance.TradeOrderFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.feign.order.OrderFeignService;
import cn.htd.s2bplus.nongzi.feign.purchase.PurchaseFeignService;
import cn.htd.s2bplus.nongzi.feign.user.UserService;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.common.Pager;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ExportSaleOrderDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.Constant;
import cn.htd.s2bplus.nongzi.pojo.dto.order.*;
import cn.htd.s2bplus.nongzi.pojo.dto.purchase.PurchaseSaleWaitDeliveryDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.purchase.PurchaseSaleWaitDeliveryItemVO;
import cn.htd.s2bplus.nongzi.pojo.dto.purchase.PurchaseSaleWaitDeliveryVO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.service.goods.GoodsService;
import cn.htd.s2bplus.nongzi.service.order.BusinessOperateLogService;
import cn.htd.s2bplus.nongzi.service.order.OrderService;
import cn.htd.s2bplus.nongzi.service.security.BaseService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("orderService")
public class OrderServiceImpl implements OrderService {

    private static final Logger logger = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Autowired
    private BaseService baseService;

    @Autowired
    private PurchaseFeignService purchaseFeignService;

    //文件名样式
    SimpleDateFormat simpleDateFormatName = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
    //文件数据日期格式
    SimpleDateFormat dateFormatName = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final static String EXCEL_2007 =".xlsx";   //2007+ 版本的excel

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;

    @Value("${cipher.common.saleShipOrderReportExportSceneId}")
    private String saleShipOrderReportExportSceneId;

    @Value("${batch.importCount}")
    private String importCount;

    @Value("${cipher.common.saleOrderReportExportSceneId}")
    private String saleOrderReportExportSceneId;

    @Autowired
    private OrderFeignService orderFeignService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private UserService userService;

    @Autowired
    private AuthServiceAPI authServiceAPI;

    @Autowired
    private MiddleGroundAPI middleGroundAPI;

    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;

    @Autowired
    private OssNacosConfig ossNacosConfig;
    @Autowired
    private TradeOrderFeignService tradeOrderFeignService;

    private int getTotalPage(int totalCount,int rows){
        int totalPage = 1;
        totalPage = (int) (totalCount % rows == 0 ? totalCount / rows : (totalCount / rows + 1));
        return totalPage;
    }

    @Override
    public Result<OrderDetailsPageDTO> getExportOrderList(OrderAndAllStatusReqDTO orderAndAllStatus, LoginUserDetail loginUser) {
        //循环查询数据,每页200条
        int page = 1;
        int rows = 200;
        Result<OrderDetailsPageDTO> orderDetailsPageDTOResult = this.orderList(orderAndAllStatus, page, rows,loginUser);
        //获取需要生成的所有订单列表数据
        return this.getExportAllOrderList(orderDetailsPageDTOResult,orderAndAllStatus,loginUser,rows,CommonConstants.OPERATION_FROM_YC);
    }


    @Override
    public Result<OrderDetailsPageDTO> orderList(OrderAndAllStatusReqDTO orderAndAllStatus, int current, int size,LoginUserDetail loginUserDetail) {
        Result<OrderDetailsPageDTO> result = new Result<>();
        //设置撮合订单查询掉件
        this.changeSubOrderFrom(orderAndAllStatus);
        //订单查询增加子账号店铺过滤
        List<Long> shopIds = new ArrayList<>();
        if (null != loginUserDetail && !ObjectUtils.isEmpty(loginUserDetail.getParentAccount())) {
            Map<Long,String> map = new HashMap<>();
            map = goodsService.getShopInfo(loginUserDetail,null);
            if(CollectionUtils.isEmpty(map)){
                result.setCode(ResultEnum.SUCCESS.getCode());
                result.setMsg("订单列表查询成功");
                return result;
            }
            if(null == orderAndAllStatus.getShopId()){
                shopIds = new ArrayList<>(map.keySet());
            }else {
                Boolean flag = true;
                Set<Map.Entry<Long, String>> entries = map.entrySet();
                for (Map.Entry<Long, String> entry : entries) {
                    if(entry.getKey().equals(orderAndAllStatus.getShopId())){
                        flag = false;
                    }
                }
                if(flag){
                    result.setCode(ResultEnum.SUCCESS.getCode());
                    result.setMsg("套餐列表查询成功");
                    return result;
                }
            }
        }
        if(CollectionUtils.isEmpty(shopIds) && !ObjectUtils.isEmpty(orderAndAllStatus.getShopId())){
            shopIds.add(orderAndAllStatus.getShopId());
        }
        orderAndAllStatus.setShopIdFilterList(shopIds);
        logger.info("订单列表 orderAndAllStatus req:{}",orderAndAllStatus);
        Result<OrderDetailsPageDTO> orderDetailsPageResult = orderFeignService.orderList(orderAndAllStatus,current,size);
        //订单列表结果转换
        this.orderListResultConversion(orderDetailsPageResult);
        return orderDetailsPageResult;
    }


    @Override
    public Result<ReportHistoryDTO> createReportHistory(ReportHistoryDTO reportHistoryDTO) {
        Result<ReportHistoryDTO> result = orderFeignService.createReportHistory(reportHistoryDTO);
        logger.info("orderService-->createReportHistory,resp:{}",result.toString());
        return result;
    }

    @Override
    public Result<OrderDeliverySelfPickupDTO> queryOrderDeliverySelfPickup(String orderNo) {
        logger.info("根据订单编码查询自提信息,req:{}",orderNo);
        Result<OrderDeliverySelfPickupDTO> result = orderFeignService.queryOrderDeliverySelfPickup(orderNo);
        logger.info("根据订单编码查询自提信息,resp:{}",result.toString());
        return result;
    }

    /**
     * 确认发货
     * @param inShipmentDTO
     * @return
     */
    @Override
    public Result<Boolean> deliverGood(InShipmentDTO inShipmentDTO){
        logger.info("orderService-->deliverGood,resp:{}",inShipmentDTO.toString());
        Result<Boolean> result = new Result<>();
        List<ShipmentDTO> shipmentList = inShipmentDTO.getShipmentList();
        if (CollectionUtils.isEmpty(shipmentList)){
            result.setMsg("信息为空！");
            result.setCode(ResultEnum.ERROR.getCode());
            return result;
        }
        Boolean flag = Boolean.TRUE;
        a:for (ShipmentDTO shipmentDTO : shipmentList) {
            List<LogisticsDTO> logisticsList = shipmentDTO.getLogisticsList();
            if (!CollectionUtils.isEmpty(logisticsList)){
                for (LogisticsDTO logisticsDTO : logisticsList) {
                    if ("2".equals(logisticsDTO.getLogisticsType())){
                        result = orderFeignService.deliverGood(inShipmentDTO);
                        flag = Boolean.FALSE;
                        break a;
                    }
                    if (org.apache.commons.lang.StringUtils.isNotEmpty(logisticsDTO.getLogisticsNo())){
                        String[] split = logisticsDTO.getLogisticsNo().split(",");
                        List<LogisticsDTO> logistics= new ArrayList<>();
                        for (String s : split) {
                            LogisticsDTO copylogisticsDTO = new LogisticsDTO();
                            BeanUtil.copy(logisticsDTO,copylogisticsDTO);
                            copylogisticsDTO.setLogisticsNo(s);
                            logistics.add(copylogisticsDTO);
                        }
                        shipmentDTO.setLogisticsList(logistics);
                    }
                }
            }
        }
        if(flag){
            result = orderFeignService.deliverGood(inShipmentDTO);
            logger.info("orderFeignService.deliverGood resp:{}",JSON.toJSONString(result));
            if (!result.isSuccess()){
                result.setMsg(result.getMsg());
                result.setCode(result.getCode());
                result.setData(false);
                return result;
            }
        }
        //调取财务域新增业务单据结果信息接口
        String orderNoStage = inShipmentDTO.getShipmentList().get(0).getOrderStageNo();
        String orderNo = orderNoStage.substring(0,orderNoStage.length()-1);
        Result<QueryOrderDTO> queryOrderDTOResult = orderFeignService.queryOrderInfo(orderNo);
        if (!queryOrderDTOResult.isSuccess()){
            logger.error("orderFeignService.queryOrderInfo 报错:{}",queryOrderDTOResult.toString());
            result.setMsg(ResultEnum.ERROR.getMsg());
            result.setCode(ResultEnum.ERROR.getCode());
            return result;
        }
        if (null == queryOrderDTOResult.getData()) {
            logger.error("setTradeOrderDTO 订单信息不存在:{}",orderNo);
            result.setMsg("订单信息不存在");
            result.setCode(ResultEnum.ERROR.getCode());
            return result;
        }
        //增加如果佣金单编号为空，不走修改逻辑
        if(org.apache.commons.lang.StringUtils.isNotBlank(queryOrderDTOResult.getData().getFinOrderNo())){
            TradeOrderBargainDTO tradeOrderDTO = new TradeOrderBargainDTO();
            tradeOrderDTO.setFinOrderNo(queryOrderDTOResult.getData().getFinOrderNo());
            tradeOrderDTO.setSendTime(new Date());
            logger.info("deliverGood modifyTradeOrderAfterBargain req:{}",tradeOrderDTO.toString());
            orderFeignService.modifyTradeOrderAfterBargain(tradeOrderDTO);
        }
        return result;
    }

    @Override
    @Async
    public void handleSaleOrderReportExport(HttpServletResponse response, OssOrderAndAllStatusReqDTO ossOrderAndAllStatusReqDTO, LoginUserDetail loginUser) {
        //OSS生成销售单入参转换
        OrderAndAllStatusReqDTO orderAndAllStatus = this.conversionOrderAndAllStatus(ossOrderAndAllStatusReqDTO);
        String sheetNameStart = StrConstant.STR_EMPTY_STRING;
        Date beginTime = orderAndAllStatus.getBeginTime();
        Date endTime = orderAndAllStatus.getEndTime();
        Integer tabTime = orderAndAllStatus.getTabTime();
        // 如果查询时间不传，则查询最近三个月的数据
        if ((ObjectUtils.isEmpty(beginTime) && ObjectUtils.isEmpty(endTime))
                && (StringUtils.isBlank(orderAndAllStatus.getOrderStatus()) && ObjectUtils.isEmpty(tabTime))) {
            orderAndAllStatus.setTabTime(CommonConstants.ORDER_LIST_TAB_TIME_THREE_MONTHS);
        }
        //设置订单查询开始结束时间
        this.setStartAndEndTime(orderAndAllStatus);
        //分页查询订单数据
        Result<OrderDetailsPageDTO> orderDetailsPageDTOResult = this.getExportOssOrderList(orderAndAllStatus,loginUser);
        logger.info("分页查询订单数据总数:{}",ObjectUtils.isEmpty(orderDetailsPageDTOResult.getData()) ? null : orderDetailsPageDTOResult.getData().getTotal());
        if (!orderDetailsPageDTOResult.isSuccess() || ObjectUtils.isEmpty(orderDetailsPageDTOResult.getData())) {
            logger.info("查询销售单数据出错:{}",orderDetailsPageDTOResult);
            return;
        }
        OrderDetailsPageDTO orderDetailsPageDTO = orderDetailsPageDTOResult.getData();
        List<OrderDetailResDTO> orderDetailResDTOS = orderDetailsPageDTO.getOrderDetailResDTOS();
        //数据转换
        List<SaleOrderDetailExportDTO> saleOrderDetailDTOList = this.saleOrderDetailDataTransform(orderDetailResDTOS,loginUser,ossOrderAndAllStatusReqDTO.getOrderAuthorizationStatus());
        sheetNameStart = CommonConstants.SALES_ORDER;
        //生成excel文件，并上传的阿里云服务器返回文件地址路径
        String downloadUrl = this.getDownloadUrl(saleOrderDetailDTOList, response, sheetNameStart);
        if (StringUtils.isBlank(downloadUrl)) {
            logger.info("获取上传文件地址出错:{}",downloadUrl);
            return;
        }
        ReportHistoryDTO reportHistoryDTO = new ReportHistoryDTO();
        reportHistoryDTO.setBusinessType((BusinessTypeEnum.SALE_ORDER.getCode().byteValue()));
        reportHistoryDTO.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
        reportHistoryDTO.setDownloadUrl(downloadUrl);
        reportHistoryDTO.setFinishTime(new Date());
        if (ObjectUtils.isEmpty(beginTime)){
            reportHistoryDTO.setBeginTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
        }else{
            reportHistoryDTO.setBeginTime(beginTime);
        }
        if (!ObjectUtils.isEmpty(tabTime) && CommonConstants.ORDER_LIST_TAB_TIME_BEFORE_THREE_MONTHS.equals(tabTime)){
            reportHistoryDTO.setEndTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
        }else{
            if (ObjectUtils.isEmpty(endTime)){
                reportHistoryDTO.setEndTime(new Date());
            }else{
                reportHistoryDTO.setEndTime(endTime);
            }
        }
        reportHistoryDTO.setCreateId(loginUser.getUserId());
        reportHistoryDTO.setCreateName(loginUser.getUserName());
        reportHistoryDTO.setCreateTime(new Date());
        reportHistoryDTO.setModifyId(loginUser.getUserId());
        reportHistoryDTO.setModifyName(loginUser.getUserName());
        reportHistoryDTO.setModifyTime(new Date());
        // 保存报表生成下载历史
        this.createReportHistory(reportHistoryDTO);
    }

    @Override
    public Result<Pager<ReportHistoryDTO>> queryReportHistoryPage(String finishBeginTime, String finishEndtime, Long userId, int page, int rows) {
        //默认显示30天
        if (StringUtils.isBlank(finishBeginTime) && StringUtils.isBlank(finishEndtime)){
            Date date = DateUtil.rollDay(new Date(),CommonConstants.OSS_REPORT_HISTORY_LIST_DEFAULT_TIME);
            finishBeginTime = DateUtil.setDateToString(date);
        }
        Result<Pager<ReportHistoryDTO>> result = orderFeignService.queryReportHistoryPage(finishBeginTime,finishEndtime,userId,page,rows);
        logger.info("分页查询报表生成下载历史出参:{}",result);
        return result;
    }



    private void changeSubOrderFrom(OrderAndAllStatusReqDTO orderAndAllStatus){
        if (!StringUtils.isEmpty(orderAndAllStatus.getSubOrderFrom())){
            if (Constant.CLOUD_ORDER_TYPE_NORMAL.equals(orderAndAllStatus.getSubOrderFrom())){
               // 撮合订单由于终端优化，原来查询字段SubOrderFrom不传值，将所有撮合渠道放入subOrderFromList字段中
                orderAndAllStatus.setSubOrderFrom(null);
                List<String> subOrderFromList = new ArrayList<>();
                for (SubOrderFromEnum sub : SubOrderFromEnum.values()) {
                    subOrderFromList.add(sub.getCode());
                }
                orderAndAllStatus.setSubOrderFromList(subOrderFromList);
            }
        }
    }

    /**
     * 数据转换
     * @param orderDetailResDTOS 转换入参
     * @param loginUser 登陆人信息
     * @return 转换出参对象集合
     */
    private List<SaleOrderDetailExportDTO> saleOrderDetailDataTransform(List<OrderDetailResDTO> orderDetailResDTOS,LoginUserDetail loginUser,String orderAuthorizationStatus){
        List<SaleOrderDetailExportDTO> saleOrderDetailDTOList = new ArrayList<>();
        List<String> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderDetailResDTOS)) {
            return saleOrderDetailDTOList;
        }
        //订单状态集合
        Map<String,String> orderReportStatusMap = Arrays.stream(OrderReportStatusEnum.values())
                .collect(Collectors.toMap(OrderReportStatusEnum::getStatus,OrderReportStatusEnum::getDescription));
        for (OrderDetailResDTO orderDetailResDTO : orderDetailResDTOS){
            List<OrderItemsDTO> orderItemsDTOS = orderDetailResDTO.getOrderItemsDTOS();
            for (OrderItemsDTO orderItemsDTO : orderItemsDTOS){
                SaleOrderDetailExportDTO saleOrderDetailDTO = new SaleOrderDetailExportDTO();
                saleOrderDetailDTO.setOrderNo(orderItemsDTO.getOrderNo());
                saleOrderDetailDTO.setOrderItemNo(orderItemsDTO.getOrderItemNo());
                saleOrderDetailDTO.setCreateTime(dateFormatName.format(orderDetailResDTO.getCreateTime()));
                if (null != orderDetailResDTO.getPayOrderTime()) {
                    saleOrderDetailDTO.setPayOrderTime(dateFormatName.format(orderDetailResDTO.getPayOrderTime()));
                }
                list.add(orderDetailResDTO.getBuyerCode());
                saleOrderDetailDTO.setBuyerCode(orderDetailResDTO.getBuyerCode());
                saleOrderDetailDTO.setBuyerName(orderDetailResDTO.getBuyerName());
                saleOrderDetailDTO.setRecommender(!ObjectUtils.isEmpty(orderItemsDTO.getRecommender())
                        ? orderItemsDTO.getRecommender() : orderDetailResDTO.getRecommender());
                saleOrderDetailDTO.setSkuCode(orderItemsDTO.getSkuCode());
                saleOrderDetailDTO.setGoodsName(orderItemsDTO.getGoodsName());
                saleOrderDetailDTO.setBrandName(orderItemsDTO.getBrandName());
                saleOrderDetailDTO.setSalesUnit("");
                saleOrderDetailDTO.setGoodsPrice(orderItemsDTO.getGoodsPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                saleOrderDetailDTO.setGoodsCount(orderItemsDTO.getGoodsCount());
                saleOrderDetailDTO.setGoodsAmount(orderItemsDTO.getGoodsAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                saleOrderDetailDTO.setGoodsFreight(orderItemsDTO.getGoodsFreight().toPlainString());
                saleOrderDetailDTO.setTotalDiscountAmount(orderItemsDTO.getTotalDiscountAmount().toPlainString());
                saleOrderDetailDTO.setPlatformDiscountAmount(orderItemsDTO.getPlatformDiscountAmount().toPlainString());

                //议价金额
                saleOrderDetailDTO.setBargainingAmount(null);
                //计息金额
                saleOrderDetailDTO.setInterestBearingAmount(null);

                saleOrderDetailDTO.setShopDiscountAmount(orderItemsDTO.getShopDiscountAmount().toPlainString());
                saleOrderDetailDTO.setOrderItemPayAmount(orderItemsDTO.getOrderItemPayAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                if(ParamEnum.PAY_TYPE_BALANCE.getCode().equals(orderDetailResDTO.getPayType())){
                    saleOrderDetailDTO.setPayType(ParamEnum.PAY_TYPE_BALANCE.getMsg());
                }else if(ParamEnum.PAY_TYPE_PLATFORM.getCode().equals(orderDetailResDTO.getPayType())){
                    saleOrderDetailDTO.setPayType(ParamEnum.PAY_TYPE_PLATFORM.getMsg());
                }else if(ParamEnum.PAY_TYPE_ONLINE.getCode().equals(orderDetailResDTO.getPayType())){
                    saleOrderDetailDTO.setPayType(ParamEnum.PAY_TYPE_ONLINE.getMsg());
                }
                saleOrderDetailDTO.setPayChannel(orderDetailResDTO.getPayChannel());
                if(ParamEnum.ORDER_TYPE_ORDINARY.getCode().equals(orderDetailResDTO.getOrderType())){
                    saleOrderDetailDTO.setOrderType(ParamEnum.ORDER_TYPE_ORDINARY.getMsg());
                }else if(ParamEnum.ORDER_TYPE_ADVANCE_SALE.getCode().equals(orderDetailResDTO.getOrderType())) {
                    saleOrderDetailDTO.setOrderType(ParamEnum.ORDER_TYPE_ADVANCE_SALE.getMsg());
                }
                //订单状态描述转换
                this.orderStatusDescriptionConversion(orderReportStatusMap,orderDetailResDTO,saleOrderDetailDTO,null);
                if (ParamEnum.ORDER_DELIVERYTYPE_DISTRIBUTION.getCode().equals(orderDetailResDTO.getDeliveryType())){
                    saleOrderDetailDTO.setDeliveryType(ParamEnum.ORDER_DELIVERYTYPE_DISTRIBUTION.getMsg());
                    saleOrderDetailDTO.setConsigneeName(orderDetailResDTO.getConsigneeName());
                    saleOrderDetailDTO.setConsigneePhoneNum(baseService.decryptSingle(orderDetailResDTO.getDsConsigneePhoneNum(),saleOrderReportExportSceneId));
                    saleOrderDetailDTO.setConsigneeAddress(baseService.decryptSingle(orderDetailResDTO.getDsConsigneeAddress(),saleOrderReportExportSceneId));
                }else{
                    saleOrderDetailDTO.setDeliveryType(ParamEnum.ORDER_DELIVERYTYPE_SELFRAISING.getMsg());
                    Result<OrderDeliverySelfPickupDTO> pickupDTOResult = this.queryOrderDeliverySelfPickup(orderItemsDTO.getOrderNo());
                    if (pickupDTOResult.isSuccess() && null != pickupDTOResult.getData()){
                        saleOrderDetailDTO.setConsigneeName(pickupDTOResult.getData().getPickupName());
                        saleOrderDetailDTO.setConsigneePhoneNum(baseService.decryptSingle(pickupDTOResult.getData().getDsContactPhone(),saleOrderReportExportSceneId));
                        saleOrderDetailDTO.setConsigneeAddress(baseService.decryptSingle(pickupDTOResult.getData().getDsPickupAddress(),saleOrderReportExportSceneId));
                        saleOrderDetailDTO.setCarNumber(pickupDTOResult.getData().getCarNo());
                        saleOrderDetailDTO.setIdentityCard(baseService.decryptSingle(pickupDTOResult.getData().getDsIdentityNo(),saleOrderReportExportSceneId));
                    }
                }
                saleOrderDetailDTO.setBuyerRemarks(orderDetailResDTO.getBuyerRemarks());
                saleOrderDetailDTO.setOrderRemarks(orderDetailResDTO.getOrderRemarks());

                if(!org.apache.commons.lang.StringUtils.equals(orderDetailResDTO.getInvoiceType(), InvoiceEnum.OFFLINE_INVOICE.getType())){
                    saleOrderDetailDTO.setEmailAddress(orderDetailResDTO.getEmailAddress());
                    saleOrderDetailDTO.setInvoiceReceiverName(orderDetailResDTO.getInvoiceReceiverName());
                    saleOrderDetailDTO.setInvoiceReceiverPhone(baseService.decryptSingle(orderDetailResDTO.getDsInvoiceReceiverPhone(),saleOrderReportExportSceneId));
                    saleOrderDetailDTO.setInvoiceAddressDetail(baseService.decryptSingle(orderDetailResDTO.getDsInvoiceAddressDetail(),saleOrderReportExportSceneId));

                    saleOrderDetailDTO.setTaxManId(orderDetailResDTO.getTaxManId());
                    saleOrderDetailDTO.setBankName(orderDetailResDTO.getBankName());
                    saleOrderDetailDTO.setBankAccount(baseService.decryptSingle(orderDetailResDTO.getDsBankAccount(),saleOrderReportExportSceneId));
                    saleOrderDetailDTO.setContactPhone(baseService.decryptSingle(orderDetailResDTO.getDsContactPhone(),saleOrderReportExportSceneId));
                    saleOrderDetailDTO.setInvoiceAddress(baseService.decryptSingle(orderDetailResDTO.getDsInvoiceAddress(),saleOrderReportExportSceneId));
                    saleOrderDetailDTO.setInvoiceNotify(orderDetailResDTO.getInvoiceNotify());
                    if(org.apache.commons.lang.StringUtils.equals(orderDetailResDTO.getInvoiceType(), InvoiceEnum.PLAIN_INVOICE.getType())){//普通发票
                        saleOrderDetailDTO.setInvoiceType(InvoiceEnum.PLAIN_INVOICE.getName());
                        saleOrderDetailDTO.setInvoiceNotify(orderDetailResDTO.getInvoiceNotify());
                    }
                    if(org.apache.commons.lang.StringUtils.equals(orderDetailResDTO.getInvoiceType(), InvoiceEnum.VALUE_ADDED_TAX_INVOICE.getType())){//增值税发票
                        saleOrderDetailDTO.setInvoiceType(InvoiceEnum.VALUE_ADDED_TAX_INVOICE.getName());
                        saleOrderDetailDTO.setInvoiceNotify(orderDetailResDTO.getInvoiceCompanyName());
                    }
                }else {
                    saleOrderDetailDTO.setInvoiceType(InvoiceEnum.OFFLINE_INVOICE.getName());
                }

                saleOrderDetailDTO.setShopName(orderDetailResDTO.getShopName());
                saleOrderDetailDTOList.add(saleOrderDetailDTO);
            }
        }
        if(!CollectionUtils.isEmpty(list) &&
                SellerTypeEnum.INTER_SUPPLIERS.getCode().equals(loginUser.getSellerType())){
            this.convertRes(list, saleOrderDetailDTOList);
        }
        return saleOrderDetailDTOList;
    }

    /**
     * 协议查询
     * @param list
     * @param saleOrderDetailDTOList
     */
    private void convertRes(List<String> list,List<SaleOrderDetailExportDTO> saleOrderDetailDTOList) {
        logger.info("批量查询会员是否签代付协议返回map 入参:{}",list);
        Result<Map<String,MemberExtendInfoDTO>> mapResult = userService.selectInfoAnotherPay(list);
        if(mapResult.isSuccess() && null != mapResult.getData()){
            logger.info("批量查询会员是否签代付协议返回map 出参:{}",mapResult.getData().size());
            saleOrderDetailDTOList.stream().forEach(saleOrderDetailExportDTO -> {
                if(mapResult.getData().containsKey(saleOrderDetailExportDTO.getBuyerCode())){
                    MemberExtendInfoDTO memberExtendInfo =  mapResult.getData().get(saleOrderDetailExportDTO.getBuyerCode());
                    if(CommonConstants.ANOTHER_PAY.equals(memberExtendInfo.getPaymentTag())){
                        saleOrderDetailExportDTO.setBuyerName(memberExtendInfo.getCompanyName());
                    }
                }
            });
        }else{
            throw new BusinessException(ResultEnum.QUERY_MEMBER_INFO_NULL.getCode(),
                    ResultEnum.QUERY_MEMBER_INFO_NULL.getMsg());
        }
    }

    /**
     * 生成excel文件，并上传的阿里云服务器返回文件地址路径
     */
    private String  getDownloadUrl(List<SaleOrderDetailExportDTO> saleOrderDetailDTOList, HttpServletResponse response, String sheetNameStart){
        String downloadUrl = "";
        try{
            String sheetName = simpleDateFormatName.format(new Date());
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", SaleOrderDetailExportDTO.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(saleOrderDetailDTOList) ?
                    new ArrayList<SaleOrderDetailDTO>() : saleOrderDetailDTOList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            // 改成输出excel文件
            response.setContentType("applicationnd.ms-excel");
            String fileName = sheetNameStart +"_"+ sheetName;
            // 03版本后缀xls，之后的xlsx
            response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xls");
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            OssUtils ossUtils = new OssUtils();
            String ossFileName = UUID.randomUUID().toString().replace("-", "").toLowerCase()+".xls";
            downloadUrl = ossUtils.upload(excelStream,ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        }catch (Exception e){
            logger.error("生成excel文件,error:",e);
        }
        return downloadUrl;
    }

    /**
     *  OSS生成销售单入参转换
     * @param ossOrderAndAllStatusReqDTO OSS生成销售单入参
     * @return 生成报表入参
     */
    private OrderAndAllStatusReqDTO conversionOrderAndAllStatus(OssOrderAndAllStatusReqDTO ossOrderAndAllStatusReqDTO) {
        OrderAndAllStatusReqDTO orderAndAllStatusReqDTO = new OrderAndAllStatusReqDTO();
        BeanUtil.copy(ossOrderAndAllStatusReqDTO,orderAndAllStatusReqDTO);
        if (StringUtils.isNotBlank(ossOrderAndAllStatusReqDTO.getBeginTime())) {
            orderAndAllStatusReqDTO.setBeginTime(DateUtil.setStringToDate(ossOrderAndAllStatusReqDTO.getBeginTime()
                    ,DateUtil.YYYY_MM_DD_HH_MM_SS_SPLIT));
        }
        if (StringUtils.isNotBlank(ossOrderAndAllStatusReqDTO.getEndTime())) {
            orderAndAllStatusReqDTO.setEndTime(DateUtil.setStringToDate(ossOrderAndAllStatusReqDTO.getEndTime()
                    ,DateUtil.YYYY_MM_DD_HH_MM_SS_SPLIT));
        }
        if (StringUtils.isNotBlank(ossOrderAndAllStatusReqDTO.getStartPayTime())) {
            orderAndAllStatusReqDTO.setStartPayTime(DateUtil.setStringToDate(ossOrderAndAllStatusReqDTO.getStartPayTime()
                    ,DateUtil.YYYY_MM_DD_HH_MM_SS_SPLIT));
        }
        if (StringUtils.isNotBlank(ossOrderAndAllStatusReqDTO.getEndPayTime())) {
            orderAndAllStatusReqDTO.setEndPayTime(DateUtil.setStringToDate(ossOrderAndAllStatusReqDTO.getEndPayTime()
                    ,DateUtil.YYYY_MM_DD_HH_MM_SS_SPLIT));
        }
        return orderAndAllStatusReqDTO;
    }

    /**
     * 获取导出oss顶动感列表
     * @param orderAndAllStatus 销售单生成报表入参对象
     * @param loginUser 登陆人信息
     * @return 无
     */
    private Result<OrderDetailsPageDTO> getExportOssOrderList(OrderAndAllStatusReqDTO orderAndAllStatus, LoginUserDetail loginUser) {
        //循环查询数据,每页200条
        int page = CommonConstants.OSS_ORDER_LIST_PAGE;
        int rows = CommonConstants.OSS_ORDER_LIST_ROWS;
        Result<OrderDetailsPageDTO> orderDetailsPageDTOResult = this.ossOrderList(orderAndAllStatus, page, rows,loginUser);
        //获取需要生成的所有订单列表数据
        return this.getExportAllOrderList(orderDetailsPageDTOResult,orderAndAllStatus,loginUser,rows,CommonConstants.OPERATION_FROM_OP);
    }

    /**
     * oss查询订单列表
     * @param orderAndAllStatus 列表查询条件入参
     * @param current 页数
     * @param size 每页记录数
     * @param loginUser 登陆人信息
     * @return oss订单列表
     */
    private Result<OrderDetailsPageDTO> ossOrderList(OrderAndAllStatusReqDTO orderAndAllStatus, int current, int size, LoginUserDetail loginUser) {
        try {
            //查询登陆人授权信息
            List<ProxyOperateShopAuthDTO> authResult = this.getProxyOperateShopAuthResult(loginUser);
            if (CollectionUtils.isEmpty(authResult)){
                return CommonResultUtil.success(null);
            }
            // 获取店铺集合
            List<Long> shopIds = authResult.stream().map(ProxyOperateShopAuthDTO::getShopId).collect(Collectors.toList());
            // 授权商家集合
            List<String> memberCodes = authResult.stream().map(ProxyOperateShopAuthDTO::getMemberCode).collect(Collectors.toList())
                    .stream().distinct().collect(Collectors.toList());
            logger.info("oss查询订单列表授权店铺ids,授权商家集合:{},{}",shopIds,memberCodes);
            // 店铺查询条件
            if(!ObjectUtils.isEmpty(orderAndAllStatus.getShopId())){
                List<Long> shopIdsList = new ArrayList<>();
                shopIdsList.add(orderAndAllStatus.getShopId());
                orderAndAllStatus.setShopIdFilterList(shopIdsList);
            } else {
                orderAndAllStatus.setShopIdFilterList(shopIds);
            }
            orderAndAllStatus.setSellerCodes(memberCodes);
            logger.info("oss查询订单列表,req:{}",orderAndAllStatus);
            Result<OrderDetailsPageDTO> orderDetailsPageResult = orderFeignService.orderList(orderAndAllStatus,current,size);
            //订单列表结果转换
            this.orderListResultConversion(orderDetailsPageResult);
            return orderDetailsPageResult;
        }catch (Exception e){
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    /**
     * 查询授权信息
     * @param loginUser 登陆人信息
     * @return 登陆人被授权的商家和店铺信息
     */
    private List<ProxyOperateShopAuthDTO> getProxyOperateShopAuthResult(LoginUserDetail loginUser) {
        ProxyOperateShopAuthDTO proxyOperate = new ProxyOperateShopAuthDTO();
        proxyOperate.setLoginId(loginUser.getLoginId());
        logger.info("校验分组商家运行授权信息入参 {}",proxyOperate);
        Result<List<ProxyOperateShopAuthDTO>> listResult = authServiceAPI.queryAuthorizationList(proxyOperate);
        logger.info("校验分组商家运行授权信息出参 {}", listResult);
        if (!listResult.isSuccess() || listResult.getData() == null || CollectionUtils.isEmpty(listResult.getData())) {
            return new ArrayList<>();
        }
        return listResult.getData();
    }

    /**
     * 获取需要生成的所有订单列表数据
     * @param orderDetailsPageDTOResult 订单列表查询结果集合
     * @param orderAndAllStatus 生成报表入参
     * @param loginUser 登陆人信息
     * @param rows 每页记录数
     * @param operationFrom 1-云场 2-OP系统
     * @return 需要生成的订单列表数据
     */
    private Result<OrderDetailsPageDTO> getExportAllOrderList(Result<OrderDetailsPageDTO> orderDetailsPageDTOResult,
                                                              OrderAndAllStatusReqDTO orderAndAllStatus,
                                                              LoginUserDetail loginUser,
                                                              int rows,
                                                              String operationFrom) {
        int totalPage = 1;
        List<OrderDetailResDTO> allOrderList = new ArrayList<>();
        OrderDetailsPageDTO orderDetailsPageDTO = orderDetailsPageDTOResult.getData();
        if (null == orderDetailsPageDTO){
            orderDetailsPageDTOResult.setCode(ResultEnum.ERROR.getCode());
            orderDetailsPageDTOResult.setMsg("没有可导出的数据！");
            return orderDetailsPageDTOResult;
        }
        allOrderList.addAll(orderDetailsPageDTO.getOrderDetailResDTOS());
        totalPage = this.getTotalPage(orderDetailsPageDTOResult.getData().getTotal().intValue(),rows);
        logger.info("totalPage:{},totalRows:{}",totalPage,orderDetailsPageDTOResult.getData().getTotal().intValue());
        if(totalPage > 1){
            logger.info("页数超过1页：{}",totalPage);
            //分页条数大于1条
            for (int i = 2; i <= totalPage; i++){
                logger.info("分页查询导出订单列表:{}",i);
                if (CommonConstants.OPERATION_FROM_YC.equals(operationFrom)) {
                    //云场订单列表
                    orderDetailsPageDTOResult = this.orderList(orderAndAllStatus, i, rows,loginUser);
                } else if (CommonConstants.OPERATION_FROM_OP.equals(operationFrom)) {
                    //OSS订单列表
                    orderDetailsPageDTOResult = this.ossOrderList(orderAndAllStatus, i, rows,loginUser);
                }
                if(ResultEnum.SUCCESS.getCode().intValue() != orderDetailsPageDTOResult.getCode().intValue()){
                    logger.info("分页查询导出订单列表返回code:{}",orderDetailsPageDTOResult.getCode().intValue());
                    continue;
                }
                if(!ObjectUtils.isEmpty(orderDetailsPageDTOResult.getData()) && CollectionUtil.isNotEmpty(orderDetailsPageDTOResult.getData().getOrderDetailResDTOS())){
                    logger.info("分页查询导出订单列表返回,size:{}",orderDetailsPageDTOResult.getData().getOrderDetailResDTOS().size());
                    allOrderList.addAll(orderDetailsPageDTOResult.getData().getOrderDetailResDTOS());
                }
            }
        }
        logger.info("总条数:{}",allOrderList.size());
        orderDetailsPageDTOResult.getData().setOrderDetailResDTOS(allOrderList);
        return orderDetailsPageDTOResult;
    }

    /**
     * 设置订单查询开始结束时间
     * @param orderAndAllStatus 查询订单列表条件入参
     */
    private void setStartAndEndTime(OrderAndAllStatusReqDTO orderAndAllStatus) {
        //前端传入，使用传入的参数
        if (!ObjectUtils.isEmpty(orderAndAllStatus.getBeginTime()) && !ObjectUtils.isEmpty(orderAndAllStatus.getEndTime())) {
            return;
        }
        //配置为0，则使用原先逻辑
        if (CommonConstants.All_MONTH.equals(nongZiNacosConfig.getQueryMonth())) {
            return;
        }
        //开始时间为配置时间
        if (ObjectUtils.isEmpty(orderAndAllStatus.getBeginTime())) {
            orderAndAllStatus.setBeginTime(DateUtil.getDateBeforeTheSpecifiedDay(nongZiNacosConfig.getQueryMonth()));
        }
        //结束时间为当前时间
        if (ObjectUtils.isEmpty(orderAndAllStatus.getEndTime())) {
            orderAndAllStatus.setEndTime(new Date());
        }
    }

    /**
     * 订单列表结果转换
     * @param result 订单列表结果集
     */
    private void orderListResultConversion(Result<OrderDetailsPageDTO> result) {
        logger.info("订单列表结果转换:{}",result);
        if (result.isSuccess() && null  != result.getData()) {
            List<OrderDetailResDTO> detailResDTOS = result.getData().getOrderDetailResDTOS();
            Map<String,MemberExtendInfoDTO> mapResultData = new HashMap<>();
            if (BaseContextHandler.getLoginUser() != null && SellerTypeEnum.INTER_SUPPLIERS.getCode().equals(BaseContextHandler.getLoginUser().getSellerType())) {
                List<String> memberCodes = detailResDTOS.stream().map(OrderDetailResDTO::getBuyerCode).collect(Collectors.toList());
                Result<Map<String,MemberExtendInfoDTO>> mapResult = userService.selectInfoAnotherPay(memberCodes);
                if (mapResult.isSuccess() && mapResult.getData() != null) {
                    mapResultData = mapResult.getData();
                }
            }
            for (OrderDetailResDTO orderDetailResDTO : detailResDTOS) {
                if (StringUtils.isNotBlank(orderDetailResDTO.getOrderRefundStatus())){
                    orderDetailResDTO.setOrderStatus(orderDetailResDTO.getOrderRefundStatus());
                }
                //定金预售处理订单金额
                if(ParamEnum.PAY_TYPE_PLATFORM.getCode().equals(orderDetailResDTO.getOrderType())){
                    orderDetailResDTO.setOrderPayAmount(orderDetailResDTO.getOrderPayAmount().add(orderDetailResDTO.getTotalFreight()));
                }
                MemberExtendInfoDTO memberExtendInfo = mapResultData.get(orderDetailResDTO.getBuyerCode());
                if (memberExtendInfo != null && memberExtendInfo.getPaymentTag().intValue() == 1
                        && org.apache.commons.lang.StringUtils.isNotBlank(memberExtendInfo.getCompanyName())) {
                    orderDetailResDTO.setBuyerName(memberExtendInfo.getCompanyName());
                }
            }
        }
    }

    /**
     * 订单状态描述转换
     * @param orderReportStatusMap 订单状态集合
     * @param orderDetailResDTO 查询出的订单数据
     * @param saleOrderDetailDTO 导出订单数据
     */
    public void orderStatusDescriptionConversion(Map<String,String> orderReportStatusMap,
                                                 OrderDetailResDTO orderDetailResDTO,
                                                 SaleOrderDetailExportDTO saleOrderDetailDTO,
                                                 SaleShipOrderDetailExportDTO saleShipOrderDetailExportDTO) {
        //审核中状态
        if (OrderReportStatusEnum.ORDER_STATUS_NOT_DELIVERED.getStatus().equals(orderDetailResDTO.getOrderStatus())
                && ParamEnum.ORDER_STATUS_IN_REVIEW.getCode().equals(orderDetailResDTO.getOrderAuthorizationStatus())) {
            if (!ObjectUtils.isEmpty(saleOrderDetailDTO)) {
                saleOrderDetailDTO.setOrderStatus(OrderReportStatusEnum.ORDER_STATUS_IN_REVIEW.getDescription());
            }
            if (!ObjectUtils.isEmpty(saleShipOrderDetailExportDTO)) {
                saleShipOrderDetailExportDTO.setOrderStatus(OrderReportStatusEnum.ORDER_STATUS_IN_REVIEW.getDescription());
            }
            return;
        }
        if (!ObjectUtils.isEmpty(saleOrderDetailDTO)) {
            saleOrderDetailDTO.setOrderStatus(orderReportStatusMap.get(orderDetailResDTO.getOrderStatus()));
        }
        if (!ObjectUtils.isEmpty(saleShipOrderDetailExportDTO)) {
            saleShipOrderDetailExportDTO.setOrderStatus(orderReportStatusMap.get(orderDetailResDTO.getOrderStatus()));
        }
    }

    @Override
    @Async
    public void popExportOrderList(OrderAndAllStatusReqDTO orderAndAllStatus,LoginUserDetail loginUser,Integer tabTime,Date beginTime,Date endTime,HttpServletResponse response) {
        //分页查询订单数据
        Result<OrderDetailsPageDTO> orderDetailsPageDTOResult = this.getExportOrderList(orderAndAllStatus,loginUser);
        if (orderDetailsPageDTOResult.isSuccess()){
            OrderDetailsPageDTO orderDetailsPageDTO = orderDetailsPageDTOResult.getData();
            List<OrderDetailResDTO> orderDetailResDTOS = orderDetailsPageDTO.getOrderDetailResDTOS();
            List<SaleShipOrderDetailExportDTO> saleOrderDetailDTOList = this.saleShipOrderDetailDataTransform(orderDetailResDTOS);
            String sheetNameStart = "销售单";
            String downloadUrl = this.getShipOrderDownloadUrl(saleOrderDetailDTOList, response, sheetNameStart);
            if (!"".equals(downloadUrl)){
                ReportHistoryDTO reportHistoryDTO = new ReportHistoryDTO();
                reportHistoryDTO.setBusinessType((BusinessTypeEnum.SALE_ORDER.getCode().byteValue()));
                reportHistoryDTO.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
                reportHistoryDTO.setDownloadUrl(downloadUrl);
                reportHistoryDTO.setFinishTime(new Date());
                if (null == beginTime){
                    reportHistoryDTO.setBeginTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
                }else{
                    reportHistoryDTO.setBeginTime(beginTime);
                }
                if (tabTime !=null && tabTime==1){
                    reportHistoryDTO.setEndTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
                }else{
                    if (null == endTime){
                        reportHistoryDTO.setEndTime(new Date());
                    }else{
                        reportHistoryDTO.setEndTime(endTime);
                    }
                }
                reportHistoryDTO.setCreateId(loginUser.getUserId());
                reportHistoryDTO.setCreateName(loginUser.getUserName());
                reportHistoryDTO.setCreateTime(new Date());
                reportHistoryDTO.setModifyId(loginUser.getUserId());
                reportHistoryDTO.setModifyName(loginUser.getUserName());
                reportHistoryDTO.setModifyTime(new Date());
                // 保存报表生成下载历史
                Result<ReportHistoryDTO> result = this.createReportHistory(reportHistoryDTO);
                if(!result.isSuccess()){
                    logger.info("保存报表生成下载历史出错:{}",orderDetailsPageDTOResult);
                    throw new BusinessException(ResultEnum.FAILURE.getCode(),result.getMsg());
                }
            }else{
                logger.error("获取上传文件地址出错:{}",downloadUrl);
                throw new BusinessException(ResultEnum.FAILURE.getCode(),"获取上传文件地址出错！");
            }
        }else {
            logger.info("查询销售单数据出错:{}",orderDetailsPageDTOResult);
            throw new BusinessException(ResultEnum.FAILURE.getCode(),orderDetailsPageDTOResult.getMsg());
        }
    }

    /**
     * 异步处理批量线下开单列表导出
     * @param cond
     */
    @Async
    @Override
    public void handleExportGuestOrderList(QueryAllBatchGuestOrderCond cond,HttpServletResponse response,LoginUserDetail loginUser) {
        String sheetNameStart = "";
        //分页获取全部线下开单列表
        Result<List<BatchGuestOrderRecordDTO>> allBatchGuestOrderList = this.getAllBatchGuestOrderList(cond);
        if(allBatchGuestOrderList.isSuccess()){
            List<BatchGuestOrderRecordDTO> batchGuestOrderListData = allBatchGuestOrderList.getData();
            List<GuestOrderExportDto> guestOrderExportDtos = this.buildGuestOrderExport(batchGuestOrderListData);
            sheetNameStart = BusinessTypeEnum.EXPORT_GUEST_ORDER.getMsg();
            String downloadUrl = this.getUploadUrl(guestOrderExportDtos, response, sheetNameStart);
            logger.info("下载地址:{}",downloadUrl);
            if(StringUtil.isNotBlank(downloadUrl)){
                //开始封装保存报表生成下载历史Dto参数
                ReportHistoryDTO reportHistoryDTO = new ReportHistoryDTO();
                //订单业务类型
                reportHistoryDTO.setBusinessType((BusinessTypeEnum.EXPORT_GUEST_ORDER.getCode().byteValue()));
                reportHistoryDTO.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
                reportHistoryDTO.setDownloadUrl(downloadUrl);
                reportHistoryDTO.setFinishTime(new Date());
                if (null == cond.getCreateBeginTime()){
                    reportHistoryDTO.setBeginTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
                }else{
                    reportHistoryDTO.setBeginTime(cond.getCreateBeginTime());
                }
                if (null == cond.getCreateEndTime()){
                    reportHistoryDTO.setEndTime(new Date());
                }else{
                    reportHistoryDTO.setEndTime(cond.getCreateEndTime());
                }
                reportHistoryDTO.setCreateId(loginUser.getUserId());
                reportHistoryDTO.setCreateName(loginUser.getUserName());
                reportHistoryDTO.setCreateTime(new Date());
                reportHistoryDTO.setModifyId(loginUser.getUserId());
                reportHistoryDTO.setModifyName(loginUser.getUserName());
                reportHistoryDTO.setModifyTime(new Date());

                // 保存报表生成下载历史
                logger.info("报表生成完成，保存下载历史:{}",reportHistoryDTO);
                this.createReportHistory(reportHistoryDTO);
            }else {
                logger.info("获取上传文件地址出错:{}",downloadUrl);
            }
        }else {
            logger.info("查询全部线下开单列表出错:{}",allBatchGuestOrderList);
        }
    }

    @Override
    @Async
    public void handleCommissionOrdertExport(QueryCommissionOrderDTO req, HttpServletResponse response) {
        LoginUserDetail loginUser = req.getUser();

        Result<List<CommissionOrderListDTO>> queryResult = orderFeignService.queryCommissionOrderList(req);
        logger.info("查询佣金订单明细列表（线下结算）总数:{}", ObjectUtils.isEmpty(queryResult.getData()) ? null : queryResult.getData().size());
        if (!queryResult.isSuccess() || ObjectUtils.isEmpty(queryResult.getData())) {
            logger.info("查询佣金订单明细列表（线下结算）出错:{}", queryResult);
            return;
        }
        List<CommissionOrderListDTO> dataList = queryResult.getData();
        // 返回数据处理
        List<CommissionOrderListExportDTO> dataExportList = this.handleCommissionOrder(dataList);
        OssUtils ossUtils = new OssUtils();
        String fileName = BusinessTypeEnum.EXPORT_SETTLEMENT_OFFLINE.getMsg();
        //生成excel文件，并上传的阿里云服务器返回文件地址路径
        String downloadUrl = ossUtils.getDownloadUrl(dataExportList, CommissionOrderListExportDTO.class, fileName, ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        if (StringUtils.isBlank(downloadUrl)) {
            logger.info("获取上传文件地址出错:{}", downloadUrl);
            return;
        }
        // 保存佣金订单明细列表（线下结算）报表
        FinanceReportformEntity financeReportformEntity = new FinanceReportformEntity();
        financeReportformEntity.setType((String.valueOf(BusinessTypeEnum.EXPORT_SETTLEMENT_OFFLINE.getCode())));
        financeReportformEntity.setCreateId(loginUser.getUserId() + "");
        financeReportformEntity.setCreateName(loginUser.getUserName());
        financeReportformEntity.setCreateDate(new Date());
        financeReportformEntity.setDownUrl(downloadUrl);
        financeReportformEntity.setFileName(fileName);
        Result<Boolean> saveFinanceReportForm = tradeOrderFeignService.saveFinanceReportForm(financeReportformEntity);
        if (!saveFinanceReportForm.isSuccess() ||  !saveFinanceReportForm.getData()) {
            logger.info("保存佣金订单明细列表（线下结算）报表出错:{}", downloadUrl);
        }


    }

    /**
     * 组装查询列表参数
     * @param dataList
     * @return
     */
    private List<CommissionOrderListExportDTO> handleCommissionOrder(List<CommissionOrderListDTO> dataList) {
        List<CommissionOrderListExportDTO> dataExportList = new ArrayList<>();
        for (CommissionOrderListDTO orderListDTO : dataList) {
            CommissionOrderListExportDTO orderListExportDTO = new CommissionOrderListExportDTO();
            orderListExportDTO.setOrderNo(orderListDTO.getOrderNo());
            orderListExportDTO.setShopName(orderListDTO.getShopName());
            orderListExportDTO.setCreateTime(dateFormatName.format(orderListDTO.getCreateTime()));
            orderListExportDTO.setOrderPayAmount(null == orderListDTO.getOrderPayAmount() ? null : orderListDTO.getOrderPayAmount().stripTrailingZeros().toPlainString());
            orderListExportDTO.setBuyerCode(orderListDTO.getBuyerCode());
            orderListExportDTO.setBuyerName(orderListDTO.getBuyerName());
            orderListExportDTO.setOrderReceiptTime(null == orderListDTO.getOrderReceiptTime() ? "" : dateFormatName.format(orderListDTO.getOrderReceiptTime()));
            orderListExportDTO.setTotalCommissionAmount(null == orderListDTO.getTotalCommissionAmount() ? null : orderListDTO.getTotalCommissionAmount().stripTrailingZeros().toPlainString());
            orderListExportDTO.setTotalTechnologyAmount(null == orderListDTO.getTotalTechnologyAmount() ? null : orderListDTO.getTotalTechnologyAmount().stripTrailingZeros().toPlainString());
            orderListExportDTO.setSellerCode(orderListDTO.getSellerCode());
            orderListExportDTO.setSellerName(orderListDTO.getSellerName());
            orderListExportDTO.setAppCode(orderListDTO.getAppCode());
            orderListExportDTO.setAppName(orderListDTO.getAppName());
            orderListExportDTO.setDepositPayType(BusinessTypeEnum.DEPOSIT_PAY.getMsg());
            orderListExportDTO.setOfflineSettlementStatusDesc(BusinessTypeEnum.OFFLINE_SETTLEMENT_STATUS_0.getMsg());
            // 线下汇款
            if (BusinessTypeEnum.OFFLINE_REMIT.getCode().equals(orderListDTO.getMallPayType())) {
                orderListExportDTO.setDepositPayType(BusinessTypeEnum.OFFLINE_REMIT.getMsg());
            }
            // 线下结算
            if (BusinessTypeEnum.OFFLINE_SETTLEMENT_STATUS_1.getCode().equals(orderListDTO.getOfflineSettlementStatus())) {
                orderListExportDTO.setOfflineSettlementStatusDesc(BusinessTypeEnum.OFFLINE_SETTLEMENT_STATUS_1.getMsg());
            }
            // 能查询到的都是自营商家
            orderListExportDTO.setSellerTypeName(BusinessTypeEnum.MEMBER_TYPE_DEALER.getMsg());
            dataExportList.add(orderListExportDTO);
        }
        return dataExportList;
    }


    /**
     *  转换为要导出的excel Dto格式
     * @param batchGuestOrderListData
     * @return
     */
    public List<GuestOrderExportDto> buildGuestOrderExport(List<BatchGuestOrderRecordDTO> batchGuestOrderListData){
        Map<String,String> orderReportStatusMap = Arrays.stream(BatchGuestOrderReportStatusEnum.values())
                .collect(Collectors.toMap(BatchGuestOrderReportStatusEnum::getStatus,BatchGuestOrderReportStatusEnum::getDescription));
        List<GuestOrderExportDto> resultExcelDtos=new ArrayList<>();
        for(BatchGuestOrderRecordDTO batchGuestOrderRecordDTO:batchGuestOrderListData){
            List<BatchGuestOrderItemDTO> itemDetailList = batchGuestOrderRecordDTO.getItemDetailList();
            for(BatchGuestOrderItemDTO itemDTO:itemDetailList){
                GuestOrderExportDto guestOrderExportDto=new GuestOrderExportDto();
                guestOrderExportDto.setOrderNo(batchGuestOrderRecordDTO.getOrderNo());
                guestOrderExportDto.setBatchNo(batchGuestOrderRecordDTO.getBatchNo());
                guestOrderExportDto.setCreateTime(dateFormatName.format(batchGuestOrderRecordDTO.getCreateTime()));
                guestOrderExportDto.setBuyerCode(batchGuestOrderRecordDTO.getBuyerCode());
                guestOrderExportDto.setSkuCode(itemDTO.getSkuCode());
                guestOrderExportDto.setSupplierName(itemDTO.getSupplierName());
                guestOrderExportDto.setThirdOrderNo(itemDTO.getThirdOrderNo());
                guestOrderExportDto.setGoodCount(itemDTO.getGoodCount());
                guestOrderExportDto.setGoodPrice(itemDTO.getGoodPrice());
                guestOrderExportDto.setResultMsg(batchGuestOrderRecordDTO.getResultMsg());
                guestOrderExportDto.setWarehouseName(itemDTO.getWarehouseName());
                guestOrderExportDto.setDeleteFlag(CommonConstants.DELETE_FLAG_YES.equals(batchGuestOrderRecordDTO.getDeleteFlag()) ? CommonConstants.NOT_DELETE : CommonConstants.ALREADY_DELETE);
                guestOrderExportDto.setOrderStatus(orderReportStatusMap.get(String.valueOf(batchGuestOrderRecordDTO.getResultCode())));
                resultExcelDtos.add(guestOrderExportDto);
            }
        }
        return resultExcelDtos;
    }

    /**
     * 循环分页获取全部批量线下开单列表
     * @param cond
     * @return
     */
    public Result<List<BatchGuestOrderRecordDTO>> getAllBatchGuestOrderList(QueryAllBatchGuestOrderCond cond){
        Result<List<BatchGuestOrderRecordDTO>> result=new Result<>();
        //循环查询数据,每页200条
        int page = 1;
        int rows = 200;
        int totalPage = 1;
        Result<DataGrid<BatchGuestOrderRecordDTO>> dataGridResult = orderFeignService.queryAllBatchGuestOrderList(cond, page, rows);
            DataGrid<BatchGuestOrderRecordDTO> gridResultData = dataGridResult.getData();
            if (CollectionUtils.isEmpty(gridResultData.getRows())) {
                return CommonResultUtil.error(ResultEnum.FAILURE.getCode(),"没有可以导出的数据");
            }
            List<BatchGuestOrderRecordDTO> allGuestOrderList = new ArrayList<>(dataGridResult.getData().getRows());
            //查出的总页数
            totalPage = (int)Math.ceil((double)gridResultData.getTotal()/(double) rows);
            logger.info("totalPage:{},totalSize:{}",totalPage,gridResultData.getTotal());
            //分页总页数大于1
            if(totalPage>1){
                logger.info("页数超过1页：{}",totalPage);
                for (int i = 2; i <= totalPage; i++ ){
                    logger.info("分页查询导出批量线下开单:{}",i);
                    Result<DataGrid<BatchGuestOrderRecordDTO>> dataGridResult1 = orderFeignService.queryAllBatchGuestOrderList(cond, i, rows);
                    if(!ResultEnum.SUCCESS.getCode().equals(dataGridResult1.getCode())){
                        logger.info("分页查询导出订单列表返回code:{}",dataGridResult1.getCode());
                        continue;
                    }
                    if(!ObjectUtils.isEmpty(dataGridResult1.getData()) && CollectionUtil.isNotEmpty(dataGridResult1.getData().getRows())){
                        logger.info("分页查询导出订单列表返回,size:{}",dataGridResult1.getData().getRows().size());
                        allGuestOrderList.addAll(dataGridResult1.getData().getRows());
                    }
                }
            }
            logger.info("总条数:{}",allGuestOrderList.size());
            return CommonResultUtil.success(allGuestOrderList);
    }

    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     */
    private String  getUploadUrl(List<?> list, HttpServletResponse response, String sheetNameStart){

        String uploadUrl = "";
        try{
            String sheetName = simpleDateFormatName.format(new Date());
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", GuestOrderExportDto.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(list) ? new ArrayList<T>() : list);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType("applicationnd.ms-excel"); // 改成输出excel文件
            String fileName = sheetNameStart +"_"+ sheetName;
            response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xls");// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            OssUtils ossUtils = new OssUtils();
            String ossFileName = UUID.randomUUID().toString().replace("-", "").toLowerCase()+".xls";
            uploadUrl = ossUtils.upload(excelStream,ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        }catch (Exception e){
            logger.error("reportExportHandle error:",e);
        }
        return uploadUrl;
    }

    /**
     * 数据转换
     * @param orderDetailResDTOS
     * @return
     */
    private List<SaleShipOrderDetailExportDTO> saleShipOrderDetailDataTransform(List<OrderDetailResDTO> orderDetailResDTOS){
        List<SaleShipOrderDetailExportDTO> saleOrderDetailDTOList = new ArrayList<>();
        //订单状态集合
        Map<String,String> orderReportStatusMap = Arrays.stream(OrderReportStatusEnum.values())
                .collect(Collectors.toMap(OrderReportStatusEnum::getStatus,OrderReportStatusEnum::getDescription));
        for (OrderDetailResDTO orderDetailResDTO : orderDetailResDTOS){
            List<OrderItemsDTO> orderItemsDTOS = orderDetailResDTO.getOrderItemsDTOS();
            for (OrderItemsDTO orderItemsDTO:orderItemsDTOS){
                SaleShipOrderDetailExportDTO saleOrderDetailDTO = new SaleShipOrderDetailExportDTO();
                saleOrderDetailDTO.setOrderNo(orderItemsDTO.getOrderNo());
                saleOrderDetailDTO.setOrderItemNo(orderItemsDTO.getOrderItemNo());
                saleOrderDetailDTO.setCreateTime(dateFormatName.format(orderDetailResDTO.getCreateTime()));
                saleOrderDetailDTO.setBuyerCode(orderDetailResDTO.getBuyerCode());
                saleOrderDetailDTO.setBuyerName(orderDetailResDTO.getBuyerName());
                saleOrderDetailDTO.setSkuCode(orderItemsDTO.getSkuCode());
                saleOrderDetailDTO.setGoodsName(orderItemsDTO.getGoodsName());
                saleOrderDetailDTO.setItemAttributes(orderItemsDTO.getItemAttributes());
                saleOrderDetailDTO.setBrandName(orderItemsDTO.getBrandName());
                saleOrderDetailDTO.setGoodsPrice(orderItemsDTO.getGoodsPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                saleOrderDetailDTO.setGoodsCount(orderItemsDTO.getGoodsCount());
                saleOrderDetailDTO.setGoodsFreight(orderItemsDTO.getGoodsFreight().toPlainString());
                saleOrderDetailDTO.setOrderItemPayAmount(orderItemsDTO.getOrderItemPayAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                saleOrderDetailDTO.setRecommender(!ObjectUtils.isEmpty(orderItemsDTO.getRecommender())
                        ? orderItemsDTO.getRecommender() : orderDetailResDTO.getRecommender());
                this.orderStatusDescriptionConversion(orderReportStatusMap,orderDetailResDTO,null,saleOrderDetailDTO);

                if(ParamEnum.ORDER_TYPE_ORDINARY.getCode().equals(orderDetailResDTO.getOrderType())){
                    saleOrderDetailDTO.setOrderType(ParamEnum.ORDER_TYPE_ORDINARY.getMsg());
                }else if(ParamEnum.ORDER_TYPE_ADVANCE_SALE.getCode().equals(orderDetailResDTO.getOrderType())) {
                    saleOrderDetailDTO.setOrderType(ParamEnum.ORDER_TYPE_ADVANCE_SALE.getMsg());
                }


                if (ParamEnum.ORDER_DELIVERYTYPE_DISTRIBUTION.getCode().equals(orderDetailResDTO.getDeliveryType())){
                    saleOrderDetailDTO.setConsigneeName(orderDetailResDTO.getConsigneeName());
                    saleOrderDetailDTO.setConsigneePhoneNum(baseService.decryptSingle(orderDetailResDTO.getDsConsigneePhoneNum(),saleShipOrderReportExportSceneId));
                    saleOrderDetailDTO.setConsigneeAddress(baseService.decryptSingle(orderDetailResDTO.getDsConsigneeAddress(),saleShipOrderReportExportSceneId));
                }else{
                    Result<OrderDeliverySelfPickupDTO> pickupDTOResult = this.queryOrderDeliverySelfPickup(orderItemsDTO.getOrderNo());
                    if (pickupDTOResult.isSuccess() && null != pickupDTOResult.getData()){
                        saleOrderDetailDTO.setConsigneeName(pickupDTOResult.getData().getPickupName());
                        saleOrderDetailDTO.setConsigneePhoneNum(baseService.decryptSingle(pickupDTOResult.getData().getDsContactPhone(),saleShipOrderReportExportSceneId));
                        saleOrderDetailDTO.setConsigneeAddress(baseService.decryptSingle(pickupDTOResult.getData().getDsPickupAddress(),saleShipOrderReportExportSceneId));

                    }
                }
                saleOrderDetailDTO.setOrderRemarks(orderDetailResDTO.getOrderRemarks());

                if(!org.apache.commons.lang.StringUtils.equals(orderDetailResDTO.getInvoiceType(), InvoiceEnum.OFFLINE_INVOICE.getType())){
                    saleOrderDetailDTO.setEmailAddress(orderDetailResDTO.getEmailAddress());
                    saleOrderDetailDTO.setInvoiceReceiverName(orderDetailResDTO.getInvoiceReceiverName());
                    saleOrderDetailDTO.setInvoiceReceiverPhone(baseService.decryptSingle(orderDetailResDTO.getDsInvoiceReceiverPhone(),saleShipOrderReportExportSceneId));
                    saleOrderDetailDTO.setInvoiceAddressDetail(baseService.decryptSingle(orderDetailResDTO.getDsInvoiceAddressDetail(),saleShipOrderReportExportSceneId));

                    saleOrderDetailDTO.setTaxManId(orderDetailResDTO.getTaxManId());
                    saleOrderDetailDTO.setBankName(orderDetailResDTO.getBankName());
                    saleOrderDetailDTO.setBankAccount(baseService.decryptSingle(orderDetailResDTO.getDsBankAccount(),saleShipOrderReportExportSceneId));
                    saleOrderDetailDTO.setContactPhone(baseService.decryptSingle(orderDetailResDTO.getDsContactPhone(),saleShipOrderReportExportSceneId));
                    saleOrderDetailDTO.setInvoiceAddress(baseService.decryptSingle(orderDetailResDTO.getDsInvoiceAddress(),saleShipOrderReportExportSceneId));
                    saleOrderDetailDTO.setInvoiceNotify(orderDetailResDTO.getInvoiceNotify());
                    if(org.apache.commons.lang.StringUtils.equals(orderDetailResDTO.getInvoiceType(), InvoiceEnum.PLAIN_INVOICE.getType())){//普通发票
                        saleOrderDetailDTO.setInvoiceType(InvoiceEnum.PLAIN_INVOICE.getName());
                        saleOrderDetailDTO.setInvoiceNotify(orderDetailResDTO.getInvoiceNotify());
                    }
                    if(org.apache.commons.lang.StringUtils.equals(orderDetailResDTO.getInvoiceType(), InvoiceEnum.VALUE_ADDED_TAX_INVOICE.getType())){//增值税发票
                        saleOrderDetailDTO.setInvoiceType(InvoiceEnum.VALUE_ADDED_TAX_INVOICE.getName());
                        saleOrderDetailDTO.setInvoiceNotify(orderDetailResDTO.getInvoiceCompanyName());
                    }
                }else {
                    saleOrderDetailDTO.setInvoiceType(InvoiceEnum.OFFLINE_INVOICE.getName());
                }
                saleOrderDetailDTO.setShopName(orderDetailResDTO.getShopName());
                saleOrderDetailDTO.setPaySerialNo(orderDetailResDTO.getPaySerialNo());
                saleOrderDetailDTOList.add(saleOrderDetailDTO);
            }
        }
        return saleOrderDetailDTOList;
    }

    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     */
    private String  getShipOrderDownloadUrl(List<SaleShipOrderDetailExportDTO> saleOrderDetailDTOList, HttpServletResponse response, String sheetNameStart){
        String downloadUrl = "";
        try{
            String sheetName = simpleDateFormatName.format(new Date());
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", SaleShipOrderDetailExportDTO.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(saleOrderDetailDTOList) ?
                    new ArrayList<SaleOrderDetailDTO>() : saleOrderDetailDTOList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType("applicationnd.ms-excel"); // 改成输出excel文件
            String fileName = sheetNameStart +"_"+ sheetName;
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            OssUtils ossUtils = new OssUtils();
            String ossFileName = UUID.randomUUID().toString().replace("-", "").toLowerCase()+".xls";
            downloadUrl = ossUtils.upload(excelStream,ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        }catch (Exception e){
            logger.error("reportExportHandle error:{}",e);
        }
        return downloadUrl;
    }


    /**
     * 异步导出供货中心销售订单
     */
    @Async
    @Override
    public void exportPurchaserCenterSaleOrder(QueryPurchaserOrderListDTO reqDto, LoginUserDetail loginUserDetail) {
        reqDto.setSupplierCode(loginUserDetail.getLoginId());
        reqDto.setPage(1);
        reqDto.setRow(100);
        logger.info("首次分页查询销售订单 入参：{}", reqDto);
        PageResult<List<PurchaserOrderListVO>> orderListResult = purchaseFeignService.queryPurchaserOrderList(reqDto);
        if (!orderListResult.isSuccess() || ObjectUtils.isEmpty(orderListResult) || CollectionUtils.isEmpty(orderListResult.getData())) {
            logger.info("首次分页查询销售订单是空：{}", reqDto);
            return;
        }
        // 循环分页查询销售订单
        List<PurchaserOrderListVO> totalPurchaserOrderList = this.queryTotalPurchaserOrderList(orderListResult,reqDto);

        // 组装销售订单数据
        List<ExportSaleOrderDTO> exportSaleOrderDTOS = this.buildExportSaleOrderDTOS(totalPurchaserOrderList,loginUserDetail);
        if(CollectionUtils.isEmpty(exportSaleOrderDTOS)){
            return;
        }

        // 上传阿里云，保存文件
        OssUtils ossUtils = new OssUtils();
        String downloadUrl = ossUtils.getDownloadUrl(CommonConstants.EXPORT_SALE_ORDER_OSS_PREFIX, exportSaleOrderDTOS, ExportSaleOrderDTO.class, BusinessTypeEnum.EXPORT_PURCHASER_CENTER_SALE_ORDER.getMsg(), bucket, endpoint, accessKeyId, accessKeySecret);
        if (StringUtils.isBlank(downloadUrl)) {
            logger.info("供货中心销售订单文件上传阿里云失败");
            return;
        }
        this.saveOrderHistory(downloadUrl, BusinessTypeEnum.EXPORT_PURCHASER_CENTER_SALE_ORDER.getCode(), loginUserDetail);
    }


    /**
     * 循环分页查询销售订单
     */
    private List<PurchaserOrderListVO> queryTotalPurchaserOrderList(PageResult<List<PurchaserOrderListVO>> orderListResult,QueryPurchaserOrderListDTO reqDto){
        logger.info("循环分页查询销售订单:{}",JSON.toJSONString(orderListResult));
        List<PurchaserOrderListVO> totalPurchaserOrderList = orderListResult.getData();
        int totalPage = (int) Math.ceil((double) orderListResult.getPage().getTotal() / (double) reqDto.getRow());
        if (totalPage > 1) {
            for (int i = 2; i <= totalPage; i++) {
                reqDto.setPage(i);
                logger.info("循环分页查询销售订单 入参:{}", reqDto);
                PageResult<List<PurchaserOrderListVO>> orderListTempResult = purchaseFeignService.queryPurchaserOrderList(reqDto);
                if (!orderListTempResult.isSuccess() || ObjectUtils.isEmpty(orderListTempResult) || CollectionUtils.isEmpty(orderListTempResult.getData())) {
                    logger.info("循环分页查询销售订单 是空：{}", reqDto);
                    continue;
                }
                totalPurchaserOrderList.addAll(orderListTempResult.getData());
            }
        }
        return totalPurchaserOrderList;
    }

    /**
     * 组装销售订单数据
     */
    private List<ExportSaleOrderDTO> buildExportSaleOrderDTOS(List<PurchaserOrderListVO> purchaserOrderListVOS, LoginUserDetail loginUserDetail) {
        logger.info("组装销售订单开始 出参：{}", JSON.toJSONString(purchaserOrderListVOS));
        List<ExportSaleOrderDTO> exportSaleOrderDTOS = new ArrayList<>();
        for (PurchaserOrderListVO purchaserOrderVO : purchaserOrderListVOS) {

            ExportSaleOrderDTO tempSaleOrder = new ExportSaleOrderDTO();
            tempSaleOrder.setPurchaserOrderNumber(purchaserOrderVO.getPurchaserOrderNumber());
            tempSaleOrder.setContractCode(purchaserOrderVO.getContractCode());
            tempSaleOrder.setBuyerName(purchaserOrderVO.getBuyerName());
            if (PurchaseOrderTypeEnum.DIRECT.getType().equals(purchaserOrderVO.getOrderType())) {
                tempSaleOrder.setOrderType(PurchaseOrderTypeEnum.DIRECT.getName());
            } else if (PurchaseOrderTypeEnum.STOCK_UP.getType().equals(purchaserOrderVO.getOrderType())) {
                tempSaleOrder.setOrderType(PurchaseOrderTypeEnum.STOCK_UP.getName());
            }
            if (StringUtils.isNotBlank(purchaserOrderVO.getPurchaserOrderStatus())) {
                tempSaleOrder.setPurchaserOrderStatus(PurchaseOrderStatueEnum.getNameByStatus(purchaserOrderVO.getPurchaserOrderStatus()));
            }
            tempSaleOrder.setCreatePurchaserOrderTime(purchaserOrderVO.getCreatePurchaserOrderTime());

            // 订单号对应的商品信息 key:商品名称
            Map<String,PurchaserOrderGoodsListDTO> orderGoodsMap = new HashMap<>();
            for(PurchaserOrderGoodsListDTO orderGoodsDTO : purchaserOrderVO.getGoodsList()){
                if(ObjectUtils.isEmpty(orderGoodsMap.get(orderGoodsDTO.getGoodsName()))){
                    orderGoodsMap.put(orderGoodsDTO.getGoodsName(),orderGoodsDTO);
                }
            }
            try {
                // 查询待发货配送记录
                int waitDelivery = 0;
                PurchaseSaleWaitDeliveryDTO waitDeliveryDTO = new PurchaseSaleWaitDeliveryDTO();
                waitDeliveryDTO.setPage(1);
                waitDeliveryDTO.setRows(200);
                waitDeliveryDTO.setPurchaseOrderNo(purchaserOrderVO.getPurchaserOrderNumber());
                waitDeliveryDTO.setPurchaseOrderUniqueNo(purchaserOrderVO.getPurchaseOrderUniqueNo());
                waitDeliveryDTO.setSupplierCode(loginUserDetail.getLoginId());
                logger.info("查询单笔销售订单待发货记录列表 入参：{}", waitDeliveryDTO);
                PageResult<List<PurchaseSaleWaitDeliveryVO>>  waitDeliveryResult = purchaseFeignService.selectPurchaseSaleWaitDeliveryList(waitDeliveryDTO);
                // 没有配送记录，按照 订单+商品 维度导出
                if (waitDeliveryResult.isSuccess() && !ObjectUtils.isEmpty(waitDeliveryResult) && CollectionUtil.isNotEmpty(waitDeliveryResult.getData())) {
                    waitDelivery = waitDeliveryResult.getData().get(0).getPurchaseSaleWaitDeliveryItemList().size();
                    for (PurchaseSaleWaitDeliveryItemVO waitDeliveryItemVO : waitDeliveryResult.getData().get(0).getPurchaseSaleWaitDeliveryItemList()) {
                        ExportSaleOrderDTO exportSaleOrderDTO = new ExportSaleOrderDTO();
                        BeanUtils.copyProperties(tempSaleOrder, exportSaleOrderDTO);
                        if(PurchaseOrderTypeEnum.STOCK_UP.getType().equals(purchaserOrderVO.getOrderType())){
                            exportSaleOrderDTO.setRelationNo(waitDeliveryItemVO.getSubOrderNo());
                        }else {
                            exportSaleOrderDTO.setRelationNo(waitDeliveryItemVO.getEntrustedOrderNo());
                        }
                        exportSaleOrderDTO.setSkuCode(waitDeliveryItemVO.getSkuCode());
                        exportSaleOrderDTO.setGoodsName(waitDeliveryItemVO.getSkuName());
                        exportSaleOrderDTO.setGoodsNumber(waitDeliveryItemVO.getSaleItemWaitDeliveryNumber().setScale(2, RoundingMode.HALF_UP));
                        exportSaleOrderDTO.setUnitPrice(orderGoodsMap.get(waitDeliveryItemVO.getSkuName()).getUnitPrice().setScale(2, RoundingMode.HALF_UP));


                        if (StringUtils.isNotBlank(waitDeliveryItemVO.getReceiverOrPickUpDetail())) {
                            this.buildOrderGoodsDelivery(waitDeliveryItemVO.getReceiverOrPickUpDetail(), exportSaleOrderDTO);
                        }
                        exportSaleOrderDTOS.add(exportSaleOrderDTO);
                    }
                }


                // 查询已发货配送记录
                PurchaseOrderDeliveryRecordDTO deliveryRecordDTO = new PurchaseOrderDeliveryRecordDTO();
                deliveryRecordDTO.setPage(1);
                deliveryRecordDTO.setRows(200);
                deliveryRecordDTO.setPurchaseOrderUniqueNo(purchaserOrderVO.getPurchaseOrderUniqueNo());
                deliveryRecordDTO.setSupplierCode(loginUserDetail.getLoginId());
                logger.info("查询单笔销售订单已发货记录列表 入参：{}", deliveryRecordDTO);
                PageResult<List<PurchaseOrderDeliveryRecordVO>> deliveryRecordResult = purchaseFeignService.selectPurchaseOrderDeliveryRecordPage(deliveryRecordDTO);
                // 没有配送记录，按照 订单+商品 维度导出
                if (!deliveryRecordResult.isSuccess() || ObjectUtils.isEmpty(deliveryRecordResult)
                        || CollectionUtils.isEmpty(deliveryRecordResult.getData())) {
                    if (waitDelivery == 0) {
                        List<ExportSaleOrderDTO> singleOrderGoodsList = this.buildNoDeliveryOrderList(purchaserOrderVO.getGoodsList(), tempSaleOrder);
                        exportSaleOrderDTOS.addAll(singleOrderGoodsList);
                        continue;
                    }
                }

                // 有配送记录，按照 委托单号 维度导出
                logger.info("查询单笔销售订单已发货记录列表 出参：{}", JSON.toJSONString(deliveryRecordResult.getData()));
                for (PurchaseOrderDeliveryRecordVO deliveryRecordVO : deliveryRecordResult.getData()) {
                    if (StringUtils.isNotBlank(deliveryRecordVO.getSkuInfo())) {
                        String[] skuInfoList = deliveryRecordVO.getSkuInfo().split("[;]");
                        for (String skuInfo : skuInfoList) {
                            ExportSaleOrderDTO exportSaleOrderDTO = new ExportSaleOrderDTO();
                            BeanUtils.copyProperties(tempSaleOrder, exportSaleOrderDTO);
                            if(PurchaseOrderTypeEnum.STOCK_UP.getType().equals(purchaserOrderVO.getOrderType())){
                                exportSaleOrderDTO.setRelationNo(deliveryRecordVO.getDeliveryOrderNo());
                            }else {
                                exportSaleOrderDTO.setRelationNo(deliveryRecordVO.getEntrustedOrderNo());
                            }

                            String[] skuParts = skuInfo.split("[，,]");
                            if (skuParts.length == 3) {
                                exportSaleOrderDTO.setSkuCode(orderGoodsMap.get(skuParts[0]).getSkuCode());
                                exportSaleOrderDTO.setGoodsName(skuParts[0]);
                                exportSaleOrderDTO.setGoodsNumber(new BigDecimal(skuParts[2]).setScale(2, RoundingMode.HALF_UP));
                                exportSaleOrderDTO.setUnitPrice(orderGoodsMap.get(skuParts[0]).getUnitPrice().setScale(2, RoundingMode.HALF_UP));
                            }
                            if (StringUtils.isNotBlank(deliveryRecordVO.getDeliveryInfo())) {
                                this.buildOrderGoodsDelivery(deliveryRecordVO.getDeliveryInfo(), exportSaleOrderDTO);
                            }
                            exportSaleOrderDTOS.add(exportSaleOrderDTO);
                        }
                    }
                }
            }catch (BusinessException be){
                logger.info("组装销售订单数据业务异常:{}",be.getMessage());
                continue;
            }catch (Exception e){
                logger.info("组装销售订单数据异常",e);
                continue;
            }
        }
        logger.info("组装销售订单开始 结束：{}", exportSaleOrderDTOS);
        return exportSaleOrderDTOS;
    }


    /**
     * 组装订单配送信息
     */
    private void  buildOrderGoodsDelivery(String deliveryInfo,ExportSaleOrderDTO exportSaleOrderDTO){
        logger.info("组装订单单行配送信息 开始：{}：{}",deliveryInfo,exportSaleOrderDTO);
        String[] deliveryParts = deliveryInfo.split(" ", 3);
        String deliveryFirstStr = deliveryParts[0];
        String deliverySecondStr = deliveryParts[1];
        if(deliveryParts.length == 2){
            if(Character.isDigit(deliveryFirstStr.charAt(0))){
                exportSaleOrderDTO.setConsigneePhoneNum(deliveryFirstStr);
                if(deliverySecondStr.substring(1, deliverySecondStr.length() - 1).contains("*")){
                    exportSaleOrderDTO.setDeliveryAddress(deliverySecondStr);
                }else {
                    exportSaleOrderDTO.setConsigneeName(deliverySecondStr);
                }
            }else if(Character.isDigit(deliverySecondStr.charAt(0))){
                exportSaleOrderDTO.setConsigneePhoneNum(deliverySecondStr);
                if(deliveryFirstStr.substring(1, deliveryFirstStr.length() - 1).contains("*")){
                    exportSaleOrderDTO.setDeliveryAddress(deliveryFirstStr);
                }else {
                    exportSaleOrderDTO.setConsigneeName(deliveryFirstStr);
                }
            }
        }else if(deliveryParts.length == 3){
            exportSaleOrderDTO.setConsigneeName(deliveryFirstStr);
            exportSaleOrderDTO.setConsigneePhoneNum(deliverySecondStr);
            exportSaleOrderDTO.setDeliveryAddress(deliveryParts[2]);
        }
        logger.info("组装订单单行配送信息 结束：{}",exportSaleOrderDTO);
    }

    /**
     * 组装订单配送信息
     */
    private Map<String,BigDecimal> buildOrderDeliveryInfo(List<PurchaseOrderDeliveryRecordVO> orderDeliveryRecordVOS){
        logger.info("组装销售订单商品的配送信息 开始：{}",orderDeliveryRecordVOS);
        // key:收货人信息_商品名称 value:商品数量
        Map<String,BigDecimal> deliveryMap = new HashMap<>();
        for(PurchaseOrderDeliveryRecordVO deliveryRecordVO : orderDeliveryRecordVOS){
            if(StringUtils.isBlank(deliveryRecordVO.getDeliveryInfo())){
                logger.info("无配送信息：{}",deliveryRecordVO);
                continue;
            }
            String[] deliveryParts = deliveryRecordVO.getDeliveryInfo().split(" ", 3);
            String key = deliveryParts[0];
            if(deliveryParts.length == 2){
                key = key + "_" + deliveryParts[1];
            }
            if(deliveryParts.length == 3){
                key = key + "_" + deliveryParts[1] + "_" + deliveryParts[2];
            }
            if (StringUtils.isBlank(deliveryRecordVO.getSkuInfo())) {
                continue;
            }
            String[] skuItems = deliveryRecordVO.getSkuInfo().split(";");
            for(String skuInfo : skuItems){

                String[] parts = skuInfo.split(",");
                if (parts.length != 3) {
                    logger.info("商品信息不全：{}",skuInfo);
                    continue;
                }
                BigDecimal goodNumber = new BigDecimal(parts[2]);
                if (!ObjectUtils.isEmpty(deliveryMap.get(key + "_" + parts[0]))) {
                    goodNumber = goodNumber.add(deliveryMap.get(key + "_" + parts[0]));
                }
                deliveryMap.put(key + "_" + parts[0], goodNumber);
            }
        }
        logger.info("组装销售订单商品的配送信息 结束：{}",deliveryMap);
        return deliveryMap;
    }

    /**
     * 组装无配送信息的订单
     */
    private List<ExportSaleOrderDTO> buildNoDeliveryOrderList(List<PurchaserOrderGoodsListDTO> goodsListDTOS, ExportSaleOrderDTO tempSaleOrder){
        List<ExportSaleOrderDTO> exportSaleOrderDTOS = new ArrayList<>();
        for(PurchaserOrderGoodsListDTO orderGoodsDTO : goodsListDTOS){
            ExportSaleOrderDTO exportSaleOrderDTO = new  ExportSaleOrderDTO();
            BeanUtils.copyProperties(tempSaleOrder,exportSaleOrderDTO);
            exportSaleOrderDTO.setGoodsName(orderGoodsDTO.getGoodsName());
            exportSaleOrderDTO.setUnitPrice(orderGoodsDTO.getUnitPrice().setScale(2,RoundingMode.HALF_UP));
            exportSaleOrderDTO.setGoodsNumber(orderGoodsDTO.getGoodsNumber().setScale(2,RoundingMode.HALF_UP));
            exportSaleOrderDTO.setSkuCode(orderGoodsDTO.getSkuCode());
            exportSaleOrderDTOS.add(exportSaleOrderDTO);
        }
        return exportSaleOrderDTOS;
    }



    /**
     * 保存报表数据
     */
    private void saveOrderHistory(String downloadUrl,Integer businessType,LoginUserDetail loginUserDetail){
        logger.info("保存订单相关报表数据：{}：{}：{}",downloadUrl,businessType,loginUserDetail);
        ReportHistoryDTO reportHistoryDTO = new ReportHistoryDTO();
        reportHistoryDTO.setBusinessType(businessType.byteValue());
        reportHistoryDTO.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
        reportHistoryDTO.setDownloadUrl(downloadUrl);
        reportHistoryDTO.setFinishTime(new Date());
        reportHistoryDTO.setBeginTime(new Date());
        reportHistoryDTO.setEndTime(new Date());
        reportHistoryDTO.setCreateId(loginUserDetail.getUserId());
        reportHistoryDTO.setCreateName(loginUserDetail.getUserName());
        reportHistoryDTO.setCreateTime(new Date());
        reportHistoryDTO.setModifyId(loginUserDetail.getUserId());
        reportHistoryDTO.setModifyName(loginUserDetail.getUserName());
        reportHistoryDTO.setModifyTime(new Date());
        // 保存报表生成下载历史
        orderFeignService.createReportHistory(reportHistoryDTO);
    }
}
