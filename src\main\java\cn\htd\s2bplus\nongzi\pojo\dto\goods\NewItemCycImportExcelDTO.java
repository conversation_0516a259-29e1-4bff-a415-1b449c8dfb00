package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2024/09/06 17:08
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class NewItemCycImportExcelDTO implements Serializable {

    private static final long serialVersionUID = -4934948066808570176L;



    @ApiModelProperty(value = "商品库存编码")
    @Excel(name = "商品库存编码（必填）", width = 20,orderNum  = "1")
    private String outerSkuId;

    @ApiModelProperty(value = "国条码")
    @Excel(name = "国条码", width = 20,orderNum  = "2")
    private String eanCode;

    @ApiModelProperty(value = "品牌（必填）")
    @Excel(name = "品牌（必填）", width = 20,orderNum  = "3")
    private String brand;

    @ApiModelProperty(value = "型号（必填）")
    @Excel(name = "型号（必填", width = 20,orderNum  = "4")
    private String modelType;

    @ApiModelProperty(value = "单位（必填）")
    @Excel(name = "单位（必填）", width = 20,orderNum  = "5")
    private String unitName;

    @ApiModelProperty(value = "储藏温区 1:普通储存 2：冷藏储存 3：冷冻储存")
    @Excel(name = "储藏温区", width = 20,orderNum  = "6")
    private String storageTemperature;

    @ApiModelProperty(value = "产地")
    @Excel(name = "产地", width = 20,orderNum  = "7")
    private String origin;

    @ApiModelProperty(value = "毛重")
    @Excel(name = "毛重", width = 20,orderNum  = "8")
    private String weight;

    @ApiModelProperty(value = "净重")
    @Excel(name = "净重", width = 20,orderNum  = "9")
    private String netWeight;

    @ApiModelProperty(value = "尺寸")
    @Excel(name = "尺寸（长*宽*高 纯数字）", width = 20,orderNum  = "10")
    private String lengths;

    @ApiModelProperty(value = "规格名称（必填）")
    @Excel(name = "规格名称（必填）", width = 20,orderNum  = "11")
    private String attrName;

    @ApiModelProperty(value = "规格值（必填）")
    @Excel(name = "规格值（必填）", width = 20,orderNum  = "12")
    private String attrValueName;

    @ApiModelProperty(value = "商品标题（必填）")
    @Excel(name = "商品标题（必填）", width = 20,orderNum  = "13")
    private String subTitle;

    @ApiModelProperty(value = "商品图片（非必填）")
    @Excel(name = "商品图片（非必填）", width = 20,orderNum  = "14")
    private String pictureUrl;

    @ApiModelProperty(value = "商品详情（必填）")
    @Excel(name = "商品详情（必填）", width = 20,orderNum  = "15")
    private String describeContent;

    @ApiModelProperty(value = "商品卖点")
    @Excel(name = "商品卖点", width = 20,orderNum  = "16")
    private String ad;

    @ApiModelProperty(value = "采购人")
    @Excel(name = "采购人", width = 20,orderNum  = "17")
    private String purchaseMan;

    @ApiModelProperty(value = "采购模式")
    @Excel(name = "采购模式", width = 20,orderNum  = "18")
    private String purchaseMode;

    @ApiModelProperty(value = "供应商名称")
    @Excel(name = "供应商名称", width = 20,orderNum  = "19")
    private String supplierName;

    @ApiModelProperty(value = "供应商编码")
    @Excel(name = "供应商编码", width = 20,orderNum  = "20")
    private String supplierCode;

    @ApiModelProperty(value = "仓库")
    @Excel(name = "仓库", width = 20,orderNum  = "21")
    private String warehouse;

    @ApiModelProperty(value = "配送方式 配送方式 1:普通配送 2：冷藏配送 3：冷冻配送")
    @Excel(name = "配送方式", width = 20,orderNum  = "22")
    private String deliveryType;

    @ApiModelProperty(value = "起订量")
    @Excel(name = "起订量", width = 20,orderNum  = "23")
    private String orderQuantity;

    @ApiModelProperty(value = "运费模板（必填）")
    @Excel(name = "运费模板（必填）", width = 20,orderNum  = "24")
    private String shopFreightTemplate;

    @ApiModelProperty(value = "售后服务")
    @Excel(name = "售后服务", width = 20,orderNum  = "25")
    private String afterService;

    @ApiModelProperty(value = "包装清单")
    @Excel(name = "包装清单", width = 20,orderNum  = "26")
    private String packingList;

    @ApiModelProperty(value = "成本价")
    @Excel(name = "成本价", width = 20,orderNum  = "27")
    private String costPrice;

    @ApiModelProperty(value = "基础价格（必填）")
    @Excel(name = "基础价格（必填）", width = 20,orderNum  = "28")
    private String price;

    @ApiModelProperty(value = "竞品名称")
    @Excel(name = "竞品名称", width = 20,orderNum  = "29")
    private String competitorName;

    @ApiModelProperty(value = "竞品价格")
    @Excel(name = "竞品价格", width = 20,orderNum  = "30")
    private String competitorPrice;

}
