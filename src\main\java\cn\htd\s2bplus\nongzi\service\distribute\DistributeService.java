
package cn.htd.s2bplus.nongzi.service.distribute;


import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.CommissionRecordReqDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.DistributorCommissionReqDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.dto.user.PlatformUserQueryDTO;

import javax.servlet.http.HttpServletResponse;

public interface DistributeService {

    void exportDistributorCommission(DistributorCommissionReqDTO requestDTO, HttpServletResponse response);

    void exportCommissionRecord(CommissionRecordReqDTO requestDTO, HttpServletResponse response);

    Result<String> exportUserList(PlatformUserQueryDTO requestDTO, LoginUserDetail loginUser, HttpServletResponse response);
    Result<String> exportUserListSync(PlatformUserQueryDTO requestDTO, LoginUserDetail loginUser, HttpServletResponse response);
}
