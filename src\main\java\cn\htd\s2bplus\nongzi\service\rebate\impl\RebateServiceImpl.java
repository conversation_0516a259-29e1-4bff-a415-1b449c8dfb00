package cn.htd.s2bplus.nongzi.service.rebate.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.rdc.base.development.framework.core.util.StringUtil;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.enums.ResultEnum;
import cn.htd.s2bplus.nongzi.feign.user.UserService;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportCouponRuleDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.VirtualInventoryExcelDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.Constant;
import cn.htd.s2bplus.nongzi.pojo.dto.rebate.RebateCashConfigDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.rebate.RebateLimitQuotaVO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.MemberBaseInfoDTO;
import cn.htd.s2bplus.nongzi.service.rebate.RebateService;
import cn.htd.s2bplus.nongzi.utils.FileCheckUtil;
import cn.htd.s2bplus.nongzi.utils.XssFilter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title RebateServiceImpl
 * @Date: 2024/5/11 15:27
 */
@Slf4j
@Service
public class RebateServiceImpl implements RebateService {
    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;

    @Autowired
    private UserService userService;
    /**
     * 导入返利兑付配置
     *
     * @param file
     * @return
     */
    @Override
    public Result<Boolean> importRebateCashConfig(MultipartFile file) {
        try {
            int importMaxCount = nongZiNacosConfig.getImportCashConfigCount();
            // 校验文件
            FileCheckUtil.checkFile(file);
            List<RebateCashConfigDTO> list = new ArrayList<>();
            // 查询额度配置限制
            Integer limitQuota = this.getLimitQuota();

            StringBuilder errorMsg = new StringBuilder();
            //将excel解析成list
            EasyExcel.read(file.getInputStream(), RebateCashConfigDTO.class, new ReadListener<RebateCashConfigDTO>() {
                public static final int BATCH_COUNT = 100;
                private final List<RebateCashConfigDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

                @Override
                public void invoke(RebateCashConfigDTO importCashConfigDTO, AnalysisContext context) {
                    int rowIndex = context.readSheetHolder().getRowIndex() + 1;
                    int rowNumber = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
                    if (rowNumber == 0) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
                    }
                    if (rowNumber > importMaxCount) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_MAX_ERROR.getMsg() + importMaxCount + "条");
                    }
                    if(!StringUtil.isBlank(importCashConfigDTO.getMemberCode())) {
                        importCashConfigDTO.setMemberName(this.checkMemberCode(importCashConfigDTO.getMemberCode(),rowIndex, Constant.MEMBER_TYPE));
                    } else {
                        errorMsg.append("第").append(rowIndex).append("行").append(ApiResultEnum.MEMBER_CODE_ERROR.getMsg()).append(",");
                    }

                    if(!StringUtil.isBlank(importCashConfigDTO.getSellerCode())) {
                        importCashConfigDTO.setSellerName(this.checkMemberCode(importCashConfigDTO.getSellerCode(),rowIndex,Constant.SELLER_TYPE));
                    } else {
                        errorMsg.append("第").append(rowIndex).append("行").append(ApiResultEnum.SELLER_CODE_ERROR.getMsg()).append(",");
                    }

                    if (!CollectionUtils.isEmpty(cachedDataList)) {
                        for (int i=0;i<=cachedDataList.size()-1;i++) {
                            if (importCashConfigDTO.getMemberCode().equals(cachedDataList.get(i).getMemberCode()) && importCashConfigDTO.getSellerCode().equals(cachedDataList.get(i).getSellerCode())) {
                                errorMsg.append("第")
                                        .append(rowIndex)
                                        .append("行与第")
                                        .append(i + 2)
                                        .append("行的客户商家信息相同")
                                        .append(",");
                            }
                        }
                    }

                    if (!ObjectUtils.isEmpty(importCashConfigDTO.getQuota()) && importCashConfigDTO.getQuota() <= limitQuota) {
                        errorMsg.append("第").append(rowIndex).append("行").append(ApiResultEnum.QUOTA_ERROR.getMsg()).append(",");
                    } else if (ObjectUtils.isEmpty(importCashConfigDTO.getQuota()) || importCashConfigDTO.getQuota() < 0) {
                        errorMsg.append("第").append(rowIndex).append("行").append(ApiResultEnum.QUOTA_IS_NULL.getMsg()).append(",");
                    } else if (!ObjectUtils.isEmpty(importCashConfigDTO.getQuota()) && importCashConfigDTO.getQuota() > 100) {
                        errorMsg.append("第").append(rowIndex).append("行").append("配置额度不允许超过100").append(",");
                    }
                    cachedDataList.add(importCashConfigDTO);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (!CollectionUtils.isEmpty(cachedDataList)) {
                        list.addAll(cachedDataList);
                    }
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) throws Exception {
                    log.info("解析超额返利配置失败 :{}", exception.getMessage());
                    if (exception instanceof BusinessException) {
                        throw new BusinessException(ResultEnum.ERROR.getCode(), exception.getMessage());
                    } else if (exception instanceof ExcelDataConvertException) {
                        ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                        String errorMsg = ApiResultEnum.IMPORT_ERROR_LINE_COLUMN.getMsg() + "解析异常";
                        Integer rowIndex = excelDataConvertException.getRowIndex() + 1;
                        Integer columnIndex = excelDataConvertException.getColumnIndex() + 1;
                        log.info("解析超额返利配置,第{}行，第{}列解析异常，数据为:{}", rowIndex, columnIndex, JSON.toJSONString(excelDataConvertException.getCellData()));
                        throw new BusinessException(ResultEnum.ERROR.getCode(), String.format(errorMsg, rowIndex, columnIndex));
                    }
                }

                /**
                 * 校验会员编码
                 *
                 * @param memberCode
                 */
                private String checkMemberCode(String memberCode,int rowIndex,String type) {
                    log.info("查询会员基本信息入参:{}",memberCode);
                    Result<MemberBaseInfoDTO> result = userService.memberBaseInfo(memberCode);
                    log.info("查询会员基本信息出参:{}",result);
                    if (!result.isSuccess() || ObjectUtils.isEmpty(result.getData()) || StringUtils.isEmpty(result.getData().getCompanyName())) {
                        String msg = Constant.MEMBER_TYPE.equals(type) ? "会员编码" : "商家编码";
                        throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + msg + "输入错误");
                    }
                    return result.getData().getCompanyName();
                }
            }).sheet().doRead();
            if (errorMsg.length() > 0) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), errorMsg.toString());
            }
            log.info("解析超额返利配置出来的数据量：{}", list.size());
            if (list.size() == 0) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            // 新增返利额度配置
            log.info("新增返利额度配置入参:{}",list);
            Result<Boolean> result = userService.batchOSSRebateQuotaInfo(list);
            log.info("新增返利额度配置出参:{}",result);
            if (!result.isSuccess()) {
                throw new BusinessException(ResultEnum.ERROR.getCode(), "新增返利额度配置失败");
            }
            return ResultUtil.success(Boolean.TRUE);
        } catch (BusinessException e) {
            log.info("导入超额返利配置失败:{}", e.getMessage());
            return ResultUtil.error(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("导入超额返利配置异常:", e);
            return ResultUtil.error(ApiResultEnum.ERROR.getCode(),"导入超额返利配置异常");
        }
    }

    /**
     * 查询所有返利渠道限额
     *
     * @return
     */
    private Integer getLimitQuota() {
        Integer resultData = CommonConstants.REBATE_DEFAULT_QUOTA;
        Result<List<RebateLimitQuotaVO>> listResult = userService.queryRebateChannelLimitQuota();
        log.info("查询所有返利渠道限额出参：{}", listResult);
        if (!listResult.isSuccess()) {
            throw new BusinessException(ResultEnum.ERROR.getCode(), "查询所有返利渠道限额失败");
        }
        List<RebateLimitQuotaVO> limitQuotaList = listResult.getData();
        if (CollectionUtils.isEmpty(limitQuotaList)) {
            return resultData;
        }
        for (RebateLimitQuotaVO quotaVO : limitQuotaList) {
            if (CommonConstants.YUN_CHANG_AUTO_BUY.equals(quotaVO.getChannelType())) {
                if (!StringUtils.isEmpty(quotaVO.getQuota())) {
                    resultData = Integer.valueOf(quotaVO.getQuota());
                }
                break;
            }
        }
        return resultData;
    }


}
