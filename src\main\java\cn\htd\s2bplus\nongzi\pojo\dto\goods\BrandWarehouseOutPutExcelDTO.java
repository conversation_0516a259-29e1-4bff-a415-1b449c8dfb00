package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2024/09/06 17:08
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class BrandWarehouseOutPutExcelDTO implements Serializable {

    private static final long serialVersionUID = -4934948066808570176L;


    @ApiModelProperty(value = "商品库存编码")
    @Excel(name = "商品库存编码（必填）", width = 20,orderNum  = "1")
    private String stockCode;

    @ApiModelProperty(value = "仓库编码")
    @Excel(name = "仓库编码（必填）", width = 20,orderNum  = "2",format = "@")
    private String warehouseCode;

    @ApiModelProperty(value = "变动类型（增加/减少）")
    @Excel(name = "变动类型 增加/减少（必填）", width = 20,orderNum  = "3")
    private String businessType;

    @ApiModelProperty(value = "变动数量")
    @Excel(name = "变动数量（必填）", width = 20,orderNum  = "4",format = "@")
    private String useNum;

}
