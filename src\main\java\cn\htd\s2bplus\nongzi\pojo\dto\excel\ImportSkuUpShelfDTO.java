package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
@ExcelTarget("ImportSkuUpShelfDTO")
public class ImportSkuUpShelfDTO implements Serializable {


    private static final long serialVersionUID = 6266817088396731758L;

    @ExcelProperty("SKU编码")
    private String skuCode;

    @ExcelProperty("店铺编码")
    private Long shopId;

    @ExcelProperty("上下架（1-上架；0-下架）")
    private Integer isVisable;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}