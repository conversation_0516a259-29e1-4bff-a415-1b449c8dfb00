FROM test-dockimgws.htd.cn/b2b_p/centos6v5:latest

EXPOSE 8080


WORKDIR /home/<USER>

RUN rm -rf  tomcat7 tomcat8

ADD ./target/s2bplus-nongzi-api.jar ./

ENV nacos_config_server_addr=************:8848 \
    nacos_discovery_server_addr=************:8848 \
    nacos_config_namespace=4bffc39a-1e4d-44c0-9a57-55f32cf170f3

ENV JAVA_OPTS="-Xms2G -Xmx2G -XX:PermSize=256M -XX:MaxPermSize=512M  -Dfile.encoding=UTF8 -Dsun.jnu.encoding=UTF8  -Dapp.id=s2bplus-nongzi-api"

ENTRYPOINT java ${JAVA_OPTS}   -Dmaven.test.skip=true  -jar  s2bplus-nongzi-api.jar
