package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SettlementOrderDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "id",hidden = true)
    private Long settleId;

    @ApiModelProperty(value = "结算单编号",position = 1)
    private String settlementNo;

    @ApiModelProperty(value = "订单来源",position = 2,hidden = true)
    private String orderFrom;
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "店铺id",position = 3,hidden = true)
    private Long shopId;

    @ApiModelProperty(value = "店铺名称",position = 4)
    private String shopName;

    @ApiModelProperty(value = "卖家编号",position = 5,hidden = true)
    private String sellerId;

    @ApiModelProperty(value = "卖家名称",position = 6,hidden = true)
    private String sellerName;

    @ApiModelProperty(value = "账单日",position = 7,hidden = true)
    private Date periodTime;

    @ApiModelProperty(value = "结算金额",position = 8,hidden = true)
    private BigDecimal settleAmount;

    @ApiModelProperty(value = "结算单完成时间",position = 9,hidden = true)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date settleCompleteTime;

    @ApiModelProperty(value = "结算单生成时间",position = 10,hidden = true)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date settleCreateTime;

    @ApiModelProperty(value = "结算单状态10待财务确认11待商家提款,12商家提款处理中,13结算完成14结算单失败",position = 11)
    private String settleStatus;

    @ApiModelProperty(value = "订单数",position = 12,hidden = true)
    private Integer settleOrderNum;

    @ApiModelProperty(value = "删除状态",position = 13,hidden = true)
    private String deleteFlag;

    @ApiModelProperty(value = "创建人ID",position = 14,hidden = true)
    private String createId;

    @ApiModelProperty(value = "创建人",position = 15,hidden = true)
    private String createName;

    @ApiModelProperty(value = "创建时间",position = 16,hidden = true)
    private Date createTime;

    @ApiModelProperty(value = "修改人ID",position = 15,hidden = true)
    private String modifyId;

    @ApiModelProperty(value = "修改人",position = 16,hidden = true)
    private String modifyName;

    @ApiModelProperty(value = "修改时间",position = 17,hidden = true)
    private Date modifyTime;

    @ApiModelProperty(value = "删除人id",position = 18,hidden = true)
    private String deleteId;

    @ApiModelProperty(value = "删除人姓名",position = 19,hidden = true)
    private String deleteName;

    @ApiModelProperty(value = "删除时间",position = 20,hidden = true)
    private Date deleteTime;

    @ApiModelProperty(hidden = true)
    private List<SettlementOrderItemDTO> settlementOrderItemDTOList;

    @ApiModelProperty(value = "分页查询条数")
    @NotEmpty(message = "分页查询条数不能为空")
    private int pagesize;
    @ApiModelProperty(value = "分页查询页数")
    @NotEmpty(message = "分页查询页数不能为空")
    private int current;

    @NotEmpty(message = "总页数")
    private int pageTotal;

    @ApiModelProperty("结算单完成开始时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date settleCompleteTimeStart;
    @ApiModelProperty("结算单完成结束时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date settleCompleteTimeEnd;

    @ApiModelProperty("结算单生成开始时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date settleCreateTimeStart;
    @ApiModelProperty("结算单生成结束时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date settleCreateTimeEnd;

    @ApiModelProperty(value = "结算单类型 0:云场；1：OSS",hidden = true)
    private String type;

    @ApiModelProperty(value = "佣金总额")
    private BigDecimal statementAmount;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "结算方式 1自动结算，2手动结算")
    private String settlementType;

    @ApiModelProperty(value = "渠道编码集合")
    private List<String> channelCodeList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}

