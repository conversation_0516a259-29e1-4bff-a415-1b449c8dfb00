package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Auther: shixiao
 * @Date: 2024/09/03/10:54
 */
@Data
public class RaisePriceDTO implements Serializable {

    @ApiModelProperty(value = "SKU编码", example = "SKU12345")
    @NotBlank(message = "SKU编码不可为空")
    private String skuCode;

    @ApiModelProperty(value = "SKUID", example = "12345")
    @NotNull(message = "SKUID不可为空")
    private Long skuId;

    @ApiModelProperty(value = "商品名称", example = "SKU12345")
    @NotBlank(message = "商品名称不可为空")
    private String itemName;

    @ApiModelProperty(value = "国条码", example = "************")
    private String eanCode;

    @ApiModelProperty(value = "基础价格", example = "99.99")
    @NotNull(message = "基础价格不可为空")
    private BigDecimal price;

    @ApiModelProperty(value = "加价数值", example = "10.00")
    @NotNull(message = "加价数值不可为空")
    private BigDecimal markupValue;

    @ApiModelProperty(value = "加价方式 1比例 2绝对值", example = "1")
    @NotNull(message = "加价方式不可为空")
    private Integer markupMethod;

    @ApiModelProperty(value = "店铺ID", example = "10001")
    @NotNull(message = "店铺ID不可为空")
    private Long shopId;

    @ApiModelProperty(value = "商家编码", example = "Seller123")
    @NotBlank(message = "商家编码不可为空")
    private String sellerCode;

    @ApiModelProperty(value = "商家id", example = "12345")
    @NotNull(message = "商家id不可为空")
    private Long sellerId;

    @ApiModelProperty(value = "生效标识 1长期有效 2指定时间", example = "1")
    @NotNull(message = "生效标识不可为空")
    private Integer effectiveMark;

    @ApiModelProperty(value = "生效开始时间", example = "2023-01-01 00:00:00")
    private Date effectiveTime;

    @ApiModelProperty(value = "生效结束时间", example = "2023-12-31 23:59:59")
    private Date invalidTime;

    @ApiModelProperty(value = "状态 0未开始 1生效中 2已结束", example = "1",hidden = true)
    private String status;

    @ApiModelProperty(value = "私域公域标识 1私域 2公域", example = "2")
    @NotNull(message = "私域公域标识不可为空")
    private Integer domainType;

    @ApiModelProperty(value = "操作标识 0未操作 1已操作", example = "0",hidden = true)
    private Integer operationType = 0;

    @ApiModelProperty(value = "创建人ID", example = "user123",hidden = true)
    private String createId;

    @ApiModelProperty(value = "更新人ID", example = "user123",hidden = true)
    private String modifyId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
