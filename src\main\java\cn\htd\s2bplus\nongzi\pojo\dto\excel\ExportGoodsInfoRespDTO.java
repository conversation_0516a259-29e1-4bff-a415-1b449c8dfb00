package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ExcelTarget("ExportGoodsInfoRespDTO")
public class ExportGoodsInfoRespDTO implements Serializable {

    @Excel(name = "商品sku编码", height = 10, width = 30)
    private String skuCode;

    @Excel(name = "条形码", height = 10, width = 30)
    private String eanCode;

    @Excel(name = "商品名称", height = 10, width = 30)
    private String itemName;

    @Excel(name = "单位", height = 10, width = 30)
    private String unitName;

    @Excel(name = "成本价", height = 10, width = 30)
    private BigDecimal cost;

    @Excel(name = "供应商", height = 10, width = 30)
    private String supplierName;


    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
