package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

@Data
public class SubmitAssignmentGoodVO {


    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "渠道id")
    private Long appId;

    @ApiModelProperty(value = "渠道名称")
    private String appName;

    @ApiModelProperty(value = "提报人工号")
    private String createId;

    @ApiModelProperty(value = "提报人名称")
    private String createName;

    @ApiModelProperty(value = "提报人时间")
    private String createTime;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
