package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ExcelTarget("PopSkuExport")
@Data
public class PpfxPopSkuExport implements Serializable {

    @ApiModelProperty(value = "商品编码",example = "xx",required = false)
    @Excel(name = "商品编码", height = 10, width = 20, orderNum = "1")
    private String itemCode;

    @ApiModelProperty(value = "sku编码",example = "xx",required = false)
    @Excel(name = "sku编码", height = 10, width = 20, orderNum = "3")
    private String skuCode;

    @ApiModelProperty(value = "商品名称",example = "xx",required = false)
    @Excel(name = "商品名称", height = 10, width = 60, orderNum = "4")
    private String itemName;

    @ApiModelProperty(value = "商品库存编码",example = "xx",required = false)
    @Excel(name = "商家库存编码", height = 10, width = 20, orderNum = "5")
    private String outerSkuId;

    @ApiModelProperty(value = "sku编码",example = "xx",required = false)
    @Excel(name = "商品条形码", height = 10, width = 20, orderNum = "6")
    private String eanCode;

    @ApiModelProperty(value = "可用库存")
    @Excel(name = "可用库存", height = 10, width = 20, orderNum = "7")
    private String useNum;

    @ApiModelProperty(value = "成本",example = "xx",required = false)
    @Excel(name = "成本", height = 10, width = 20, orderNum = "8")
    private String cost;

    @ApiModelProperty(value = "销售价格",example = "xx",required = false)
    @Excel(name = "销售价格", height = 10, width = 20, orderNum = "9")
    private String price;

    @ApiModelProperty(value = "规格",example = "xx",required = false)
    @Excel(name = "规格", height = 10, width = 60, orderNum = "10")
    private String skuAttributesName;

    @ApiModelProperty(value = "店铺名称",example = "xx",required = false)
    @Excel(name = "店铺名称", height = 10, width = 50, orderNum = "11")
    private String shopName;

    @ApiModelProperty(value = "店铺ID",example = "xx",required = false)
    @Excel(name = "店铺ID", height = 10, width = 50, orderNum = "12")
    private Long shopId;

    @ApiModelProperty(value = "上下架",example = "0:下架1:上架",required = false)
    @Excel(name = "上下架", height = 10, width = 20, orderNum = "13")
    private String status;

    @ApiModelProperty(value = "品牌名称",example = "xx",required = false)
    @Excel(name = "品牌名称", height = 10, width = 20, orderNum = "14")
    private String brandName;

    @ApiModelProperty(value = "商品类目",example = "家用空调>家用空调>挂机>1.5P",required = false)
    @Excel(name = "商品类目", height = 10, width = 40, orderNum = "15")
    private String categoryPath;

    @ApiModelProperty(value = "单位",example = "xx",required = false)
    @Excel(name = "单位", height = 10, width = 20, orderNum = "16")
    private String unitName;

    @ApiModelProperty(value = "发布时间",example = "xx",required = false)
    @Excel(name = "发布时间", height = 10, width = 20, orderNum = "17")
    private String createTime;

    @ApiModelProperty(value = "修改时间",example = "xx",required = false)
    @Excel(name = "修改时间", height = 10, width = 20, orderNum = "18")
    private String modifyTime;


    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
