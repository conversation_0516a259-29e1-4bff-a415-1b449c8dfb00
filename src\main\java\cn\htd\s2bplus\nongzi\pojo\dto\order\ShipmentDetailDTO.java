package cn.htd.s2bplus.nongzi.pojo.dto.order;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @author: xqw
 * @date: 2020/10/14
 * @time: 11:24
 */
public class ShipmentDetailDTO  implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    @ApiModelProperty(value = "发货单id")
    private Long consignmentWarehouseId;
    @ApiModelProperty(value = "货品编号")
    private String cargoCode;
    @ApiModelProperty(value = "货品数量")
    private Integer cargoCount;
    @ApiModelProperty(value = "仓库编号")
    private String warehouseCode;
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;
    @ApiModelProperty(value = "快递物流单号")
    private String logisticsNo;

    public ShipmentDetailDTO() {
    }

    public Long getId() {
        return this.id;
    }

    public Long getConsignmentWarehouseId() {
        return this.consignmentWarehouseId;
    }

    public String getCargoCode() {
        return this.cargoCode;
    }

    public Integer getCargoCount() {
        return this.cargoCount;
    }

    public String getWarehouseCode() {
        return this.warehouseCode;
    }

    public String getWarehouseName() {
        return this.warehouseName;
    }

    public String getLogisticsNo() {
        return this.logisticsNo;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setConsignmentWarehouseId(Long consignmentWarehouseId) {
        this.consignmentWarehouseId = consignmentWarehouseId;
    }

    public void setCargoCode(String cargoCode) {
        this.cargoCode = cargoCode;
    }

    public void setCargoCount(Integer cargoCount) {
        this.cargoCount = cargoCount;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ShipmentDetailDTO)) {
            return false;
        } else {
            ShipmentDetailDTO other = (ShipmentDetailDTO)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label95: {
                    Object this$id = this.getId();
                    Object other$id = other.getId();
                    if (this$id == null) {
                        if (other$id == null) {
                            break label95;
                        }
                    } else if (this$id.equals(other$id)) {
                        break label95;
                    }

                    return false;
                }

                Object this$consignmentWarehouseId = this.getConsignmentWarehouseId();
                Object other$consignmentWarehouseId = other.getConsignmentWarehouseId();
                if (this$consignmentWarehouseId == null) {
                    if (other$consignmentWarehouseId != null) {
                        return false;
                    }
                } else if (!this$consignmentWarehouseId.equals(other$consignmentWarehouseId)) {
                    return false;
                }

                Object this$cargoCode = this.getCargoCode();
                Object other$cargoCode = other.getCargoCode();
                if (this$cargoCode == null) {
                    if (other$cargoCode != null) {
                        return false;
                    }
                } else if (!this$cargoCode.equals(other$cargoCode)) {
                    return false;
                }

                label74: {
                    Object this$cargoCount = this.getCargoCount();
                    Object other$cargoCount = other.getCargoCount();
                    if (this$cargoCount == null) {
                        if (other$cargoCount == null) {
                            break label74;
                        }
                    } else if (this$cargoCount.equals(other$cargoCount)) {
                        break label74;
                    }

                    return false;
                }

                label67: {
                    Object this$warehouseCode = this.getWarehouseCode();
                    Object other$warehouseCode = other.getWarehouseCode();
                    if (this$warehouseCode == null) {
                        if (other$warehouseCode == null) {
                            break label67;
                        }
                    } else if (this$warehouseCode.equals(other$warehouseCode)) {
                        break label67;
                    }

                    return false;
                }

                Object this$warehouseName = this.getWarehouseName();
                Object other$warehouseName = other.getWarehouseName();
                if (this$warehouseName == null) {
                    if (other$warehouseName != null) {
                        return false;
                    }
                } else if (!this$warehouseName.equals(other$warehouseName)) {
                    return false;
                }

                Object this$logisticsNo = this.getLogisticsNo();
                Object other$logisticsNo = other.getLogisticsNo();
                if (this$logisticsNo == null) {
                    if (other$logisticsNo != null) {
                        return false;
                    }
                } else if (!this$logisticsNo.equals(other$logisticsNo)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof ShipmentDetailDTO;
    }

    public int hashCode() {
        int result = 1;
        Object $id = this.getId();
         result = result * 59 + ($id == null ? 43 : $id.hashCode());
        Object $consignmentWarehouseId = this.getConsignmentWarehouseId();
        result = result * 59 + ($consignmentWarehouseId == null ? 43 : $consignmentWarehouseId.hashCode());
        Object $cargoCode = this.getCargoCode();
        result = result * 59 + ($cargoCode == null ? 43 : $cargoCode.hashCode());
        Object $cargoCount = this.getCargoCount();
        result = result * 59 + ($cargoCount == null ? 43 : $cargoCount.hashCode());
        Object $warehouseCode = this.getWarehouseCode();
        result = result * 59 + ($warehouseCode == null ? 43 : $warehouseCode.hashCode());
        Object $warehouseName = this.getWarehouseName();
        result = result * 59 + ($warehouseName == null ? 43 : $warehouseName.hashCode());
        Object $logisticsNo = this.getLogisticsNo();
        result = result * 59 + ($logisticsNo == null ? 43 : $logisticsNo.hashCode());
        return result;
    }

    public String toString() {
        return "ShipmentDetailDTO(id=" + this.getId() + ", consignmentWarehouseId=" + this.getConsignmentWarehouseId() + ", cargoCode=" + this.getCargoCode() + ", cargoCount=" + this.getCargoCount() + ", warehouseCode=" + this.getWarehouseCode() + ", warehouseName=" + this.getWarehouseName() + ", logisticsNo=" + this.getLogisticsNo() + ")";
    }
}

