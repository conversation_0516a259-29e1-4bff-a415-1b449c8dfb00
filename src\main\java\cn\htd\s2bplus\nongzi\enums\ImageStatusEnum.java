package cn.htd.s2bplus.nongzi.enums;

/**
 * 图片状态枚举:1被引用 0未被引用 2待补充商品信息
 */
public enum ImageStatusEnum {
    REFERENCED("1", "被引用"),
    UNREFERENCED("0", "未被引用"),
    PENDING_SUPPLEMENT("2", "待补充商品信息");


    private String code;
    private String msg;

    ImageStatusEnum(String code,String msg){
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}

