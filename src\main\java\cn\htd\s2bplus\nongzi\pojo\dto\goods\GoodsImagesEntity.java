package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
public class GoodsImagesEntity implements Serializable {
    private static final long serialVersionUID = -3564982626782848823L;

    /**
     * 商家Id
     */
    private String sellerId;
    /**
     * 商品item编码
     */
    private String itemCode;
    /**
     * 商品sku编码
     */
    private String skuCode;
    /**
     * 图片名称
     */
    private String picName;
    /**
     * 图片地址
     */
    private String picUrl;
    /**
     * 图片分类 1:item图 2:item描述图 3:sku图
     */
    private String picType;

    /**
     * 图片状态:1被引用 0未被引用 2待补充商品信息
     */
    private String imageStatus;
    /**
     * 图片描述
     */
    private String description;
    /**
     * 图片扩展信息JSON,例如图片大小等信息
     */
    private String extendInfo;

    /**
     * 创建人id
     */
    private String createId;
    /**
     * 创建人名称
     */
    private String createName;
    /**
     * 创建时间
     */
    private Date createTime;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
