package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CategoryDTO implements Serializable {

    private static final long serialVersionUID = 6421020686382190730L;

    /**
     * 类目ID
     */
    @ApiModelProperty(value = "类目ID")
    private Long cid;
    /**
     * 类目名称
     */
    @ApiModelProperty(value = "类目名称")
    private String cName;
    /**
     * 类目等级
     */
    @ApiModelProperty(value = "类目等级")
    private Integer cLeve;
    /**
     * 父级类目ID
     */
    @ApiModelProperty(value = "父级类目ID")
    private Long parentCid;
    /**
     * 管理类目集
     */
    @ApiModelProperty(value = "管理类目集")
    private String manageCategoryIds;
    /**
     * 渠道ID
     */
    @ApiModelProperty(value = "渠道ID")
    private Long channelId;
    /**
     * 中台渠道ID
     */
    @ApiModelProperty(value = "中台渠道ID")
    private Long appId;
    /**
     * 类目图片
     */
    @ApiModelProperty(value = "类目图片")
    private String cPictureUrl;

    /**
     * 类目排列顺序索引
     */
    @ApiModelProperty(value = "类目排列顺序索引")
    private Long cIndex;

    private Integer status;

    private Long createId;

    private String createName;

    private Date createTime;

    private Long modifyId;

    private String modifyName;

    private Date modifyTime;

    @ApiModelProperty(value = "配置类目图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "是否展示1:展示 0:不展示")
    private Integer isShow;

    @ApiModelProperty(value = "多个父级id,集合")
    private List<Long> parentCids;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}