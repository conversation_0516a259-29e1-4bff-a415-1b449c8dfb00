spring:
  application:
    name: s2bplus-nongzi-api
  main:
    #基础架构jar依赖必须加这个配置，不然无法启动
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      config:
        server-addr: ${nacos_config_server_addr}
        file-extension: yml
        namespace:  ${nacos_config_namespace}
        dataId: ${spring_application_name}.yml
      discovery:
        server-addr: ${nacos_discovery_server_addr}
        namespace:  ${nacos_config_namespace}
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB

# 由于基础日志框架限定了/home/<USER>/logs/default/路径,导致mac有权限限制无法启动,故而需在此增加日志路径配置覆盖
logging:
  level:
    root: info
    com.alibaba.nacos.client.naming: error
  path: ./logs/native
  name: ${spring.application.name}


#业务参数配置
biz:
  source:
    # 资源自控开关 0-关闭 1-打开
    monitor-flag: 1
    threshold:
      cpu: 0.8
      memory: 0.8

