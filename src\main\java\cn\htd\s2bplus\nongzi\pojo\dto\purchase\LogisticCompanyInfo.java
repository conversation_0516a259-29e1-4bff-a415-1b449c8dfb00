package cn.htd.s2bplus.nongzi.pojo.dto.purchase;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

@Data
public class LogisticCompanyInfo {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "供应商ID")
    private Long sellerId;

    @ApiModelProperty(value = "供应商编码")
    private String sellerCode;

    @ApiModelProperty(value = "供应商名称")
    private String sellerName;

    @ApiModelProperty(value = "物流公司编码")
    private String logisticCode;

    @ApiModelProperty(value = "物流公司名称")
    private String logisticName;

    @ApiModelProperty(value = "是否默认地址 0：否，1：是")
    private Integer defaultFlag;

    @ApiModelProperty(value = "删除状态：0：未删除 1：已删除")
    private Integer deleteFlag;

    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人ID")
    private Long modifyId;

    @ApiModelProperty(value = "更新人名称")
    private String modifyName;

    @ApiModelProperty(value = "更新时间")
    private Date modifyTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}