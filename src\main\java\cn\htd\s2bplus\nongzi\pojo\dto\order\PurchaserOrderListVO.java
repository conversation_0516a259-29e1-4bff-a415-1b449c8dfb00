package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class PurchaserOrderListVO implements Serializable {

    private static final long serialVersionUID = -2985528089865253L;

    @ApiModelProperty(value = "采购单唯一键")
    private String purchaseOrderUniqueNo;

    @ApiModelProperty(value = "订单编号")
    private String purchaserOrderNumber;

    @ApiModelProperty(value = "订单状态")
    private String purchaserOrderStatus;

    @ApiModelProperty(value = "当采购单展示操作为去发货时,判断是否展示发货按钮 0 展示 , 1 不展示")
    private String showDeliveryButtonFlag;

    @ApiModelProperty(value = "订单类型 1=囤货订单,2:直发订单")
    private String orderType;

    @ApiModelProperty(value = "下单时间")
    private String createPurchaserOrderTime;

    @ApiModelProperty(value = "买家编码")
    private String buyerCode;

    @ApiModelProperty(value = "买家名称")
    private String buyerName;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "电签系统合同id")
    private String signcontractNo;

    @ApiModelProperty(value = "签约方式 1：线上签约 2：线下签约")
    private Byte signType;

    @ApiModelProperty(value = "商品行信息")
    private List<PurchaserOrderGoodsListDTO> goodsList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
