package cn.htd.s2bplus.nongzi.handle;

import cn.htd.rdc.base.development.framework.core.exception.CommonRuntimeException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.utils.AuthRuntimeException;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2019/10
 */
@ControllerAdvice
@Slf4j
@Order(0)
public class MyExceptionHandle {

	MyExceptionHandle() {
	}

	@ExceptionHandler({Exception.class})
	@ResponseBody
	public Result handle(Exception e, HttpServletRequest request) {
		if(e instanceof CommonRuntimeException) {
			log.error("服务异常:" , e);
			CommonRuntimeException exception = (CommonRuntimeException)e;
			return ResultUtil.error(exception.getCode(), exception.getMessage());
		} else if(e instanceof HttpRequestMethodNotSupportedException){
			log.error("请求类型不支持异常: ",e);
			return ResultUtil.error(ResultEnum.METHOD_NOT_SUPPORTED.getCode(),ResultEnum.METHOD_NOT_SUPPORTED.getMsg());
		}else if(e instanceof HystrixRuntimeException){
			log.error("HystrixRuntimeException : ",e);
			return ResultUtil.error(ResultEnum.ERROR.getCode(),"系统繁忙请稍后再试!");
		}else if(e instanceof ConstraintViolationException){
			log.error("ConstraintViolationException : ",e);
			return ResultUtil.error(ResultEnum.PARAM_ERROR.getCode(),((ConstraintViolationException) e).getConstraintViolations().iterator().next().getMessage());
		}else if(e instanceof JsonMappingException){
			log.error("Json映射异常 : ",e);
			return ResultUtil.error(ApiResultEnum.JSON_DATA_FORMAT_ERROR.getCode(),ApiResultEnum.JSON_DATA_FORMAT_ERROR.getMsg());
		}else if(e instanceof MissingServletRequestParameterException){
			log.error("url:[{}], params: [{}]",request.getRequestURI(), Objects.nonNull(request.getParameterMap())?JSONObject.toJSONString(request.getParameterMap()):"");
			log.error("MissingServletRequestParameterException 异常 : ",e);
			return ResultUtil.error(ResultEnum.PARAM_ERROR.getCode(),((MissingServletRequestParameterException) e).getMessage());
		}else if(e instanceof AuthRuntimeException){
			log.info(e.getMessage());
			AuthRuntimeException authRuntimeException = (AuthRuntimeException)e;
			return ResultUtil.error(authRuntimeException.getCode(), authRuntimeException.getMessage());
		}else {
			log.error("系统异常:", e);
			return ResultUtil.error(ResultEnum.UNKNOWN_ERR.getCode(), ResultEnum.UNKNOWN_ERR.getMsg());
		}

	}
}
