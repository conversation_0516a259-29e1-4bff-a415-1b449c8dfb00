FROM   171.16.55.100/b2b_p/centos6v5-tingyun-v2:latest

RUN sed -i '0,/micloud-biz/s//nongzi-api/' /home/<USER>/tingyun/tingyun.properties

EXPOSE 8080




WORKDIR /home/<USER>

RUN rm -rf  tomcat7 tomcat8

ADD ./target/s2bplus-nongzi-api.jar ./

ENV nacos_config_server_addr=aliyun-nacos-prod.htd.cn \
    nacos_discovery_server_addr=aliyun-nacos-prod.htd.cn \
    nacos_config_namespace=0b141e80-bf11-4d75-95e9-132c32fc67ac

ENV JAVA_OPTS="-Xms2G -Xmx2G -XX:PermSize=512M -XX:MaxPermSize=512M  -Dfile.encoding=UTF8 -Dsun.jnu.encoding=UTF8   -Dapp.id=s2bplus-nongzi-api"

ENTRYPOINT java ${JAVA_OPTS}  -javaagent:/home/<USER>/tingyun/tingyun-agent-java.jar   -Dmaven.test.skip=true  -jar  s2bplus-nongzi-api.jar
