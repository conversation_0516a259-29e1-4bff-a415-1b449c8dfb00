package cn.htd.s2bplus.nongzi.utils;

import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public class FileUtil {

    /**
     * 创建单个文件夹。
     *
     * @param dir
     * @param ignoreIfExitst
     *            true 表示如果文件夹存在就不再创建了。false是重新创建。
     * @throws IOException
     */
    public static void createDir(String dir, boolean ignoreIfExitst)
            throws IOException {
        File file = new File(dir);

        if (ignoreIfExitst && file.exists()) {
            return;
        }

        if (file.mkdir() == false) {
            throw new IOException("Cannot create the directory = " + dir);
        }
    }

    /**
     * 创建多个文件夹
     *
     * @param dir
     * @param ignoreIfExitst
     * @throws IOException
     */
    public static void createDirs(String dir, boolean ignoreIfExitst)
            throws IOException {
        File file = new File(dir);

        if (ignoreIfExitst && file.exists()) {
            return;
        }

        if (file.mkdirs() == false) {
            throw new IOException("Cannot create directories = " + dir);
        }
    }

    /**
     * 删除一个文件。
     *
     * @param filename
     * @throws IOException
     */
    public static void deleteFile(String filename) throws IOException {
        File file = new File(filename);
        log.trace("Delete file = " + filename);
        if (file.isDirectory()) {
            throw new IOException(
                    "IOException -> BadInputException: not a file.");
        }
        if (file.exists() == false) {
            throw new IOException(
                    "IOException -> BadInputException: file is not exist.");
        }
        if (file.delete() == false) {
            throw new IOException("Cannot delete file. filename = " + filename);
        }
    }

    /**
     * 删除文件夹及其下面的子文件夹
     *
     * @param dir
     * @throws IOException
     */
    public static void deleteDir(File dir) throws IOException {
        if (dir.isFile())
            throw new IOException(
                    "IOException -> BadInputException: not a directory.");
        File[] files = dir.listFiles();
        if (files != null) {
            for (int i = 0; i < files.length; i++) {
                File file = files[i];
                if (file.isFile()) {
                    file.delete();
                } else {
                    deleteDir(file);
                }
            }
        }// if
        dir.delete();
    }

    public static String getPathSeparator() {
        return File.pathSeparator;
    }

    public static String getFileSeparator() {
        return File.separator;
    }

    /**
     * 列出指定文件目录下面的文件信息。
     *
     * @param dir
     * @return
     * @throws IOException
     */
    public static List<FileInfo> getFiles(File dir) throws IOException {

        if (dir.isFile())
            throw new IOException("BadInputException: not a directory.");
        if (!dir.exists()) {
            throw new IOException(" don't exist ");
        }

        File[] files = dir.listFiles();

        int LEN = 0;
        if (files != null) {
            LEN = files.length;
        }
        List<FileInfo> l = new ArrayList<FileInfo>();
        long tempFLen = 0; //文件长度
        for (int i = 0; i < LEN; i++) {
            FileInfo temp = new FileInfo();
            temp.setFileName(files[i].getName());
            temp.setDir(files[i].isDirectory());
            //是文件，且包含.
            if (files[i].isFile()) {
                if(files[i].getName().lastIndexOf(".")!=-1)
                    temp.setFileType(files[i].getName().substring(
                            files[i].getName().lastIndexOf(".")));
            }else{
                temp.setFileType("文件夹");
            }

            tempFLen = files[i].length();
            temp.setFileLen(tempFLen);
            if(tempFLen/1024/1024/1024 >0){
                temp.setFileLength(files[i].length() / 1024/1024/1024 + "G");
            }else if(tempFLen/1024/1024 >0){
                temp.setFileLength(files[i].length() / 1024/1024 + "M");
            }else if(tempFLen/1024 >0){
                temp.setFileLength(files[i].length() / 1024 + "K");
            }else{
                temp.setFileLength(tempFLen+"byte");
            }

            temp.setFilePath(files[i].getAbsolutePath().replaceAll("[\\\\]", "/"));
            temp.setLastModifiedTime((files[i].lastModified()));

            temp.setAuthor(null);
            temp.setVersion(null);
            temp.setRemark(null);
            l.add(temp);

        }
        return l;
    }

    /**
     * 获取到目录下面文件的大小。包含了子目录。
     *
     * @param dir
     * @return
     * @throws IOException
     */
    public static long getDirLength(File dir) throws IOException {
        if (dir.isFile())
            throw new IOException("BadInputException: not a directory.");
        long size = 0;
        File[] files = dir.listFiles();
        if (files != null) {
            for (int i = 0; i < files.length; i++) {
                File file = files[i];
                // file.getName();
                // log.info(file.getName());
                long length = 0;
                if (file.isFile()) {
                    length = file.length();
                } else {
                    length = getDirLength(file);
                }
                size += length;
            }// for
        }// if
        return size;
    }

    /**
     * 将文件清空。
     *
     * @param srcFilename
     * @throws IOException
     */
    public static void emptyFile(String srcFilename) throws IOException {
        File srcFile = new File(srcFilename);
        if (!srcFile.exists()) {
            throw new FileNotFoundException("Cannot find the file: "
                    + srcFile.getAbsolutePath());
        }
        if (!srcFile.canWrite()) {
            throw new IOException("Cannot write the file: "
                    + srcFile.getAbsolutePath());
        }

        FileOutputStream outputStream = new FileOutputStream(srcFilename);
        outputStream.close();
    }

    /**
     * Write content to a fileName with the destEncoding 写文件。如果此文件不存在就创建一个。
     *
     * @param content
     *            String
     * @param fileName
     *            String
     * @param destEncoding
     *            String
     * @throws FileNotFoundException
     * @throws IOException
     */
    public static void writeFile(String content, String fileName,
                                 String destEncoding) throws FileNotFoundException, IOException {

        File file = null;
        try {
            file = new File(fileName);
            if (!file.exists()) {
                if (file.createNewFile() == false) {
                    throw new IOException("create file '" + fileName
                            + "' failure.");
                }
            }
            if (file.isFile() == false) {
                throw new IOException("'" + fileName + "' is not a file.");
            }
            if (file.canWrite() == false) {
                throw new IOException("'" + fileName + "' is a read-only file.");
            }
        } finally {
            // we dont have to close File here
        }

        BufferedWriter out = null;
        try {
            FileOutputStream fos = new FileOutputStream(fileName);
            out = new BufferedWriter(new OutputStreamWriter(fos, destEncoding));

            out.write(content);
            out.flush();
        } catch (FileNotFoundException fe) {
            //log.error("Error", fe);
            throw fe;
        } catch (IOException e) {
            //log.error("Error", e);
            throw e;
        } finally {
            try {
                if (out != null)
                    out.close();
            } catch (IOException ex) {
            }
        }
    }

    /**
     * 读取文件的内容，并将文件内容以字符串的形式返回。
     *
     * @param fileName
     * @param srcEncoding
     * @return
     * @throws FileNotFoundException
     * @throws IOException
     */
    public static String readFile(String fileName, String srcEncoding)
            throws FileNotFoundException, IOException {

        File file = null;
        try {
            file = new File(fileName);
            if (file.isFile() == false) {
                throw new IOException("'" + fileName + "' is not a file.");
            }
        } finally {
            // we dont have to close File here
        }

        BufferedReader reader = null;
        try {
            StringBuffer result = new StringBuffer(1024);
            FileInputStream fis = new FileInputStream(fileName);
            reader = new BufferedReader(new InputStreamReader(fis, srcEncoding));

            char[] block = new char[512];
            while (true) {
                int readLength = reader.read(block);
                if (readLength == -1)
                    break;// end of file
                result.append(block, 0, readLength);
            }
            return result.toString();
        } catch (FileNotFoundException fe) {
            //log.error("Error", fe);
            throw fe;
        } catch (IOException e) {
            //log.error("Error", e);
            throw e;
        } finally {
            try {
                if (reader != null)
                    reader.close();
            } catch (IOException ex) {
            }
        }
    }

    /*
     * 1 ABC 2 abC Gia su doc tu dong 1 lay ca thay 5 dong => 1 --> 5 3 ABC
     */
    public static String[] getLastLines(File file, int linesToReturn)
            throws IOException, FileNotFoundException {

        final int AVERAGE_CHARS_PER_LINE = 250;
        final int BYTES_PER_CHAR = 2;

        RandomAccessFile randomAccessFile = null;
        StringBuffer buffer = new StringBuffer(linesToReturn
                * AVERAGE_CHARS_PER_LINE);
        int lineTotal = 0;
        try {
            randomAccessFile = new RandomAccessFile(file, "r");
            long byteTotal = randomAccessFile.length();
            long byteEstimateToRead = linesToReturn * AVERAGE_CHARS_PER_LINE
                    * BYTES_PER_CHAR;

            long offset = byteTotal - byteEstimateToRead;
            if (offset < 0) {
                offset = 0;
            }

            randomAccessFile.seek(offset);
            // log.debug("SKIP IS ::" + offset);

            String line = null;
            String lineUTF8 = null;
            while ((line = randomAccessFile.readLine()) != null) {
                lineUTF8 = new String(line.getBytes("ISO8859_1"), "UTF-8");
                lineTotal++;
                buffer.append(lineUTF8).append("\n");
            }
        } finally {
            if (randomAccessFile != null) {
                try {
                    randomAccessFile.close();
                } catch (IOException ex) {
                }
            }
        }

        String[] resultLines = new String[linesToReturn];
        BufferedReader in = null;
        try {
            in = new BufferedReader(new StringReader(buffer.toString()));

            int start = lineTotal /* + 2 */- linesToReturn; // Ex : 55 - 10 = 45
            // ~ offset
            if (start < 0)
                start = 0; // not start line
            for (int i = 0; i < start; i++) {
                in.readLine(); // loop until the offset. Ex: loop 0, 1 ~~ 2
                // lines
            }

            int i = 0;
            String line = null;
            while ((line = in.readLine()) != null) {
                resultLines[i] = line;
                i++;
            }
        } catch (IOException ie) {
            //log.error("Error" + ie);
            throw ie;
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException ex) {
                }
            }
        }
        return resultLines;
    }

    /**
     * 单个文件拷贝。
     *
     * @param srcFilename
     * @param destFilename
     * @param overwrite
     * @throws IOException
     */
    public static void copyFile(String srcFilename, String destFilename,
                                boolean overwrite) throws IOException {

        File srcFile = new File(srcFilename);
        // 首先判断源文件是否存在
        if (!srcFile.exists()) {
            throw new FileNotFoundException("Cannot find the source file: "
                    + srcFile.getAbsolutePath());
        }
        // 判断源文件是否可读
        if (!srcFile.canRead()) {
            throw new IOException("Cannot read the source file: "
                    + srcFile.getAbsolutePath());
        }

        File destFile = new File(destFilename);

        if (overwrite == false) {
            // 目标文件存在就不覆盖
            if (destFile.exists())
                return;
        } else {
            // 如果要覆盖已经存在的目标文件，首先判断是否目标文件可写。
            if (destFile.exists()) {
                if (!destFile.canWrite()) {
                    throw new IOException("Cannot write the destination file: "
                            + destFile.getAbsolutePath());
                }
            } else {
                // 不存在就创建一个新的空文件。
                if (!destFile.createNewFile()) {
                    throw new IOException("Cannot write the destination file: "
                            + destFile.getAbsolutePath());
                }
            }
        }

        BufferedInputStream inputStream = null;
        BufferedOutputStream outputStream = null;
        byte[] block = new byte[1024];
        try {
            inputStream = new BufferedInputStream(new FileInputStream(srcFile));
            outputStream = new BufferedOutputStream(new FileOutputStream(
                    destFile));
            while (true) {
                int readLength = inputStream.read(block);
                if (readLength == -1)
                    break;// end of file
                outputStream.write(block, 0, readLength);
            }
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException ex) {
                    // just ignore
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException ex) {
                    // just ignore
                }
            }
        }
    }

    /**
     * 单个文件拷贝。
     *
     * @param srcFile
     * @param destFile
     * @param overwrite
     *            是否覆盖目的文件
     * @throws IOException
     */
    public static void copyFile(File srcFile, File destFile, boolean overwrite)
            throws IOException {

        // 首先判断源文件是否存在
        if (!srcFile.exists()) {
            throw new FileNotFoundException("Cannot find the source file: "
                    + srcFile.getAbsolutePath());
        }
        // 判断源文件是否可读
        if (!srcFile.canRead()) {
            throw new IOException("Cannot read the source file: "
                    + srcFile.getAbsolutePath());
        }

        if (overwrite == false) {
            // 目标文件存在就不覆盖
            if (destFile.exists())
                return;
        } else {
            // 如果要覆盖已经存在的目标文件，首先判断是否目标文件可写。
            if (destFile.exists()) {
                if (!destFile.canWrite()) {
                    throw new IOException("Cannot write the destination file: "
                            + destFile.getAbsolutePath());
                }
            } else {
                // 不存在就创建一个新的空文件。
                if (!destFile.createNewFile()) {
                    throw new IOException("Cannot write the destination file: "
                            + destFile.getAbsolutePath());
                }
            }
        }

        BufferedInputStream inputStream = null;
        BufferedOutputStream outputStream = null;
        byte[] block = new byte[1024];
        try {
            inputStream = new BufferedInputStream(new FileInputStream(srcFile));
            outputStream = new BufferedOutputStream(new FileOutputStream(
                    destFile));
            while (true) {
                int readLength = inputStream.read(block);
                if (readLength == -1)
                    break;// end of file
                outputStream.write(block, 0, readLength);
            }
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException ex) {
                    // just ignore
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException ex) {
                    // just ignore
                }
            }
        }
    }

    /**
     * 拷贝文件，从源文件夹拷贝文件到目的文件夹。 <br>
     * 参数源文件夹和目的文件夹，最后都不要带文件路径符号，例如：c:/aa正确，c:/aa/错误。
     *
     * @param srcDirName
     *            源文件夹名称 ,例如：c:/test/aa 或者c:\\test\\aa
     * @param destDirName
     *            目的文件夹名称,例如：c:/test/aa 或者c:\\test\\aa
     * @param overwrite
     *            是否覆盖目的文件夹下面的文件。
     * @throws IOException
     */
    public static void copyFiles(String srcDirName, String destDirName,
                                 boolean overwrite) throws IOException {
        File srcDir = new File(srcDirName);// 声明源文件夹
        // 首先判断源文件夹是否存在
        if (!srcDir.exists()) {
            throw new FileNotFoundException(
                    "Cannot find the source directory: "
                            + srcDir.getAbsolutePath());
        }

        File destDir = new File(destDirName);
        if (overwrite == false) {
            if (destDir.exists()) {
                // do nothing
            } else {
                if (destDir.mkdirs() == false) {
                    throw new IOException(
                            "Cannot create the destination directories = "
                                    + destDir);
                }
            }
        } else {
            // 覆盖存在的目的文件夹
            if (destDir.exists()) {
                // do nothing
            } else {
                // create a new directory
                if (destDir.mkdirs() == false) {
                    throw new IOException(
                            "Cannot create the destination directories = "
                                    + destDir);
                }
            }
        }

        // 循环查找源文件夹目录下面的文件（屏蔽子文件夹），然后将其拷贝到指定的目的文件夹下面。
        File[] srcFiles = srcDir.listFiles();
        if (srcFiles == null || srcFiles.length < 1) {
            // throw new IOException ("Cannot find any file from source
            // directory!!!");
            return;// do nothing
        }

        // 开始复制文件
        int SRCLEN = srcFiles.length;

        for (int i = 0; i < SRCLEN; i++) {
            // File tempSrcFile = srcFiles[i];

            File destFile = new File(destDirName + File.separator
                    + srcFiles[i].getName());
            // 注意构造文件对象时候，文件名字符串中不能包含文件路径分隔符";".
            // log.debug(destFile);
            if (srcFiles[i].isFile()) {
                copyFile(srcFiles[i], destFile, overwrite);
            } else {
                // 在这里进行递归调用，就可以实现子文件夹的拷贝
                copyFiles(srcFiles[i].getAbsolutePath(), destDirName
                        + File.separator + srcFiles[i].getName(), overwrite);
            }
        }
    }

    /**
     * 压缩文件。注意：中文文件名称和中文的评论会乱码。
     * @param srcFilename
     * @param destFilename
     * @param overwrite
     * @throws IOException
     */
    public static void zipFile(String srcFilename, String destFilename,
                               boolean overwrite) throws IOException {

        File srcFile = new File(srcFilename);
        // 首先判断源文件是否存在
        if (!srcFile.exists()) {
            throw new FileNotFoundException("Cannot find the source file: "
                    + srcFile.getAbsolutePath());
        }
        // 判断源文件是否可读
        if (!srcFile.canRead()) {
            throw new IOException("Cannot read the source file: "
                    + srcFile.getAbsolutePath());
        }

        if(destFilename==null || destFilename.trim().equals("")){
            destFilename = srcFilename+".zip";
        }else{
            destFilename += ".zip";
        }
        File destFile = new File(destFilename);

        if (overwrite == false) {
            // 目标文件存在就不覆盖
            if (destFile.exists())
                return;
        } else {
            // 如果要覆盖已经存在的目标文件，首先判断是否目标文件可写。
            if (destFile.exists()) {
                if (!destFile.canWrite()) {
                    throw new IOException("Cannot write the destination file: "
                            + destFile.getAbsolutePath());
                }
            } else {
                // 不存在就创建一个新的空文件。
                if (!destFile.createNewFile()) {
                    throw new IOException("Cannot write the destination file: "
                            + destFile.getAbsolutePath());
                }
            }
        }

        BufferedInputStream inputStream = null;
        BufferedOutputStream outputStream = null;
        ZipOutputStream zipOutputStream = null;
        byte[] block = new byte[1024];
        try {
            inputStream = new BufferedInputStream(new FileInputStream(srcFile));
            outputStream = new BufferedOutputStream(new FileOutputStream(destFile));
            zipOutputStream = new ZipOutputStream(outputStream);

            zipOutputStream.setComment("通过java程序压缩的");
            ZipEntry  zipEntry = new ZipEntry(srcFile.getName());
            zipEntry.setComment(" zipEntry通过java程序压缩的");
            zipOutputStream.putNextEntry(zipEntry);
            while (true) {
                int readLength = inputStream.read(block);
                if (readLength == -1)
                    break;// end of file
                zipOutputStream.write(block, 0, readLength);
            }
            zipOutputStream.flush();
            zipOutputStream.finish();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException ex) {
                    // just ignore
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException ex) {
                    // just ignore
                }
            }
            if (zipOutputStream != null) {
                try {
                    zipOutputStream.close();
                } catch (IOException ex) {
                    // just ignore
                }
            }
        }
    }

    public boolean isExistFile(String fileUrl) {
        HttpURLConnection hc = null;
        try {
            // 打开一个http连接
            URL url = new URL(fileUrl);
            hc = (HttpURLConnection) url.openConnection();
            // 定义输入流
            InputStream instream = hc.getInputStream();
            // 创建这个文件输出流
            // String tempFileName = "1.zip";
            // FileOutputStream fos = new FileOutputStream(tempFileName);
            // 定义一个大小为1024的字节数组
            byte[] buf = new byte[1024];
            // 从输入流中读出字节到定义的字节数组
            int len = instream.read(buf, 0, 1024);
            // 循环读入字节，然后写到文件输出流中
            while (len != -1) {
                // fos.write(buf, 0, len);
                len = instream.read(buf, 0, 1024);
            }
            // fos.flush();
            // fos.close();
            instream.close();
        } catch (Exception e) {
            log.info("下载出错！");
            return false;
        } finally {
            if (hc != null)
                hc.disconnect();
        }
        return true;
    }


    public static boolean saveRemoteRes(String httpUrl, String savePath)/*fileUrl网络资源地址*/
    {
        try {
            URL url = new URL(httpUrl);/* 将网络资源地址传给,即赋值给url */
            /* 此为联系获得网络资源的固定格式用法，以便后面的in变量获得url截取网络资源的输入流 */
            HttpURLConnection connection = (HttpURLConnection) url
                    .openConnection();
            DataInputStream in = new DataInputStream(connection
                    .getInputStream());
            /* 此处也可用BufferedInputStream与BufferedOutputStream */
            DataOutputStream out = new DataOutputStream(new FileOutputStream(
                    savePath));
            /* 将参数savePath，即将截取的图片的存储在本地地址赋值给out输出流所指定的地址 */
            byte[] buffer = new byte[4096];
            int count = 0;
            while ((count = in.read(buffer)) > 0)/* 将输入流以字节的形式读取并写入buffer中 */
            {
                out.write(buffer, 0, count);
            }
            out.close();/* 后面三行为关闭输入输出流以及网络资源的固定格式 */
            in.close();
            connection.disconnect();
            return true;/* 网络资源截取并存储本地成功返回true */

        } catch (Exception e) {
            log.info("saveRemoteRes error:"+"|"+httpUrl+"|"+savePath+"|"+e.getMessage());
            return false;
        }
    }

    /**
     * 追加方式写文件
     * @param inputstr
     * @param filepath
     * @param filename
     */
    public static void writeFileAppend(String inputstr, String filepath, String filename) {
        if(inputstr==null||"".equals(inputstr)){
            return;
        }
        inputstr += "\r\n";
        String strFilePath = filepath+filename;
        try {
            FileOutputStream fos = new FileOutputStream(strFilePath, true);
            fos.write(inputstr.getBytes());
            fos.close();
        } catch (FileNotFoundException ex) {
            log.info("FileNotFoundException : " + ex);
        } catch (IOException ioe) {
            log.info("IOException : " + ioe);
        }
    }

    /**
     * 获取指定目录下的所有文件（忽略子目录）
     *
     * @param directoryPath 目录路径
     * @return 文件列表（List<Path>）
     */
    public static List<Path> getFilesInDirectory(String directoryPath) {
        List<Path> fileList = new ArrayList<>();

        Path path = Paths.get(directoryPath);

        // 检查路径是否为有效目录
        if (!Files.isDirectory(path, LinkOption.NOFOLLOW_LINKS)) {
            log.info("指定路径不是一个目录");
            return fileList; // 返回空列表
        }

        try (Stream<Path> stream = Files.list(path)) {
            // 过滤出普通文件并添加到列表
            stream.filter(Files::isRegularFile)
                    .forEach(fileList::add);
        } catch (IOException e) {
            log.info("读取目录时发生错误: " + e.getMessage());
        }

        return fileList;
    }


    /**
     * 获取指定目录下的所有子目录名称（不递归）
     *
     * @param directoryPath 目录路径
     * @return 子目录名称列表（List<String>）
     */
    public static List<String> getSubdirectories(String directoryPath) {
        List<String> subdirectoryNames = new ArrayList<>();

        Path path = Paths.get(directoryPath);

        // 检查路径是否为有效目录
        if (!Files.isDirectory(path, LinkOption.NOFOLLOW_LINKS)) {
            log.info("指定路径不是一个目录");
            return subdirectoryNames; // 返回空列表
        }

        try (Stream<Path> stream = Files.list(path)) {
            // 过滤出子目录并提取名称
            stream.filter(Files::isDirectory)
                    .map(p -> p.getFileName().toString())
                    .forEach(subdirectoryNames::add);
        } catch (IOException e) {
            log.info("读取目录时发生错误: " + e.getMessage());
        }

        return subdirectoryNames;
    }

    /**
     * 检查在指定目录下是否存在某个文件（通过完整文件名）
     *
     * @param directoryPath 目录路径
     * @param fullFileName  完整文件名（包括扩展名，例如 "example.txt"）
     * @return 如果文件存在，则返回 true；否则返回 false
     */
    public static Path checkFileExists(String directoryPath, String fullFileName) {
        Path path = Paths.get(directoryPath);

        // 检查路径是否为有效目录
        if (!Files.isDirectory(path, LinkOption.NOFOLLOW_LINKS)) {
            log.info("指定路径不是一个目录");
            return null; // 返回 false
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(path)) {
            // 遍历目录中的文件，检查是否存在符合条件的文件
            for (Path filePath : stream) {
                if (Files.isRegularFile(filePath) && filePath.getFileName().toString().equals(fullFileName)) {
                    return filePath; // 找到匹配的文件
                }
            }
        } catch (IOException e) {
            log.info("读取目录时发生错误: " + e.getMessage());
            return null;
        }

        return null; // 如果遍历结束仍未找到匹配的文件，返回 false
    }

    /**
     * 从指定目录下查找文件名等于给定名称的文件（忽略扩展名）
     *
     * @param directoryPath 目录路径
     * @param fileName      文件名（不包含扩展名）
     * @return 符合条件的文件列表（List<Path>）
     */
    public static List<Path> findFileByName(String directoryPath, String fileName) {
        List<Path> fileList = new ArrayList<>();

        Path path = Paths.get(directoryPath);

        // 检查路径是否为有效目录
        if (!Files.isDirectory(path, LinkOption.NOFOLLOW_LINKS)) {
            log.info("指定路径不是一个目录");
            return fileList; // 返回空列表
        }

        try (Stream<Path> stream = Files.list(path)) {
            // 过滤出普通文件，并检查文件名是否匹配（忽略扩展名）
            stream.filter(Files::isRegularFile)
                    .filter(p -> matchesFileNameWithoutExtension(p, fileName))
                    .forEach(fileList::add);
        } catch (IOException e) {
            log.info("读取目录时发生错误: " + e.getMessage());
        }

        return fileList;
    }

    /**
     * 检查文件名是否匹配（忽略扩展名）
     *
     * @param filePath 要检查的文件路径
     * @param fileName 目标文件名（不包含扩展名）
     * @return 是否匹配
     */
    private static boolean matchesFileNameWithoutExtension(Path filePath, String fileName) {
        String actualFileName = filePath.getFileName().toString();
        int dotIndex = actualFileName.lastIndexOf('.');
        String nameWithoutExtension = (dotIndex == -1) ? actualFileName : actualFileName.substring(0, dotIndex);
        return nameWithoutExtension.equals(fileName);
    }


    public static String getFileTypeByExtension(Path path) {
        String fileName = path.getFileName().toString();

        // 获取最后一个点之后的部分
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        }
        return ""; // 无扩展名
    }


    // 支持的Excel文件扩展名
    private static final String[] EXCEL_EXTENSIONS = {"xlsx", "xls"};
    // 支持的图片文件扩展名
    private static final String[] IMAGE_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp"};

    /**
     * 查找目录中的所有Excel和图片文件
     */
    public static List<Path> findExcelAndImageFiles(String dirPath) throws IOException {
        List<Path> result = new ArrayList<>();
        Path startDir = Paths.get(dirPath);

        Files.walkFileTree(startDir, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                String extension = FilenameUtils.getExtension(file.toString()).toLowerCase();

                // 检查是否是Excel文件
                for (String excelExt : EXCEL_EXTENSIONS) {
                    if (excelExt.equals(extension)) {
                        result.add(file);
                        return FileVisitResult.CONTINUE;
                    }
                }

                // 检查是否是图片文件
                for (String imageExt : IMAGE_EXTENSIONS) {
                    if (imageExt.equals(extension)) {
                        result.add(file);
                        return FileVisitResult.CONTINUE;
                    }
                }

                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult visitFileFailed(Path file, IOException exc) {
                // 处理访问文件失败的情况（如权限问题）
                log.info("无法访问文件: " + file + " - " + exc.getMessage());
                return FileVisitResult.CONTINUE;
            }
        });

        return result;
    }


    /**
     * 从解压目录获取所有Excel和图片文件
     * @param directoryPath 解压后的临时目录路径
     * @return 包含文件信息的列表
     */
    public static List<FileInfoDTO> getFilesFromUnzippedDirectory(String directoryPath) {
        List<FileInfoDTO> fileInfoDTOS = new ArrayList<>();
        Path rootPath = Paths.get(directoryPath);

        try {
            Files.walkFileTree(rootPath, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                    String fileName = file.getFileName().toString().toLowerCase();

                    // 获取相对路径（相对于解压根目录）
                    Path relativePath = rootPath.relativize(file);

                    // 获取父文件夹名称（多级路径处理）
                    String parentFolders = "";
                    if (relativePath.getParent() != null) {
                        parentFolders = relativePath.getParent().toString().replace("\\", "/");
                    }

                    // 检查文件类型
                    if (isExcelFile(fileName) || isImageFile(fileName)) {
                        FileInfoDTO fileInfoDTO = new FileInfoDTO();
                        fileInfoDTO.setFileType(getFileType(fileName));
                        fileInfoDTO.setFileName(getImageNameWithoutExtension(file.getFileName().toString()));
                        fileInfoDTO.setParentFolders(getDivisonPath(parentFolders));
                        fileInfoDTO.setAbsolutePath(file.toString());
                        fileInfoDTOS.add(fileInfoDTO);
                    }
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult visitFileFailed(Path file, IOException exc) {
                   log.info("无法访问文件: " + file + " - " + exc.getMessage());
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException e) {
            log.info("遍历目录时发生错误: " + e.getMessage());
        }

        return fileInfoDTOS;
    }

    // 判断是否是Excel文件
    public static boolean isExcelFile(String fileName) {
        return fileName.endsWith(".xlsx") || fileName.endsWith(".xls");
    }

    // 判断是否是图片文件
    public static boolean isImageFile(String fileName) {
        return fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") ||
                fileName.endsWith(".png") || fileName.endsWith(".bmp");
    }

    private static String getFileType(String fileName) {
        if (isExcelFile(fileName)) return "excel";
        if (isImageFile(fileName)) return "image";
        return "other";
    }

    /**
     * 获取文件名
     */
    private static String getDivisonPath(String imagePath) {
        String[] parts = imagePath.split("/");
       return  parts.length > 1 ? parts[1] : "";
    }

    /**
     * 获取图片名
     */
    private static String getImageNameWithoutExtension(String imagePath) {
        if (imagePath == null || imagePath.isEmpty()) {
            return "";
        }

        // 1. 提取文件名（包含扩展名）
        int lastSlashIndex = imagePath.lastIndexOf('/');
        String fileName = (lastSlashIndex != -1)
                ? imagePath.substring(lastSlashIndex + 1)
                : imagePath;

        // 2. 去除扩展名
        int lastDotIndex = fileName.lastIndexOf('.');
        return (lastDotIndex != -1)
                ? fileName.substring(0, lastDotIndex)
                : fileName;
    }


    /**
     * 批量导入商品excel表头
     */
    private static final List<String> BATCH_IMPORT_GOODS_EXCEL_HEADERS = Arrays.asList(
            "序号", "商品货号（选填）", "四级管理类目ID", "三级销售类目ID", "品牌ID", "型号","单位",
            "商品名称","规格名称1","规格值1","规格名称2（选填）","规格值2（选填）","规格名称3（选填）","规格值3（选填）",
            "库存数量","商品条形码（选填）","商品图片文件夹","商品图片名称","规格图片名称（选填）","详情图片名称（选填）","商品详情","发布店铺名称","基础售价");

    /**
     * 验证Excel表头是否符合预期
     * @param excelPath Excel文件路径
     * @return 验证结果（包含错误信息，空字符串表示验证通过）
     */
    public static boolean validateHeaders(String excelPath) {
        try (FileInputStream fis = new FileInputStream(excelPath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0); // 第一个工作表
            Row headerRow = sheet.getRow(0);   // 第一行作为表头

            // 检查表头数量
            if (headerRow == null || headerRow.getLastCellNum() < BATCH_IMPORT_GOODS_EXCEL_HEADERS.size()) {
                throw new BusinessException(ResultEnum.ERROR.getCode(),"表头列数不足，预期至少" + BATCH_IMPORT_GOODS_EXCEL_HEADERS.size() + "列");
            }

            // 收集实际表头
            List<String> actualHeaders = new ArrayList<>();
            for (Cell cell : headerRow) {
                actualHeaders.add(cell.getStringCellValue().trim());
            }

            // 验证表头内容和顺序
            for (int i = 0; i < BATCH_IMPORT_GOODS_EXCEL_HEADERS.size(); i++) {
                String expected = BATCH_IMPORT_GOODS_EXCEL_HEADERS.get(i);
                String actual = i < actualHeaders.size() ? actualHeaders.get(i) : "";

                if (!expected.equals(actual)) {
                    throw new BusinessException(ResultEnum.ERROR.getCode(),"表头顺序有误，请重新上传");
                }
            }
            return true;
        } catch (IOException e) {
            return false;
        } catch (Exception e) {
            return false;
        }
    }
}

