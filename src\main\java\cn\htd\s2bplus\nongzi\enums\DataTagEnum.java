package cn.htd.s2bplus.nongzi.enums;

/**
 * 平台枚举，0:默认值 1:老中台  2:云原生
 *
 * <AUTHOR>
 * @date 2020/03/09
 */
public enum DataTagEnum {

    OLD_MIDDLE_CHANNEL(1, "老中台"),
    NATIVE_CLOUD_CHANNEL(2, "云原生");

    private Integer code;
    private String msg;

    DataTagEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
