package cn.htd.s2bplus.nongzi.interceptor;

import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @Date 2022/8/29 14:34
 * @Description
 */
@Component
@Slf4j
public class SourceMonitorInterceptor implements HandlerInterceptor {

    private final long GB = 1024L * 1024L * 1024L;

    @Value("${biz.source.monitor-flag}")
    private String sourceMOnitorFlag;
    @Value("${biz.source.threshold.cpu}")
    private String cpuThreshold;
    @Value("${biz.source.threshold.memory}")
    private String memoryThreshold;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 是否开启监控，0-关闭，1-开启
        if(StringUtils.equals(sourceMOnitorFlag, "0")){
            return HandlerInterceptor.super.preHandle(request, response, handler);
        }
        OperatingSystemMXBean operatingSystemMXBean = ManagementFactory.getOperatingSystemMXBean();
        String osJson = JSON.toJSONString(operatingSystemMXBean);
        JSONObject jsonObject = JSON.parseObject(osJson);
        //进程CPU使用率
        double processCpuLoad = jsonObject.getDouble("processCpuLoad");
        //系统CPU使用率
        double systemCpuLoad = jsonObject.getDouble("systemCpuLoad");
        //物理内存剩余可用量
        Long freePhysicalMemorySize = jsonObject.getLong("freePhysicalMemorySize");
        //总物理内存
        Long totalPhysicalMemorySize = jsonObject.getLong("totalPhysicalMemorySize");
        //内存占用率
        double memoryUseRatio = 1.0 * (totalPhysicalMemorySize - freePhysicalMemorySize) / totalPhysicalMemorySize;
        //系统总内存
        double totalMemory = 1.0 * totalPhysicalMemorySize / GB;
        //系统剩余内存
        double freeMemory = 1.0 * freePhysicalMemorySize / GB;

        log.info("预估解析所有数据占用CPU：" + twoDecimal(processCpuLoad * 100) + "%");
        if(processCpuLoad > Double.parseDouble(cpuThreshold) || processCpuLoad > Double.parseDouble(memoryThreshold)){
            throw new BusinessException(ResultEnum.FAILURE.getCode(), "当前服务器资源已耗尽，请稍后再试");
        }
        StringBuilder result = new StringBuilder();
        result.append("系统CPU占用率: ").append(twoDecimal(systemCpuLoad * 100 )).append("%，")
                .append("内存占用率：").append(twoDecimal(memoryUseRatio * 100 )).append("%，")
                .append("系统总内存：").append(twoDecimal(totalMemory)).append("GB，")
                .append("系统剩余内存：").append(twoDecimal(freeMemory)).append("GB，")
                .append("该进程占用CPU：").append(twoDecimal(processCpuLoad  * 100 )).append("%");
        log.info(result.toString());

        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    public double twoDecimal(double doubleValue) {
        BigDecimal bigDecimal = BigDecimal.valueOf(doubleValue).setScale(2, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }
}
