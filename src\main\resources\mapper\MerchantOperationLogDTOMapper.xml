<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.htd.s2bplus.nongzi.mapper.MerchantOperationLogDTOMapper">

    <insert id="insert" parameterType="cn.htd.s2bplus.nongzi.pojo.dto.common.MerchantOperationLogDTO" >
        insert into s2bplus_merchant_operation_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="sellerCode != null" >
                seller_code,
            </if>
            <if test="businessType != null" >
                business_type,
            </if>
            <if test="operatorType != null" >
                operator_type,
            </if>
            <if test="businessKey != null" >
                business_key,
            </if>
            <if test="operationRecord != null" >
                operation_record,
            </if>
            <if test="operatorCode != null" >
                operator_code,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="sellerCode != null" >
                #{sellerCode,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                #{businessType,jdbcType=BIGINT},
            </if>
            <if test="operatorType != null" >
                #{operatorType,jdbcType=BIGINT},
            </if>
            <if test="businessKey != null" >
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="operationRecord != null" >
                #{operationRecord,jdbcType=VARCHAR},
            </if>
            <if test="operatorCode != null" >
                #{operatorCode,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>
