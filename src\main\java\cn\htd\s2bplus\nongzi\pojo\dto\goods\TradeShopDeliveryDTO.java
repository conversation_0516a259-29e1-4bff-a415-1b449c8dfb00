package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;

@Data
public class TradeShopDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 3669715782461073072L;
    private Long templateId;

    private Long sellerId;

    private String postageFree;
    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
