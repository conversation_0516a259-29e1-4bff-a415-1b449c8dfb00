
package cn.htd.s2bplus.nongzi.service.order;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.common.Pager;
import cn.htd.s2bplus.nongzi.pojo.dto.order.*;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 订单原子服务，实现为云原生或者S2B，方便扩展替换
 *
 */
public interface OrderService {


    /**
     * 查询导出定信息
     * @param orderAndAllStatus
     * @return
     */
    Result<OrderDetailsPageDTO> getExportOrderList(OrderAndAllStatusReqDTO orderAndAllStatus, LoginUserDetail loginUser);

    /**
     * 查订单及商品行信息
     * @param orderAndAllStatus
     * @param current
     * @param size
     * @return
     */
    Result<OrderDetailsPageDTO> orderList(OrderAndAllStatusReqDTO orderAndAllStatus, int current, int size,LoginUserDetail loginUserDetail);


    /**
     * 新增报表生成下载历史
     * @param reportHistoryDTO
     * @return
     */
    Result<ReportHistoryDTO> createReportHistory(ReportHistoryDTO reportHistoryDTO);


    Result<OrderDeliverySelfPickupDTO> queryOrderDeliverySelfPickup(String orderNo);

    /**
     * 确认发货
     */
    Result<Boolean> deliverGood(InShipmentDTO inShipmentDTO);

    /**
     * 销售单生成报表
     * @param response 返回
     * @param ossOrderAndAllStatusReqDTO 销售单生成报表入参对象
     * @param loginUser 登陆人信息
     */
    void handleSaleOrderReportExport(HttpServletResponse response, OssOrderAndAllStatusReqDTO ossOrderAndAllStatusReqDTO, LoginUserDetail loginUser);

    /**
     * 分页查询报表生成下载历史
     * @param finishBeginTime 检索开始时间
     * @param finishEndtime 检索结束时间
     * @param userId 创建人id
     * @param page 页数
     * @param rows 每页记录数
     * @return 报表生成下载历史列表
     */
    Result<Pager<ReportHistoryDTO>> queryReportHistoryPage(String finishBeginTime, String finishEndtime, Long userId, int page, int rows);

    void handleExportGuestOrderList(QueryAllBatchGuestOrderCond cond,HttpServletResponse response,LoginUserDetail loginUser);

    /**
     * 导出佣金订单明细（线下结算）
     * @param queryCommissionOrderDTO
     */
    void handleCommissionOrdertExport(QueryCommissionOrderDTO queryCommissionOrderDTO, HttpServletResponse response);

    /**
     *
     * @param orderAndAllStatus
     * @param loginUser
     * @param tabTime
     * @param beginTime
     * @param endTime
     * @param response
     */
    void popExportOrderList(OrderAndAllStatusReqDTO orderAndAllStatus,LoginUserDetail loginUser,Integer tabTime,Date beginTime,Date endTime,HttpServletResponse response);

    /**
     * 异步导出供货中心销售订单
     */
    void exportPurchaserCenterSaleOrder( QueryPurchaserOrderListDTO reqDto,LoginUserDetail loginUserDetail);
}
