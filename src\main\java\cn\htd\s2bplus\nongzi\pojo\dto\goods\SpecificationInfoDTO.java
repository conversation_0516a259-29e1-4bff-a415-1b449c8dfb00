package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class SpecificationInfoDTO implements Serializable {
    private static final long serialVersionUID = -1344397510189686806L;


    @ApiModelProperty(value = "商家ID")
    private Long sellerId;

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
