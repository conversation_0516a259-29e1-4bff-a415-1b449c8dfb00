package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

@Data
public class SubmitAssignmentExecutorVO {


    @ApiModelProperty(value = "员工工号")
    @Excel(name = "员工账号", height = 10, width = 20)
    private String employeeId;

    @ApiModelProperty(value = "员工姓名")
    @Excel(name = "员工姓名", height = 10, width = 20)
    private String employeeName;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注", height = 10,width = 20)
    private String remarks;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
