package cn.htd.s2bplus.nongzi.enums;

/**
 * <AUTHOR>
 */
public enum BatchGuestOrderReportStatusEnum {

    ORDER_CREATION("0", "订单创建中"),
    ORDER_CREATE_SUCCESS("1", "创单成功"),
    ORDER_CREATE_FAIL("2", "创单失败"),

    ;

    private String status;
    private String description;

    BatchGuestOrderReportStatusEnum(String status, String description) {
        this.status = status;
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public String getDescription() {
        return description;
    }
}
