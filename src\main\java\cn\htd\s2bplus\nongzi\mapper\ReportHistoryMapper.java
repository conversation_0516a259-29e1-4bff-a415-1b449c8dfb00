package cn.htd.s2bplus.nongzi.mapper;


import cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory;
import cn.htd.s2bplus.nongzi.pojo.dto.common.Pager;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ReportHistoryMapper {
    /**
     *
     *  2020-05-09
     */
    int deleteByPrimaryKey(Long id);

    /**
     *
     *  2020-05-09
     */
    int insert(ReportHistory record);

    /**
     *
     *  2020-05-09
     */
    boolean insertSelective(ReportHistory record);

    /**
     *
     *  2020-05-09
     */
    ReportHistory selectByPrimaryKey(Long id);

    /**
     *
     *  2020-05-09
     */
    int updateByPrimaryKeySelective(ReportHistory record);

    /**
     *
     *  2020-05-09
     */
    int updateByPrimaryKey(ReportHistory record);


    /**
     * 分页查询报表生成下载历史
     * @param finishBeginTime
     * @param finishEndtime
     * @param page
     * @return
     */
    List<ReportHistory> queryReportHistoryPage(@Param("finishBeginTime") Date finishBeginTime, @Param("finishEndTime") Date finishEndtime, @Param("memberId") Long memberId,@Param("businessType") Integer businessType, @Param("page") Pager page);


    /**
     * 查询报表生成下载历史总条数
     * @param finishBeginTime
     * @param finishEndTime
     * @return
     */
    Integer queryReportHistoryCount(@Param("finishBeginTime") Date finishBeginTime, @Param("finishEndTime") Date finishEndTime,@Param("memberId") Long memberId,@Param("businessType") Integer businessType);

    /**
     * 查询报表生成下载历史总条数
     * @param bean
     * @return
     */
    Integer queryReportHistoryListCount(ReportHistory bean);
}
