package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title AuthChannelDTO
 * @Date: 2024/2/28 14:32
 */
@Data
public class AuthChannelDTO implements Serializable {
    private static final long serialVersionUID = 77108323811348152L;

    @ApiModelProperty(value = "渠道编码,对应中台appCode")
    private String channelCode;

    @ApiModelProperty(value = "渠道名称,对应中台appName")
    private String externalChannelName;

    @ApiModelProperty(value = "渠道类型 0:内部渠道，1:第三方渠道")
    private Integer appType;

    @ApiModelProperty(value = "中台渠道id")
    private Long appId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
