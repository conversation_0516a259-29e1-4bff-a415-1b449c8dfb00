package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class VirtualInventoryExcelDTO implements Serializable {

    @Excel(name = "* 商品SKU编码（单次导入上限：200个）",fixedIndex = 0,width = 30,orderNum = "1")
    private String skuCode;

    @Excel(name = "* 剩余可卖虚拟库存（大于0，小于100000）",fixedIndex = 1,width = 30,orderNum = "2")
    private BigDecimal stockTotalNum;

    @Excel(name = "* 生效开始时间（时间格式：xxxx-xx-xx xx:xx:xx）",fixedIndex = 2,width = 30,orderNum = "3")
    private String effectiveStartTime;

    @Excel(name = "* 生效结束时间（时间格式：xxxx-xx-xx xx:xx:xx）",fixedIndex = 3,width = 30,orderNum = "4")
    private String effectiveEndTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
