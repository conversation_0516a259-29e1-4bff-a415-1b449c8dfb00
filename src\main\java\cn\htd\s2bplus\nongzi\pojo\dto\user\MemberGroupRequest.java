package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.rdc.base.development.framework.core.mp.support.Query;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class MemberGroupRequest extends Query implements Serializable {

    private static final long serialVersionUID = 9198350717449673827L;

    @ApiModelProperty(value = "会员分组id集合",notes = "会员分组id集合",example = "{'1','2'}")
    private List<String> groupList;

    @ApiModelProperty(value = "会员名称")
    private String name;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
