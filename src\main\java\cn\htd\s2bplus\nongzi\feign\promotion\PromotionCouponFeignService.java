package cn.htd.s2bplus.nongzi.feign.promotion;

import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.coupon.AutoSendCouponDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.coupon.AutoSendCouponRecordDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.coupon.BalanceInformationDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.coupon.BalanceInformationVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @program: s2bplus-promotion-service
 * @description: 活动券 Feign服务
 * @author: ylc
 * @create: 2023-11-08 12:30
 **/
@FeignClient(name = "s2bplus-promotion-service")
public interface PromotionCouponFeignService {

    /**
     * 活动发券结余列表
     * @param balanceInformationDTO
     * @param page
     * @param rows
     * @return
     */
    @PostMapping(value = "yc/coupon/queryBalanceInformationPageList")
    PageResult<List<BalanceInformationVO>> queryBalanceInformationPageList(@RequestBody BalanceInformationDTO balanceInformationDTO,
                                                                           @RequestParam("page")Integer page,
                                                                           @RequestParam("rows")Integer rows);
    /**
     * 根据会员编码查询最新结余
     * @param sellerCode
     * @param memberCodeList
     * @return
     */
    @ApiOperation(value = "根据会员编码查询最新结余", notes = "根据会员编码查询最新结余")
    @PostMapping(value = "yc/coupon/queryLatestRemainAmountByMemberCodeList")
    Result<List<AutoSendCouponRecordDTO>> queryLatestRemainAmountByMemberCodeList(@RequestParam("sellerCode")String sellerCode,
                                                                                  @RequestParam("memberCodeList")List<String> memberCodeList);

    @PostMapping(value = "yc/coupon/calculateAutoSendCouponAmount")
    @ApiOperation("计算自动发券金额")
    Result<List<AutoSendCouponRecordDTO>> calculateAutoSendCouponAmount(@RequestBody AutoSendCouponDTO autoSendCouponDTO);
}
