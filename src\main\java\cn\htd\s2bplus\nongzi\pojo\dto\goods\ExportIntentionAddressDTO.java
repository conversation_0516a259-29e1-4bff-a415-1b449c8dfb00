package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;


@Data
public class ExportIntentionAddressDTO {
    @ApiModelProperty(value = "买家编码")
    @Excel(name = "会员店htd编码", width = 20,orderNum = "2")
    private String buyerCode;

    @ApiModelProperty(value = "appleId")
    @Excel(name = "appleId", width = 20,orderNum = "3")
    private String appleId;

    @ApiModelProperty(value = "店铺名称")
    @Excel(name = "店铺名称", width = 20,orderNum = "4")
    private String shopName;

    @ApiModelProperty(value = "SKU编码")
    @Excel(name = "SKU编码", width = 20,orderNum = "5")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    @Excel(name = "商品名称", width = 20,orderNum = "6")
    private String itemName;

    @ApiModelProperty(value = "供应商编码")
    @Excel(name = "供应商编码", width = 20,orderNum = "7")
    private String supplierCode;

    @ApiModelProperty(value = "仓库编码")
    @Excel(name = "仓库编码", width = 20,orderNum = "8")
    private String warehouseCode;

    @ApiModelProperty(value = "采购部门名称")
    @Excel(name = "采购部门名称", width = 20,orderNum = "9")
    private String departmentName;

    @ApiModelProperty(value = "商品价格")
    @Excel(name = "单价（元）", width = 20,orderNum = "10")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "商品数量")
    @Excel(name = "数量", width = 20,orderNum = "11")
    private BigDecimal goodsCount;

    @ApiModelProperty(value = "合计金额")
    @Excel(name = "合计金额", width = 20,orderNum = "12")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "订单号")
    @Excel(name = "订单号", width = 20,orderNum = "13")
    private String orderNo;

    @ApiModelProperty(value = "串码集合")
    @Excel(name = "串码", width = 20,orderNum = "14")
    private String snList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
