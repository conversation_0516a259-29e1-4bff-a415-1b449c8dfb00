package cn.htd.s2bplus.nongzi.pojo.dto.purchase;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title LogisticInfoDTO
 * @Date: 2023/9/21 15:04
 */
@Data
public class LogisticInfoDTO implements Serializable {

    private static final long serialVersionUID = 3516541678552810637L;

    @ApiModelProperty(value = "供应商实际发货方式 1:快递公司承运(ERP:第三方物流),2:派车配送(erp:供应商配送) 3:其他(erp:自提)  4:线上发货（汇送达）")
    private String realDeliveryType;

    @ApiModelProperty(value = "物流公司名称")
    private String logisticCompanyName;

    @ApiModelProperty(value = "货运方式 1-快递 2-汽配 3-火车 4-轮船")
    private String freightMethod;

    @ApiModelProperty(value = "物流单号")
    private String logisticNo;

    @ApiModelProperty(value = "发货时间 yyyy-MM-dd HH:mm:ss")
    private String deliveryTime;

    @ApiModelProperty(value = "发货地址-省")
    private String deliveryProvince;

    @ApiModelProperty(value = "发货地址-市")
    private String deliveryCity;

    @ApiModelProperty(value = "发货地址-区")
    private String deliveryDistrict;

    @ApiModelProperty(value = "发货地址-镇")
    private String deliveryTown;

    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @ApiModelProperty(value = "区编码")
    private String districtCode;

    @ApiModelProperty(value = "镇编码")
    private String townCode;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "详细地址-加密")
    private String dsDetailAddress;

    @ApiModelProperty(value = "联系人")
    private String deliveryName;

    @ApiModelProperty(value = "联系手机")
    private String deliveryPhone;

    @ApiModelProperty(value = "联系手机加密")
    private String dsDeliveryPhone;

    @ApiModelProperty(value = "附件URL集合")
    private List<String> appendixUrl;

    @ApiModelProperty(value = "发货车号")
    private String carNo;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机联系方式")
    private String driverPhone;

    @ApiModelProperty(value = "司机联系方式-密文")
    private String dsDriverPhone;

    @ApiModelProperty(value = "取件码")
    private String pickUpCode;

    @ApiModelProperty(value = "操作时间 yyyy-MM-dd HH:mm:ss")
    private String operateTime;


    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
