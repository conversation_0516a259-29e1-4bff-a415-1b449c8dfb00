package cn.htd.s2bplus.nongzi.service.promotion.impl;

import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.rdc.base.development.framework.core.util.StringUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.enums.ResultEnum;
import cn.htd.s2bplus.nongzi.feign.user.UserService;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.promotion.MerchantInfoDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.MemberBaseInfoDTO;
import cn.htd.s2bplus.nongzi.service.promotion.PromotionInfoService;
import cn.htd.s2bplus.nongzi.utils.FileCheckUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title PromotionInfoServiceImpl
 * @Date: 2024/7/20 13:43
 */
@Slf4j
@Service
public class PromotionInfoServiceImpl implements PromotionInfoService {
    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;

    @Autowired
    private UserService userService;
    /**
     * @description 解析导入商家信息
     *
     * @param file
     * @return
     */
    @Override
    public Result<List<MerchantInfoDTO>> analysisMerchantExcel(MultipartFile file) {
        try {
            int importMaxCount = nongZiNacosConfig.getImportMerchantCount();
            // 校验文件
            FileCheckUtil.checkFile(file);

            List<MerchantInfoDTO> list = this.analysisMerchantInfo(file, importMaxCount);
            return ResultUtil.success(list);
        }catch (BusinessException e) {
            log.info("解析导入商家信息失败:{}", e.getMessage());
            return ResultUtil.error(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("解析导入商家信息失败异常:", e);
            return ResultUtil.error(ApiResultEnum.ERROR.getCode(),"解析导入商家信息失败");
        }
    }

    /**
     * 解析商家信息
     *
     * @param file
     * @param importMaxCount
     * @return
     * @throws IOException
     */
    private List<MerchantInfoDTO> analysisMerchantInfo(MultipartFile file, int importMaxCount) throws IOException {
        List<MerchantInfoDTO> list = new ArrayList<>();

        StringBuilder errorMsg = new StringBuilder();
        //将excel解析成list
        EasyExcel.read(file.getInputStream(), MerchantInfoDTO.class, new ReadListener<MerchantInfoDTO>() {
            public static final int BATCH_COUNT = 100;
            private final List<MerchantInfoDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

            @Override
            public void invoke(MerchantInfoDTO importMerchantDTO, AnalysisContext context) {
                int rowIndex = context.readSheetHolder().getRowIndex() + 1;
                int rowNumber = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
                if (rowNumber == 0) {
                    throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
                }
                if (rowNumber > importMaxCount) {
                    throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_MAX_ERROR.getMsg() + importMaxCount + "条");
                }
                if(!StringUtil.isBlank(importMerchantDTO.getSellerCode())) {
                    importMerchantDTO.setSellerName(this.checkMemberCode(importMerchantDTO.getSellerCode(),rowIndex));
                } else {
                    errorMsg.append("第").append(rowIndex).append("行").append(ApiResultEnum.MEMBER_CODE_ERROR.getMsg()).append(",");
                }

                if (!CollectionUtils.isEmpty(cachedDataList)) {
                    for (int i=0;i<=cachedDataList.size()-1;i++) {
                        if (importMerchantDTO.getSellerCode().equals(cachedDataList.get(i).getSellerCode())) {
                            throw new BusinessException(ResultEnum.ERROR.getCode(), "导入的商家账号"+importMerchantDTO.getSellerCode()+"存在重复，请检查");
                        }
                    }
                }
                cachedDataList.add(importMerchantDTO);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                if (!CollectionUtils.isEmpty(cachedDataList)) {
                    list.addAll(cachedDataList);
                }
            }

            @Override
            public void onException(Exception exception, AnalysisContext context) throws Exception {
                log.info("解析导入商家信息失败 :{}", exception.getMessage());
                if (exception instanceof BusinessException) {
                    throw new BusinessException(ResultEnum.ERROR.getCode(), exception.getMessage());
                } else if (exception instanceof ExcelDataConvertException) {
                    ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                    String errorMsg = ApiResultEnum.IMPORT_ERROR_LINE_COLUMN.getMsg() + "解析异常";
                    Integer rowIndex = excelDataConvertException.getRowIndex() + 1;
                    Integer columnIndex = excelDataConvertException.getColumnIndex() + 1;
                    log.info("解析导入商家信息失败,第{}行，第{}列解析异常，数据为:{}", rowIndex, columnIndex, JSON.toJSONString(excelDataConvertException.getCellData()));
                    throw new BusinessException(ResultEnum.ERROR.getCode(), String.format(errorMsg, rowIndex, columnIndex));
                }
            }

            /**
             * 校验会员编码
             *
             * @param memberCode
             */
            private String checkMemberCode(String memberCode,int rowIndex) {
                log.info("查询会员基本信息入参:{}",memberCode);
                Result<MemberBaseInfoDTO> result = userService.memberBaseInfo(memberCode);
                log.info("查询会员基本信息出参:{}",result);
                if (!result.isSuccess() || ObjectUtils.isEmpty(result.getData()) || StringUtils.isEmpty(result.getData().getCompanyName())) {
                    throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rowIndex + "行商家账号输入错误");
                }
                return result.getData().getCompanyName();
            }
        }).sheet().doRead();
        if (errorMsg.length() > 0) {
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), errorMsg.toString());
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
        }
        return list;
    }
}
