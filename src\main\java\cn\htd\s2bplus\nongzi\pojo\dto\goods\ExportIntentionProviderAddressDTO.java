package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
@Data
public class ExportIntentionProviderAddressDTO implements Serializable {

    @ApiModelProperty(value = "服务商编号")
    @Excel(name = "服务商编号", width = 20,orderNum = "1")
    private String serviceProviderCode;

    @ApiModelProperty(value = "服务商姓名")
    @Excel(name = "服务商姓名", width = 20,orderNum = "2")
    private String serviceProviderName;

    @ApiModelProperty(value = "买家编码")
    @Excel(name = "代收客户编码（htd）", width = 20,orderNum = "3")
    private String buyerCode;

    @ApiModelProperty(value = "买家姓名")
    @Excel(name = "客户名称", width = 20,orderNum = "4")
    private String buyerName;

    @ApiModelProperty(value = "apple_id")
    @Excel(name = "代收客户Apple id", width = 20,orderNum = "5")
    private String appleId;

    @ApiModelProperty(value = "收货地址ID")
    @Excel(name = "地址ID", width = 20,orderNum = "6")
    private Long consigneeId;

    @ApiModelProperty(value = "收货人姓名")
    @Excel(name = "收货人姓名", width = 20,orderNum = "7")
    private String consigneeName;

    @ApiModelProperty(value = "收货人手机号码",example = "13100000001")
    @Excel(name = "收货人电话", width = 20,orderNum = "8")
    private String consigneeMobile;

    @ApiModelProperty(value = "收货地址-省",example = "江苏省")
    @Excel(name = "收货地址-省", width = 20,orderNum = "9")
    private String consigneeAddressProvinceStr;

    @ApiModelProperty(value = " 收货地址-市",example = "南京市")
    @Excel(name = "收货地址-市", width = 20,orderNum = "10")
    private String consigneeAddressCityStr;

    @ApiModelProperty(value = "收货地址-区",example = "玄武区")
    @Excel(name = "收货地址-区", width = 20,orderNum = "11")
    private String consigneeAddressDistrictStr;

    @ApiModelProperty(value = "收货地址-镇",example = "麒麟镇")
    @Excel(name = "收货地址-镇", width = 20,orderNum = "12")
    private String consigneeAddressTownStr;

    @ApiModelProperty(value = "收货地址-详细",example = "柳营西路50号")
    @Excel(name = "收货地址-详细", width = 20,orderNum = "13")
    private String consigneeAddressDetail;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
