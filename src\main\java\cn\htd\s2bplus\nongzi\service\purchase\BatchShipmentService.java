package cn.htd.s2bplus.nongzi.service.purchase;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.enums.ImportBusinessTypeEnum;
import cn.htd.s2bplus.nongzi.enums.PurchaseOrderTypeEnum;
import cn.htd.s2bplus.nongzi.enums.ResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.*;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.service.goods.GoodsService;
import cn.htd.s2bplus.nongzi.utils.*;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Service
@Slf4j
public class BatchShipmentService {

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;

    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;

    @Autowired
    private AsyncOrderDelivery asyncOrderDelivery;

    @Autowired
    private GoodsService goodsService;

    private static final String TEMP_DIR_PATH = System.getProperty("java.io.tmpdir"); // 获取系统临时目录

    private static final String UPLOAD_FILE_PREFIX = "/batchShipment" ;

    /**
     * 批量导入商品excel表头
     */
    private static final List<String> BATCH_IMPORT_GOODS_EXCEL_HEADERS = Arrays.asList(
            "商品货号（选填）", "四级管理类目ID", "三级销售类目ID", "品牌ID", "型号","单位",
            "商品名称","规格名称1","规格值1","规格名称2（选填）","规格值2（选填）","规格名称3（选填）","规格值3（选填）",
            "库存数量","商品条形码（选填）","商品图片文件夹","商品图片名称","规格图片名称（选填）","详情图片名称（选填）","商品详情","发布店铺名称","基础售价");

    /**
     * 囤货模式导入表头
     */
    private static final List<String> STOCK_UP_ORDER_EXPECTED_HEADERS = Arrays.asList("提单号", "发货数量", "发货方式（1:快递公司承运）", "物流单号", "发货时间（yyyy-mm-dd）", "附件文件名称");

    /**
     * 直发模式导入表头
     */
    private static final List<String> DIRECT_ORDER_EXPECTED_HEADERS = Arrays.asList("提单号", "配送方式（1:供应商配送）", "发货方式（1:快递公司承运）", "物流公司编码",
            "货运方式（1:快递2:汽配3:火车4:轮船）", "物流单号", "发货时间（yyyy-mm-dd）", "发货地址ID", "附件文件名称");

    /**
     *  一、处理压缩文件（.rar 或 .zip），并存储到临时目录
     *  二、解压文件到临时目录，查找主Excel文件解析，上传解析出来的附件至OSS
     *  三、校验Excel中解析出来的数据是否合格，循环调用发货接口
     *  四、将错误结果生成Excel上传OSS、落库备查
     *
     * @param file 上传的文件
     * @param orderType 订单类型 0:囤货订单,1:直发订单,2:批量导入商品发布
     * @return 操作结果
     */
    public Result<Boolean> handleCompressedFile(MultipartFile file, String orderType, LoginUserDetail user) {
        Result<Boolean> result = new Result<>();
        try {
            // 校验文件是否为空
            if (ObjectUtils.isEmpty(file) || StringUtils.isEmpty(file.getOriginalFilename())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }

            // 临时子目录ID
            String uUId = UUID.randomUUID().toString();

            // 创建临时目录
            Path tempDir = Paths.get(TEMP_DIR_PATH, uUId);
            Files.createDirectories(tempDir);

            // 将文件保存到临时目录
            Path targetFilePath = tempDir.resolve(file.getOriginalFilename());
            FileUtils.copyInputStreamToFile(file.getInputStream(), targetFilePath.toFile());


            log.info("压缩文件已成功存储到临时目录: {}", targetFilePath.toFile());

            // 【核心改进】查找当前临时目录下唯一的 zip 或 rar 文件
            File[] filesInTempDir = tempDir.toFile().listFiles((dir, name) -> name.toLowerCase().endsWith(".zip") || name.toLowerCase().endsWith(".rar"));
            if (filesInTempDir == null || filesInTempDir.length == 0) {
                throw new IOException("未找到有效的 ZIP 或 RAR 文件，请确认压缩包已正确上传");
            } else if (filesInTempDir.length > 1) {
                throw new IOException("临时目录中包含多个压缩文件，请确保只上传一个压缩包");
            }

            Path actualFilePath = filesInTempDir[0].toPath();
            String actualFilename = actualFilePath.getFileName().toString();
            log.info("检测到压缩文件: {}", actualFilename);

            // 判断文件扩展名并调用相应的解压方法
            String extension = actualFilename.substring(actualFilename.lastIndexOf(".") + 1).toLowerCase();
            if ("zip".equals(extension)) {
                ZipUtil.unzipWithCharsetGBK(actualFilePath.toString(), TEMP_DIR_PATH + File.separator + uUId);
            } else if ("rar".equals(extension)) {
                RarUtil.unrar(actualFilePath.toString(), TEMP_DIR_PATH + File.separator + uUId);
            } else {
                throw new IOException("不支持的文件格式：" + extension);
            }

            // 批量导入商品发布业务
            if (ImportBusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS.getType().equals(orderType)) {
                // 压缩包中图片文件集合
                List<FileInfoDTO> picFiles = new ArrayList<>();
                // 解析压缩包中excel的商品信息
                List<BatchImportGoodsDTO> batchImportGoodsDTOS = this.parsingBatchImportGoods(TEMP_DIR_PATH + File.separator + uUId,picFiles,user);
                log.info("压缩包：{}中包含的excel文件：{}所有图片文件：{}",actualFilename,batchImportGoodsDTOS,picFiles);

                // 异步校验并保存商品
                goodsService.checkAndSaveImportGoods(batchImportGoodsDTOS,picFiles,uUId,user);

                // 异步保存图片至商品图库
                goodsService.batchSaveGoodsImage(picFiles,uUId,user);
                return CommonResultUtil.success(true);
            }

            //处理文件并解析成list
            if (Objects.equals(orderType, PurchaseOrderTypeEnum.STOCK_UP.getType())) {
                // 处理失败的数据列表
                List<StockUpOrderBatchShipmentRecordDTO> errList = new ArrayList<>();

                // 处理囤货订单,获取指定目录下的批量发货Excel文件并解析为 囤货订单 DTO列表
                List<StockUpOrderBatchShipmentDTO> batchShipmentDTOList = this.processBatchShipmentFiles(TEMP_DIR_PATH + File.separator + uUId);
                //处理压缩包中的附件信息
                this.filterAndSortBatchShipments(batchShipmentDTOList, errList, uUId);
                // 输出处理结果
                log.info("可提交处理的发货单数量：{}", batchShipmentDTOList.size());
                log.info("参数异常的发货单数量：{}", errList.size());
                // 将异常数据和正常数据&登陆用户信息，提交接口封装发货参数，调用供应商相关接口 - 订单类型 0:囤货订单
                asyncOrderDelivery.stockUpOrderDelivery(batchShipmentDTOList, errList, BaseContextHandler.getLoginUser());
            }else {
                // 处理失败的数据列表
                List<DirectOrderBatchShipmentRecordDTO> errList = new ArrayList<>();

                // 处理直发订单,获取指定目录下的批量发货Excel文件并解析为 直发订单 DTO列表
                List<DirectOrderBatchShipmentDTO> batchShipmentDTOList = this.processDirectOrderBatchShipmentFiles(TEMP_DIR_PATH + File.separator + uUId);
                //处理压缩包中的附件信息
                this.filterDirectOrderBatchShipments(batchShipmentDTOList, errList, uUId);

                // 输出处理结果
                log.info("可提交处理的发货单数量：{}", batchShipmentDTOList.size());
                log.info("参数异常的发货单数量：{}", errList.size());

                // 将异常数据和正常数据提交接口封装发货参数，调用供应商相关接口 - 订单类型 1:直发订单
                asyncOrderDelivery.directOrderDelivery(batchShipmentDTOList, errList, BaseContextHandler.getLoginUser());
            }

        } catch (BusinessException be) {
            log.info("处理压缩文件业务异常: {}", be.getMessage());
            result.setCode(be.getCode());
            result.setMsg(be.getMessage());
            return result;
        } catch (IOException e) {
            log.error("处理压缩文件时发生IO异常", e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("处理压缩文件时发生IO异常:" + e.getMessage());
            return result;
        } catch (Exception e) {
            log.error("处理压缩文件时发生未知异常", e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("处理压缩文件时发生未知异常:" + e.getMessage());
            return result;
        }

        return CommonResultUtil.success(true);
    }


    /**
     * 解析批量导入商品的压缩包
     */
    private List<BatchImportGoodsDTO> parsingBatchImportGoods(String directoryPath, List<FileInfoDTO> picFiles,LoginUserDetail user) {
        List<BatchImportGoodsDTO> resultList = new ArrayList<>();
        try {
            List<Path> filePathList = FileUtil.findExcelAndImageFiles(directoryPath);
            log.info("打印压缩包内所有文件路径：{}", filePathList);
            if(CollectionUtils.isEmpty(filePathList)) {
                throw new BusinessException(ResultEnum.BATCH_IMPORT_ITEM_FILE_IS_NULL.getCode(), ResultEnum.BATCH_IMPORT_ITEM_FILE_IS_NULL.getMsg());
            }
            // 文件基础校验
            this.checkFilesCount(filePathList);
            for (Path path : filePathList) {
                if (FileUtil.isExcelFile(path.getFileName().toString())) {
                    // 表头校验
                    try (InputStream inputStream = Files.newInputStream(path)) {
                        boolean checkResult = validateExcelHeader(inputStream, ImportBusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS.getType());
                        if (!checkResult) {
                            throw new BusinessException(ResultEnum.IMPORT_FILE_HEAD_ERROR.getCode(), ResultEnum.IMPORT_FILE_HEAD_ERROR.getMsg());
                        }
                    } catch (IOException e) {
                        log.info("校验批量导入商品excel表头异常:{}", e.getMessage());
                        throw new BusinessException(ResultEnum.IMPORT_FILE_READ_ERROR.getCode(), ResultEnum.IMPORT_FILE_READ_ERROR.getMsg());
                    }


                    // 解析excel数据
                    try (InputStream inputStream = Files.newInputStream(path)) {
                        ImportParams params = new ImportParams();
                        params.setTitleRows(0);
                        params.setStartSheetIndex(0);

                        ExcelImportResult<BatchImportGoodsDTO> resultExcel = ExcelImportUtil.importExcelMore(
                                inputStream, BatchImportGoodsDTO.class, params);
                        List<BatchImportGoodsDTO> batchImportGoodsDTOS =  resultExcel.getList();

                        if (CollectionUtils.isEmpty(batchImportGoodsDTOS)) {
                            throw new BusinessException(ResultEnum.BATCH_IMPORT_EXCEL_IS_NULL.getCode(), ResultEnum.BATCH_IMPORT_EXCEL_IS_NULL.getMsg());
                        }
                        resultList.addAll(batchImportGoodsDTOS);
                        // 保存导入的excel记录
                        goodsService.saveImportUploadRecordList(batchImportGoodsDTOS, user);
                        log.info("解析批量导入商品excel结果：{}：{}", path.getFileName().toString(), batchImportGoodsDTOS);
                    } catch (IOException ie) {
                        log.info("读取批量导入商品文件时发生错误:{}", ie.getMessage());
                        throw new BusinessException(ResultEnum.IMPORT_FILE_READ_ERROR.getCode(), ResultEnum.IMPORT_FILE_READ_ERROR.getMsg());
                    } catch (Exception e) {
                        log.info("读取批量导入商品文件时发生异常:{}", e.getMessage());
                        throw new BusinessException(ResultEnum.IMPORT_FILE_READ_ERROR.getCode(), ResultEnum.IMPORT_FILE_READ_ERROR.getMsg());
                    }
                }

                // 解析图片文件
                if(FileUtil.isImageFile(path.getFileName().toString())){
                    try (InputStream inputStream = Files.newInputStream(path)) {
                        // 上传图片至OSS
                        OssUtils ossUtils = new OssUtils();
                        String downloadUrl = ossUtils.upload(UPLOAD_FILE_PREFIX, inputStream,
                                DateUtil.getDaySS() + "_" + path.getFileName().toString(), bucket, endpoint, accessKeyId, accessKeySecret);
                        log.info("上传图片至OSS后返回：{}", downloadUrl);
                        if (StringUtils.isNotBlank(downloadUrl)) {
                            downloadUrl = URLDecoder.decode(downloadUrl, "UTF-8");
                            FileInfoDTO fileInfoDTO = new FileInfoDTO();
                            fileInfoDTO.setImageOssUrl(downloadUrl.substring(downloadUrl.indexOf("/b2b")));
                            fileInfoDTO.setFileName(path.getFileName().toString());
                            fileInfoDTO.setParentFolders(path.getParent().getFileName().toString());
                            picFiles.add(fileInfoDTO);
                        }
                    }catch (IOException ie){
                        log.info("解析文件夹图片失败：{}",ie.getMessage());
                    }catch (Exception e){
                        log.info("解析文件夹图片异常",e);
                    }
                }
            }
            return resultList;
        } catch (IOException ie) {
            log.info("读取批量导入商品文件时发生异常");
        }
        return resultList;
    }


    /**
     *
     * 校验文件数量
     */
    public void checkFilesCount(List<Path> filePathList) {
        // 遍历文件列表，统计Excel、image文件数量
        int excelFileCount = 0;
        int imageFileCount = 0;
        for (Path path : filePathList) {
            if(FileUtil.isExcelFile(path.getFileName().toString())){
                excelFileCount++;
            }else if(FileUtil.isImageFile(path.getFileName().toString())){
                imageFileCount++;
            }
        }
        if (excelFileCount == 0) {
            throw new BusinessException(ResultEnum.BATCH_IMPORT_EXCEL_NOT_EXIST.getCode(), ResultEnum.BATCH_IMPORT_EXCEL_NOT_EXIST.getMsg());
        } else if (excelFileCount > 1) {
            throw new BusinessException(ResultEnum.BATCH_IMPORT_CONTAIN_MULTIPLE_EXCEL.getCode(), ResultEnum.BATCH_IMPORT_CONTAIN_MULTIPLE_EXCEL.getMsg());
        }
        if(imageFileCount == 0){
            throw new BusinessException(ResultEnum.BATCH_IMPORT_ITEM_IMAGE_IS_NULL.getCode(), ResultEnum.BATCH_IMPORT_ITEM_IMAGE_IS_NULL.getMsg());
        }
    }

    /**
     *
     * @param batchShipmentDTOList
     * @param errList
     * @param uUId
     * @throws Exception
     */
    private void filterAndSortBatchShipments(List<StockUpOrderBatchShipmentDTO> batchShipmentDTOList, List<StockUpOrderBatchShipmentRecordDTO> errList, String uUId) throws Exception{
        // 使用迭代器遍历并处理发货信息
        if (CollectionUtils.isNotEmpty(batchShipmentDTOList)) {
            //存储附件信息map，key：附件名称，value：附件路径
            Map<String, String> appendixMap = new HashMap<>();
            Iterator<StockUpOrderBatchShipmentDTO> iterator = batchShipmentDTOList.iterator(); // 创建迭代器
            while (iterator.hasNext()) {
                StockUpOrderBatchShipmentDTO batchShipmentDTO = iterator.next(); // 获取当前元素
                String appendixName = batchShipmentDTO.getAppendixName();
                String deliveryOrderNo = batchShipmentDTO.getDeliveryOrderNo();

                // 若附件名称不为空则处理
                if (StringUtils.isNotEmpty(appendixName)) {
                    //判断同名附件是否已经存在，存在则无需再次上传阿里云，直接赋值附件地址
                    if (appendixMap.containsKey(appendixName)) {
                        log.info("发货单号：{} , 附件：{}已存在，无需再次上传", deliveryOrderNo, appendixName);
                        batchShipmentDTO.setAppendixUrl(appendixMap.get(appendixName));
                        continue;
                    }

                    // 判断解压目录下是否存在对应的发货单附件
                    Path appendixPath = FileUtil.checkFileExists(TEMP_DIR_PATH + File.separator + uUId, appendixName);
                    if (null != appendixPath) {
                        log.info("发货单号：{} 执行发货", deliveryOrderNo);
                        // 读取文件流
                        try (InputStream is = Files.newInputStream(appendixPath)) {
                            // 上传附件文件至OSS
                            OssUtils ossUtils = new OssUtils();
                            String downloadUrl = ossUtils.upload(UPLOAD_FILE_PREFIX, is,
                                    DateUtil.getDaySS() + "_" + appendixName, bucket, endpoint, accessKeyId, accessKeySecret);
                            log.info("发货单号：{} , 附件：{}， 上传OSS后返回：{}", deliveryOrderNo, appendixName, downloadUrl);
                            batchShipmentDTO.setAppendixUrl(downloadUrl);
                        }
                        // 删除解压后的临时文件
                        boolean delResult = Files.deleteIfExists(appendixPath);
                        log.info("del file :{}  return :{}", appendixPath.getFileName(), delResult);
                        //FileUtil.deleteFile(TEMP_DIR_PATH + File.separator + uUId + File.separator + appendixName);
                    } else {
                        // 添加到错误列表，并从原始列表中移除
                        StockUpOrderBatchShipmentRecordDTO errDto = new StockUpOrderBatchShipmentRecordDTO();
                        BeanUtil.copy(batchShipmentDTO, errDto);
                        errDto.setErrorReason("没有上传对应的附件:" + appendixName);
                        errList.add(errDto);
                        iterator.remove(); // 从原始列表中移除当前元素
                        log.info("发货单号：{} 没有上传对应的附件：{} ，此数据不处理", deliveryOrderNo, appendixName);
                    }
                } else {
                    // 添加到错误列表，并从原始列表中移除
                    StockUpOrderBatchShipmentRecordDTO errDto = new StockUpOrderBatchShipmentRecordDTO();
                    BeanUtil.copy(batchShipmentDTO, errDto);
                    errDto.setErrorReason("附件名称为空");
                    errList.add(errDto);
                    iterator.remove(); // 从原始列表中移除当前元素
                    log.info("发货单号：{} 没有上传对应的附件，此数据不处理", deliveryOrderNo);
                }
            }
        }
    }

    /**
     * 过滤直发订单-发货信息中的附件
     * @param batchShipmentDTOList
     * @param errList
     * @param uUId
     * @throws Exception
     */
    private void filterDirectOrderBatchShipments(List<DirectOrderBatchShipmentDTO> batchShipmentDTOList, List<DirectOrderBatchShipmentRecordDTO> errList, String uUId) throws Exception{
        if (CollectionUtils.isEmpty(batchShipmentDTOList)) {
            return;
        }
        List<DirectOrderBatchShipmentDTO> validShipments = new ArrayList<>();
        //存储附件信息map，key：附件名称，value：附件路径
        Map<String, String> appendixMap = new HashMap<>();
        batchShipmentDTOList.forEach(dto -> {
            String appendixName = dto.getAppendixName();
            String deliveryOrderNo = dto.getDeliveryOrderNo();

            if (StringUtils.isEmpty(appendixName)) {
                addToErrorList(dto, errList, "附件名称为空");
                log.info("发货单号：{} 没有上传对应的附件，此数据不处理", deliveryOrderNo);
                return;
            }

            //判断同名附件是否已经存在，存在则无需再次上传阿里云，直接赋值附件地址
            if (appendixMap.containsKey(appendixName)) {
                log.info("发货单号：{} , 附件：{}已存在，无需再次上传", deliveryOrderNo, appendixName);
                dto.setAppendixUrl(appendixMap.get(appendixName));
                //将附件信息放入validShipments中
                validShipments.add(dto);
                return;
            }

            // 检查在指定目录下是否存在某个文件
            Path appendixPath = FileUtil.checkFileExists(TEMP_DIR_PATH + java.io.File.separator + uUId, appendixName);
            if (appendixPath == null) {
                addToErrorList(dto, errList, "没有上传对应的附件:" + appendixName);
                log.info("发货单号：{} 没有上传对应的附件：{} ，此数据不处理", deliveryOrderNo, appendixName);
                return;
            }

            log.info("发货单号：{} 执行附件上传", deliveryOrderNo);
            //上传附件文件至OSS服务器
            try (InputStream is = Files.newInputStream(appendixPath)) {
                OssUtils ossUtils = new OssUtils();
                String downloadUrl = ossUtils.upload(UPLOAD_FILE_PREFIX, is,
                        DateUtil.getDaySS() + "_" + appendixName, bucket, endpoint, accessKeyId, accessKeySecret);
                log.info("发货单号：{} , 附件：{}， 上传OSS后返回：{}", deliveryOrderNo, appendixName, downloadUrl);
                dto.setAppendixUrl(downloadUrl);
                //放入appendixMap中
                appendixMap.put(appendixName, downloadUrl);
                //将附件信息放入validShipments中
                validShipments.add(dto);
            } catch (Exception e) {
                log.error("发货单号：{} 上传附件时出错", deliveryOrderNo, e);
                addToErrorList(dto, errList, "上传附件时出错: " + e.getMessage());
            }
            //删除临时文件
            try {
                boolean delResult = Files.deleteIfExists(appendixPath);
                log.info("del file :{}  return :{}", appendixPath.getFileName(), delResult);
            } catch (Exception e) {
                log.error("发货单号：{} 删除临时文件时出错", deliveryOrderNo, e);
            }
        });
        batchShipmentDTOList.clear();
        batchShipmentDTOList.addAll(validShipments);
    }

    private void addToErrorList(DirectOrderBatchShipmentDTO dto, List<DirectOrderBatchShipmentRecordDTO> errList, String errorReason) {
        DirectOrderBatchShipmentRecordDTO errDto = new DirectOrderBatchShipmentRecordDTO();
        BeanUtil.copy(dto, errDto);
        errDto.setErrorReason(errorReason);
        errList.add(errDto);
    }

    /**
     * 获取指定目录下的批量发货Excel文件并解析为DTO列表
     *
     * @param directoryPath 文件目录路径
     * @return 解析后的 囤货订单 BatchShipmentDTO 列表
     */
    public List<StockUpOrderBatchShipmentDTO> processBatchShipmentFiles(String directoryPath) throws Exception{
        List<StockUpOrderBatchShipmentDTO> result = new ArrayList<>();

        // 是否存在主Excel文件
        boolean isExistsRootFile = false;

        // 获取目录下的所有文件
        List<Path> fileList = FileUtil.getFilesInDirectory(directoryPath);
        if (CollectionUtils.isNotEmpty(fileList)) {
            // 提前过滤出符合条件的文件
            for (Path filePath : fileList) {
                if (isBatchShipmentFile(filePath)) {

                    log.info("正在读取文件: {}", filePath.getFileName());

                    // 表头校验
                    try (InputStream inputStream = Files.newInputStream(filePath)) {
                        boolean checkResult = validateExcelHeader(inputStream, PurchaseOrderTypeEnum.STOCK_UP.getType());
                        if(checkResult == false){
                            throw new BusinessException(ResultEnum.IMPORT_FILE_HEAD_ERROR.getCode(),ResultEnum.IMPORT_FILE_HEAD_ERROR.getMsg());
                        }
                    }

                    // 仅对符合条件的文件创建文件流
                    try (InputStream inputStream = Files.newInputStream(filePath)) {
                        // 解析 Excel 文件
                        ImportParams params = new ImportParams();
                        params.setTitleRows(0);
                        params.setStartSheetIndex(0);

                        ExcelImportResult<StockUpOrderBatchShipmentDTO> resultExcel = ExcelImportUtil.importExcelMore(
                                inputStream, StockUpOrderBatchShipmentDTO.class, params);
                        List<StockUpOrderBatchShipmentDTO> batchShipmentDTOList = resultExcel.getList();

                        log.info("正在读取文件: {} 结果：{}", filePath.getFileName(), batchShipmentDTOList);

                        // 将解析结果添加到最终结果列表中
                        result.addAll(batchShipmentDTOList);

                        isExistsRootFile = true;

                    } catch (Exception e) {
                        log.error("读取模板文件时发生错误: {}", e);
                        throw new BusinessException(ResultEnum.IMPORT_FILE_READ_ERROR.getCode(),ResultEnum.IMPORT_FILE_READ_ERROR.getMsg());
                    }
                }
            }
        }

        if(isExistsRootFile == false){
            throw new BusinessException(ResultEnum.IMPORT_FILE_NO_ROOT_FILE.getCode(),ResultEnum.IMPORT_FILE_NO_ROOT_FILE.getMsg());
        }
        if (CollectionUtils.isEmpty(result)) {
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), "导入文件不能为空，请核对");
        }
        if (result.size() > nongZiNacosConfig.getBatchShipmentMaxSize()) {
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), "导入文件数量不能超过" + nongZiNacosConfig.getBatchShipmentMaxSize() + "条, 请核对");
        }

        return result;
    }

    /**
     * 验证上传的 Excel 文件表头是否符合预期
     *
     * @param file InputStream 文件
     * @return true 如果表头匹配；false 否则
     */
    private static boolean validateExcelHeader(InputStream file, String orderType) throws IOException {
        // 创建 ImportParams 对象，用于配置导入参数
        ImportParams params = new ImportParams();
        params.setHeadRows(1); // 只读取第一行为表头

        try (Workbook workbook = WorkbookFactory.create(file)) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);

            List<String> actualHeaders = new ArrayList<>();
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                String cellValue = headerRow.getCell(i).getStringCellValue();
                actualHeaders.add(cellValue);
            }

            // 比较实际的表头和预期的表头
            log.info("实际导入表头:{}", JSON.toJSONString(actualHeaders));
            if (ImportBusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS.getType().equals(orderType)) {
                return BATCH_IMPORT_GOODS_EXCEL_HEADERS.equals(actualHeaders);
            } else if ("0".equals(orderType)) {
                return STOCK_UP_ORDER_EXPECTED_HEADERS.equals(actualHeaders);
            } else {
                return DIRECT_ORDER_EXPECTED_HEADERS.equals(actualHeaders);
            }
        } catch (Exception e) {
            throw new IOException("无法读取导入模板 Excel 文件", e);
        }
    }

    /**
     * 判断文件是否是批量发货文件
     *
     * @param filePath 文件路径
     * @return 是否是批量发货文件  供应商直发导入发货/供应商囤货导入发货
     */
    private static boolean isBatchShipmentFile(Path filePath) {
        String fileName = filePath.getFileName().toString();
        return fileName.contains("供应商直发导入发货") || fileName.contains("供应商囤货导入发货");
    }


    /**
     * 获取指定目录下的批量发货Excel文件并解析为 直发订单DTO列表
     * @param directoryPath
     * @return
     * @throws Exception
     */
    public List<DirectOrderBatchShipmentDTO> processDirectOrderBatchShipmentFiles(String directoryPath) throws Exception{
        List<DirectOrderBatchShipmentDTO> result = new ArrayList<>();

        // 是否存在主Excel文件
        boolean isExistsRootFile = false;

        // 获取目录下的所有文件
        List<Path> fileList = FileUtil.getFilesInDirectory(directoryPath);
        if (CollectionUtils.isNotEmpty(fileList)) {
            // 提前过滤出符合条件的文件
            for (Path filePath : fileList) {
                if (isBatchShipmentFile(filePath)) {

                    log.info("正在读取文件: {}", filePath.getFileName());

                    // 表头校验
                    try (InputStream inputStream = Files.newInputStream(filePath)) {
                        boolean checkResult = validateExcelHeader(inputStream, PurchaseOrderTypeEnum.DIRECT.getType());
                        if(checkResult == false){
                            throw new BusinessException(ResultEnum.IMPORT_FILE_HEAD_ERROR.getCode(),ResultEnum.IMPORT_FILE_HEAD_ERROR.getMsg());
                        }
                    }

                    // 仅对符合条件的文件创建文件流
                    try (InputStream inputStream = Files.newInputStream(filePath)) {
                        // 解析 Excel 文件
                        ImportParams params = new ImportParams();
                        params.setTitleRows(0);
                        params.setStartSheetIndex(0);

                        ExcelImportResult<DirectOrderBatchShipmentDTO> resultExcel = ExcelImportUtil.importExcelMore(
                                inputStream, DirectOrderBatchShipmentDTO.class, params);
                        List<DirectOrderBatchShipmentDTO> batchShipmentDTOList = resultExcel.getList();

                        log.info("正在读取文件: {} 结果：{}", filePath.getFileName(), batchShipmentDTOList);

                        // 将解析结果添加到最终结果列表中
                        result.addAll(batchShipmentDTOList);

                        isExistsRootFile = true;

                    } catch (Exception e) {
                        log.error("读取模板文件时发生错误: {}", e);
                        throw new BusinessException(ResultEnum.IMPORT_FILE_READ_ERROR.getCode(),ResultEnum.IMPORT_FILE_READ_ERROR.getMsg());
                    }
                }
            }
        }

        if(isExistsRootFile == false){
            throw new BusinessException(ResultEnum.IMPORT_FILE_NO_ROOT_FILE.getCode(),ResultEnum.IMPORT_FILE_NO_ROOT_FILE.getMsg());
        }
        if (CollectionUtils.isEmpty(result)) {
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), "导入文件不能为空，请核对");
        }
        if (result.size() > nongZiNacosConfig.getBatchShipmentMaxSize()) {
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), "导入文件数量不能超过" + nongZiNacosConfig.getBatchShipmentMaxSize() + "条, 请核对");
        }

        return result;
    }

}
