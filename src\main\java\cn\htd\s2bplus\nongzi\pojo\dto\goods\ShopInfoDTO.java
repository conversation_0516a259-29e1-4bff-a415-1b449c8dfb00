package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;

/**
 * @program: s2bplus-middleground-gateway
 * @description: ShopInfoDTO
 * @author: jiangfeng
 * @create: 2020-03-20 11:10
 **/
@Data
public class ShopInfoDTO implements Serializable {

    @ApiModelProperty(value = "店铺ID",hidden = false)
    private Long shopId;
    @NotBlank(message = "店铺名称")
    @ApiModelProperty(value = "店铺名称",required = true)
    private String shopName;
    @ApiModelProperty(value = "商家ID",hidden = true)
    private Long sellerId;
    @ApiModelProperty(value = "应用渠道ID",hidden = true)
    private Long appId;
    @ApiModelProperty(value = "应用渠道名称")
    private String appName;
    @ApiModelProperty(value = "店铺域名",hidden = true)
    private String shopUrl;
    @ApiModelProperty(value = "店铺logo url",hidden = true)
    private String logoUrl;
    @ApiModelProperty(value = "关键字",hidden = true)
    private String keyword;
    @ApiModelProperty(value = "店铺介绍",hidden = true)
    private String introduce;
    //@NotBlank(message = "店铺类型")
    @ApiModelProperty(value = "店铺类型 1 品牌商 2经销商 3专营店",required = true)
    private String shopType;
    @ApiModelProperty(value = "品牌类经营类型  1自有品牌 2 代理品牌",required = false)
    private String businessType;
    @ApiModelProperty(value = "开通时间",hidden = true)
    private Date passTime;
    @ApiModelProperty(value = "关闭时间",hidden = true)
    private Date endTime;
    @ApiModelProperty(value = "免责声明",required = false)
    private String disclaimer;
    @ApiModelProperty(value = "店铺申请类型;1是开通，2是关闭",hidden = true)
    private String type;
    @ApiModelProperty(value = "审核状态; 0-申请 1是通过，2是驳回")
    private String reviewStatus;
    @ApiModelProperty(value = "店铺建新状态;1是申请，2是通过，3是驳回， 4是平台关闭，5是开通")
    private String status;
    @ApiModelProperty(value = "店铺状态 0：未开通；1：已开通 2：已关闭")
    private String runStatus;
    @ApiModelProperty(value = "电商通校验结果 0-无效 1-有效",hidden = true)
    private String ecommerceStatus;
    @ApiModelProperty(value = "审核信息ID",hidden = true)
    private Long verifyId;
    @ApiModelProperty(value = "删除标记",hidden = true)
    private Integer deleteFlag;
    @ApiModelProperty(value = "创建人ID",hidden = true)
    private Long createId;
    @ApiModelProperty(value = "创建人名称",hidden = true)
    private String createName;
    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createTime;
    @ApiModelProperty(value = "更新人ID",hidden = true)
    private Long modifyId;
    @ApiModelProperty(value = "更新人名称",hidden = true)
    private String modifyName;
    @ApiModelProperty(value = "更新时间",hidden = true)
    private Date modifyTime;
    @ApiModelProperty(value = "bannerUrl",hidden = true)
    private String bannerUrl;
    @ApiModelProperty(value = "租户ID",hidden = true)
    private Long tenementId;
    @ApiModelProperty(value = "默认值 1:老中台  2:云原生",hidden = true)
    private Integer dataTag;
    @ApiModelProperty(value = "ERP商户编码" )
    private String erpMerchantCode;
    @ApiModelProperty(value = "商家编码")
    private String sellerCode;
    @ApiModelProperty(value = "商家名称")
    private String companyName;
    @ApiModelProperty(value = "店铺Id集合")
    private Long[] shopIds;

    @ApiModelProperty(value = "渠道类型 0内部渠道 1外部渠道")
    private String appType;
    @ApiModelProperty(value = "应用渠道编码")
    private String appCode;

    @ApiModelProperty(value = "子账号分配店铺查询停用店铺-用于屏蔽setRunStatus")
    private String subAccount;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"shopId\":")
                .append(shopId);
        sb.append(",\"shopName\":\"")
                .append(shopName).append('\"');
        sb.append(",\"sellerId\":")
                .append(sellerId);
        sb.append(",\"appId\":")
                .append(appId);
        sb.append(",\"appName\":\"")
                .append(appName).append('\"');
        sb.append(",\"shopUrl\":\"")
                .append(shopUrl).append('\"');
        sb.append(",\"logoUrl\":\"")
                .append(logoUrl).append('\"');
        sb.append(",\"keyword\":\"")
                .append(keyword).append('\"');
        sb.append(",\"introduce\":\"")
                .append(introduce).append('\"');
        sb.append(",\"shopType\":\"")
                .append(shopType).append('\"');
        sb.append(",\"businessType\":\"")
                .append(businessType).append('\"');
        sb.append(",\"passTime\":\"")
                .append(passTime).append('\"');
        sb.append(",\"endTime\":\"")
                .append(endTime).append('\"');
        sb.append(",\"disclaimer\":\"")
                .append(disclaimer).append('\"');
        sb.append(",\"type\":\"")
                .append(type).append('\"');
        sb.append(",\"reviewStatus\":\"")
                .append(reviewStatus).append('\"');
        sb.append(",\"status\":\"")
                .append(status).append('\"');
        sb.append(",\"runStatus\":\"")
                .append(runStatus).append('\"');
        sb.append(",\"ecommerceStatus\":\"")
                .append(ecommerceStatus).append('\"');
        sb.append(",\"verifyId\":")
                .append(verifyId);
        sb.append(",\"deleteFlag\":")
                .append(deleteFlag);
        sb.append(",\"createId\":")
                .append(createId);
        sb.append(",\"createName\":\"")
                .append(createName).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append(",\"modifyId\":")
                .append(modifyId);
        sb.append(",\"modifyName\":\"")
                .append(modifyName).append('\"');
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append(",\"bannerUrl\":\"")
                .append(bannerUrl).append('\"');
        sb.append(",\"tenementId\":")
                .append(tenementId);
        sb.append(",\"dataTag\":")
                .append(dataTag);
        sb.append(",\"erpMerchantCode\":\"")
                .append(erpMerchantCode).append('\"');
        sb.append(",\"sellerCode\":\"")
                .append(sellerCode).append('\"');
        sb.append(",\"companyName\":\"")
                .append(companyName).append('\"');
        sb.append(",\"shopIds\":")
                .append(Arrays.toString(shopIds));
        sb.append(",\"appType\":\"")
                .append(appType).append('\"');
        sb.append(",\"appCode\":\"")
                .append(appCode).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
