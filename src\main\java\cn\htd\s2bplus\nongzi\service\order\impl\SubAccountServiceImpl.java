package cn.htd.s2bplus.nongzi.service.order.impl;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.feign.auth.AuthServiceAPI;
import cn.htd.s2bplus.nongzi.pojo.vo.ShopAuthSubAccountVO;
import cn.htd.s2bplus.nongzi.service.order.SubAccountService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @title SubAccountServiceImpl
 * @description 子账号service
 * @Date: 2022/5/11 14:00
 */
@Service
@Slf4j
public class SubAccountServiceImpl implements SubAccountService {
    @Autowired
    private AuthServiceAPI authServiceAPI;

    /**
     * 查询已分配的子账号信息
     *
     * @return
     */
    @Override
    public Result<List<ShopAuthSubAccountVO>> querySubAllocateShop(String shopName) {
        LoginUserDetail loginUser = BaseContextHandler.getLoginUser();
        // 判断是否为子账号
        if (ObjectUtils.isEmpty(loginUser.getParentAccount())) {
            ResultUtil.error(ResultEnum.FAILURE.getCode(), "此账号为主账号");
        }

        log.info("调用auth查询已分配的子账号信息 login:{}",loginUser.getLoginId());
        Result<List<ShopAuthSubAccountVO>> shopListResult = authServiceAPI.querySubAccountShop(loginUser.getSubAccountLoginId(),shopName,loginUser.getMemberCode());
        log.info("调用auth查询已分配的子账号信息结束");

        if (CollectionUtils.isEmpty(shopListResult.getData())) {
            return ResultUtil.success();
        }

        log.info("查询已分配的子账号信息成功");
        return ResultUtil.success(shopListResult.getData());
    }


    @Override
    public Result<List<ShopAuthSubAccountVO>> querySubAllocateShopByLoginId(String subAccountLoginId,String shopName,String memberCode) {
        // 判断是否为子账号
        if (ObjectUtils.isEmpty(subAccountLoginId)) {
            ResultUtil.error(ResultEnum.FAILURE.getCode(), "此账号为主账号");
        }
        log.info("调用auth查询已分配的子账号信息 subAccountLoginId:{}，shopName：{}，memberCode：{}",subAccountLoginId,shopName,memberCode);
        Result<List<ShopAuthSubAccountVO>> shopListResult = authServiceAPI.querySubAccountShop(subAccountLoginId,shopName,memberCode);
        log.info("调用auth查已分配的子账号信息 shopListResult:{}",shopListResult);
        if (CollectionUtils.isEmpty(shopListResult.getData())) {
            return ResultUtil.success();
        }
        return ResultUtil.success(shopListResult.getData());
    }
}
