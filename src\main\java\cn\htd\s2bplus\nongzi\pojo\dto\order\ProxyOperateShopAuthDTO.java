package cn.htd.s2bplus.nongzi.pojo.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * OSS销售管理平台店铺授权关系dto,用于新增和查询
 * <AUTHOR>
 * @date 2023/02/03
 */
@Data
@ApiModel(value = "OSS销售管理平台店铺授权关系dto")
public class ProxyOperateShopAuthDTO {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 登录id
     */
    @ApiModelProperty(value = "登录id")
    private String loginId;

    /**
     * 归属商家编码
     */
    @ApiModelProperty(value = "归属商家编码")
    private String memberCode;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    /**
     * 操作人编码
     */
    @ApiModelProperty(value = "操作人编码")
    private String operatorCode;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operatorName;

    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    private Long memberId;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"userId\":")
                .append(userId);
        sb.append(",\"loginId\":\"")
                .append(loginId).append('\"');
        sb.append(",\"memberCode\":\"")
                .append(memberCode).append('\"');
        sb.append(",\"shopId\":")
                .append(shopId);
        sb.append(",\"shopName\":\"")
                .append(shopName).append('\"');
        sb.append(",\"operatorCode\":\"")
                .append(operatorCode).append('\"');
        sb.append(",\"operatorName\":\"")
                .append(operatorName).append('\"');
        sb.append(",\"memberId\":")
                .append(memberId);
        sb.append('}');
        return sb.toString();
    }
}
