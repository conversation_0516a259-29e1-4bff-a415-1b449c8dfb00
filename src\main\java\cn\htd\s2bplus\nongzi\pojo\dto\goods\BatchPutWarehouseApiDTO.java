package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchPutWarehouseApiDTO implements Serializable {


	@ApiModelProperty(name = "批量入库信息")
	@NotNull(message = "批量入库信息不可为空")
	private List<BatchPutWarehouseApiEntity> putInfo;

	@ApiModelProperty(name = "操作类型 1：出库，2：入库，3=订单扣减，4=退货入库")
	@NotNull(message = "操作类型 不可为空")
	private int businessType;

}
