package cn.htd.s2bplus.nongzi.pojo.dto.contract;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title SelectContractInParamDTO
 * @Date: 2024/12/16 9:33
 */
@Data
public class SelectContractInParamDTO implements Serializable {
    private static final long serialVersionUID = 7352273881996703546L;

    @ApiModelProperty(value = "枚举名称",example = "顺丰快递")
    private String enumName;

    @ApiModelProperty(value = "对应入参属性",example = "companyCode")
    private String fieldName;

    @ApiModelProperty(value = "对应入参名称",example = "卖方")
    private String labelName;

    @ApiModelProperty(value = "组件类型 0-文本，1-日期组件，2-单选框，3-复选框")
    private Integer fieldType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
