package cn.htd.s2bplus.nongzi.utils;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class XssFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(XssFilter.class);

    private List<String> excludes = new ArrayList<>();

    private boolean enabled = false;

    private static Charset charSet;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        String strExcludes = filterConfig.getInitParameter("excludes");
        String strEnabled = filterConfig.getInitParameter("enabled");
        //将不需要xss过滤的接口添加到列表中
        if(StringUtils.isNotEmpty(strExcludes)){
            String[] urls = strExcludes.split(",");
            for(String url:urls){
                excludes.add(url);
            }
        }
        if(StringUtils.isNotEmpty(strEnabled)){
            enabled = Boolean.valueOf(strEnabled);
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;

        //如果该访问接口在排除列表里面则不拦截
        if(isExcludeUrl(request.getServletPath())){
            filterChain.doFilter(servletRequest,servletResponse);
            return;
        }

        String contentType = request.getContentType();
        // logger.info("xss接口contentType：{}",contentType);
        if (StringUtils.isNotEmpty(contentType) && contentType.contains("multipart/form-data")){
            filterChain.doFilter(servletRequest,servletResponse);
            return;
        }

        //拦截该url并进行xss过滤
        XssSqlHttpServletRequestWrapper xssHttpServletRequestWrapper = new XssSqlHttpServletRequestWrapper(request);
        filterChain.doFilter(xssHttpServletRequestWrapper,servletResponse);

    }

    @Override
    public void destroy() {

    }

    private boolean isExcludeUrl(String urlPath){
        if(!enabled){
            //如果xss开关关闭了，则所有url都不拦截
            return true;
        }
        if(excludes==null||excludes.isEmpty()){
            return false;
        }

        String url = urlPath;
        for(String pattern:excludes){
            Pattern p = Pattern.compile("^"+pattern);
            Matcher m = p.matcher(url);
            if(m.find()){
                return true;
            }
        }
        return false;
    }


    public String getRequestPostStr(HttpServletRequest request)
            throws IOException {
        String charSetStr = request.getCharacterEncoding();
        if (charSetStr == null) {
            charSetStr = "UTF-8";
        }
        charSet = Charset.forName(charSetStr);

        return StreamUtils.copyToString(request.getInputStream(), charSet);
    }


    /**
     * 导入文件过滤
     * @param file
     * @param className
     * @throws IOException
     */
    public void importFilter(MultipartFile file, String fileUrl, String className) throws Exception {
        if (ObjectUtils.isEmpty(file) && ObjectUtils.isEmpty(fileUrl)) {
            throw new BusinessException(ResultEnum.ERROR.getCode(),"上传文件为空");
        }
        if(!ObjectUtils.isEmpty(file)){
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1,originalFilename.length());
            if (org.apache.commons.lang3.StringUtils.isEmpty(substring) || !(StrConstant.XLS.equals(substring) || StrConstant.XLSX.equals(substring))) {
                throw new BusinessException(ResultEnum.ERROR.getCode(),"导入的文件格式有误,非Excel！");
            }
        }
        InputStream inputStream = !ObjectUtils.isEmpty(file)?file.getInputStream():this.getInputStreamByUrl(fileUrl);
        Object dto = Class.forName(className).newInstance();
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        //原始数据
        List<Object> objectList = ExcelImportUtil.importExcel(inputStream,dto.getClass(),params);

        // 删除导入数据对象中每个字段的前后空格 此处去空格处理没办法改变流文件 先注释
        //dealExcelImportStringTrim(objectList);
        String originalData = JSONObject.toJSONString(objectList);
        //XSS过滤后数据
        String filteredData = XssUtil.stripSqlXss(originalData);
        //比较长度结果不相等，则表示包含需过滤字段
        if (originalData.length() != filteredData.length()) {
            throw new BusinessException(ResultEnum.ERROR.getCode(),"导入文件包含异常字段");
        }
    }

    private void dealExcelImportStringTrim(List<Object> objectList) {
       // 删除导入数据对象中每个字段的前后空格
        for (Object object : objectList) {
            // 获取对象属性
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    Object value = field.get(object);
                    if (value instanceof String) {
                        field.set(object, ((String) value).trim());
                    }
                } catch (Exception e) {
                    logger.warn("删除导入数据对象中每个字段的前后空格异常", e);
                }
            }
        }
    }


    /**
     * 通过url获取文件流
     * @param strUrl
     * @return
     */
    public InputStream getInputStreamByUrl(String strUrl) {
        InputStream is = null;
        try {
            URLEncoder.encode(strUrl,"utf-8");
            //strUrl=strUrl.replaceAll(" ", "%20");
            URL url = new URL(strUrl);
            // 打开连接
            HttpURLConnection httpUrlConn = (HttpURLConnection) url.openConnection();
            //设置请求超时
            httpUrlConn.setConnectTimeout(50 * 1000);
            httpUrlConn.setDoInput(true);
            httpUrlConn.setDoInput(true);
            //设置链接为GET形式
            httpUrlConn.setRequestMethod("GET");
            httpUrlConn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            httpUrlConn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            is = httpUrlConn.getInputStream();
            return is;
        } catch (Exception e) {
            logger.error("getInputStreamByUrl 异常,exception is", e);
        }
        return null;
    }


}
