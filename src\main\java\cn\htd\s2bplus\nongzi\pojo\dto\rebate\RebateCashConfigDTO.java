package cn.htd.s2bplus.nongzi.pojo.dto.rebate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title RebateCashConfigDTO
 * @Date: 2024/5/11 15:12
 */
@Data
public class RebateCashConfigDTO implements Serializable {
    private static final long serialVersionUID = -3333419471990648325L;
    @ExcelProperty("商家编码")
    private String sellerCode;

    @ExcelProperty("会员编码")
    private String memberCode;

    @ExcelProperty("单笔最大兑付额度")
    private Integer quota;

    @ApiModelProperty(value = "商家名称",required = true)
    private String sellerName;

    @ApiModelProperty(value = "会员编码",required = true)
    private String memberName;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
    }
}
