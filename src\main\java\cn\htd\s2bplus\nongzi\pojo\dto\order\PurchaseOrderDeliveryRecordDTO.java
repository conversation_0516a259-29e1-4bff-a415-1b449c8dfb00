package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PurchaseOrderDeliveryRecordDTO implements Serializable {
    private static final long serialVersionUID = 6130868932804919792L;

    @ApiModelProperty(value = "采购单唯一编号,ERP采购单号+平台公司代码(4位)")
    private String purchaseOrderUniqueNo;

    @ApiModelProperty(value = "签收状态: 10待签收 20已签收 30驳回")
    private String status;

    @NotNull
    @ApiModelProperty(value = "当前页数")
    private Integer page;

    @NotNull
    @ApiModelProperty(value = "每页记录数")
    private Integer rows;

    private String supplierCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
