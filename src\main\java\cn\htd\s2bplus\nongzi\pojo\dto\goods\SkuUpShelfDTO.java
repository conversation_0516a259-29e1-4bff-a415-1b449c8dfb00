package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportSkuUpShelfDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class SkuUpShelfDTO implements Serializable {

    private static final long serialVersionUID = -4418935552951218154L;

    @ApiModelProperty(value = "sku列表")
    private List<SkuShopReqDTO> skuShopDTOS;

    @ApiModelProperty(value = "商家类型 1内部2外部3分销商")
    private String sellerType;

    @ApiModelProperty(value = "卖家ID")
    private Long sellerId;

    @ApiModelProperty(value = "商家账号")
    private String sellerCode;

    @ApiModelProperty(value = "操作账号")
    private String operationCode;

    @ApiModelProperty(value = "操作人id")
    private Long modifyId;

    @ApiModelProperty(value = "操作人名称")
    private String modifyName;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
