package cn.htd.s2bplus.nongzi.service.shop.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.s2bplus.common.AppNacosConfig;
import cn.htd.s2bplus.common.enums.PlatformEnum;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.enums.BusinessTypeEnum;
import cn.htd.s2bplus.nongzi.enums.DataTagEnum;
import cn.htd.s2bplus.nongzi.enums.SellerTypeEnum;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.SellerSkuExport;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.*;
import cn.htd.s2bplus.nongzi.pojo.vo.SellerSkuVO;
import cn.htd.s2bplus.nongzi.pojo.vo.ShopAuthSubAccountVO;
import cn.htd.s2bplus.nongzi.service.order.SubAccountService;
import cn.htd.s2bplus.nongzi.service.shop.ShopService;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShopServiceImpl implements ShopService {

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;

    @Autowired
    private SubAccountService subAccountService;

    @Autowired
    private MiddleGroundAPI middleGroundAPI;

    @Autowired
    private GoodsFeignService goodsFeignService;


    @Autowired
    private AppNacosConfig appNacosConfig;
    /** 分隔符*/
    public final static String SPLIT_STR = "_";
    @Override
    @Async
    public void importGoodsList(BaseDTO baseDto, SellerSkuQuery sellerSkuQuery, HttpServletResponse response) throws ParseException {
        Result<Boolean> result = new Result<>();
        //设置上下架状态
        this.setStatusList(sellerSkuQuery);
        //设置中台接口入参
        SellerSkuQueryDTO requestParam = this.getSellerSkuQueryDTOBasePagerRequestParam(baseDto, sellerSkuQuery);
        log.info("querySellerGoodsList req:{}", requestParam);
        if(requestParam.getFlag()){
            return;
        }
        List<SellerSku> sellerSkuList = new ArrayList<>();
        int initSize = 10;
        Result<SellerSkuVO> executeResult = middleGroundAPI.querySellerGoodsList(requestParam, 1, initSize);
        if (executeResult.isSuccess() && executeResult.getData() != null) {
            SellerSkuVO sellerSkuVo = executeResult.getData();
            if (CollectionUtils.isEmpty(sellerSkuVo.getSellerSkuList())) {
                return;
            }
            long total = sellerSkuVo.getTotal();
            if (total <= initSize) {
                sellerSkuList.addAll(sellerSkuVo.getSellerSkuList());
            } else{
                //说明数据没有查完
                int pageSize = 50;
                for (int tempPage = 1; sellerSkuList.size() < total; tempPage++) {
                    log.info("分页查询商品sku列表当前页:{}", tempPage);
                    Result<SellerSkuVO> tempResult = middleGroundAPI.querySellerGoodsList(requestParam, tempPage, pageSize);
                    if (!tempResult.isSuccess() || tempResult.getData() == null) {
                        throw new BusinessException(ResultEnum.FAILURE.getCode(), "查询商品列表失败");
                    }
                    List<SellerSku> tempSellerSkuList = tempResult.getData().getSellerSkuList();
                    if (!CollectionUtils.isEmpty(sellerSkuVo.getSellerSkuList())) {
                        sellerSkuList.addAll(tempSellerSkuList);
                    }
                    this.queryGoodsStock(tempSellerSkuList,baseDto.getUser().getMemberCode());
                }
            }

            Map<String, RaisePriceDTO> raisePriceMap = new HashMap<>();
            if (PlatformEnum.BRAND_POP.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())) {
                List<RaisePriceQueryDTO> raisePriceQueryDTOList = new ArrayList<>();
                for (SellerSku sellerSku : sellerSkuList) {
                    RaisePriceQueryDTO raisePriceQueryDTO = new RaisePriceQueryDTO();
                    raisePriceQueryDTO.setShopId(sellerSku.getShopId());
                    raisePriceQueryDTO.setSkuCode(sellerSku.getSkuCode());
                    raisePriceQueryDTOList.add(raisePriceQueryDTO);
                }
                Result<List<RaisePriceDTO>> raisePriceListResult = goodsFeignService.queryRaisePriceList(raisePriceQueryDTOList);
                if (!raisePriceListResult.isSuccess()) {
                    log.error("查询商品原基础价失败: {}",raisePriceListResult);
                    throw new BusinessException(raisePriceListResult.getCode(),raisePriceListResult.getMsg());
                }
                if (!CollectionUtils.isEmpty(raisePriceListResult.getData())) {
                    for (RaisePriceDTO raisePriceDTO : raisePriceListResult.getData()) {
                        raisePriceMap.put(raisePriceDTO.getShopId() + SPLIT_STR +raisePriceDTO.getSkuCode(), raisePriceDTO);
                    }
                }
            }
            for(SellerSku skuOutDTO : sellerSkuList){
                String priceKey = skuOutDTO.getShopId() + SPLIT_STR + skuOutDTO.getSkuCode();
                RaisePriceDTO raisePriceDTO = raisePriceMap.get(priceKey);
                if (raisePriceDTO != null) {
                    skuOutDTO.setPrice(raisePriceDTO.getPrice());
                }
            }
            if (PlatformEnum.BRAND_POP.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())) {
                this.exportPpfxRecord(sellerSkuList, baseDto, response);
            } else {
                this.getRecordExport(sellerSkuList, baseDto, response);
            }
        }
        log.info("处理后的查询商品价格列表导出接口>>>>:{}",JSON.toJSONString(result));
    }

    private void exportPpfxRecord(List<SellerSku> skuDTOList,BaseDTO baseDto,HttpServletResponse response){
        String sheetNameStart = "商品价格";
        List<PpfxPopSkuExport> list = new ArrayList<>();
        for (SellerSku sellerSku : skuDTOList) {
            PpfxPopSkuExport skuExport = new PpfxPopSkuExport();
            BeanUtils.copyProperties(sellerSku,skuExport);
            //修改状态值
            this.extracted(sellerSku, skuExport);
            list.add(skuExport);
        }
        String downloadUrl = this.getPpfxDownloadUrl(list, response, sheetNameStart,baseDto);
        if (!StringUtils.isEmpty(downloadUrl)){
            ReportHistory reportHistory = new ReportHistory();
            reportHistory.setBusinessType((BusinessTypeEnum.SALE_ORDER.getCode().byteValue()));
            reportHistory.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
            reportHistory.setDownloadUrl(downloadUrl);
            reportHistory.setFinishTime(new Date());
            reportHistory.setBeginTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
            reportHistory.setEndTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
            reportHistory.setEndTime(new Date());
            reportHistory.setCreateId(baseDto.getUser().getMemberId());
            reportHistory.setCreateName(baseDto.getUser().getUserName());
            reportHistory.setCreateTime(new Date());
            reportHistory.setModifyId(baseDto.getUser().getMemberId());
            reportHistory.setModifyName(baseDto.getUser().getUserName());
            reportHistory.setModifyTime(new Date());
            // 保存报表生成下载历史
            goodsFeignService.insertSelective(reportHistory);
        }else{
            log.info("获取上传文件地址出错:{}",downloadUrl);
        }
    }

    private SellerSkuQueryDTO getSellerSkuQueryDTOBasePagerRequestParam(BaseDTO baseDto, SellerSkuQuery sellerSkuQuery) throws ParseException {
        SellerSkuQueryDTO skuQueryDTO = new SellerSkuQueryDTO();
        BeanUtils.copyProperties(sellerSkuQuery,skuQueryDTO);
        skuQueryDTO.setDataTag(DataTagEnum.NATIVE_CLOUD_CHANNEL.getCode());
        if (!StringUtils.isBlank(sellerSkuQuery.getReleaseTime())){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            skuQueryDTO.setCreateStartTime(sdf.parse(sellerSkuQuery.getReleaseTime()));
        }
        if (!StringUtils.isBlank(sellerSkuQuery.getEndTime())){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            skuQueryDTO.setCreateEndTime(sdf.parse(sellerSkuQuery.getEndTime()));
        }
        log.info("goodsPriceList req, skuQueryDTO:{}", skuQueryDTO);
        // 判断是否为子账号
        skuQueryDTO.setSellerId(baseDto.getUser().getMemberId());
        if (!ObjectUtils.isEmpty(baseDto.getUser().getParentAccount())) {
            this.setShopIds(skuQueryDTO,baseDto);
        }
        return skuQueryDTO;
    }

    private void setShopIds(SellerSkuQueryDTO skuQueryDTO,BaseDTO baseDto) {
        Result<List<ShopAuthSubAccountVO>> listResult = subAccountService.querySubAllocateShopByLoginId(baseDto.getUser().getSubAccountLoginId(),skuQueryDTO.getShopName(),
                baseDto.getUser().getMemberCode());
        if(listResult.isSuccess()){
            if(!CollectionUtils.isEmpty(listResult.getData())){
                Map<Long,String> allocateShopList = listResult.getData().stream().
                        collect(Collectors.toMap(ShopAuthSubAccountVO::getShopId,ShopAuthSubAccountVO::getShopName));
                skuQueryDTO.setShopIds(new ArrayList<>(allocateShopList.keySet()));
            }else{
                skuQueryDTO.setFlag(true);
            }
        }else{
            throw new BusinessException(listResult.getCode(),listResult.getMsg());
        }
    }

    /**
     * 设置上下架状态
     * @param sellerSkuQuery
     */
    private void setStatusList(SellerSkuQuery sellerSkuQuery){
        List<Integer> listStatus = new ArrayList<>(2);
        if (null == sellerSkuQuery.getStatus()){
            listStatus.add(0);
            listStatus.add(1);
        }else {
            listStatus.add(sellerSkuQuery.getStatus());
        }
        sellerSkuQuery.setStatusList(listStatus);
    }

    public Long getBrandByName(String brandName) {
        log.info("调用查询品牌接口req:{}",brandName);
        Long brand = null;
        Result<Brand> brandResult = goodsFeignService.getBrandByName(brandName);
        log.info("调用查询品牌接口resp:{}",brandResult);
        if (brandResult.isSuccess() && brandResult.getData() != null){
            brand = brandResult.getData().getId();
        }
        return brand;
    }

    private void getRecordExport(List<SellerSku> skuDTOList, BaseDTO baseDto, HttpServletResponse response){
        String sheetNameStart = "商品价格";
        List<SellerSkuExport> list = new ArrayList<>();
        for (SellerSku sellerSku : skuDTOList) {
            SellerSkuExport skuExport = new SellerSkuExport();
            BeanUtils.copyProperties(sellerSku,skuExport);
            //修改状态值
            this.extracted(sellerSku, skuExport);
            list.add(skuExport);
        }
        String downloadUrl = this.getDownloadUrl(list, response, sheetNameStart,baseDto);
        if (!StringUtils.isEmpty(downloadUrl)){
            ReportHistory reportHistory = new ReportHistory();
            reportHistory.setBusinessType((BusinessTypeEnum.SALE_ORDER.getCode().byteValue()));
            reportHistory.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
            reportHistory.setDownloadUrl(downloadUrl);
            reportHistory.setFinishTime(new Date());
            reportHistory.setBeginTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
            reportHistory.setEndTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
            reportHistory.setEndTime(new Date());
            reportHistory.setCreateId(baseDto.getUser().getMemberId());
            reportHistory.setCreateName(baseDto.getUser().getUserName());
            reportHistory.setCreateTime(new Date());
            reportHistory.setModifyId(baseDto.getUser().getMemberId());
            reportHistory.setModifyName(baseDto.getUser().getUserName());
            reportHistory.setModifyTime(new Date());
            // 保存报表生成下载历史
            goodsFeignService.insertSelective(reportHistory);
        }else{
            log.info("获取上传文件地址出错:{}",downloadUrl);
        }
    }
    @Override
    public void queryGoodsStock(List<SellerSku> skuOutDTOList, String memberCode) {
        if(PlatformEnum.CYC.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())
                || PlatformEnum.BRAND_POP.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())){
            Map<Long, List<SellerSku>> collect = skuOutDTOList.stream().collect(Collectors.groupingBy(SellerSku::getShopId));
            List<GoodsStockDTO> goodsStockDTOList = new ArrayList<>();
            for (Long shopId : collect.keySet()) {
                QueryGoodsStockDTO queryGoodsStockDTO = new QueryGoodsStockDTO();
                queryGoodsStockDTO.setShopId(shopId);
                queryGoodsStockDTO.setSellerCode(memberCode);
                queryGoodsStockDTO.setSkuCodeList(collect.get(shopId).stream().map(SellerSku::getSkuCode).collect(Collectors.toList()));
                Result<List<GoodsStockDTO>> listResult = goodsFeignService.selectAllBySkuCodesOop(queryGoodsStockDTO);
                if(!listResult.isSuccess()){
                    throw new BusinessException(listResult.getCode(),listResult.getMsg());
                }
                goodsStockDTOList.addAll(listResult.getData());
            }
            for (SellerSku sellerSku : skuOutDTOList) {
                sellerSku.setUseNumDecimal(new BigDecimal("0"));
                for (GoodsStockDTO goodsStockDTO : goodsStockDTOList) {
                    if(sellerSku.getSkuCode().equals(goodsStockDTO.getSkuCode()) && goodsStockDTO.getShopId().equals(sellerSku.getShopId())){
                        sellerSku.setUseNumDecimal(goodsStockDTO.getSaleStockNum());
                        if (goodsStockDTO.getSaleStockNum() != null) {
                            sellerSku.setUseNum(goodsStockDTO.getSaleStockNum().longValue());
                        }
                        break;
                    }
                }
            }
        }
    }

    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     */
    private String getPpfxDownloadUrl(List<PpfxPopSkuExport> list, HttpServletResponse response, String sheetNameStart,BaseDTO baseDto){
        String downloadUrl = "";
        try{
            //文件名样式
            SimpleDateFormat simpleDateFormatName = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
            String sheetName = simpleDateFormatName.format(new Date());
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 区分自营pop
            sheet1DataMap.put("entity", PpfxPopSkuExport.class);
            sheet1DataMap.put("data", CollectionUtils.isEmpty(list) ?
                    new ArrayList<PpfxPopSkuExport>() : list);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            // 改成输出excel文件
            response.setContentType("application/msexcel");
            String fileName = sheetNameStart +"_"+ sheetName;
            // 03版本后缀xls，之后的xlsx
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            OssUtils ossUtils = new OssUtils();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = sdf.format(new Date());
            String ossFileName = "商品明细信息"+format+".xls";
            downloadUrl = ossUtils.upload(excelStream,ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        }catch (Exception e){
            log.info("reportExportHandle error:",e);
        }
        return downloadUrl;
    }
    private void extracted(SellerSku sellerSku, PpfxPopSkuExport skuExport) {
        if (org.springframework.util.StringUtils.isEmpty(sellerSku.getUseNum())){
            skuExport.setUseNum("--");
        }else {
            skuExport.setUseNum(String.valueOf(sellerSku.getUseNum()));
        }
        if (org.springframework.util.StringUtils.isEmpty(sellerSku.getSkuAttributesName())){
            skuExport.setSkuAttributesName("--");
        }
        if (sellerSku.getCost() == null){
            skuExport.setCost("--");
        } else {
            skuExport.setCost(String.valueOf(sellerSku.getCost()));
        }
        if (org.springframework.util.StringUtils.isEmpty(sellerSku.getPrice())){
            skuExport.setPrice("--");
        } else {
            skuExport.setPrice(String.valueOf(sellerSku.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()));
        }
        if (sellerSku.getStatus() == 0){
            skuExport.setStatus("下架");
        }else {
            skuExport.setStatus("上架");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(sellerSku.getModifyTime());
        skuExport.setModifyTime(date);
        String createDate = sdf.format(sellerSku.getCreateTime());
        skuExport.setCreateTime(createDate);
    }
    private void extracted(SellerSku sellerSku, SellerSkuExport skuExport) {
        if (sellerSku.getUseNum() == null){
            skuExport.setUseNum("--");
        }else {
            skuExport.setUseNum(String.valueOf(sellerSku.getUseNum()));
        }
        if (StringUtils.isBlank(sellerSku.getSkuAttributesName())){
            skuExport.setSkuAttributesName("--");
        }
        if (ObjectUtils.isEmpty(sellerSku.getSaleLimitedPrice())){
            skuExport.setSaleLimitedPrice("--");
        } else {
            skuExport.setSaleLimitedPrice(String.valueOf(sellerSku.getSaleLimitedPrice()));
        }
        if (ObjectUtils.isEmpty(sellerSku.getPrice())){
            skuExport.setPrice("--");
        } else {
            skuExport.setPrice(String.valueOf(sellerSku.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()));
        }
        if (sellerSku.getIsUseAreaPrice() == 0){
            skuExport.setIsUseAreaPrice("不使用");
        }else {
            skuExport.setIsUseAreaPrice("使用");
        }
        if (sellerSku.getIsUseGroupPrice() == 0){
            skuExport.setIsUseGroupPrice("不使用");
        }else {
            skuExport.setIsUseGroupPrice("使用");
        }
        if (sellerSku.getIsUseLadderPrice() == 0){
            skuExport.setIsUseLadderPrice("不使用");
        }else {
            skuExport.setIsUseLadderPrice("使用");
        }
        if (sellerSku.getStatus() == 0){
            skuExport.setStatus("下架");
        }else {
            skuExport.setStatus("上架");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(sellerSku.getModifyTime());
        skuExport.setModifyTime(date);
        String createDate = sdf.format(sellerSku.getCreateTime());
        skuExport.setCreateTime(createDate);
    }

    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     */
    private String  getDownloadUrl(List<SellerSkuExport> list, HttpServletResponse response, String sheetNameStart,BaseDTO baseDto){
        String downloadUrl = "";
        try{
            //文件名样式
            SimpleDateFormat simpleDateFormatName = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
            String sheetName = simpleDateFormatName.format(new Date());
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 区分自营pop
            if(SellerTypeEnum.INTER_SUPPLIERS.getCode().equals(baseDto.getUser().getSellerType())){
                // 模版导出对应得实体类型
                sheet1DataMap.put("entity", SellerSkuExport.class);
                // sheet中要填充得数据
                sheet1DataMap.put("data", CollectionUtils.isEmpty(list) ?
                        new ArrayList<SellerSkuExport>() : list);
            }else{
                // 模版导出对应得实体类型
                sheet1DataMap.put("entity", PopSkuExport.class);
                sheet1DataMap.put("data", CollectionUtils.isEmpty(list) ?
                        new ArrayList<PopSkuExport>() : list);
            }
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            // 改成输出excel文件
            response.setContentType("application/msexcel");
            String fileName = sheetNameStart +"_"+ sheetName;
            // 03版本后缀xls，之后的xlsx
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            OssUtils ossUtils = new OssUtils();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = sdf.format(new Date());
            String ossFileName = "商品明细信息"+format+".xls";
            downloadUrl = ossUtils.upload(excelStream,ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        }catch (Exception e){
            log.info("reportExportHandle error:",e);
        }
        return downloadUrl;
    }

}
