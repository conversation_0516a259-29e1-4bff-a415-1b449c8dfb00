package cn.htd.s2bplus.nongzi.pojo.dto.excel;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ExcelTarget("ImportGoodsRecordDTO")
public class ImportGoodsRecordDTO implements Serializable {
    private static final long serialVersionUID = -989863375457989689L;

    @Excel(name = "条形码", height = 10, width = 30)
    private String eanCode;

    @Excel(name = "*商品名称", height = 10, width = 30)
    private String itemName;

    @Excel(name = "*单位", height = 10, width = 30)
    private String unit;

    @Excel(name = "*成本价", height = 10, width = 30)
    private BigDecimal cost;

    @Excel(name = "*销售价", height = 10, width = 30)
    private BigDecimal retailPrice;

    @Excel(name = "供应商", height = 10, width = 30)
    private String supplierName;

    @Excel(name = "导入失败原因", height = 30, width = 30)
    private String errorReason;

    private List<String> errorReasonList;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
