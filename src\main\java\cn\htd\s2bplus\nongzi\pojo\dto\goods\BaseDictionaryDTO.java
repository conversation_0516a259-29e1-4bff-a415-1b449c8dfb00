package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseDictionaryDTO implements Serializable {
    @ApiModelProperty(
            value = "主键"
    )
    private Long id;

    @ApiModelProperty(
            value = "名称"
    )
    private String name = "";

    @ApiModelProperty(
            value = "编码"
    )
    private String code = "";

    @ApiModelProperty(
            value = "值"
    )
    private String value = "";

    @ApiModelProperty(
            value = "类型ID"
    )
    private Long type;

    @ApiModelProperty(
            value = "类型名称"
    )
    private String typeName = "";

    @ApiModelProperty(
            value = "字典分组编码"
    )
    private String parentCode = "";

    @ApiModelProperty(
            value = "字典分组名称"
    )
    private String parentName = "";

    @ApiModelProperty(
            value = "备注"
    )
    private String remark = "";

    @ApiModelProperty(
            value = "0:禁用，1:启用"
    )
    private Integer status;

    @ApiModelProperty(
            value = "排序号"
    )
    private int sortNum;

    @ApiModelProperty(
            value = "创建者ID"
    )
    private Long createId;

    @ApiModelProperty(
            value = "创建者名称"
    )
    private String createName;

    @ApiModelProperty(
            value = "创建时间"
    )
    private Date createTime;

    @ApiModelProperty(
            value = "更新者"
    )
    private Long modifyId;

    @ApiModelProperty(
            value = "更行者名称"
    )
    private String modifyName;

    @ApiModelProperty(
            value = "更新时间"
    )
    private Date modifyTime;

    @ApiModelProperty(
            value = "是否迷糊搜索父code,不为空则模糊",
            notes = "是否迷糊搜索父code,不为空则模糊",
            example = "1"
    )
    private String isLike;

    @ApiModelProperty(
            value = "页数"
    )
    private Integer page;

    @ApiModelProperty(
            value = "每页记录数"
    )
    private Integer rows;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
