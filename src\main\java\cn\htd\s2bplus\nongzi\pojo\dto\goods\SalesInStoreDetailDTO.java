package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SalesInStoreDetailDTO implements Serializable {
    private static final long serialVersionUID = -8586386866027777701L;

    @ApiModelProperty(value = "会员店编码")
    private String buyerCode;

    @ApiModelProperty(value = "会员店名称")
    private String buyerName;

    @ApiModelProperty(value = "apple_id")
    private String appleId;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "店铺编码")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "采购部门名称")
    private String departmentName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsCount;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "合计金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "合计数量")
    private BigDecimal totalCount;

    @ApiModelProperty(value = "销售属性集合名称 颜色:白色;内存:16G;")
    private String attributesName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
