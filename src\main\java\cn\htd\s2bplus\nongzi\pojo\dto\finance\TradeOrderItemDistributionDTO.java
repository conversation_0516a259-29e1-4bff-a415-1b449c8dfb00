package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class TradeOrderItemDistributionDTO implements Serializable {
    private static final long serialVersionUID = -3694035606631154521L;
    private Long id;

    @ApiModelProperty(value = "分润人角色CODE",position = 1)
    private String finRoleCode;
    private String finOrderNo;
    private Long ftoItemId;
    private String orderItemId;

    @ApiModelProperty(value = "分佣人编码",position = 2)
    private String memberCode;

    @ApiModelProperty(value = "分佣人名称",position = 3)
    private String memberName;

    @ApiModelProperty(value = "分佣是否计算 0否，1是",position = 4)
    private String needCalculate;

    @ApiModelProperty(value = "分佣方式 1固定值，2百分比",position = 5)
    private Long distributionType;

    @ApiModelProperty(value = "分佣系数 分佣方式 1-对应金额 2-百分比",position = 6)
    private BigDecimal distributionValue;

    @ApiModelProperty(value = "创建人ID",position = 7)
    private Long createId;

    @ApiModelProperty(value = "创建人名称",position = 8)
    private String createName;

    @ApiModelProperty(value = "创建时间",position = 9)
    private Date createTime;

    public TradeOrderItemDistributionDTO() {
    }

    public Long getId() {
        return this.id;
    }

    public String getFinRoleCode() {
        return this.finRoleCode;
    }

    public String getFinOrderNo() {
        return this.finOrderNo;
    }

    public Long getFtoItemId() {
        return this.ftoItemId;
    }

    public String getOrderItemId() {
        return this.orderItemId;
    }

    public String getMemberCode() {
        return this.memberCode;
    }

    public String getMemberName() {
        return this.memberName;
    }

    public String getNeedCalculate() {
        return this.needCalculate;
    }

    public Long getDistributionType() {
        return this.distributionType;
    }

    public BigDecimal getDistributionValue() {
        return this.distributionValue;
    }

    public Long getCreateId() {
        return this.createId;
    }

    public String getCreateName() {
        return this.createName;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setFinRoleCode(String finRoleCode) {
        this.finRoleCode = finRoleCode;
    }

    public void setFinOrderNo(String finOrderNo) {
        this.finOrderNo = finOrderNo;
    }

    public void setFtoItemId(Long ftoItemId) {
        this.ftoItemId = ftoItemId;
    }

    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId;
    }

    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public void setNeedCalculate(String needCalculate) {
        this.needCalculate = needCalculate;
    }

    public void setDistributionType(Long distributionType) {
        this.distributionType = distributionType;
    }

    public void setDistributionValue(BigDecimal distributionValue) {
        this.distributionValue = distributionValue;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof TradeOrderItemDistributionDTO)) {
            return false;
        } else {
            TradeOrderItemDistributionDTO other = (TradeOrderItemDistributionDTO)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label167: {
                    Object this$id = this.getId();
                    Object other$id = other.getId();
                    if (this$id == null) {
                        if (other$id == null) {
                            break label167;
                        }
                    } else if (this$id.equals(other$id)) {
                        break label167;
                    }

                    return false;
                }

                Object this$finRoleCode = this.getFinRoleCode();
                Object other$finRoleCode = other.getFinRoleCode();
                if (this$finRoleCode == null) {
                    if (other$finRoleCode != null) {
                        return false;
                    }
                } else if (!this$finRoleCode.equals(other$finRoleCode)) {
                    return false;
                }

                label153: {
                    Object this$finOrderNo = this.getFinOrderNo();
                    Object other$finOrderNo = other.getFinOrderNo();
                    if (this$finOrderNo == null) {
                        if (other$finOrderNo == null) {
                            break label153;
                        }
                    } else if (this$finOrderNo.equals(other$finOrderNo)) {
                        break label153;
                    }

                    return false;
                }

                Object this$ftoItemId = this.getFtoItemId();
                Object other$ftoItemId = other.getFtoItemId();
                if (this$ftoItemId == null) {
                    if (other$ftoItemId != null) {
                        return false;
                    }
                } else if (!this$ftoItemId.equals(other$ftoItemId)) {
                    return false;
                }

                label139: {
                    Object this$orderItemId = this.getOrderItemId();
                    Object other$orderItemId = other.getOrderItemId();
                    if (this$orderItemId == null) {
                        if (other$orderItemId == null) {
                            break label139;
                        }
                    } else if (this$orderItemId.equals(other$orderItemId)) {
                        break label139;
                    }

                    return false;
                }

                Object this$memberCode = this.getMemberCode();
                Object other$memberCode = other.getMemberCode();
                if (this$memberCode == null) {
                    if (other$memberCode != null) {
                        return false;
                    }
                } else if (!this$memberCode.equals(other$memberCode)) {
                    return false;
                }

                label125: {
                    Object this$memberName = this.getMemberName();
                    Object other$memberName = other.getMemberName();
                    if (this$memberName == null) {
                        if (other$memberName == null) {
                            break label125;
                        }
                    } else if (this$memberName.equals(other$memberName)) {
                        break label125;
                    }

                    return false;
                }

                label118: {
                    Object this$needCalculate = this.getNeedCalculate();
                    Object other$needCalculate = other.getNeedCalculate();
                    if (this$needCalculate == null) {
                        if (other$needCalculate == null) {
                            break label118;
                        }
                    } else if (this$needCalculate.equals(other$needCalculate)) {
                        break label118;
                    }

                    return false;
                }

                Object this$distributionType = this.getDistributionType();
                Object other$distributionType = other.getDistributionType();
                if (this$distributionType == null) {
                    if (other$distributionType != null) {
                        return false;
                    }
                } else if (!this$distributionType.equals(other$distributionType)) {
                    return false;
                }

                label104: {
                    Object this$distributionValue = this.getDistributionValue();
                    Object other$distributionValue = other.getDistributionValue();
                    if (this$distributionValue == null) {
                        if (other$distributionValue == null) {
                            break label104;
                        }
                    } else if (this$distributionValue.equals(other$distributionValue)) {
                        break label104;
                    }

                    return false;
                }

                label97: {
                    Object this$createId = this.getCreateId();
                    Object other$createId = other.getCreateId();
                    if (this$createId == null) {
                        if (other$createId == null) {
                            break label97;
                        }
                    } else if (this$createId.equals(other$createId)) {
                        break label97;
                    }

                    return false;
                }

                Object this$createName = this.getCreateName();
                Object other$createName = other.getCreateName();
                if (this$createName == null) {
                    if (other$createName != null) {
                        return false;
                    }
                } else if (!this$createName.equals(other$createName)) {
                    return false;
                }

                Object this$createTime = this.getCreateTime();
                Object other$createTime = other.getCreateTime();
                if (this$createTime == null) {
                    if (other$createTime != null) {
                        return false;
                    }
                } else if (!this$createTime.equals(other$createTime)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof TradeOrderItemDistributionDTO;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $id = this.getId();
        result = result * 59 + ($id == null ? 43 : $id.hashCode());
        Object $finRoleCode = this.getFinRoleCode();
        result = result * 59 + ($finRoleCode == null ? 43 : $finRoleCode.hashCode());
        Object $finOrderNo = this.getFinOrderNo();
        result = result * 59 + ($finOrderNo == null ? 43 : $finOrderNo.hashCode());
        Object $ftoItemId = this.getFtoItemId();
        result = result * 59 + ($ftoItemId == null ? 43 : $ftoItemId.hashCode());
        Object $orderItemId = this.getOrderItemId();
        result = result * 59 + ($orderItemId == null ? 43 : $orderItemId.hashCode());
        Object $memberCode = this.getMemberCode();
        result = result * 59 + ($memberCode == null ? 43 : $memberCode.hashCode());
        Object $memberName = this.getMemberName();
        result = result * 59 + ($memberName == null ? 43 : $memberName.hashCode());
        Object $needCalculate = this.getNeedCalculate();
        result = result * 59 + ($needCalculate == null ? 43 : $needCalculate.hashCode());
        Object $distributionType = this.getDistributionType();
        result = result * 59 + ($distributionType == null ? 43 : $distributionType.hashCode());
        Object $distributionValue = this.getDistributionValue();
        result = result * 59 + ($distributionValue == null ? 43 : $distributionValue.hashCode());
        Object $createId = this.getCreateId();
        result = result * 59 + ($createId == null ? 43 : $createId.hashCode());
        Object $createName = this.getCreateName();
        result = result * 59 + ($createName == null ? 43 : $createName.hashCode());
        Object $createTime = this.getCreateTime();
        result = result * 59 + ($createTime == null ? 43 : $createTime.hashCode());
        return result;
    }

    public String toString() {
        return "TradeOrderItemDistributionDTO(id=" + this.getId() + ", finRoleCode=" + this.getFinRoleCode() + ", finOrderNo=" + this.getFinOrderNo() + ", ftoItemId=" + this.getFtoItemId() + ", orderItemId=" + this.getOrderItemId() + ", memberCode=" + this.getMemberCode() + ", memberName=" + this.getMemberName() + ", needCalculate=" + this.getNeedCalculate() + ", distributionType=" + this.getDistributionType() + ", distributionValue=" + this.getDistributionValue() + ", createId=" + this.getCreateId() + ", createName=" + this.getCreateName() + ", createTime=" + this.getCreateTime() + ")";
    }
}


