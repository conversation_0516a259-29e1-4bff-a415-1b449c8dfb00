package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

@Data
public class ExportSalesInStoreDetailDTO {

    @ApiModelProperty(value = "会员店编码")
    @Excel(name = "会员店编码", width = 20,orderNum = "1")
    private String buyerCode;

    @ApiModelProperty(value = "会员店名称")
    @Excel(name = "会员店名称", width = 20,orderNum = "2")
    private String buyerName;

    @ApiModelProperty(value = "apple_id")
    @Excel(name = "appleId", width = 20,orderNum = "3")
    private String appleId;

    @ApiModelProperty(value = "商品编码")
    @Excel(name = "商品编码", width = 20,orderNum = "4")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    @Excel(name = "商品名称", width = 20,orderNum = "5")
    private String itemName;

    @ApiModelProperty(value = "店铺编码")
    @Excel(name = "店铺编码", width = 20,orderNum = "6")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    @Excel(name = "店铺名称", width = 20,orderNum = "7")
    private String shopName;

    @ApiModelProperty(value = "供应商编码")
    @Excel(name = "供应商编码", width = 20,orderNum = "8")
    private String supplierCode;

    @ApiModelProperty(value = "仓库编码")
    @Excel(name = "仓库编码", width = 20,orderNum = "9")
    private String warehouseCode;

    @ApiModelProperty(value = "采购部门名称")
    @Excel(name = "采购部门名称", width = 20,orderNum = "10")
    private String departmentName;

    @ApiModelProperty(value = "商品数量")
    @Excel(name = "商品数量", width = 20,orderNum = "11")
    private BigDecimal goodsCount;

    @ApiModelProperty(value = "商品价格")
    @Excel(name = "商品价格", width = 20,orderNum = "12")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "合计金额")
    @Excel(name = "合计金额", width = 20,orderNum = "13")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "合计数量")
    @Excel(name = "合计数量", width = 20,orderNum = "14")
    private BigDecimal totalCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
