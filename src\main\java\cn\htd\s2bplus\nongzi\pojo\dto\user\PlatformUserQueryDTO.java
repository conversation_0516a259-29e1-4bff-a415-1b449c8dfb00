package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class PlatformUserQueryDTO implements Serializable {

    private static final long serialVersionUID = 3281051318056231484L;

    @ApiModelProperty(value = "客户编码",example = "htd20070002")
    private String memberCode;

    @ApiModelProperty(value = "联系人姓名/昵称",example = "大福")
    private String businessPersonName;

    @ApiModelProperty(value = "注册手机号",example = "***********")
    private String businessPersonMobile;

    @ApiModelProperty(value = "一级分类 门店会员：MD、云店会员：YD、政企客户：ZQ",example = "MD")
    private String firstClassCode;

    @ApiModelProperty(value = "二级分类"+
            " 会员门店：A1、授权门店：A2、经销代理：A3" +
    "网店店主:B1、社群团长：B2、主播达人：B3"+
    "政府/中大型企业:C1、普通企业:C2",example = "C1")
    private String secondClassCode;

    @ApiModelProperty(value = "注册时间开始",example = "2023-01-01 00:00:00")
    private Date registerTimeBegin;

    @ApiModelProperty(value = "注册时间结束",example = "2023-01-01 00:00:00")
    private Date registerTimeEnd;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "页码",example = "1",required = true)
    private Integer page;

    @NotNull(message = "分页数量不能为空")
    @ApiModelProperty(value = "分页数量",example = "10",required = true)
    private Integer size;

    @ApiModelProperty(value = "客户性质分类：" +
            "MD-A1：门店会员-会员门店，" +
            "MD-A2：门店会员-授权门店，" +
            "MD-A3：门店会员-经销代理，" +
            "YD-B1：云店会员-网店店主，" +
            "YD-B2：云店客户-社群团长，" +
            "YD-B3：云店客户-主播达人，" +
            "ZQ-C1：政企客户-政府/中大型企业，"+
            "ZQ-C2：政企客户-普通企业",hidden = true)
    private String customerClassCode;

    @ApiModelProperty(value = "橙意采:客户分类是否确认(1-确认 2-未确认),其他平台:客户标签来源(不再维护)")
    private Integer customerLabelSource;

    @ApiModelProperty(value = "实际经营地址-省 实际经营地址-省", example = "32")
    private String actualBusinessProvince;
    @ApiModelProperty(value = "实际经营地址-市 实际经营地址-市", example = "3206")
    private String actualBusinessCity;
    @ApiModelProperty(value = "实际经营地址-区 实际经营地址-区", example = "320611")
    private String actualBusinessCounty;
    @ApiModelProperty(value = "实际经营地址-镇 实际经营地址-镇", example = "320611400")
    private String actualBusinessTown;
    @ApiModelProperty(value = "审核状态 1审核中、2审核通过、3审核驳回")
    private String verifyStatus;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
