package cn.htd.s2bplus.nongzi.service.security.impl;

import cn.htd.rdc.base.development.framework.core.exception.CommonRuntimeException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.common.S2bPlusCommonConstants;
import cn.htd.s2bplus.common.dto.cipher.SingleDecryptInputDTO;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.service.security.BaseService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/12/23 15:50
 */
@Service
@Slf4j
public class BaseServiceImpl implements BaseService {

    @Autowired
    private MiddleGroundAPI middleGroundAPI;


    @Override
    public String decryptSingle(String str, String scene){
        if (StringUtils.isEmpty(str)) {
            return str;
        }
        String userAccount = BaseContextHandler.getLoginId();
        // 兼容(姓名 电话 地址)密文拼接场景
        return getString(str, scene, userAccount);
    }

    private String getString(String str, String scene, String userAccount) {
        if (str.contains(StringUtils.SPACE)) {
            String[] theCipherArr = str.split(StringUtils.SPACE);
            StringBuilder decipher = new StringBuilder();
            for (String cipher : theCipherArr) {
                if (StringUtils.isBlank(cipher)) {
                    log.info("请检查数据,str:{}",str);
                    continue;
                }
                Result<String> result = middleGroundAPI.decryptSingle(new SingleDecryptInputDTO(cipher, userAccount, S2bPlusCommonConstants.APP_NAME_OOP, scene));
                if (!result.isSuccess()) {
                    throw new CommonRuntimeException(result.getCode(), result.getMsg());
                }
                decipher.append(result.getData());
            }
            return decipher.toString();
        }
        SingleDecryptInputDTO inputDTO = new SingleDecryptInputDTO();
        inputDTO.setCipher(str);
        inputDTO.setUserAccount(userAccount);
        inputDTO.setBusinessSystem(S2bPlusCommonConstants.APP_NAME_OOP);
        inputDTO.setSceneId(scene);
        Result<String> result = middleGroundAPI.decryptSingle(inputDTO);
        if (!result.isSuccess()) {
            throw new CommonRuntimeException(result.getCode(), result.getMsg());
        }
        return result.getData();
    }
}
