package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@ExcelTarget("MemberGroupResponseDTO")
@Data
public class MemberGroupResponseDTO implements Serializable {
    private static final long serialVersionUID = 5316408737818022103L;

    @ApiModelProperty(value = "分组ID",example = "1")
    private Long groupId;

    @ApiModelProperty(value = "会员编码",example = "1")
    private Long buyerId;

    @ApiModelProperty(value = "会员类型 1.非会员、2.产业会员、3.标准会员", notes = "会员类型 1.非会员、2.产业会员、3.标准会员", example = "1")
    private String memberFlag;

    @ApiModelProperty(value = "会员编码", example = "926388")
    @Excel(name = "会员编码", height = 10, width = 22)
    private String memberCode;

    @ApiModelProperty(value = "会员名称",example = "美的")
    private String name;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
