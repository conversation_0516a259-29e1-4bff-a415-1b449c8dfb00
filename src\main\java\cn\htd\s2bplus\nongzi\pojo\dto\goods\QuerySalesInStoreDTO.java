package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class QuerySalesInStoreDTO implements Serializable {
    private static final long serialVersionUID = 6679935127126577174L;

    @ApiModelProperty(value = "活动编码")
    private String activityCode;

    @ApiModelProperty(value = "会员店名称")
    private String buyerName;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "页数")
    @NotNull(message = "页数不可为空")
    private Integer page;

    @ApiModelProperty(value = "条数")
    @NotNull(message = "条数不可为空")
    private Integer rows;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
