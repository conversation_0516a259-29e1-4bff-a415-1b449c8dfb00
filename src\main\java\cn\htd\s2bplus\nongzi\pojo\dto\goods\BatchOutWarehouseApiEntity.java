package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

@Data
public class BatchOutWarehouseApiEntity implements Serializable {


	@ApiModelProperty(name = "仓库编码")
	@NotBlank(message ="仓库编码 不可为空")
	private String warehouseCode;

	@ApiModelProperty(name="商品库存编码")
	@NotBlank(message ="商品库存编码 不可为空")
	private String stockCode;

	@ApiModelProperty(name = "出库数量")
	@NotBlank(message ="出库数量 不可为空")
	private BigDecimal useNum;

	private String orderNo;

	private String skuCode;


	/**
	 * 校验并聚合重复的出库记录
	 * @param dtoList 出库实体列表
	 * @return 聚合后的出库实体列表
	 */
	public static List<BatchOutWarehouseApiEntity> aggregateBatchOutWarehouseEntities(List<BatchOutWarehouseApiEntity> dtoList) {
		// 定义复合键类
		class CompositeKey {
			String warehouseCode;
			String stockCode;

			CompositeKey(String warehouseCode, String stockCode) {
				this.warehouseCode = warehouseCode;
				this.stockCode = stockCode;
			}

			@Override
			public boolean equals(Object o) {
				if (this == o) return true;
				if (o == null || getClass() != o.getClass()) return false;
				CompositeKey that = (CompositeKey) o;
				return
						Objects.equals(warehouseCode, that.warehouseCode) &&
						Objects.equals(stockCode, that.stockCode);
			}

			@Override
			public int hashCode() {
				return Objects.hash( warehouseCode, stockCode);
			}
		}
		// 使用复合键作为Map的键
		Map<CompositeKey, BigDecimal> aggregatedMap = new HashMap<>();

		// 遍历原始列表
		for (BatchOutWarehouseApiEntity entity : dtoList) {
			CompositeKey key = new CompositeKey(entity.getWarehouseCode(), entity.getStockCode());
			BigDecimal currentSum = aggregatedMap.getOrDefault(key, BigDecimal.ZERO);
			aggregatedMap.put(key, currentSum.add(entity.getUseNum()));
		}

		// 构建结果列表
		List<BatchOutWarehouseApiEntity> result = new ArrayList<>();
		for (Map.Entry<CompositeKey, BigDecimal> entry : aggregatedMap.entrySet()) {
			BatchOutWarehouseApiEntity entity = new BatchOutWarehouseApiEntity();
			entity.setWarehouseCode(entry.getKey().warehouseCode);
			entity.setStockCode(entry.getKey().stockCode);
			entity.setUseNum(entry.getValue());
			result.add(entity);
		}

		return result;
	}
}
