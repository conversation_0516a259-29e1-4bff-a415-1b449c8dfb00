package cn.htd.s2bplus.nongzi.service.goods.impl;

import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.CollectionUtil;
import cn.htd.rdc.base.development.framework.core.util.StringUtil;
import cn.htd.s2bplus.common.AppNacosConfig;
import cn.htd.s2bplus.common.enums.PlatformEnum;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.OssNacosConfig;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.*;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsUpShelfFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.MdmCategoryQueryDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.*;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.*;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.pojo.vo.ImportItemOrSkuListVO;
import cn.htd.s2bplus.nongzi.pojo.vo.MdmCategoryVO;
import cn.htd.s2bplus.nongzi.pojo.vo.SellerSkuVO;
import cn.htd.s2bplus.nongzi.pojo.vo.ShopAuthSubAccountVO;
import cn.htd.s2bplus.nongzi.service.goods.GoodsService;
import cn.htd.s2bplus.nongzi.service.history.ReportHistoryService;
import cn.htd.s2bplus.nongzi.service.order.SubAccountService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.FileInfoDTO;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class GoodsServiceImpl implements GoodsService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    SubAccountService subAccountService;

    @Autowired
    private GoodsFeignService goodsFeignService;

    @Autowired
    private GoodsUpShelfFeignService goodsUpShelfFeignService;

    @Autowired
    private MiddleGroundAPI middleGroundAPI;

    @Autowired
    private OssNacosConfig ossNacosConfig;

    @Autowired
    private ReportHistoryService reportHistoryService;

    @Autowired
    private AppNacosConfig appNacosConfig;

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;

    /**
     * @Date 2022/5/31
     * @description 查询子账号店铺
     */
    @Override
    public Map<Long,String> getShopInfo(LoginUserDetail loginUser, String shopName) {
        Map<Long,String> map = new HashMap<>();
        Result<List<ShopAuthSubAccountVO>> subResult = subAccountService.querySubAllocateShopByLoginId(loginUser.getSubAccountLoginId(),shopName,loginUser.getMemberCode());
        log.info("查询子账号店铺queryShopInfoByCondition resp:{}",subResult);
        if(subResult.isSuccess()&&!CollectionUtils.isEmpty(subResult.getData())){
            map = subResult.getData().stream().collect(Collectors.toMap(ShopAuthSubAccountVO::getShopId,ShopAuthSubAccountVO::getShopName));
        }
        return map;
    }

    @Override
    public Result<ImportItemOrSkuListVO> importItemOrSkuList(MultipartFile file, Long shopId, Integer type) {
        logger.info("解析商品/SKU-限购策略开始");
        Result<ImportItemOrSkuListVO> result = new Result();
        ImportItemOrSkuListVO importItemOrSkuListVO = new ImportItemOrSkuListVO();
        List<ImportItemByPurchaseDTO> list = new ArrayList();
        List<SkuQueryDTO> skuQueryDTOS = new ArrayList();
        List<String> errorCodeList = new ArrayList();
        List<String> codeList = new ArrayList();
        OssUtils ossUtils = new OssUtils();
        String downloadUrl="";
        String fileName = "";
        String time = DateUtil.getCurrentDateFull();
        int importMaxCount = 100;

        try {
            if (ObjectUtils.isEmpty(shopId) || ObjectUtils.isEmpty(type)) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ResultEnum.PARAM_MISS.getMsg());
            }
            //校验文件类型
            if (ObjectUtils.isEmpty(file) || StringUtils.isEmpty(file.getOriginalFilename())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
            if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equals(substring) || StrConstant.XLSX.equals(substring))) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_FILE_ERROR.getMsg());
            }
            long start = System.currentTimeMillis();
            EasyExcel.read(file.getInputStream(), ImportItemByPurchaseDTO.class, new ReadListener<ImportItemByPurchaseDTO>() {
                public static final int BATCH_COUNT = 200;
                private final List<ImportItemByPurchaseDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

                @Override
                public void invoke(ImportItemByPurchaseDTO data, AnalysisContext context) {
                    int rowNumber = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
                    int rowIndex = context.readSheetHolder().getRowIndex() + 1;
                    SkuQueryDTO skuQueryDTO = new SkuQueryDTO();
                    if (rowNumber == 0) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
                    }
                    if (rowNumber > importMaxCount) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_MAX_ERROR.getMsg() + importMaxCount);
                    }
                    if (StringUtils.isEmpty(data.getCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_ITEMCODE_NULL.getMsg());
                    }
                    if (ObjectUtils.isNotEmpty(data.getPurchaseCount()) && (data.getPurchaseCount() < 1 || data.getPurchaseCount() > 999)) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.PURCHASE_COUNT_ITEMCODE_ERROR.getMsg());
                    }
                    if (codeList.contains(data.getCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "编码:" + data.getCode() + ApiResultEnum.IMPORT_ITEM_REPEAT.getMsg());
                    }
                    if (CommonConstants.IMPORT_TYPE_ITEM.equals(type)) {
                        skuQueryDTO.setItemCode(data.getCode());
                    } else if (CommonConstants.IMPORT_TYPE_SKU.equals(type)) {
                        skuQueryDTO.setSkuCode(data.getCode());
                    }
                    skuQueryDTO.setShopId(shopId);
                    skuQueryDTOS.add(skuQueryDTO);
                    codeList.add(data.getCode());
                    cachedDataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (!CollectionUtils.isEmpty(cachedDataList)) {
                        list.addAll(cachedDataList);
                    }
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) {
                    logger.info("解析商品/SKU-限购策略失败 :{}", exception.getMessage());
                    if (exception instanceof BusinessException) {
                        throw new BusinessException(ResultEnum.ERROR.getCode(), exception.getMessage());
                    } else if (exception instanceof ExcelDataConvertException) {
                        ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                        String errorMsg = ApiResultEnum.IMPORT_ERROR_LINE_COLUMN.getMsg() + "解析异常";
                        Integer rowIndex = excelDataConvertException.getRowIndex() + 1;
                        Integer columnIndex = excelDataConvertException.getColumnIndex() + 1;
                        logger.info("商品/SKU-限购策略,第{}行，第{}列解析异常，数据为:{}", rowIndex, columnIndex, JSON.toJSONString(excelDataConvertException.getCellData()));
                        throw new BusinessException(ResultEnum.ERROR.getCode(), String.format(errorMsg, rowIndex, columnIndex));
                    }
                }
            }).sheet().doRead();
            logger.info("商品/SKU-限购策略解析出来的数据量：{}", list.size());
            logger.info("商品/SKU-限购策略解析excel时间占用 :{}ms", System.currentTimeMillis() - start);
            logger.info("商品/SKU-限购策略数据完毕数据:{}", list);
            if (skuQueryDTOS.size() == 0) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            logger.info("根据商品/sku编码、店铺id，批量查询商品/sku信息 入参:{}", skuQueryDTOS);
            Result<List<SkuOutDTO>> listResult = goodsFeignService.batchQueryItemInfo(skuQueryDTOS);
            logger.info("根据商品/sku编码、店铺id，批量查询商品/sku信息 出参:{}", listResult);
            if (!listResult.isSuccess()) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.BATCH_QUERY_ITEM_INFO_ERROR.getMsg());
            }
            if (ObjectUtils.isEmpty(listResult.getData())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), codeList.get(0) + ApiResultEnum.IMPORT_ITEM_ERROR.getMsg());
            }
            if (CommonConstants.IMPORT_TYPE_ITEM.equals(type)) {
                fileName = "批量导入商品_限购策略" + time + CommonConstants.EXCEL_XLSX;
                errorCodeList = codeList.stream().filter(
                        code -> listResult.getData().stream().noneMatch(
                                each -> code.equals(each.getItemCode()))).collect(Collectors.toList());
            } else if (CommonConstants.IMPORT_TYPE_SKU.equals(type)) {
                fileName = "批量导入SKU_限购策略" + time + CommonConstants.EXCEL_XLSX;
                errorCodeList = codeList.stream().filter(
                        code -> listResult.getData().stream().noneMatch(
                                each -> code.equals(each.getSkuCode()))).collect(Collectors.toList());
            }
            if (ObjectUtils.isNotEmpty(errorCodeList)) {
                if (CommonConstants.IMPORT_TYPE_ITEM.equals(type)) {
                    throw new BusinessException(ApiResultEnum.ERROR.getCode(), errorCodeList.get(0) + ApiResultEnum.IMPORT_ITEM_ERROR.getMsg());
                }
                if (CommonConstants.IMPORT_TYPE_SKU.equals(type)) {
                    throw new BusinessException(ApiResultEnum.ERROR.getCode(), errorCodeList.get(0) + ApiResultEnum.IMPORT_ITEM_ERROR.getMsg());
                }
            }
            downloadUrl = ossUtils.upload(file.getInputStream(),fileName, bucket, endpoint, accessKeyId, accessKeySecret);
            if (StringUtils.isEmpty(downloadUrl)){
                throw new BusinessException(ApiResultEnum.ERROR.getCode(),ApiResultEnum.UPLOAD_OSS_ERROR.getMsg());
            }
            importItemOrSkuListVO.setImportItemByPurchaseList(list);
            importItemOrSkuListVO.setDownloadUrl(downloadUrl);
            result.setData(importItemOrSkuListVO);
            result.setCode(ApiResultEnum.SUCCESS.getCode());
            result.setMsg("限购批量导入商品/SKU成功");
        } catch (BusinessException e) {
            logger.info("限购批量导入商品/SKU失败:{}", e.getMessage());
            result.setCode(e.getCode());
            result.setMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("限购批量导入商品/SKU异常", e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("限购批量导入商品异常");
        }
        return result;
    }


    /**
     * sku上下架导入
     * @param file
     */
    @Override
    public Result<String> ImportSkuUpShelf(MultipartFile file){
        Result<String> result = new Result<>();
        List<ImportSkuUpShelfDTO> importList = new ArrayList<>();
        Map<String,ImportSkuUpShelfDTO> importMap = new HashMap<>();
        int importMaxCount = 100;
        try{
            //校验文件类型
            if (ObjectUtils.isEmpty(file) || StringUtils.isEmpty(file.getOriginalFilename())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
            if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equals(substring) || StrConstant.XLSX.equals(substring))) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_FILE_ERROR.getMsg());
            }

            //校验导入行数据
            long start = System.currentTimeMillis();
            EasyExcel.read(file.getInputStream(), ImportSkuUpShelfDTO.class, new ReadListener<ImportSkuUpShelfDTO>() {

                @Override
                public void invoke(ImportSkuUpShelfDTO data, AnalysisContext context) {
                    int rowNumber = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
                    int rowIndex = context.readSheetHolder().getRowIndex() + 1;
                    if (rowNumber == 0) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
                    }
                    if (rowNumber > importMaxCount) {
                        throw new BusinessException(ApiResultEnum.IMPORT_MAX_NUM_ERROR.getCode(), ApiResultEnum.IMPORT_MAX_NUM_ERROR.getMsg() + importMaxCount);
                    }
                    if (StringUtils.isEmpty(data.getSkuCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_SKU_CODE_NULL.getMsg());
                    }
                    if (!StringUtils.isNumeric(data.getSkuCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_SKU_CODE_ERROR.getMsg());
                    }
                    if (ObjectUtils.isEmpty(data.getShopId())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_SHOP_ID_NULL.getMsg());
                    }
                    if (!StringUtils.isNumeric(data.getShopId().toString())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_SHOP_ID_ERROR.getMsg());
                    }
                    if (ObjectUtils.isEmpty(data.getIsVisable())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_OPERATION_NULL.getMsg());
                    }
                    if (!(CommonConstants.UP_SHELF.equals(data.getIsVisable()) || CommonConstants.OFF_SHELF.equals(data.getIsVisable()))) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_OPERATION_ERROR.getMsg());
                    }
                    //存在重复数据，取后一条为准
                    importMap.put(data.getSkuCode() + CommonConstants.UNDERLINE + data.getShopId(),data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (ObjectUtils.isNotEmpty(importMap)) {
                        importList.addAll(importMap.values());
                    }
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) {
                    log.info("解析sku上下架导入失败 :{}", exception.getMessage());
                    if (exception instanceof BusinessException) {
                        throw new BusinessException(((BusinessException) exception).getCode(), exception.getMessage());
                    } else if (exception instanceof ExcelDataConvertException) {
                        ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                        Integer rowIndex = excelDataConvertException.getRowIndex() + 1;
                        Integer columnIndex = excelDataConvertException.getColumnIndex() + 1;
                        String errorMsg = ApiResultEnum.IMPORT_ERROR_LINE_COLUMN.getMsg() + "解析异常";
                        log.info("解析sku上下架导入第{}行，第{}列异常，数据为:{}", rowIndex, columnIndex, JSON.toJSONString(excelDataConvertException.getCellData()));
                        throw new BusinessException(ResultEnum.ERROR.getCode(), String.format(errorMsg, rowIndex, columnIndex));
                    }
                }
            }).sheet().doRead();
            log.info("解析sku上下架导入excel耗时:{}", System.currentTimeMillis() - start);
            log.info("解析sku上下架导入完成数量:{}列表:{}", importList.size(),importList);
            if (CollectionUtils.isEmpty(importList)) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            SkuUpShelfDTO skuUpShelfDTO = this.buildSkuUpShelfDTO(importList);
            logger.info("sku上下架导入 req:{}",skuUpShelfDTO);
            Result<BatchSkuUpShelfRespDTO> respDTOResult = goodsUpShelfFeignService.batchSkuUpShelf(skuUpShelfDTO);
            logger.info("sku上下架导入 resp:{}",respDTOResult);
            if(respDTOResult.isSuccess()){
                return CommonResultUtil.success(respDTOResult.getData().getFileUrl());
            }
            result.setCode(ResultEnum.ERROR.getCode());
            result.setSuccess(false);
            result.setMsg(StringUtils.isEmpty(respDTOResult.getMsg()) ? "上下架导入失败" : respDTOResult.getMsg());
            return result;
        }catch (BusinessException be){
            log.info("解析sku上下架导入失败:{}", be.getMessage());
            result.setCode(be.getCode());
            result.setMsg(be.getMessage());
        }catch (Exception e){
            logger.error("解析sku上下架导入异常",e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("解析sku上下架导入异常");
        }
        return result;
    }

    @Override
    public Result<String> importRemoveItem(MultipartFile file) {
        Result<String> result = new Result<>();
        List<ImportRemoveItemDTO> importList = new ArrayList<>();
        Map<String, ImportRemoveItemDTO> importMap = new HashMap<>();
        int importMaxCount = 100;
        try {
            //校验文件类型
            if (ObjectUtils.isEmpty(file) || StringUtils.isEmpty(file.getOriginalFilename())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
            if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equals(substring) || StrConstant.XLSX.equals(substring))) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_FILE_ERROR.getMsg());
            }

            //校验导入行数据
            long start = System.currentTimeMillis();
            EasyExcel.read(file.getInputStream(), ImportRemoveItemDTO.class, new ReadListener<ImportRemoveItemDTO>() {

                @Override
                public void invoke(ImportRemoveItemDTO data, AnalysisContext context) {
                    // 新增检查，如果data为null或者重要字段全为空，则直接返回，忽略此行
                    if (data == null || (StringUtils.isEmpty(data.getItemCode())  && ObjectUtils.isEmpty(data.getDelistingType()))) {
                        log.warn("第 {} 行数据为空，已跳过。", context.readSheetHolder().getRowIndex() + 1);
                        return;
                    }
                    int rowNumber = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
                    int rowIndex = context.readSheetHolder().getRowIndex() + 1;
                    if (rowNumber == 0) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
                    }
                    if (rowNumber > importMaxCount) {
                        throw new BusinessException(ApiResultEnum.IMPORT_MAX_NUM_ERROR.getCode(), ApiResultEnum.IMPORT_MAX_NUM_ERROR.getMsg() + importMaxCount);
                    }
                    if (StringUtils.isEmpty(data.getItemCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_ITEM_CODE_NULL.getMsg());
                    }
                    if (StringUtils.isEmpty(data.getDelistingTypeName())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_DELISTING_TYPE_NULL.getMsg());
                    }
                    if (null==DelistingTypeEnum.getCodeByMsg(data.getDelistingTypeName())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_DELISTING_TYPE_ERROR.getMsg());
                    }
                    data.setDelistingType(DelistingTypeEnum.getCodeByMsg(data.getDelistingTypeName()));
                    if (StringUtils.isNotBlank(data.getDelistingRemark())&& data.getDelistingRemark().length() > 50) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_DELISTING_REMARK_ERROR.getMsg());
                    }
                    if(StringUtils.isBlank(data.getDelistingRemark())){
                        data.setDelistingRemark("");
                    }
                    //存在重复数据，取后一条为准
                    importMap.put(data.getItemCode(), data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (ObjectUtils.isNotEmpty(importMap)) {
                        importList.addAll(importMap.values());
                    }
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) {
                    log.info("解析oss-批量导入下架商品失败 :{}", exception.getMessage());
                    if (exception instanceof BusinessException) {
                        throw new BusinessException(((BusinessException) exception).getCode(), exception.getMessage());
                    } else if (exception instanceof ExcelDataConvertException) {
                        ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                        Integer rowIndex = excelDataConvertException.getRowIndex() + 1;
                        Integer columnIndex = excelDataConvertException.getColumnIndex() + 1;
                        String errorMsg = ApiResultEnum.IMPORT_ERROR_LINE_COLUMN.getMsg() + "解析异常";
                        log.info("解析oss-批量导入下架商品第{}行，第{}列异常，数据为:{}", rowIndex, columnIndex, JSON.toJSONString(excelDataConvertException.getCellData()));
                        throw new BusinessException(ResultEnum.ERROR.getCode(), String.format(errorMsg, rowIndex, columnIndex));
                    }
                }
            }).sheet().doRead();
            log.info("解析oss-批量导入下架商品excel耗时:{}", System.currentTimeMillis() - start);
            log.info("解析oss-批量导入下架商品完成数量:{}列表:{}", importList.size(), importList);
            if (CollectionUtils.isEmpty(importList)) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            UpShelfDTO upShelfDTO = this.buildUpShelfDTO(importList);
            logger.info("批量导入下架商品 req:{}", upShelfDTO);
            Result<String> respDTOResult = goodsUpShelfFeignService.batchUpShelfItems(upShelfDTO);
            logger.info("批量导入下架商品 resp:{}", respDTOResult);
            if (respDTOResult.isSuccess()) {
                return CommonResultUtil.success("批量导入下架商品成功");
            }
            result.setCode(ResultEnum.ERROR.getCode());
            result.setSuccess(false);
            result.setMsg(StringUtils.isEmpty(respDTOResult.getMsg()) ? "上下架导入失败" : respDTOResult.getMsg());
            return result;
        } catch (BusinessException be) {
            log.info("批量导入下架商品失败:{}", be.getMessage());
            result.setCode(be.getCode());
            result.setMsg(be.getMessage());
        } catch (Exception e) {
            logger.error("批量导入下架商品异常", e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("批量导入下架商品异常");
        }
        return result;
    }

    /**
     * 更新意向单收货人信息
     *
     * @param addressRecordAddDTOS
     */
    @Async
    @Override
    public void updateConsigneeInfo(List<IntentionAddressRecordAddDTO> addressRecordAddDTOS) {
        if (CollectionUtils.isEmpty(addressRecordAddDTOS)) {
            return;
        }
        List<IntentionAddressRecordAddDTO> updateAddressRecordAddDTOS = addressRecordAddDTOS.stream().filter(address -> (StringUtils.isNotBlank(address.getConsigneeMobile()) || StringUtils.isNotBlank(address.getConsigneeName()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateAddressRecordAddDTOS)) {
            log.info("更新收货人信息入参:{}", addressRecordAddDTOS);
            Result<Boolean> consigneeInfoResult = goodsFeignService.updateConsigneeInfo(updateAddressRecordAddDTOS);
            log.info("更新收货人信息出参:{}", consigneeInfoResult);
        }
    }

    private UpShelfDTO buildUpShelfDTO(List<ImportRemoveItemDTO> importList) {
        UpShelfDTO upShelfDTO = new UpShelfDTO();
        LoginUserDetail user = BaseContextHandler.getLoginUser();
        upShelfDTO.setOperateId(user.getUserId());
        upShelfDTO.setOperateName(user.getUserName());
        upShelfDTO.setIsVisable(CommonConstants.OFF_SHELF);
        // 根据商品编码查询商品id
        List<ItemsUpShelfDTO> itemsUpShelfList = new ArrayList<>();
        for(ImportRemoveItemDTO ImportItemDTO : importList){
            ItemsUpShelfDTO itemsUpShelfDTO = new ItemsUpShelfDTO();
            itemsUpShelfDTO.setItemCode(ImportItemDTO.getItemCode());
            itemsUpShelfDTO.setDelistingType(ImportItemDTO.getDelistingType());
            itemsUpShelfDTO.setDelistingRemark(ImportItemDTO.getDelistingRemark());
            itemsUpShelfDTO.setIsVisable(CommonConstants.OFF_SHELF);
            itemsUpShelfDTO.setItemActionType(CommonConstants.ITEM_ACTION_TYPE_OSS);
            itemsUpShelfList.add(itemsUpShelfDTO);
        }
        upShelfDTO.setItemsUpShelfList(itemsUpShelfList);
        return upShelfDTO;
    }


    /**
     * 组装sku上下架导入参数
     *
     */
    private SkuUpShelfDTO  buildSkuUpShelfDTO(List<ImportSkuUpShelfDTO> importList){
        SkuUpShelfDTO skuUpShelfDTO = new SkuUpShelfDTO();
        LoginUserDetail user = BaseContextHandler.getLoginUser();
        skuUpShelfDTO.setSellerCode(user.getMemberCode());
        skuUpShelfDTO.setOperationCode(user.getLoginId());
        skuUpShelfDTO.setSellerType(user.getSellerType());
        skuUpShelfDTO.setModifyId(user.getMemberId());
        skuUpShelfDTO.setModifyName(user.getUserName());
        List<SkuShopReqDTO> skuShopReqDTOS = new ArrayList<>();
        importList.forEach(importSkuUpShelfDTO -> {
            SkuShopReqDTO skuShopReqDTO = new SkuShopReqDTO();
            BeanUtils.copyProperties(importSkuUpShelfDTO,skuShopReqDTO);
            if(user.getLoginId().equals(user.getMemberCode())){
                skuUpShelfDTO.setSellerId(user.getMemberId());
                skuShopReqDTO.setSellerId(user.getMemberId());
                skuShopReqDTO.setModifyId(user.getMemberId());
                skuShopReqDTO.setModifyName(user.getUserName());
                skuShopReqDTOS.add(skuShopReqDTO);
            } else {
                LoginUserDetail parentAccount = user.getParentAccount();
                skuUpShelfDTO.setSellerId(parentAccount.getMemberId());
                skuShopReqDTO.setSellerId(parentAccount.getMemberId());
                skuShopReqDTO.setModifyId(parentAccount.getMemberId());
                skuShopReqDTO.setModifyName(user.getUserName());
                skuShopReqDTOS.add(skuShopReqDTO);
            }
        });
        skuUpShelfDTO.setSkuShopDTOS(skuShopReqDTOS);
        return skuUpShelfDTO;
    }


    /**
     * 	校验导入商品数据，将校验结果生成excel文件上传并保存，同时保存校验通过的货盘商品
     *
     */
    @Async
    @Override
    public void importGoodsList(List<ImportGoodsInfoDTO> importGoodsInfoDTOS, LoginUserDetail user, String fileName, HttpServletResponse response){
        // 将校验通过的商品、校验不通过的商品分别生成文件
        List<ImportGoodsRecordDTO> failedRecordList = new ArrayList<>();
        List<ImportGoodsInfoDTO> successRecordList = new ArrayList<>();

        List<String> itemNameList = new ArrayList<>();
        // 校验通过的商品保存
        List<ImportGoodsInfo> saveImportGoodList = new ArrayList<>();
        for (ImportGoodsInfoDTO importGoodsReq : importGoodsInfoDTOS) {
            ImportGoodsRecordDTO importGoodsRecordDTO = new ImportGoodsRecordDTO();
            ImportGoodsInfo importSuccessGoodDTO = new ImportGoodsInfo();
            BeanUtils.copyProperties(importGoodsReq, importGoodsRecordDTO);
            // 校验不通过的商品加上失败原因
            List<String> errorReasonList = this.validateImportGoods(importGoodsReq,user,itemNameList);
            if (CollectionUtil.isNotEmpty(errorReasonList)) {
                logger.info("导入货盘商品：{}校验不通过原因：{}", importGoodsReq, errorReasonList);
                importGoodsRecordDTO.setErrorReason(String.join(";\n", errorReasonList));
                importGoodsRecordDTO.setErrorReasonList(errorReasonList);
                failedRecordList.add(importGoodsRecordDTO);
            } else {
                BaseDictionaryDTO baseDictionaryDTO = new BaseDictionaryDTO();
                baseDictionaryDTO.setName(importGoodsReq.getUnit());
                baseDictionaryDTO.setParentCode("ITEM_UNIT");
                baseDictionaryDTO.setIsLike("1");
                log.info("查询单位列表 入参:{}",baseDictionaryDTO);
                PageResult<List<BaseDictionaryDTO>> pageResult = middleGroundAPI.queryBaseDictionaryListByCondition(baseDictionaryDTO,1,200);
                log.info("查询单位列表 出参:{}",pageResult);
                // 单位不存在，去新增单位
                if(!pageResult.isSuccess() || ObjectUtils.isEmpty(pageResult) || CollectionUtils.isEmpty(pageResult.getData())){
                    log.info("新增商品单位 入参:{}",importGoodsReq.getUnit());
                    Result<String> result = middleGroundAPI.addSingleItemUnit(importGoodsReq.getUnit(),user.getMemberId(),user.getUserName());
                    log.info("新增商品单位 出参:{}",result);
                }
                BeanUtils.copyProperties(importGoodsReq, importSuccessGoodDTO);
                importSuccessGoodDTO.setBusinessStatus(CommonConstants.BUSINESS_INITIAL_STATUS);
                importSuccessGoodDTO.setBusinessType(CommonConstants.PUBLISH_IMPORT_GOODS_TYPE);
                importSuccessGoodDTO.setSellerCode(user.getLoginId());
                importSuccessGoodDTO.setCreateId(user.getMemberId().toString());
                importSuccessGoodDTO.setCreateName(user.getUserName());
                // 判断是否为子账号
                if (!org.springframework.util.ObjectUtils.isEmpty(user.getParentAccount())) {
                    importSuccessGoodDTO.setSubSellerCode(user.getSubAccountLoginId());
                    importSuccessGoodDTO.setSellerCode(user.getParentAccount().getLoginId());
                    importSuccessGoodDTO.setCreateId(user.getParentAccount().getMemberId().toString());
                    importSuccessGoodDTO.setCreateName(user.getParentAccount().getUserName());
                }
                saveImportGoodList.add(importSuccessGoodDTO);
                successRecordList.add(importGoodsReq);
                itemNameList.add(importGoodsReq.getItemName());
            }
        }
        if(CollectionUtil.isNotEmpty(saveImportGoodList)){
            log.info("导入货盘商品-保存校验通过商品 req:{}",saveImportGoodList);
            Result<Boolean> saveResult = goodsFeignService.saveImportGoods(saveImportGoodList);
            log.info("导入货盘商品-保存校验通过商品 resp:{}",saveResult);
        }

        // 将导入货盘商品记录生成excel文件，并保存记录
        this.saveImportGoodRecord(failedRecordList,new ArrayList<>(),user,fileName ,response);
        this.saveImportGoodRecord(new ArrayList<>(),successRecordList,user,fileName,response);
    }

    /**
     * 	校验导入的商品，校验不通过的商品加上失败原因
     *
     */
    private List<String> validateImportGoods(ImportGoodsInfoDTO importGoodsReq,LoginUserDetail user,  List<String> itemNameList) {
        List<String> errorReasonList = new ArrayList<>();
        if (StringUtils.isBlank(importGoodsReq.getItemName())) {
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_NAME_NULL.getMsg());
        } else {
            // 校验导入是否存在重复的商品名称
            if (CollectionUtil.isNotEmpty(itemNameList)) {
                if (itemNameList.contains(importGoodsReq.getItemName())) {
                    errorReasonList.add(ApiResultEnum.IMPORT_ITEM_NAME_REPEAT.getMsg());
                }
            }
            // 校验商品名称是否已存在
            SellerSkuQueryDTO queryReqDTO = new SellerSkuQueryDTO();
            queryReqDTO.setItemName(importGoodsReq.getItemName());
            queryReqDTO.setSellerId(user.getMemberId());
            if (ObjectUtils.isNotEmpty(user.getParentAccount())) {
                queryReqDTO.setSellerId(user.getParentAccount().getMemberId());
            }
            logger.info("导入货盘商品-根据名称查询商品列表 入参：{}", queryReqDTO);
            Result<SellerSkuVO> executeResult = middleGroundAPI.querySellerGoodsList(queryReqDTO, 1, 50);
            if (executeResult.isSuccess() && executeResult.getData() != null
                    && CollectionUtil.isNotEmpty(executeResult.getData().getSellerSkuList())) {
                logger.info("导入货盘商品-根据名称查询商品列表 出参：{}", executeResult);
                errorReasonList.add(ApiResultEnum.IMPORT_ITEM_NAME_EXISTS.getMsg()
                        + executeResult.getData().getSellerSkuList().get(0).getItemCode());
            }
        }
        if (StringUtils.isBlank(importGoodsReq.getUnit())) {
            errorReasonList.add(ApiResultEnum.IMPORT_UNIT_NULL.getMsg());
        }
        if (StringUtils.isNotBlank(importGoodsReq.getUnit()) && !importGoodsReq.getUnit().matches("[\u4e00-\u9fa5]+")) {
            errorReasonList.add(ApiResultEnum.UNIT_FORMAT_ERROR.getMsg());
        }
        if (ObjectUtils.isEmpty(importGoodsReq.getCost())) {
            errorReasonList.add(ApiResultEnum.IMPORT_COST_NULL.getMsg());
        } else {
            if (Boolean.FALSE.equals(importGoodsReq.getCost().compareTo(BigDecimal.ZERO) > 0)) {
                errorReasonList.add(ApiResultEnum.IMPORT_COST_BIGGER_ZERO.getMsg());
            }
            if(importGoodsReq.getCost().scale() > 2){
                errorReasonList.add(ApiResultEnum.IMPORT_COST_ERROR.getMsg());
            }
        }
        if (ObjectUtils.isEmpty(importGoodsReq.getRetailPrice())) {
            errorReasonList.add(ApiResultEnum.IMPORT_RETAIL_PRICE_NULL.getMsg());
        }else {
            if (Boolean.FALSE.equals(importGoodsReq.getRetailPrice().compareTo(BigDecimal.ZERO) > 0)) {
                errorReasonList.add(ApiResultEnum.IMPORT_RETAIL_PRICE_BIGGER_ZERO.getMsg());
            }
            if(importGoodsReq.getRetailPrice().scale() > 2){
                errorReasonList.add(ApiResultEnum.IMPORT_RETAIL_PRICE_ERROR.getMsg());
            }
        }
        if (StringUtils.isNotBlank(importGoodsReq.getEanCode()) && !StringUtils.isNumeric(importGoodsReq.getEanCode())) {
            errorReasonList.add(ApiResultEnum.EAN_CODE_FORMAT_ERROR.getMsg());
        }
        return errorReasonList;
    }

    /**
     * 	保存导入的商品记录
     *
     */
    private void saveImportGoodRecord(List<ImportGoodsRecordDTO> failedRecordList, List<ImportGoodsInfoDTO> successRecordList,LoginUserDetail user,String fileName,HttpServletResponse response){
        int lastDotIndex = fileName.lastIndexOf(".");
        // 生成excel文件，并上传的阿里云服务器返回文件地址路径
        OssUtils ossUtils = new OssUtils();
        String downloadUrl = "";
        if(CollectionUtil.isNotEmpty(failedRecordList)){
            downloadUrl = ossUtils.getDownloadUrl(failedRecordList, ImportGoodsRecordDTO.class, fileName.substring(0, lastDotIndex) + "_导入失败记录", ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        }
        if(CollectionUtil.isNotEmpty(successRecordList)){
            downloadUrl = ossUtils.getDownloadUrl(successRecordList, ImportGoodsInfoDTO.class, fileName.substring(0, lastDotIndex) +  "_导入成功记录", ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        }
        if (StringUtils.isBlank(downloadUrl)) {
            logger.info("上传阿里云货盘商品导入文件失败");
            return;
        }
        // 保存导入的货盘商品记录
        logger.info("保存导入的商品记录:{}:{}:{}",failedRecordList,successRecordList,downloadUrl);
        this.saveReportHistoryToGoodsService(downloadUrl,BusinessTypeEnum.GOODS_SERVICE_IMPORT_GOODS_INFO_RECORD.getCode(),user);
    }

    /**
     * 保存报表生成下载历史(保存至goods-service)
     */
    @Override
    public void saveReportHistoryToGoodsService(String downloadUrl,Integer businessType, LoginUserDetail loginUser){
        ReportHistory reportHistoryDTO = new ReportHistory();
        Date date = new Date();
        Long memberId = loginUser.getMemberId();
        String userName = loginUser.getUserName();
        // 判断是否为子账号，取主账号信息
        if (!org.springframework.util.ObjectUtils.isEmpty(loginUser.getParentAccount())) {
            memberId = loginUser.getParentAccount().getMemberId();
            userName = loginUser.getParentAccount().getUserName();
        }
        Timestamp monthTime = DateUtil.getMonthTime(CommonConstants.THREEMONTH);
        reportHistoryDTO.setBusinessType(businessType.byteValue());
        reportHistoryDTO.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
        reportHistoryDTO.setDownloadUrl(downloadUrl);
        reportHistoryDTO.setFinishTime(date);
        reportHistoryDTO.setBeginTime(monthTime);
        reportHistoryDTO.setEndTime(monthTime);
        reportHistoryDTO.setCreateId(memberId);
        reportHistoryDTO.setCreateName(userName);
        reportHistoryDTO.setCreateTime(date);
        reportHistoryDTO.setModifyId(memberId);
        reportHistoryDTO.setModifyName(userName);
        reportHistoryDTO.setModifyTime(date);

        // 保存报表生成下载历史
        log.info("保存报表生成下载历史 入参:{}",reportHistoryDTO);
        goodsFeignService.insertSelective(reportHistoryDTO);
    }




    /**
     * 校验保存商品信息
     */
    @Async
    @Override
    public void checkAndSaveImportGoods(List<BatchImportGoodsDTO> batchImportGoodsDTOS, List<FileInfoDTO> picFiles,String batchNo,LoginUserDetail loginUser) {
        log.info("校验保存商品信息 开始：{}：{}：{}：{}",batchImportGoodsDTOS,picFiles,batchNo,loginUser);
        if(CollectionUtils.isEmpty(batchImportGoodsDTOS)){
            return;
        }

        List<BatchImportGoodsDTO> importGoodsDTOS = new ArrayList<>();
        List<BatchImportGoodsRecordDTO> errorGoodsRecordList = new ArrayList<>();
        List<String> errorItemNameList = new ArrayList<>();
        List<String> errorAttrItemNameList = new ArrayList<>();
        for (BatchImportGoodsDTO batchImportGoodsDTO : batchImportGoodsDTOS) {
            // 基本字段校验
            this.checkNecessaryRow(batchImportGoodsDTO,picFiles);

            // 如果商品名称为空，不做后续业务校验
            if (StringUtils.isBlank(batchImportGoodsDTO.getItemName())) {
                this.handleMissingItemName(batchImportGoodsDTO,errorGoodsRecordList);
                continue;
            }

            importGoodsDTOS.add(batchImportGoodsDTO);

            // 处理有错误的商品
            this.processGoodsWithErrors(batchImportGoodsDTO,errorItemNameList,errorAttrItemNameList);
        }
        if (CollectionUtils.isEmpty(importGoodsDTOS)) {
            log.info("批导数据中商品名称都为空：{}", errorGoodsRecordList);
            this.batchImportRecordErrorList(errorGoodsRecordList, loginUser);
            return;
        }

        // 根据商品名称分组
        Map<String, List<BatchImportGoodsDTO>> sameItemNameMap = importGoodsDTOS.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getItemName()))
                .collect(Collectors.groupingBy(
                        BatchImportGoodsDTO::getItemName,
                        Collectors.toList()
                ));
        List<ImportGoodsInfo> saveImportGoodList = new ArrayList<>();
        ItemDTOReq itemDTOReq = new ItemDTOReq();
        for (List<BatchImportGoodsDTO> sameItemNameList : sameItemNameMap.values()) {
            List<String> itemErrorReasonList = new ArrayList<>();
            // 1、商品维度基础业务校验
            this.checkImportByItem(sameItemNameList, picFiles, itemDTOReq, loginUser, itemErrorReasonList);

            // 2、校验导入的规格
            if(errorAttrItemNameList.contains(sameItemNameList.get(0).getItemName())){
                this.buildMultipleErrorRecord(sameItemNameList, itemErrorReasonList, errorGoodsRecordList);
                continue;
            }
            this.validateImportAttributes(sameItemNameList.get(0), sameItemNameList, itemErrorReasonList);

            // 2.1、如果商品维度校验未通过，不做后续处理
            if (CollectionUtil.isNotEmpty(itemErrorReasonList)) {
                this.buildMultipleErrorRecord(sameItemNameList, itemErrorReasonList, errorGoodsRecordList);
                continue;
            }
            // 2.2、如果商品维度校验通过，但字段基本属性未通过，不做后续处理
            if (CollectionUtil.isNotEmpty(errorItemNameList) && errorItemNameList.contains(sameItemNameList.get(0).getItemName())) {
                this.buildMultipleErrorRecord(sameItemNameList, itemErrorReasonList, errorGoodsRecordList);
                continue;
            }

            // 3、导入规格与四级管理类目下的原有规格一起校验
            this.validateImportAndOriginalAttributes(sameItemNameList, itemDTOReq, loginUser, itemErrorReasonList);
            if (CollectionUtil.isNotEmpty(itemErrorReasonList)) {
                this.buildMultipleErrorRecord(sameItemNameList, itemErrorReasonList, errorGoodsRecordList);
                continue;
            }

            // 4、组装sku
            this.assembleItemSkus(sameItemNameList.get(0), sameItemNameList, loginUser, itemErrorReasonList, itemDTOReq);

            // 5、组装有效的商品
            this.assembleValidGoodList(sameItemNameList, saveImportGoodList, itemDTOReq, loginUser, batchNo);
        }

        // 保存记录
        this.saveImportRecord(errorGoodsRecordList,saveImportGoodList,loginUser);
    }

    /**
     * 处理有错误的商品
     */
    private void processGoodsWithErrors(BatchImportGoodsDTO batchImportGoodsDTO,
                                        List<String> errorItemNameList,
                                        List<String> errorAttrItemNameList) {
        // 基本字段校验未通过的数据，后续只校验，不做发布
        if (CollectionUtil.isNotEmpty(batchImportGoodsDTO.getErrMsgList())
                && !errorItemNameList.contains(batchImportGoodsDTO.getItemName())) {
            errorItemNameList.add(batchImportGoodsDTO.getItemName());
        }

        // 基本规格校验未通过，后续不校验规格
        if(CommonConstants.INVALID_ATTR_FLAG.equals(batchImportGoodsDTO.getErrAttrFlag())
                && !errorAttrItemNameList.contains(batchImportGoodsDTO.getItemName())){
            errorAttrItemNameList.add(batchImportGoodsDTO.getItemName());
        }
    }


    /**
     * 组装商品名称为空的数据
     */
    private void handleMissingItemName(BatchImportGoodsDTO goodsDTO,
                                       List<BatchImportGoodsRecordDTO> errorGoodsRecordList) {
        BatchImportGoodsRecordDTO errorRecord = new BatchImportGoodsRecordDTO();
        BeanUtils.copyProperties(goodsDTO, errorRecord);

        String errorMessage = ApiResultEnum.IMPORT_ITEM_NAME_NOT_NULL.getMsg();
        List<String> errorMessages = goodsDTO.getErrMsgList();

        if (CollectionUtil.isNotEmpty(errorMessages)) {
            errorMessages.add(errorMessage);
            errorRecord.setErrMsg(String.join(";\n", errorMessages));
        } else {
            errorRecord.setErrMsg(errorMessage);
        }
        errorGoodsRecordList.add(errorRecord);
    }


    /**
     * 校验选填规格数据的一致性
     */
    private void validateAndCollectAttributeErrors(List<BatchImportGoodsDTO> sameItemNameList,List<String> errorReasonList){
        // 验证选填的规格和规格值是否都有数据或都没有数据
        boolean checkSecondAttrName = this.validateAttrConsistency(sameItemNameList, BatchImportGoodsDTO::getSecondAttrName);
        boolean checkSecondAttrValue = this.validateAttrConsistency(sameItemNameList, BatchImportGoodsDTO::getSecondValueName);
        boolean checkThirdAttrName = this.validateAttrConsistency(sameItemNameList, BatchImportGoodsDTO::getThirdAttrName);
        boolean checkThirdAttrValue = this.validateAttrConsistency(sameItemNameList, BatchImportGoodsDTO::getThirdValueName);

        if (!checkSecondAttrName) {
            errorReasonList.add("同一个商品规格名称2必须相同，请核实后重新导入");
        }
        if (!checkSecondAttrValue) {
            errorReasonList.add("同一个商品规格值2必须全部为空或全部有值，请核实后重新导入");
        }
        if (!checkThirdAttrName) {
            errorReasonList.add("同一个商品规格名称3必须相同，请核实后重新导入");
        }
        if (!checkThirdAttrValue) {
            errorReasonList.add("同一个商品规格值3必须全部为空或全部有值，请核实后重新导入");
        }
    }


    /**
     * 组装同一个商品名称的错误原因
     */
    private void buildMultipleErrorRecord(List<BatchImportGoodsDTO> sameItemNameList,List<String> itemErrorReasonList, List<BatchImportGoodsRecordDTO> errorGoodsRecordList){
        for(BatchImportGoodsDTO sameItemNameDTO : sameItemNameList){
            logger.info("批导商品数据校验基础数据不通过：{}：{}", sameItemNameDTO.getItemName(), itemErrorReasonList);
            BatchImportGoodsRecordDTO batchImportGoodsRecordDTO = new BatchImportGoodsRecordDTO();
            BeanUtils.copyProperties(sameItemNameDTO, batchImportGoodsRecordDTO);
            if(CollectionUtil.isNotEmpty(itemErrorReasonList)){
                if(CollectionUtils.isEmpty(sameItemNameDTO.getErrMsgList())){
                    batchImportGoodsRecordDTO.setErrMsg(String.join(";\n", itemErrorReasonList));
                }else {
                    sameItemNameDTO.getErrMsgList().addAll(itemErrorReasonList);
                    batchImportGoodsRecordDTO.setErrMsg(String.join(";\n", sameItemNameDTO.getErrMsgList()));
                }
            }else {
                batchImportGoodsRecordDTO.setErrMsg(String.join(";\n", sameItemNameDTO.getErrMsgList()));
            }
            errorGoodsRecordList.add(batchImportGoodsRecordDTO);
        }
    }


    /**
     * 验证数据是否都为空或都不为空
     */
    private boolean validateAttrConsistency(List<BatchImportGoodsDTO> itemList, Function<BatchImportGoodsDTO, String> attrGetter) {
        if (CollectionUtils.isEmpty(itemList)) {
            return true; // 空列表视为有效
        }

        // 检查第一个元素的指定属性是否为空
        Boolean firstIsBlank = StringUtils.isBlank(attrGetter.apply(itemList.get(0)));

        // 检查所有元素是否与第一个元素一致
        for (BatchImportGoodsDTO dto : itemList) {
            boolean currentIsBlank = StringUtils.isBlank(attrGetter.apply(dto));
            if (currentIsBlank != firstIsBlank) {
                return false; // 发现不一致的情况
            }
        }

        return true; // 所有记录都一致
    }


    /**
     * 按照必填商品信息
     */
    private void checkNecessaryRow(BatchImportGoodsDTO batchImportGoodsDTO,List<FileInfoDTO> picFiles){
        List<String> errorReasonList = new ArrayList<>();
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getItemName()) && batchImportGoodsDTO.getItemName().length() > 100){
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_NAME_TOO_LONG.getMsg());
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getManagerFourCid())){
            errorReasonList.add(ApiResultEnum.IMPORT_FOUR_MANAGER_ID_NOT_NULL.getMsg());
        }
        if(batchImportGoodsDTO.getSaleThirdCid() == null){
            errorReasonList.add(ApiResultEnum.IMPORT_THIRD_SALE_CATEGORY_ID_NOT_NULL.getMsg());
        }
        if(batchImportGoodsDTO.getBrandId() == null){
            errorReasonList.add(ApiResultEnum.IMPORT_BRAND_ID_NOT_NULL.getMsg());
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getModelType())){
            errorReasonList.add(ApiResultEnum.IMPORT_MODEL_TYPE_NOT_NULL.getMsg());
        }else if(batchImportGoodsDTO.getModelType().length() > 32){
            errorReasonList.add(ApiResultEnum.IMPORT_MODEL_TYPE_TOO_LONG.getMsg());
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getUnit())){
            errorReasonList.add(ApiResultEnum.IMPORT_UNIT_NOT_NULL.getMsg());
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getAttrName()) || StringUtils.isBlank(batchImportGoodsDTO.getValueName())){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_OR_VALUE_NOT_NULL.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getAttrName()) && batchImportGoodsDTO.getAttrName().length() > 8){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_1_TOO_LONG.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getValueName()) && batchImportGoodsDTO.getValueName().length() > 15){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_VALUE_1_TOO_LONG.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getSecondAttrName()) && StringUtil.isBlank(batchImportGoodsDTO.getSecondValueName())){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_VALUE_2_NOT_NULL.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getSecondAttrName()) && StringUtil.isNotBlank(batchImportGoodsDTO.getSecondValueName())){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_2_NOT_NULL.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getSecondAttrName()) && batchImportGoodsDTO.getSecondAttrName().length() > 8){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_2_TOO_LONG.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getSecondValueName()) && batchImportGoodsDTO.getSecondValueName().length() > 15){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_VALUE_2_TOO_LONG.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getThirdAttrName()) && StringUtil.isBlank(batchImportGoodsDTO.getThirdValueName())){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_VALUE_3_NOT_NULL.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getThirdAttrName()) && StringUtil.isNotBlank(batchImportGoodsDTO.getThirdValueName())){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_3_NOT_NULL.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getThirdAttrName()) && batchImportGoodsDTO.getThirdAttrName().length() > 8){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_3_TOO_LONG.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getThirdValueName()) && batchImportGoodsDTO.getThirdValueName().length() > 15){
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_VALUE_3_TOO_LONG.getMsg());
            batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
        }
        // 校验图片文件夹里是否包含了所有的规格图片
        Map<String,List<FileInfoDTO>> fileMap = picFiles.stream().collect(Collectors.groupingBy(FileInfoDTO::getParentFolders));
        if(ObjectUtils.isNotNull(fileMap.get(batchImportGoodsDTO.getItemPicFile()))) {
            if(StringUtils.isNotBlank(batchImportGoodsDTO.getSkuPicName())){
                String[] skuPics = Arrays.stream(batchImportGoodsDTO.getSkuPicName().split("[，,]"))  // 使用正则表达式匹配中文或英文逗号
                        .map(String::trim)
                        .toArray(String[]::new);
                if(skuPics.length > 1){
                    errorReasonList.add(ApiResultEnum.IMPORT_SKU_PIC_TOO_MUCH.getMsg());
                    batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
                }else {
                    List<FileInfoDTO> picList = fileMap.get(batchImportGoodsDTO.getItemPicFile());
                    List<String> fileNameList = new ArrayList<>();
                    for (FileInfoDTO fileInfoDTO : picList) {
                        if (!fileNameList.contains(fileInfoDTO.getFileName())) {
                            fileNameList.add(fileInfoDTO.getFileName());
                        }
                    }
                    // 校验图片文件夹里是否包含了所有的规格图片
                    if(!this.checkAllElementsExist(fileNameList,skuPics)){
                        errorReasonList.add(ApiResultEnum.IMPORT_SKU_PIC_NOT_COMPLETE.getMsg());
                        batchImportGoodsDTO.setErrAttrFlag(CommonConstants.INVALID_ATTR_FLAG);
                    }
                }
            }
        }

        if (ObjectUtils.isNull(batchImportGoodsDTO.getInventory())) {
            errorReasonList.add(ApiResultEnum.IMPORT_INVENTORY_NOT_NULL.getMsg());
        } else if (Boolean.FALSE.equals(batchImportGoodsDTO.getInventory().compareTo(BigDecimal.ZERO) > 0)) {
            errorReasonList.add(ApiResultEnum.IMPORT_INVENTORY_BIGGER_ZERO.getMsg());
        } else if(!(batchImportGoodsDTO.getInventory().compareTo(new BigDecimal(999999999)) <= 0)){
            errorReasonList.add(ApiResultEnum.IMPORT_INVENTORY_NOT_EXCEEDED_999999999.getMsg());
        } else if(batchImportGoodsDTO.getInventory().scale() > 0 || batchImportGoodsDTO.getInventory().stripTrailingZeros().scale() > 0){
            errorReasonList.add(ApiResultEnum.IMPORT_INVENTORY_NOT_DECIMAL.getMsg());
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getItemPicFile()) || StringUtils.isBlank(batchImportGoodsDTO.getItemPicName())){
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_PIC_FILE_NOT_NULL.getMsg());
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getItemDescribeContent())){
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_DESCRIBE_NOT_NULL.getMsg());
        }else if(batchImportGoodsDTO.getItemDescribeContent().length() > 500){
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_DESCRIBE_NOT_EXCEEDED_500.getMsg());
        }
        if(StringUtils.isBlank(batchImportGoodsDTO.getShopName())){
            errorReasonList.add(ApiResultEnum.IMPORT_SHOP_NOT_NULL.getMsg());
        }
        if (ObjectUtils.isNull(batchImportGoodsDTO.getRetailPrice())) {
            errorReasonList.add(ApiResultEnum.IMPORT_RETAIL_PRICE_NOT_NULL.getMsg());
        } else if (Boolean.FALSE.equals(batchImportGoodsDTO.getRetailPrice().compareTo(BigDecimal.ZERO) > 0)) {
            errorReasonList.add(ApiResultEnum.IMPORT_RETAIL_PRICE_BIGGER_ZERO.getMsg());
        } else if (batchImportGoodsDTO.getRetailPrice().scale() > 4) {
            errorReasonList.add(ApiResultEnum.IMPORT_RETAIL_PRICE_MAX_4_DECIMAL.getMsg());
        }
        if(StringUtils.isNotBlank(batchImportGoodsDTO.getEanCode())){
            if(batchImportGoodsDTO.getEanCode().length() > 20){
                errorReasonList.add(ApiResultEnum.IMPORT_EAN_CODE_TOO_LONG.getMsg());
            }
            if(!StringUtils.isNumeric(batchImportGoodsDTO.getEanCode())){
                errorReasonList.add(ApiResultEnum.IMPORT_EAN_CODE_MUST_PURE_DIGITAL.getMsg());
            }else if(new BigDecimal(batchImportGoodsDTO.getEanCode()).scale() > 0 || new BigDecimal(batchImportGoodsDTO.getEanCode()).stripTrailingZeros().scale() > 0){
                errorReasonList.add(ApiResultEnum.IMPORT_EAN_CODE_NOT_DECIMAL.getMsg());
            }
        }

        batchImportGoodsDTO.setErrMsgList(errorReasonList);
    }

    /**
     * 按照商品维度校验数据
     */
    private void checkImportByItem(List<BatchImportGoodsDTO> sameItemList,
                                   List<FileInfoDTO> picFiles,
                                   ItemDTOReq itemDTOReq,
                                   LoginUserDetail loginUser,
                                   List<String> errorReasonList) {
        log.info("批导商品基础校验开始：{}：{}",sameItemList,picFiles);
        BatchImportGoodsDTO checkItemDTO = sameItemList.get(0);
        // 每个商品3个规格，每个规格最多3个规格值，最多27行
        if (sameItemList.size() > 27) {
            errorReasonList.add(ApiResultEnum.IMPORT_SAME_ITEM_ATTR_LAGER_THAN_LIMIT.getMsg());
            return ;
        }
        itemDTOReq.setItemName(checkItemDTO.getItemName());

        // 四级管理类目ID有效标识
        boolean fourthCidEffectiveFlag = false;
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getManagerFourCid)) {
            errorReasonList.add(ApiResultEnum.IMPORT_FOURTH_CID_NOT_CONSISTENT.getMsg());
        } else {
            MdmCategoryQueryDTO mdmCategoryQueryDTO = new MdmCategoryQueryDTO();
            mdmCategoryQueryDTO.setCurrent(1);
            mdmCategoryQueryDTO.setSize(500);
            mdmCategoryQueryDTO.setCategoryLevel("4");
            mdmCategoryQueryDTO.setCategoryId(checkItemDTO.getManagerFourCid());
            logger.info("批导商品查询四级管理类目 入参：{}", mdmCategoryQueryDTO);
            PageResult<List<MdmCategoryVO>> managerCategoryResult = goodsFeignService.queryMdmCategoryList(mdmCategoryQueryDTO);
            if (!managerCategoryResult.isSuccess() || CollectionUtils.isEmpty(managerCategoryResult.getData())) {
                logger.info("批导商品查询四级管理类目 出参：{}", JSON.toJSONString(managerCategoryResult));
                errorReasonList.add(ApiResultEnum.IMPORT_FOUR_MANAGER_ID_NOT_VALID.getMsg());
            } else {
                fourthCidEffectiveFlag = true;

                itemDTOReq.setCid(Long.parseLong(checkItemDTO.getManagerFourCid()));
            }
        }

        // 三级销售类目ID
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getSaleThirdCid)) {
            errorReasonList.add(ApiResultEnum.IMPORT_SALE_CID_NOT_CONSISTENT.getMsg());
        } else {
            logger.info("批导商品查询三级销售类目 入参：{}", checkItemDTO.getSaleThirdCid());
            Result<SaleCategoryPathDTO> saleCategoryResult = goodsFeignService.querySaleCategoryPath(checkItemDTO.getSaleThirdCid());
            logger.info("批导商品查询三级销售类目 出参：{}", saleCategoryResult);
            if (!saleCategoryResult.isSuccess() || saleCategoryResult.getData() == null) {
                errorReasonList.add(ApiResultEnum.IMPORT_THIRD_SALE_CATEGORY_ID_NOT_VALID.getMsg());
            } else {
                SaleCategoryPathDTO saleCategoryPathDTO = saleCategoryResult.getData();
                List<ItemSaleCategoryDTO> itemSaleCategoryList = new ArrayList<>();
                ItemSaleCategoryDTO itemSaleCategoryDTO = new ItemSaleCategoryDTO();
                itemSaleCategoryDTO.setFirstCid(saleCategoryPathDTO.getFirstLevelId());
                itemSaleCategoryDTO.setFirstCname(saleCategoryPathDTO.getFirstLevelName());
                itemSaleCategoryDTO.setSecondCid(saleCategoryPathDTO.getSecondLevelId());
                itemSaleCategoryDTO.setSecondCname(saleCategoryPathDTO.getSecondLevelName());
                itemSaleCategoryDTO.setThirdCid(saleCategoryPathDTO.getThirdLevelId());
                itemSaleCategoryDTO.setThirdCname(saleCategoryPathDTO.getThirdLevelName());
                itemSaleCategoryList.add(itemSaleCategoryDTO);
                itemDTOReq.setItemSaleCategoryList(itemSaleCategoryList);
            }
        }

        // 品牌ID
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getBrandId)) {
            errorReasonList.add(ApiResultEnum.IMPORT_BRAND_ID_NOT_CONSISTENT.getMsg());
        } else {
            // 查询四级管理类目与品牌是否绑定
            if (fourthCidEffectiveFlag) {
                logger.info("批导商品查询管理类目与品牌关系 入参：{}", checkItemDTO.getManagerFourCid());
                Result<List<Brand>> brandListResult = goodsFeignService.getBrandList(Long.parseLong(checkItemDTO.getManagerFourCid()));
                logger.info("批导商品查询管理类目与品牌关系 出参：{}", brandListResult);
                if (!brandListResult.isSuccess() || CollectionUtils.isEmpty(brandListResult.getData())) {
                    errorReasonList.add(ApiResultEnum.IMPORT_BRAND_NOT_BOUND_CATEGORY_ID.getMsg());
                } else {
                    Map<Long, Brand> brandMap = new HashMap<>();
                    for(Brand brand : brandListResult.getData()){
                        if(ObjectUtils.isNull(brandMap.get(brand.getId()))){
                            brandMap.put(brand.getId(),brand);
                        }
                    }
                    if (brandMap.get(checkItemDTO.getBrandId()) == null) {
                        errorReasonList.add(ApiResultEnum.IMPORT_BRAND_NOT_BOUND_CATEGORY_ID.getMsg());
                    } else {
                        itemDTOReq.setBrand(checkItemDTO.getBrandId());
                        itemDTOReq.setBrandName(brandMap.get(checkItemDTO.getBrandId()).getBrandName());
                    }
                }
            } else {
                // 查询品牌ID是否存在
                ItemBrandDTO itemBrandDTO = new ItemBrandDTO();
                itemBrandDTO.setBrandId(checkItemDTO.getBrandId());
                logger.info("批导商品查询品牌 入参：{}", itemBrandDTO);
                Result<DataGrid<ItemBrandDTO>> brandListResult = middleGroundAPI.queryBrandPageList(itemBrandDTO);
                logger.info("批导商品查询品牌 出参：{}", brandListResult);
                if (!brandListResult.isSuccess() || brandListResult.getData() == null || CollectionUtils.isEmpty(brandListResult.getData().getRows())) {
                    errorReasonList.add(ApiResultEnum.IMPORT_BRAND_ID_NOT_VALID.getMsg());
                }
            }
        }

        // 型号
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getModelType)) {
            errorReasonList.add(ApiResultEnum.IMPORT_MODEL_TYPE_NOT_CONSISTENT.getMsg());
        } else {
            itemDTOReq.setModelType(checkItemDTO.getModelType());
        }

        // 单位
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getUnit)) {
            errorReasonList.add(ApiResultEnum.IMPORT_UNIT_NOT_CONSISTENT.getMsg());
        } else {
            BaseDictionaryDTO baseDictionaryDTO = new BaseDictionaryDTO();
            baseDictionaryDTO.setName(checkItemDTO.getUnit());
            baseDictionaryDTO.setParentCode("ITEM_UNIT");
            baseDictionaryDTO.setIsLike("1");
            log.info("批导商品查询单位 入参:{}", baseDictionaryDTO);
            PageResult<List<BaseDictionaryDTO>> unitListResult = middleGroundAPI.queryBaseDictionaryListByCondition(baseDictionaryDTO, 1, 200);
            log.info("批导商品查询单位 出参:{}", JSON.toJSONString(unitListResult));
            if (unitListResult.isSuccess() && ObjectUtils.isNotEmpty(unitListResult) && CollectionUtil.isNotEmpty(unitListResult.getData())) {
                itemDTOReq.setUnit(unitListResult.getData().get(0).getValue());
            } else {
                errorReasonList.add(ApiResultEnum.IMPORT_UNIT_NOT_EXIST.getMsg());
            }
        }

        // 商品图片文件夹或上图片名称
        String itemFileName = checkItemDTO.getItemPicFile();
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getItemPicFile)) {
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_FILE_NOT_CONSISTENT.getMsg());
        } else if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getItemPicName)) {
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_PIC_NAME_NOT_CONSISTENT.getMsg());
        } else {
            // 校验图片文件夹、商品图片名称是否存在
            Map<String,List<FileInfoDTO>> fileMap = picFiles.stream().collect(Collectors.groupingBy(FileInfoDTO::getParentFolders));
            if(ObjectUtils.isNull(fileMap.get(itemFileName))){
                errorReasonList.add(ApiResultEnum.IMPORT_ITEM_PIC_FILE_NOT_EXIST.getMsg());
            }else {
                String[] itemPics = Arrays.stream(checkItemDTO.getItemPicName().split("[，,]"))  // 使用正则表达式匹配中文或英文逗号
                        .map(String::trim)
                        .toArray(String[]::new);
                if (itemPics.length > 6) {
                    errorReasonList.add(ApiResultEnum.IMPORT_ITEM_PIC_NOT_MORE_THAN_6.getMsg());
                } else {
                    List<FileInfoDTO> picList = fileMap.get(itemFileName);
                    List<String> fileNameList = new ArrayList<>();
                    for(FileInfoDTO fileInfoDTO : picList){
                        if(!fileNameList.contains(fileInfoDTO.getFileName())){
                            fileNameList.add(fileInfoDTO.getFileName());
                        }
                    }
                    // 校验图片文件夹里是否包含了所有的商品图片
                    if(!this.checkAllElementsExist(fileNameList,itemPics)){
                        errorReasonList.add(ApiResultEnum.IMPORT_ITEM_PIC_NOT_EXIST.getMsg());
                    }else {
                        List<String> itemPicNameUrl = new ArrayList<>();
                        for (String itemPicName : itemPics) {
                            itemPicNameUrl.add(itemFileName + "_" + itemPicName);
                        }
                        itemDTOReq.setItemPicNameList(itemPicNameUrl);
                        logger.info("批导设置商品图片结果：{}", itemDTOReq.getItemPicNameList());
                    }
                }
            }
        }

        // 商品详情
        boolean validDescribeFlag = true;
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getItemDescribeContent)) {
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_DESCRIBE_NOT_CONSISTENT.getMsg());
            validDescribeFlag = false;
        } else {
            ItemDescribeDTO itemDescribeDTO = new ItemDescribeDTO();
            String describeContent = "<p>" + checkItemDTO.getItemDescribeContent() + "</p>";
            itemDescribeDTO.setDescribeContent(describeContent);
            itemDTOReq.setItemDescribeDTO(itemDescribeDTO);
        }

        // 商品详情图片
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getItemDescribePicName)) {
            errorReasonList.add(ApiResultEnum.IMPORT_ITEM_DESCRIBE_PIC_NOT_CONSISTENT.getMsg());
        } else {
            if (StringUtils.isNotBlank(checkItemDTO.getItemDescribePicName())) {
                // 校验商品详情图片是否存在
                Map<String, List<FileInfoDTO>> fileMap = picFiles.stream().collect(Collectors.groupingBy(FileInfoDTO::getParentFolders));
                if (ObjectUtils.isNotNull(fileMap.get(itemFileName))) {
                    String[] itemDescribePics = Arrays.stream(checkItemDTO.getItemDescribePicName().split("[，,]"))  // 使用正则表达式匹配中文或英文逗号
                            .map(String::trim)
                            .toArray(String[]::new);
                    List<FileInfoDTO> picList = fileMap.get(itemFileName);
                    List<String> fileNameList = new ArrayList<>();
                    for (FileInfoDTO fileInfoDTO : picList) {
                        if (!fileNameList.contains(fileInfoDTO.getFileName())) {
                            fileNameList.add(fileInfoDTO.getFileName());
                        }
                    }
                    // 校验图片文件夹里是否包含了所有的商品详情图片
                    if (!this.checkAllElementsExist(fileNameList, itemDescribePics)) {
                        errorReasonList.add(ApiResultEnum.IMPORT_ITEM_DESC_PIC_NOT_EXIST.getMsg());
                    } else {
                        if (validDescribeFlag) {
                            List<String> itemDescPicNameList = new ArrayList<>();
                            for (String itemDescPicName : itemDescribePics) {
                                itemDescPicNameList.add(itemFileName + "_" + itemDescPicName);
                            }
                            itemDTOReq.setItemDescribePicNameList(itemDescPicNameList);
                        }
                    }
                }
            }
        }

        // 发布店铺
        if (!this.checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getShopName)) {
            errorReasonList.add(ApiResultEnum.IMPORT_SHOP_NAME_NOT_CONSISTENT.getMsg());
        } else {
            BaseDTO baseDto = new BaseDTO();
            baseDto.setUser(loginUser);
            MemberShopInfoReqDTO memberShopInfoReqDTO = new MemberShopInfoReqDTO();
            memberShopInfoReqDTO.setShopName(checkItemDTO.getShopName());
            memberShopInfoReqDTO.setMemberCode(loginUser.getMemberCode());
            memberShopInfoReqDTO.setPage(1);
            memberShopInfoReqDTO.setRows(50);
            log.info("批导商品根据名称查询店铺 入参:{}", memberShopInfoReqDTO);
            Result<DataGrid<MemberShopInfoResDTO>> shopListResult = goodsFeignService.selectShopMemberList(memberShopInfoReqDTO);
            log.info("批导商品根据名称查询店铺 出参:{}", JSON.toJSONString(shopListResult));
            if (!shopListResult.isSuccess() || ObjectUtils.isNull(shopListResult)
                    || ObjectUtils.isNull(shopListResult.getData()) || CollectionUtils.isEmpty(shopListResult.getData().getRows())) {
                errorReasonList.add(ApiResultEnum.IMPORT_SHOP_NOT_EXIST.getMsg());
                return;
            }
            List<ShopDTO> shopInfoList = new ArrayList<>();
            for (MemberShopInfoResDTO memberShopInfoResDTO : shopListResult.getData().getRows()) {
                if (memberShopInfoResDTO.getShopName().equals(checkItemDTO.getShopName())) {
                    ShopDTO shopDTO = new ShopDTO();
                    shopDTO.setShopId(memberShopInfoResDTO.getShopId());
                    shopDTO.setShopName(memberShopInfoResDTO.getShopName());
                    shopInfoList.add(shopDTO);
                    itemDTOReq.setShopInfoList(shopInfoList);
                    return;
                }
            }
            if (CollectionUtils.isEmpty(shopInfoList)) {
                errorReasonList.add(ApiResultEnum.IMPORT_SHOP_NOT_EXIST.getMsg());
            }
        }
    }


    private boolean checkAllElementsExist(List<String> fileNameList, String[] itemPics) {
        // 将List转换为Set提高查找效率
        Set<String> fileNameSet = new HashSet<>(fileNameList);

        // 检查数组中的每个元素是否都在Set中
        for (String item : itemPics) {
            if (!fileNameSet.contains(item)) {
                return false; // 只要有一个不存在就返回false
            }
        }
        return true; // 所有元素都存在
    }


    /**
     * 更新商品规格
     */
    private boolean updateItemSpecifications(BatchImportGoodsDTO batchImportGoodsDTO,
                                             List<BatchImportGoodsDTO> sameItemList,
                                             LoginUserDetail loginUserDetail,
                                             List<String> errorReasonList) {
        EditItemSalesSpecificationsDTO updateSpecificationsDTO = this.buildUpdateSpecificationsDTO(batchImportGoodsDTO, sameItemList);
        updateSpecificationsDTO.setUser(loginUserDetail);
        log.info("批导商品更新导入规格 入参：{}", updateSpecificationsDTO);
        Result<Boolean> updateAttrResult = goodsFeignService.updateSalesSpecifications(updateSpecificationsDTO);
        log.info("批导商品更新导入规格 出参：{}", updateAttrResult);

        if (!updateAttrResult.isSuccess() || Boolean.FALSE.equals(updateAttrResult.getData())) {
            errorReasonList.add(StringUtils.isNotBlank(updateAttrResult.getMsg())
                    ? updateAttrResult.getMsg()
                    : ApiResultEnum.IMPORT_UPDATE_ATTR_FAIL.getMsg());
            return false;
        }
        return true;
    }


    /**
     * 构建规格更新DTO
     */
    private EditItemSalesSpecificationsDTO buildUpdateSpecificationsDTO(BatchImportGoodsDTO batchImportGoodsDTO,
                                                                        List<BatchImportGoodsDTO> sameItemList) {
        EditItemSalesSpecificationsDTO dto = new EditItemSalesSpecificationsDTO();
        dto.setCategoryId(Long.parseLong(batchImportGoodsDTO.getManagerFourCid()));

        List<EidtItemAttributeDTO> itemAttributeDTOList = new ArrayList<>();

        // 处理规格1
        itemAttributeDTOList.add(this.buildAttributeDTO(
                batchImportGoodsDTO.getAttrName(),
                sameItemList.stream()
                        .map(BatchImportGoodsDTO::getValueName)
                        .collect(Collectors.toSet())
        ));

        // 处理规格2
        if (StringUtils.isNotBlank(batchImportGoodsDTO.getSecondAttrName())) {
            itemAttributeDTOList.add(this.buildAttributeDTO(
                    batchImportGoodsDTO.getSecondAttrName(),
                    sameItemList.stream()
                            .map(BatchImportGoodsDTO::getSecondValueName)
                            .collect(Collectors.toSet()))
            );
        }

        // 处理规格3
        if (StringUtils.isNotBlank(batchImportGoodsDTO.getThirdAttrName())) {
            itemAttributeDTOList.add(this.buildAttributeDTO(
                    batchImportGoodsDTO.getThirdAttrName(),
                    sameItemList.stream()
                            .map(BatchImportGoodsDTO::getThirdValueName)
                            .collect(Collectors.toSet()))
            );
        }

        dto.setItemAttributeDTOList(itemAttributeDTOList);
        return dto;
    }


    /**
     * 构建单个规格DTO
     */
    private EidtItemAttributeDTO buildAttributeDTO(String attrName, Set<String> valueNames) {
        EidtItemAttributeDTO attrDTO = new EidtItemAttributeDTO();
        attrDTO.setAttrName(attrName);

        List<EditItemAttributeValueDTO> valueList = valueNames.stream()
                .map(valueName -> {
                    EditItemAttributeValueDTO valueDTO = new EditItemAttributeValueDTO();
                    valueDTO.setAttrName(attrName);
                    valueDTO.setValueName(valueName);
                    return valueDTO;
                })
                .collect(Collectors.toList());

        attrDTO.setItemAttributeValueList(valueList);
        return attrDTO;
    }

    /**
     * 组装SKU信息
     */
    private boolean assembleItemSkus(BatchImportGoodsDTO batchImportGoodsDTO,
                                     List<BatchImportGoodsDTO> sameItemList,
                                     LoginUserDetail loginUser,
                                     List<String> errorReasonList,
                                     ItemDTOReq itemDTOReq) {
        SpecificationInfoDTO queryAttrDto = new SpecificationInfoDTO();
        queryAttrDto.setSellerId(loginUser.getMemberId());
        queryAttrDto.setCategoryId(Long.parseLong(batchImportGoodsDTO.getManagerFourCid()));

        log.info("批量商品查询商家规格列表 入参：{}", queryAttrDto);
        Result<List<ItemAttributeDTO>> attrListResult = goodsFeignService.queryAllItemAttributeList(queryAttrDto);
        log.info("批量商品查询商家规格列表 出参：{}", attrListResult);

        if (!attrListResult.isSuccess() || CollectionUtils.isEmpty(attrListResult.getData())) {
            errorReasonList.add("查询类目规格异常，请联系技术人员");
            return false;
        }

        // 构建规格映射
        Map<String, ItemAttributeDTO> attrNameMap = attrListResult.getData().stream()
                .collect(Collectors.toMap(
                        ItemAttributeDTO::getAttrName,
                        attr -> attr,
                        (existing, replacement) -> existing
                ));

        Map<String, ItemAttributeValueDTO> valueNameMap = attrListResult.getData().stream()
                .flatMap(attr -> attr.getItemAttributeValueList().stream()
                        .map(value -> {
                            String key = attr.getAttrName() + "_" + value.getValueName();
                            return new Object() {
                                final String k = key;
                                final ItemAttributeValueDTO v = value;
                            };
                        }))
                .collect(Collectors.toMap(
                        e -> e.k,
                        e -> e.v,
                        (existing, replacement) -> existing
                ));


        // 构建SKU列表
        log.info("构建SKU列表 开始：{}：{}：{}",sameItemList,attrNameMap,valueNameMap);
        List<ItemSkuDTO> itemSkuDTOList = sameItemList.stream()
                .map(importGoodsDTO -> this.buildItemSkuDTO(importGoodsDTO, attrNameMap, valueNameMap))
                .collect(Collectors.toList());

        itemDTOReq.setItemSkuDTOList(itemSkuDTOList);
        return true;
    }


    /**
     * 构建单个SKU DTO
     */
    private ItemSkuDTO buildItemSkuDTO(BatchImportGoodsDTO importGoodsDTO,
                                       Map<String, ItemAttributeDTO> attrNameMap,
                                       Map<String, ItemAttributeValueDTO> valueNameMap) {
        ItemSkuDTO itemSkuDTO = new ItemSkuDTO();

        // 构建规格属性字符串
        StringBuilder attributesName = new StringBuilder();
        StringBuilder attributes = new StringBuilder();

        this.appendAttribute(attributesName, attributes,
                importGoodsDTO.getAttrName(), importGoodsDTO.getValueName(),
                attrNameMap, valueNameMap);

        if (StringUtils.isNotBlank(importGoodsDTO.getSecondAttrName())) {
            this.appendAttribute(attributesName, attributes,
                    importGoodsDTO.getSecondAttrName(), importGoodsDTO.getSecondValueName(),
                    attrNameMap, valueNameMap);
        }

        if (StringUtils.isNotBlank(importGoodsDTO.getThirdAttrName())) {
            this.appendAttribute(attributesName, attributes,
                    importGoodsDTO.getThirdAttrName(), importGoodsDTO.getThirdValueName(),
                    attrNameMap, valueNameMap);
        }

        itemSkuDTO.setAttributesName(attributesName.toString());
        itemSkuDTO.setAttributes(attributes.toString());
        itemSkuDTO.setInventory(importGoodsDTO.getInventory());
        if (PlatformEnum.CYC.getPlatform().equalsIgnoreCase(appNacosConfig.getPlatform())) {
            itemSkuDTO.setInventorySource(CommonConstants.MANUAL_TYPE);
        }
        itemSkuDTO.setRetailPrice(importGoodsDTO.getRetailPrice());
        if(StringUtils.isNotBlank(importGoodsDTO.getSkuPicName())){
            itemSkuDTO.setSkuPicName(importGoodsDTO.getItemPicFile() + "_" + importGoodsDTO.getSkuPicName());
        }
        if (StringUtils.isNotBlank(importGoodsDTO.getEanCode())) {
            itemSkuDTO.setEanCode(importGoodsDTO.getEanCode());
        }
        itemSkuDTO.setProductNo(importGoodsDTO.getProductNo());

        return itemSkuDTO;
    }


    /**
     * 添加规格属性到字符串构建器
     */
    private void appendAttribute(StringBuilder attributesName, StringBuilder attributes,
                                 String attrName, String valueName,
                                 Map<String, ItemAttributeDTO> attrNameMap,
                                 Map<String, ItemAttributeValueDTO> valueNameMap) {
        if (StringUtils.isNotBlank(attrName)) {
            attributesName.append(attrName).append(":").append(valueName).append(";");

            ItemAttributeDTO attrDTO = attrNameMap.get(attrName);
            ItemAttributeValueDTO valueDTO = valueNameMap.get(attrName + "_" + valueName);
            if (attrDTO != null && valueDTO != null) {
                attributes.append(attrDTO.getAttrId()).append(":")
                        .append(valueDTO.getValueId()).append(";");
            }
        }
    }



    /**
     * 校验导入的商品规格
     */
    private void validateImportAttributes(BatchImportGoodsDTO batchImportGoodsDTO,
                                             List<BatchImportGoodsDTO> sameItemList,
                                             List<String> errorReasonList) {
        log.info("批导商品校验导入规格 开始：{}", sameItemList);
        // 1. 校验规格名称1是否相同
        if (!checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getAttrName)) {
            log.info("规格名称1存在不同数据：{}", sameItemList);
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_1_NOT_CONSISTENT.getMsg());
            return;
        }

        // 2. 校验规格值1不能超过3种
        Map<String, List<BatchImportGoodsDTO>> firstAttrMap = sameItemList.stream()
                .collect(Collectors.groupingBy(BatchImportGoodsDTO::getAttrName));
        if (!checkAttrValueName(firstAttrMap.get(batchImportGoodsDTO.getAttrName()), 1)) {
            log.info("规格值1不能超过3种：{}", sameItemList);
            errorReasonList.add(ApiResultEnum.IMPORT_SAME_ATTR_VALUE_1_NO_MORE_THAN_3.getMsg());
            return;
        }

        // 3. 处理规格3为空的情况
        if (StringUtils.isBlank(batchImportGoodsDTO.getThirdAttrName()) &&
                StringUtils.isBlank(batchImportGoodsDTO.getThirdValueName())) {
            boolean checkImportThirdAttr = this.validateWhenThirdAttrEmpty(batchImportGoodsDTO, sameItemList, errorReasonList);
            if(!checkImportThirdAttr){
                return;
            }
        }

        // 4. 处理规格3不为空的情况
        boolean checkThirdAttr = this.validateThirdAttribute(batchImportGoodsDTO, sameItemList, errorReasonList);
        if (!checkThirdAttr) {
            return;
        }

        // 5.校验选填规格一致性
       this.validateAndCollectAttributeErrors(sameItemList,errorReasonList);
    }


    /**
     * 将导入规格与类目下原规格一起校验
     */
    private void validateImportAndOriginalAttributes(List<BatchImportGoodsDTO> sameItemNameList,
                                                     ItemDTOReq itemDTOReq,
                                                     LoginUserDetail loginUser,
                                                     List<String> itemErrorReasonList) {
        BatchImportGoodsDTO batchImportGoodsDTO = sameItemNameList.get(0);
        SpecificationInfoDTO queryAttrDto = new SpecificationInfoDTO();
        queryAttrDto.setSellerId(loginUser.getMemberId());
        queryAttrDto.setCategoryId(Long.parseLong(batchImportGoodsDTO.getManagerFourCid()));
        log.info("查询原类目下规格 入参：{}", queryAttrDto);
        Result<List<ItemAttributeDTO>> attrListResult = goodsFeignService.queryAllItemAttributeList(queryAttrDto);
        log.info("查询原类目下规格 出参：{}", attrListResult);
        // 如果查询失败或没有规格数据，去更新导入的规格
        if (!attrListResult.isSuccess() || CollectionUtils.isEmpty(attrListResult.getData())) {
            boolean updateAttr = this.updateItemSpecifications(batchImportGoodsDTO, sameItemNameList, loginUser, itemErrorReasonList);

            if (!updateAttr) {
                return;
            }
            // 构建销售属性勾选集合
            this.buildSelectAttrList(batchImportGoodsDTO, loginUser, itemDTOReq, itemErrorReasonList);
            return;
        }

        // 组装原规格与导入规格去更新
        this.buildOriginalAndImportAttr(batchImportGoodsDTO, sameItemNameList, attrListResult.getData(), loginUser, itemDTOReq, itemErrorReasonList);
    }



    /**
     * 组装原规格与导入规格去更新
     */
    private void buildOriginalAndImportAttr(BatchImportGoodsDTO batchImportGoodsDTO,
                                            List<BatchImportGoodsDTO> sameItemList,
                                            List<ItemAttributeDTO> originalAttributeDTOList,
                                            LoginUserDetail loginUser,
                                            ItemDTOReq itemDTOReq,
                                            List<String> errorReasonList){
        // 收集所有规格名称（原有+导入的）
        Set<String> totalAttrNames = new HashSet<>();
        Set<String> originalAttrNames = originalAttributeDTOList.stream()
                .map(ItemAttributeDTO::getAttrName)
                .collect(Collectors.toSet());
        totalAttrNames.addAll(originalAttrNames);

        // 收集导入的规格名称
        Set<String> importAttrNames = new HashSet<>();
        importAttrNames.add(batchImportGoodsDTO.getAttrName());
        if (StringUtils.isNotBlank(batchImportGoodsDTO.getSecondAttrName())) {
            importAttrNames.add(batchImportGoodsDTO.getSecondAttrName());
        }
        if (StringUtils.isNotBlank(batchImportGoodsDTO.getThirdAttrName())) {
            importAttrNames.add(batchImportGoodsDTO.getThirdAttrName());
        }

        // 合并所有规格名称
        totalAttrNames.addAll(importAttrNames);

        // 校验规格数量不超过3个
        if (totalAttrNames.size() > 3) {
            log.info("类目下原规格加导入规格超出3个：{}", batchImportGoodsDTO);
            errorReasonList.add(ApiResultEnum.IMPORT_IMPORT_ATTR_EXCEEDED_3.getMsg());
            return;
        }

        // 准备更新规格的DTO
        EditItemSalesSpecificationsDTO updateSpecificationsDTO = new EditItemSalesSpecificationsDTO();
        updateSpecificationsDTO.setUser(loginUser);
        updateSpecificationsDTO.setCategoryId(Long.parseLong(batchImportGoodsDTO.getManagerFourCid()));

        // 处理规格数据
        List<EidtItemAttributeDTO> itemAttributeDTOList = this.processAttributes(
                originalAttributeDTOList,
                batchImportGoodsDTO,
                sameItemList
        );

        // 去更新规格
        updateSpecificationsDTO.setItemAttributeDTOList(itemAttributeDTOList);
        log.info("组装原规格与导入规格去更新 入参：{}", updateSpecificationsDTO);
        Result<Boolean> updateAttrResult = goodsFeignService.updateSalesSpecifications(updateSpecificationsDTO);
        log.info("组装原规格与导入规格去更新 出参：{}", updateAttrResult);

        if (!updateAttrResult.isSuccess() || Boolean.FALSE.equals(updateAttrResult.getData())) {
            errorReasonList.add(StringUtils.isNotBlank(updateAttrResult.getMsg())
                    ? updateAttrResult.getMsg()
                    : ApiResultEnum.IMPORT_UPDATE_ATTR_FAIL.getMsg());
            return;
        }

        // 组装销售属性勾选集合
        SpecificationInfoDTO queryAttrDto = new SpecificationInfoDTO();
        queryAttrDto.setSellerId(loginUser.getMemberId());
        queryAttrDto.setCategoryId(Long.parseLong(batchImportGoodsDTO.getManagerFourCid()));
        log.info("组装销售属性勾选集合2-查询类目规格 入参：{}", queryAttrDto);
        Result<List<ItemAttributeDTO>> attrListResult = goodsFeignService.queryAllItemAttributeList(queryAttrDto);
        log.info("组装销售属性勾选集合2-查询类目规格 出参：{}", attrListResult);
        if(!attrListResult.isSuccess() || CollectionUtils.isEmpty(attrListResult.getData())){
            errorReasonList.add(ApiResultEnum.IMPORT_QUERY_ATTR_LIST_ERROR.getMsg());
            return;
        }

        Map<String, ItemAttributeDTO> attrNameMap = attrListResult.getData().stream()
                .collect(Collectors.toMap(
                        ItemAttributeDTO::getAttrName,
                        attr -> attr,
                        (existing, replacement) -> existing
                ));

        Map<String, ItemAttributeValueDTO> valueNameMap = attrListResult.getData().stream()
                .flatMap(attr -> attr.getItemAttributeValueList().stream()
                        .map(value -> {
                            String key = attr.getAttrName() + "_" + value.getValueName();
                            return new Object() {
                                final String k = key;
                                final ItemAttributeValueDTO v = value;
                            };
                        }))
                .collect(Collectors.toMap(
                        e -> e.k,
                        e -> e.v,
                        (existing, replacement) -> existing
                ));


        List<SimpleCategoryAttrDTO> chekAttrList = new ArrayList<>();

        // 处理规格1
        this.processAttribute(batchImportGoodsDTO.getAttrName(), sameItemList,
                BatchImportGoodsDTO::getValueName, attrNameMap, valueNameMap, chekAttrList);

        // 处理规格2
        if (StringUtils.isNotBlank(batchImportGoodsDTO.getSecondAttrName())) {
            this.processAttribute(batchImportGoodsDTO.getSecondAttrName(), sameItemList,
                    BatchImportGoodsDTO::getSecondValueName, attrNameMap, valueNameMap, chekAttrList);
        }

        // 处理规格3
        if (StringUtils.isNotBlank(batchImportGoodsDTO.getThirdAttrName())) {
            this.processAttribute(batchImportGoodsDTO.getThirdAttrName(), sameItemList,
                    BatchImportGoodsDTO::getThirdValueName, attrNameMap, valueNameMap, chekAttrList);
        }
        itemDTOReq.setChekAttrList(chekAttrList);
    }

    /**
     * 处理单个属性的通用方法
     */
    private void processAttribute(String attrName,
                                  List<BatchImportGoodsDTO> sameItemList,
                                  Function<BatchImportGoodsDTO, String> valueNameExtractor,
                                  Map<String, ItemAttributeDTO> attrNameMap,
                                  Map<String, ItemAttributeValueDTO> valueNameMap,
                                  List<SimpleCategoryAttrDTO> chekAttrList) {
        if (StringUtils.isBlank(attrName)) {
            return;
        }

        SimpleCategoryAttrDTO attrDTO = new SimpleCategoryAttrDTO();
        attrDTO.setAttrId(attrNameMap.get(attrName).getAttrId());
        attrDTO.setAttrName(attrName);

        List<SimpleCategoryAttrValueDTO> attrValueDTOList = new ArrayList<>();
        Set<String> processedValues = new HashSet<>();

        sameItemList.stream()
                .map(valueNameExtractor)
                .filter(StringUtils::isNotBlank)
                .filter(value -> !processedValues.contains(value))
                .forEach(value -> {
                    SimpleCategoryAttrValueDTO valueDTO = new SimpleCategoryAttrValueDTO();
                    valueDTO.setValueId(valueNameMap.get(attrName + "_" + value).getValueId());
                    valueDTO.setValueName(value);
                    attrValueDTOList.add(valueDTO);
                    processedValues.add(value);
                });

        attrDTO.setAttrValueDTOList(attrValueDTOList);
        chekAttrList.add(attrDTO);
    }



    /**
     * 组装销售属性勾选集合
     */
    private void buildSelectAttrList(BatchImportGoodsDTO batchImportGoodsDTO,
                                     LoginUserDetail loginUser,
                                     ItemDTOReq itemDTOReq,
                                     List<String> errorReasonList){
        SpecificationInfoDTO queryAttrDto = new SpecificationInfoDTO();
        queryAttrDto.setSellerId(loginUser.getMemberId());
        queryAttrDto.setCategoryId(Long.parseLong(batchImportGoodsDTO.getManagerFourCid()));
        log.info("组装销售属性勾选集合-查询类目规格 入参：{}", queryAttrDto);
        Result<List<ItemAttributeDTO>> attrListResult = goodsFeignService.queryAllItemAttributeList(queryAttrDto);
        log.info("组装销售属性勾选集合-查询类目规格 出参：{}", attrListResult);
        if(!attrListResult.isSuccess() || CollectionUtils.isEmpty(attrListResult.getData())){
            errorReasonList.add(ApiResultEnum.IMPORT_QUERY_ATTR_LIST_ERROR.getMsg());
            return;
        }

        List<SimpleCategoryAttrDTO> chekAttrList = new ArrayList<>();
        for(ItemAttributeDTO itemAttributeDTO : attrListResult.getData()){
            SimpleCategoryAttrDTO simpleCategoryAttrDTO = new SimpleCategoryAttrDTO();
            simpleCategoryAttrDTO.setAttrId(itemAttributeDTO.getAttrId());
            simpleCategoryAttrDTO.setAttrName(itemAttributeDTO.getAttrName());

            List<SimpleCategoryAttrValueDTO> attrValueDTOList = new ArrayList<>();
            for(ItemAttributeValueDTO attributeValueDTO : itemAttributeDTO.getItemAttributeValueList()){
                SimpleCategoryAttrValueDTO simpleCategoryAttrValueDTO = new SimpleCategoryAttrValueDTO();
                simpleCategoryAttrValueDTO.setValueId(attributeValueDTO.getValueId());
                simpleCategoryAttrValueDTO.setValueName(attributeValueDTO.getValueName());
                attrValueDTOList.add(simpleCategoryAttrValueDTO);
            }
            simpleCategoryAttrDTO.setAttrValueDTOList(attrValueDTOList);
            chekAttrList.add(simpleCategoryAttrDTO);
        }
        itemDTOReq.setChekAttrList(chekAttrList);
    }


    /**
     * 处理规格属性，合并原有和导入的规格
     */
    private List<EidtItemAttributeDTO> processAttributes(
            List<ItemAttributeDTO> originalAttrs,
            BatchImportGoodsDTO importDTO,
            List<BatchImportGoodsDTO> sameItems) {
        log.info("合并原有规格和导入的规格 开始：{}：{}",originalAttrs,sameItems);
        List<EidtItemAttributeDTO> result = new ArrayList<>();

        // 1. 处理原有规格
        for (ItemAttributeDTO originalAttr : originalAttrs) {
            EidtItemAttributeDTO attrDTO = this.convertOriginalAttribute(originalAttr);
            result.add(attrDTO);
        }
        log.info("原有规格是：{}",result);

        // 2. 处理导入的规格
        // 规格1
        if (!originalAttrs.stream().anyMatch(a -> a.getAttrName().equals(importDTO.getAttrName()))) {
            result.add(buildAttributeDTO(
                    importDTO.getAttrName(),
                    sameItems.stream().map(BatchImportGoodsDTO::getValueName).collect(Collectors.toSet())
            ));
        }

        // 规格2
        if (StringUtils.isNotBlank(importDTO.getSecondAttrName()) &&
                !originalAttrs.stream().anyMatch(a -> a.getAttrName().equals(importDTO.getSecondAttrName())) &&
                result.size() < 3) {
            result.add(buildAttributeDTO(
                    importDTO.getSecondAttrName(),
                    sameItems.stream().map(BatchImportGoodsDTO::getSecondValueName).collect(Collectors.toSet())
            ));
        }

        // 规格3
        if (StringUtils.isNotBlank(importDTO.getThirdAttrName()) &&
                !originalAttrs.stream().anyMatch(a -> a.getAttrName().equals(importDTO.getThirdAttrName())) &&
                result.size() < 3) {
            result.add(buildAttributeDTO(
                    importDTO.getThirdAttrName(),
                    sameItems.stream().map(BatchImportGoodsDTO::getThirdValueName).collect(Collectors.toSet())
            ));
        }

        // 3. 处理规格值冲突（同名规格不同值的情况）
        log.info("处理规格值冲突 入参：{}：{}",result,originalAttrs);
        this.processAttributeValueConflicts(result, originalAttrs, importDTO, sameItems);

        return result;
    }


    /**
     * 转换原有规格
     */
    private EidtItemAttributeDTO convertOriginalAttribute(ItemAttributeDTO original) {
        EidtItemAttributeDTO dto = new EidtItemAttributeDTO();
        dto.setAttrId(original.getAttrId());
        dto.setAttrName(original.getAttrName());

        List<EditItemAttributeValueDTO> values = original.getItemAttributeValueList().stream()
                .map(v -> {
                    EditItemAttributeValueDTO valueDTO = new EditItemAttributeValueDTO();
                    valueDTO.setAttrId(original.getAttrId());
                    valueDTO.setAttrName(original.getAttrName());
                    valueDTO.setValueId(v.getValueId());
                    valueDTO.setValueName(v.getValueName());
                    return valueDTO;
                })
                .collect(Collectors.toList());

        dto.setItemAttributeValueList(values);
        return dto;
    }


    /**
     * 处理规格值冲突（同名规格不同值的情况）
     */
    private void processAttributeValueConflicts(
            List<EidtItemAttributeDTO> result,
            List<ItemAttributeDTO> originalAttrs,
            BatchImportGoodsDTO importDTO,
            List<BatchImportGoodsDTO> sameItems) {

        // 找出所有同名的原有规格
        Map<String, ItemAttributeDTO> originalAttrMap = originalAttrs.stream()
                .collect(Collectors.toMap(ItemAttributeDTO::getAttrName, Function.identity()));
        log.info("批导商品类目原规格：{}",originalAttrMap);

        for (EidtItemAttributeDTO attrDTO : result) {
            // 如果是导入的规格且与原有规格同名
            if (originalAttrMap.containsKey(attrDTO.getAttrName())) {
                ItemAttributeDTO originalAttr = originalAttrMap.get(attrDTO.getAttrName());

                // 收集原有规格值
                Map<String, ItemAttributeValueDTO> originalValues = originalAttr.getItemAttributeValueList().stream()
                        .collect(Collectors.toMap(ItemAttributeValueDTO::getValueName, Function.identity()));

                // 根据规格名称获取导入的值
                Set<String> importValues = this.getImportValuesForAttribute(attrDTO.getAttrName(), importDTO, sameItems);
                log.info("导入的规格名称：{}包含的规格值集合：{}",attrDTO.getAttrName(),importValues);

                // 合并值
                List<EditItemAttributeValueDTO> mergedValues = new ArrayList<>();
                // 1. 首先添加导入的值
                for (String valueName : importValues) {
                    EditItemAttributeValueDTO valueDTO = new EditItemAttributeValueDTO();
                    valueDTO.setAttrId(originalAttr.getAttrId());
                    valueDTO.setAttrName(originalAttr.getAttrName());

                    if (originalValues.containsKey(valueName)) {
                        // 使用原有值ID
                        valueDTO.setValueId(originalValues.get(valueName).getValueId());
                    }
                    valueDTO.setValueName(valueName);
                    mergedValues.add(valueDTO);
                }

                // 2. 添加原有但未导入的值（如果需要保留原有不匹配的值）
                originalValues.forEach((valueName, originalValue) -> {
                    if (!importValues.contains(valueName)) {
                        EditItemAttributeValueDTO valueDTO = new EditItemAttributeValueDTO();
                        valueDTO.setAttrId(originalAttr.getAttrId());
                        valueDTO.setAttrName(originalAttr.getAttrName());
                        valueDTO.setValueName(originalValue.getValueName());
                        valueDTO.setValueId(originalValue.getValueId());
                        mergedValues.add(valueDTO);
                    }
                });

                attrDTO.setItemAttributeValueList(mergedValues);
            }
        }
        log.info("原规格合并导入规格后结果：{}",result);
    }


    /**
     * 根据规格名称获取导入的值
     */
    private Set<String> getImportValuesForAttribute(
            String attrName,
            BatchImportGoodsDTO importDTO,
            List<BatchImportGoodsDTO> sameItems) {

        if (attrName.equals(importDTO.getAttrName())) {
            return sameItems.stream().map(BatchImportGoodsDTO::getValueName).collect(Collectors.toSet());
        } else if (attrName.equals(importDTO.getSecondAttrName())) {
            return sameItems.stream().map(BatchImportGoodsDTO::getSecondValueName).collect(Collectors.toSet());
        } else if (attrName.equals(importDTO.getThirdAttrName())) {
            return sameItems.stream().map(BatchImportGoodsDTO::getThirdValueName).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }



    /**
     * 当规格3为空时的校验逻辑
     */
    private boolean validateWhenThirdAttrEmpty(BatchImportGoodsDTO batchImportGoodsDTO,
                                               List<BatchImportGoodsDTO> sameItemList,
                                               List<String> errorReasonList) {
        // 情况1: 规格2也为空
        if (StringUtils.isBlank(batchImportGoodsDTO.getSecondAttrName()) &&
                StringUtils.isBlank(batchImportGoodsDTO.getSecondValueName())) {
            if (checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getValueName) && sameItemList.size() > 1) {
                log.info("规格值1存在重复数据：{}", sameItemList);
                errorReasonList.add(ApiResultEnum.IMPORT_ATTR_VALUE_EXIST_REPEAT.getMsg());
                return false;
            }
            return true;
        }

        // 情况2: 规格2名称或值不匹配
        if (StringUtils.isBlank(batchImportGoodsDTO.getSecondAttrName()) &&
                StringUtils.isNotBlank(batchImportGoodsDTO.getSecondValueName())) {
            errorReasonList.add("规格2名称不能为空");
            return false;
        }
        if (StringUtils.isNotBlank(batchImportGoodsDTO.getSecondAttrName()) &&
                StringUtils.isBlank(batchImportGoodsDTO.getSecondValueName())) {
            errorReasonList.add("规格值2不能为空");
            return false;
        }

        // 情况3: 规格名称1与规格名称2相同
        if (batchImportGoodsDTO.getAttrName().equals(batchImportGoodsDTO.getSecondAttrName())) {
            log.info("规格名称1与规格名称2不能相同：{}",sameItemList);
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_1_AND_2_REPEAT.getMsg());
            return false;
        }

        // 情况4: 校验规格2相关规则
        return this.validateSecondAttribute(batchImportGoodsDTO, sameItemList, errorReasonList);
    }


    /**
     * 处理规格3不为空的情况
     */
    private boolean validateThirdAttribute(BatchImportGoodsDTO batchImportGoodsDTO,
                                           List<BatchImportGoodsDTO> sameItemList,
                                           List<String> errorReasonList) {
        // 1. 校验规格3名称和值的完整性
        if (StringUtils.isBlank(batchImportGoodsDTO.getThirdAttrName())) {
            if (StringUtils.isNotBlank(batchImportGoodsDTO.getThirdValueName())) {
                errorReasonList.add("规格名称3不能为空");
                return false;
            }
            return true; // 规格3完全为空，无需进一步校验
        } else if (StringUtils.isBlank(batchImportGoodsDTO.getThirdValueName())) {
            errorReasonList.add("规格值3不能为空");
            return false;
        }

        // 2. 校验规格名称3不与前两个规格名称重复
        if (batchImportGoodsDTO.getThirdAttrName().equals(batchImportGoodsDTO.getAttrName())) {
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_1_AND_3_REPEAT.getMsg());
            return false;
        }
        if (StringUtils.isNotBlank(batchImportGoodsDTO.getSecondAttrName()) &&
                batchImportGoodsDTO.getThirdAttrName().equals(batchImportGoodsDTO.getSecondAttrName())) {
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_2_AND_3_REPEAT.getMsg());
            return false;
        }

        // 3. 校验所有规格名称3是否一致
        if (!checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getThirdAttrName)) {
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_3_NOT_CONSISTENT.getMsg());
            return false;
        }

        // 4. 校验规格值3的数量限制
        Map<String, List<BatchImportGoodsDTO>> thirdAttrMap = sameItemList.stream()
                .collect(Collectors.groupingBy(BatchImportGoodsDTO::getThirdAttrName));
        List<BatchImportGoodsDTO> thirdAttrList = thirdAttrMap.get(batchImportGoodsDTO.getThirdAttrName());

        if (!checkAttrValueName(thirdAttrList, 3)) {
            errorReasonList.add(ApiResultEnum.IMPORT_SAME_ATTR_VALUE_3_NO_MORE_THAN_3.getMsg());
            return false;
        }

        if (!validateAttrValueFrequency(thirdAttrList, BatchImportGoodsDTO::getThirdValueName, 9)) {
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_VALUE_3_LARGE_THAN_LIMIT.getMsg());
            return false;
        }

        return true;
    }


    /**
     * 校验规格2的相关规则
     */
    private boolean validateSecondAttribute(BatchImportGoodsDTO batchImportGoodsDTO,
                                            List<BatchImportGoodsDTO> sameItemList,
                                            List<String> errorReasonList) {
        // 1. 校验规格名称2是否一致
        if (!checkFieldConsistent(sameItemList, BatchImportGoodsDTO::getSecondAttrName)) {
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_NAME_2_NOT_CONSISTENT.getMsg());
            return false;
        }

        // 2. 分组并校验规格值2
        Map<String, List<BatchImportGoodsDTO>> secondAttrMap = sameItemList.stream()
                .collect(Collectors.groupingBy(BatchImportGoodsDTO::getSecondAttrName));
        List<BatchImportGoodsDTO> secondAttrList = secondAttrMap.get(batchImportGoodsDTO.getSecondAttrName());

        if (!checkAttrValueName(secondAttrList, 2)) {
            errorReasonList.add(ApiResultEnum.IMPORT_SAME_ATTR_VALUE_2_NO_MORE_THAN_3.getMsg());
            return false;
        }

        // 3.无规格3时，每个规格值2不能超过3行
        if (!this.validateAttrValueFrequency(secondAttrList, BatchImportGoodsDTO::getSecondValueName,3)) {
            errorReasonList.add(ApiResultEnum.IMPORT_ATTR_VALUE_2_LARGE_THAN_LIMIT.getMsg());
            return false;
        }

        return true;
    }

    /**
     * 通用校验方法 - 检查列表中指定字段的值是否全部相同
     * @param list 要校验的对象列表
     * @param fieldExtractor 字段提取函数
     * @param <T> 列表元素类型
     * @param <R> 字段类型
     * @return 如果所有元素的指定字段值相同返回true，否则返回false
     */
    private <T, R> boolean checkFieldConsistent(List<T> list, Function<T, R> fieldExtractor) {
        if (list == null || list.isEmpty()) {
            return true;
        }

        // 获取第一个元素的字段值作为基准
        R firstValue = fieldExtractor.apply(list.get(0));

        return list.stream()
                .allMatch(item -> {
                    R currentValue = fieldExtractor.apply(item);
                    // 处理null和空字符串情况
                    if (firstValue == null) {
                        return currentValue == null;
                    }
                    return firstValue.equals(currentValue);
                });
    }



    /**
     * 检查 规格值 的种类数是否超过 3种
     */
    private boolean checkAttrValueName(List<BatchImportGoodsDTO> list, int level) {
        if (list == null || list.isEmpty()) {
            return true;
        }

        // 提取所有不同的 valueName
        Set<String> distinctValueNames = new HashSet<>();
        switch (level){
            case 1:
                distinctValueNames = list.stream()
                        .map(BatchImportGoodsDTO::getValueName)
                        .collect(Collectors.toSet());
                break;
            case 2:
                distinctValueNames = list.stream()
                        .map(BatchImportGoodsDTO::getSecondValueName)
                        .collect(Collectors.toSet());
                break;
            case 3:
                distinctValueNames = list.stream()
                        .map(BatchImportGoodsDTO::getThirdValueName)
                        .collect(Collectors.toSet());
                break;
        }
        // 检查不同 valueName 的数量是否超过 maxVariety
        return distinctValueNames.size() <= 3;
    }

    /**
     * 通用方法：校验列表中指定属性的值出现频率不超过限制
     * @param list 待校验的列表
     * @param valueExtractor 属性值提取函数
     * @param frequency 允许的最大出现次数
     * @return 是否所有值的出现次数都不超过限制
     */
    private <T> boolean validateAttrValueFrequency(List<T> list,
                                                   Function<T, String> valueExtractor,
                                                   Integer frequency) {
        if (list == null || list.isEmpty()) {
            return true; // 空列表视为有效
        }

        // 统计每个属性值出现的次数
        Map<String, Long> frequencyMap = list.stream()
                .filter(dto -> valueExtractor.apply(dto) != null) // 过滤掉null值
                .collect(Collectors.groupingBy(
                        valueExtractor,
                        Collectors.counting()
                ));

        // 检查是否有任何值出现超过限制次数
        return frequencyMap.values().stream()
                .noneMatch(count -> count > frequency);
    }


    /**
     * 组装有效的商品记录
     */
    private void assembleValidGoodList(List<BatchImportGoodsDTO> sameItemNameList,
                                       List<ImportGoodsInfo> saveImportGoodList,
                                       ItemDTOReq itemDTOReq,
                                       LoginUserDetail loginUser,
                                       String batchNo){
        ImportGoodsInfo importGoodsInfo = new ImportGoodsInfo();
        importGoodsInfo.setItemName(sameItemNameList.get(0).getItemName());
        importGoodsInfo.setExtendInfo(JSON.toJSONString(itemDTOReq));
        importGoodsInfo.setBusinessStatus(CommonConstants.BUSINESS_REPROCESS_STATUS);
        importGoodsInfo.setBusinessType(ImportBusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS.getType());
        importGoodsInfo.setSellerCode(loginUser.getLoginId());
        importGoodsInfo.setBatchNo(batchNo);
        importGoodsInfo.setCreateId(loginUser.getMemberId().toString());
        importGoodsInfo.setCreateName(loginUser.getUserName());
        // 判断是否为子账号
        if (!org.springframework.util.ObjectUtils.isEmpty(loginUser.getParentAccount())) {
            importGoodsInfo.setSellerCode(loginUser.getParentAccount().getLoginId());
            importGoodsInfo.setSubSellerCode(loginUser.getSubAccountLoginId());
            importGoodsInfo.setCreateName(loginUser.getParentAccount().getUserName());
            importGoodsInfo.setCreateId(loginUser.getParentAccount().getMemberId().toString());
        }
        saveImportGoodList.add(importGoodsInfo);
    }

    /**
     * 保存记录
     */
    private void saveImportRecord(List<BatchImportGoodsRecordDTO> errorGoodsRecordList,
                                  List<ImportGoodsInfo> saveImportGoodList,
                                  LoginUserDetail loginUser){
        // 校验不通过数据生成excel保存
        if(CollectionUtil.isNotEmpty(errorGoodsRecordList)){
            log.info("批量导入商品-保存校验未通过记录 req:{}:{}",errorGoodsRecordList,loginUser);
            this.batchImportRecordErrorList(errorGoodsRecordList,loginUser);
        }

        // 校验通过数据保存
        if(CollectionUtil.isNotEmpty(saveImportGoodList)){
            log.info("批量导入商品-保存校验通过商品 req:{}",saveImportGoodList);
            Result<Boolean> saveResult = goodsFeignService.saveImportGoods(saveImportGoodList);
            log.info("批量导入商品-保存校验通过商品 resp:{}",saveResult);
        }
    }

    /**
     * 保存校验未通过数据
     */
    private void batchImportRecordErrorList(List<BatchImportGoodsRecordDTO> errorGoodsRecordList, LoginUserDetail loginUser) {
        String fileName = BusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS_ERROR_RECORD.getMsg();
        OssUtils ossUtils = new OssUtils();
        String downloadUrl = ossUtils.getDownloadUrl(CommonConstants.BATCH_IMPORT_GOODS_OSS_PREFIX, errorGoodsRecordList, BatchImportGoodsRecordDTO.class, fileName, bucket, endpoint, accessKeyId, accessKeySecret);
        if (StringUtils.isBlank(downloadUrl)) {
            log.info("批导商品校验未通过数据上传OSS失败");
        } else {
            reportHistoryService.saveReportHistory(downloadUrl, BusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS_ERROR_RECORD.getCode(), loginUser);
        }
    }


    /**
     * 保存上传的excel数据
     */
    @Override
    @Async
    public void saveImportUploadRecordList(List<BatchImportGoodsDTO> importGoodsDTOS, LoginUserDetail loginUser){
        log.info("保存上传的excel数据:{}:{}",importGoodsDTOS,loginUser);
        String fileName = BusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS_UPLOAD_RECORD.getMsg();
        OssUtils ossUtils = new OssUtils();
        String downloadUrl = ossUtils.getDownloadUrl(CommonConstants.BATCH_IMPORT_GOODS_OSS_PREFIX, importGoodsDTOS, BatchImportGoodsDTO.class, fileName, bucket, endpoint, accessKeyId, accessKeySecret);
        if (StringUtils.isBlank(downloadUrl)) {
            log.info("批导商品校验上传初始excel数据");
        } else {
            reportHistoryService.saveReportHistory(downloadUrl, BusinessTypeEnum.BATCH_IMPORT_PUBLISH_GOODS_UPLOAD_RECORD.getCode(), loginUser);
        }
    }


    /**
     * 批量保存图片至商品图库
     */
    @Async
    @Override
    public void batchSaveGoodsImage(List<FileInfoDTO> picFiles, String batchNo,LoginUserDetail loginUser){
        log.info("批量保存商品图片开始：{}：{}",picFiles,loginUser);
        if(CollectionUtils.isEmpty(picFiles)){
            return;
        }

        List<GoodsImagesEntity> goodsImagesEntities = new ArrayList<>();
        for(FileInfoDTO picFile : picFiles){
            GoodsImagesEntity goodsImagesDTO = new GoodsImagesEntity();
            // 将batchNo保存至itemCode，商品发布成功后更新itemCode
            goodsImagesDTO.setItemCode(batchNo);
            // 将“文件夹名_图片名”存至的description
            goodsImagesDTO.setDescription(picFile.getParentFolders() + "_" + picFile.getFileName());
            goodsImagesDTO.setSellerId(loginUser.getMemberId().toString());
            int lastDotIndex = picFile.getFileName().lastIndexOf('.');
            String picName = (lastDotIndex == -1)
                    ? picFile.getFileName()  // 如果没有后缀，返回原文件名
                    : picFile.getFileName().substring(0, lastDotIndex);
            goodsImagesDTO.setPicName(picName);
            goodsImagesDTO.setPicUrl(picFile.getImageOssUrl());
            goodsImagesDTO.setImageStatus(ImageStatusEnum.PENDING_SUPPLEMENT.getCode());
            goodsImagesDTO.setPicType(PicCategoryTypeEnum.ITEM.getType());
            goodsImagesDTO.setCreateId(loginUser.getMemberId().toString());
            goodsImagesDTO.setCreateName(loginUser.getUserName());
            goodsImagesEntities.add(goodsImagesDTO);
        }

        if (CollectionUtils.isEmpty(goodsImagesEntities)) {
            return;
        }
        log.info("批量保存图片至商品图库 入参：{}",goodsImagesEntities);
        Result<String> saveResult = goodsFeignService.batchSaveGoodsImage(goodsImagesEntities);
        log.info("批量保存图片至商品图库 出参：{}",saveResult);
    }
}
