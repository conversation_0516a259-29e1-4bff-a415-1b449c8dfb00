package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/3/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LoginUserDetail implements Serializable {

	/** 中台用户ID*/
	@ApiModelProperty(value = "中台用户ID")
	private Long userId;

	/** 账户ID*/
	@ApiModelProperty(value = "S2B账户ID")
	private Long accountId;

	/** 登录账户*/
	@ApiModelProperty(value = "登录账户（即：memberCode）")
	private String loginId;

	@ApiModelProperty(value = "用户名")
	private String userName;

	@ApiModelProperty(value = "会员编码")
	private String memberCode;

	@ApiModelProperty(value = "会员ID")
	private Long memberId;

	@ApiModelProperty(value = "父级账号")
	private LoginUserDetail parentAccount;

	@ApiModelProperty(value = "商家类型 1内部2外部3分销商")
	private String sellerType;

	@ApiModelProperty(value = "子账号登录id")
	private String subAccountLoginId;

	@ApiModelProperty(value = "子账号登录id")
	private Long subAccountMemberId;

	@ApiModelProperty(value = "子账号中台用户ID")
	private Long subAccountUserId;


	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
	}
}
