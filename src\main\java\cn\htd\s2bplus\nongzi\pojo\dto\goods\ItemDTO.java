package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 商品DTO
 *
 * <AUTHOR>
 * date: 2019年5月9日 下午2:49:21
 */
@Data
public class ItemDTO implements Serializable {

    private static final long serialVersionUID = -1798484459276810166L;

    @ApiModelProperty(value = "一级类目id ")
    private Long firstCategoryId;


    @ApiModelProperty(value = "商品id ")
    private Long itemId;
    @ApiModelProperty(value = "商品编码 ")
    private String itemCode;
    @ApiModelProperty(value = "商品名称 ")
    private String itemName;
    @ApiModelProperty(value = "租户ID ")
    private Long tenementId;
    @ApiModelProperty(value = "商品状态 0:待审核 ,1：审核通过,2：审核驳回，3：待ERP上行库存及价格或外部商品品类价格待映射，4 未上架，5：已上架  6：已删除")
    private Integer itemStatus;
    @ApiModelProperty(value = "快照id ")
    private Long snapshotId;
    @ApiModelProperty(value = "当前版本号 ")
    private String version;
    @ApiModelProperty(value = "商家ID ")
    private Long sellerId;
    @ApiModelProperty(value = "商家编码 ")
    private String sellerCode;
    @ApiModelProperty(value = "公司名称 ")
    private String companyName;
    @ApiModelProperty(value = "店铺ID ")
    private Long shopId;
    @ApiModelProperty(value = "店铺名称 ")
    private String shopName;
    @ApiModelProperty(value = "类目ID:三级类目 ")
    private Long cid;
    @ApiModelProperty(value = "三级类目名称 ")
    private String categoryName;
    @ApiModelProperty(value = "类目路径")
    private String categoryPath;
    @ApiModelProperty(value = "商品所属店铺类目id ")
    private Long shopCid;
    @ApiModelProperty(value = "商品类型 0:实体 1:虚拟 2:服务 3:金融")
    private Integer itemType;
    @ApiModelProperty(value = "品牌ID ")
    private Long brand;
    @ApiModelProperty(value = "品牌名称 ")
    private String brandName;
    @ApiModelProperty(value = "型号 ")
    private String modelType;
    @ApiModelProperty(value = "商品单位 ")
    private String weightUnit;
    @ApiModelProperty(value = "商品单位名称")
    private String unitName;
    @ApiModelProperty(value = "税率 ")
    private BigDecimal taxRate;
    @ApiModelProperty(value = "特征,按业务打标签 ")
    private String features;
    @ApiModelProperty(value = "商品毛重 ")
    private BigDecimal weight;
    @ApiModelProperty(value = "净重 ")
    private BigDecimal netWeight;
    @ApiModelProperty(value = "长 ")
    private BigDecimal length;
    @ApiModelProperty(value = "宽 ")
    private BigDecimal width;
    @ApiModelProperty(value = "高 ")
    private BigDecimal height;
    @ApiModelProperty(value = "广告词 ")
    private String ad;
    @ApiModelProperty(value = "商品产地 ")
    private String origin;
    @ApiModelProperty(value = "商品类目属性 {\"481\":[431],\"485\":[67]}")
    private String attributes;
    @ApiModelProperty(value = "商品销售属性 ")
    private String attrSale;
    @ApiModelProperty(value = "商户自定义参数 ")
    private String itemQualification;
    @ApiModelProperty(value = "商品简称 ")
    private String itemAbbr;
    @ApiModelProperty(value = "渠道编码 10 :内部供应商 20 :外部供应商 3010 :京东商品＋")
    private String productChannelCode;
    @ApiModelProperty(value = "外部商品状态 商品＋使用 ")
    private String outerItemStatus;
    @ApiModelProperty(value = "外接渠道品类编码 ")
    private String outerChannelCategoryCode;
    @ApiModelProperty(value = "外接渠道品牌编码 ")
    private String outerChannelBrandCode;
    @ApiModelProperty(value = "外接商品编码 ")
    private String outerItemCode;
    @ApiModelProperty(value = "是否申请进入商品模板库 ")
    private Integer applyInSpu;
    @ApiModelProperty(value = "是否来自产品 0:否 1:是")
    private Integer isSpu;
    @ApiModelProperty(value = "产品ID ")
    private Long itemSpuId;
    @ApiModelProperty(value = "产品编码 ")
    private String spuCode;
    @ApiModelProperty(value = "产品名称 ")
    private String spuName;
    @ApiModelProperty(value = "是否来自商品模板 0:否 1:是")
    private Integer isTemplate;
    @ApiModelProperty(value = "商品模板ID ")
    private Long itemTemplateId;
    @ApiModelProperty(value = "商品主图URL 冗余字段 ")
    private String itemPictureUrl;
    @ApiModelProperty(value = "是否有报价 1：有价格；2：暂无报价")
    private Integer hasPrice;
    @ApiModelProperty(value = "是否有VIP价格 0 没有 1 有")
    private Integer hasVipPrice;
    @ApiModelProperty(value = "市场价 冗余字段 ")
    private BigDecimal marketPrice;
    @ApiModelProperty(value = "成本价 冗余字段 ")
    private BigDecimal marketPrice2;
    @ApiModelProperty(value = "商城指导价 ")
    private BigDecimal guidePrice;
    @ApiModelProperty(value = "包装清单 ")
    private String packingList;
    @ApiModelProperty(value = "售后服务 ")
    private String afterService;

    @ApiModelProperty(value = "状态变更原因 ")
    private String statusChangeReason;
    @ApiModelProperty(value = "关键字 ")
    private String keywords;
    @ApiModelProperty(value = "运费模版ID ")
    private Long shopFreightTemplateId;
    @ApiModelProperty(value = "运费金额 ")
    private BigDecimal freightAmount;
    @ApiModelProperty(value = "支付方式 0:普通商品，3：货到付款")
    private Long payType;
    @ApiModelProperty(value = "是否预售 0 否 1 是")
    private Integer isPreSale;

    @ApiModelProperty(value = "ERP一级类目编码 ")
    private String erpFirstCategoryCode;
    @ApiModelProperty(value = "ERP五级类目编码 ")
    private String erpFiveCategoryCode;
    @ApiModelProperty(value = "商品ERP编码 ")
    private String erpCode;
    @ApiModelProperty(value = "商品下行erp状态 1 待下行 2 下行中 3 下行失败 4已下行")
    private String erpStatus;

    @ApiModelProperty(value = "erp下行错误信息 ")
    private String erpErrorMsg;


    @ApiModelProperty(value = "是否VIP套餐商品 0 非vip套餐商品 1 vip套餐商品")
    private Integer isVipItem;
    @ApiModelProperty(value = "IP商品类型 当is_vip_item=1时，该字段有效 1 VIP套餐 2 智慧门店套餐")
    private Integer vipItemType;
    @ApiModelProperty(value = "同步VIP会员标记 当is_vip_item=1时，该字段为1时有效 0 无效 1 有效")
    private Integer vipSyncFlag;
    @ApiModelProperty(value = "是否使用串码 0: 不使用串码 1：使用串码")
    private Integer useSerialCode;
    @ApiModelProperty(value = "条形码 ")
    private String barCode;
    @ApiModelProperty(value = "是否自营商品 ")
    private Integer isSelfSell;
    @ApiModelProperty(value = "汇超:k3商品编码 ")
    private String k3ItemCode;
    @ApiModelProperty(value = "汇超:k3商品描述 ")
    private String k3ItemDescribe;
    @ApiModelProperty(value = "数据标记 0:默认值 1:老中台  2:云原生")
    private Integer dataTag;

    @ApiModelProperty(value = "商品图片url列表 ")
    private String[] picUrls;
    @ApiModelProperty(value = "审核状态 0待审核 1 审核通过 2 审核驳回")
    private Integer verifyStatus;
    @ApiModelProperty(value = "审核人 ")
    private String verifyName;

    @ApiModelProperty(value = "库存量 ")
    private BigDecimal inventory;

    @ApiModelProperty(value = "ean编码 ")
    private String eanCode;

    @ApiModelProperty(value = "商品模板ID ")
    private Long templateId;



    @ApiModelProperty(value = "配送方式：1物流 2自提；可多选，以“,”分隔")
    private String shippingMethods;

    @ApiModelProperty(value = "是否新增产品标识 ")
    private Boolean addSpuFlag;

    @ApiModelProperty(value = "是否使用销售区域模板 ")
    private Boolean isSalesAreaTemplate;

    @ApiModelProperty(value = "销售区域模板id ")
    private Long salesAreaTemplateId;

    @ApiModelProperty(value = "销售区域模板名称")
    private String salesAreaTemplateName;

    @ApiModelProperty(value = "是否在销售区域内")
    private Boolean isInSalesArea;


    @ApiModelProperty(value = "是否允许异业购买")
    private Boolean allowDifferentIndustries;

    @ApiModelProperty(value = "归属行业   多个用,隔开")
    private String categoryBusiness;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
