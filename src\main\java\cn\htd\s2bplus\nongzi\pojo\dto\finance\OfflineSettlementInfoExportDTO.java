package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.enums.PayTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OfflineSettlementInfoExportDTO implements Serializable {
    private static final long serialVersionUID = 5288706208071698268L;

    @Excel(name = "序号", height = 10)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value ="id", example = "")
    private Long id;

    @Excel(name = "订单号", height = 10, width = 20)
    @ApiModelProperty(value ="订单编号", example = "")
    private String orderNo;

    @Excel(name = "订单时间", height = 10, width = 20)
    @ApiModelProperty(value ="订单创建时间", example = "")
    private String createOrderTime;

    @Excel(name = "支付方式", height = 10, width = 20)
    @ApiModelProperty(value ="支付方式: 7-线下汇款支付", example = "7")
    private String payType;

    @Excel(name = "会员编码", height = 10, width = 20)
    @ApiModelProperty(value ="买家编码", example = "")
    private String buyerCode;

    @Excel(name = "会员名称", height = 10, width = 20)
    @ApiModelProperty(value ="买家名称", example = "")
    private String buyerName;

    @Excel(name = "实付金额", height = 10, width = 20)
    @ApiModelProperty(value ="订单实付金额", example = "")
    private BigDecimal orderPayAmount;

    @Excel(name = "平台佣金", height = 10, width = 20)
    @ApiModelProperty(value ="平台佣金", example = "")
    private BigDecimal platformAmount;

    @Excel(name = "结算状态", height = 10, width = 20)
    @ApiModelProperty(value ="结算状态：0-未结算，1-已结算", example = "")
    private String settleStatus;

    @Excel(name = "结算金额", height = 10, width = 20)
    @ApiModelProperty(value ="结算佣金", example = "")
    private BigDecimal settleAmount;

    @Excel(name = "结算时间", height = 10, width = 20)
    @ApiModelProperty(value ="结算时间", example = "")
    private String settleCompleteTime;

    public void setPayType(String payType) {
        this.payType = payType;
        if (StringUtils.isNotBlank(payType)) {
            this.payType = PayTypeEnum.getMsgByCode(payType);
        }
    }

    /**
     * 金额格式化,保留2位小数
     */
    public void setOrderPayAmount(BigDecimal orderPayAmount) {
        if (null == orderPayAmount) {
            this.orderPayAmount = BigDecimal.ZERO;
        }else {
            this.orderPayAmount = orderPayAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    public void setPlatformAmount(BigDecimal platformAmount) {
        if (null == platformAmount) {
            this.platformAmount = BigDecimal.ZERO;
        }else {
            this.platformAmount = platformAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    public void setSettleAmount(BigDecimal settleAmount) {
        if (null == settleAmount) {
            this.settleAmount = BigDecimal.ZERO;
        }else {
            this.settleAmount = settleAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
