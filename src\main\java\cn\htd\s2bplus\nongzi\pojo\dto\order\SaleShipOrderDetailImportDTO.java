package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * date: 2020/10/16
 */
@ExcelTarget("SaleShipOrderDetailImportDTO")
@Data
@ToString
public class SaleShipOrderDetailImportDTO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @Excel(name = "订单编号", height = 10, width = 20)
    private String orderNo;

    /**
     * 配送方式 物流配送，无需物流
     */
    @ApiModelProperty(value = "发货方式")
    @Excel(name = "发货方式(0:无物流1:物流发货)", height = 10, width = 20)
    private String deliveryType;


    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "物流公司编码")
    @Excel(name = "物流公司编码", height = 10, width = 20)
    private String  logisticsCompanyCode;

    /**
     * 物流公司名称
     */
    @ApiModelProperty(value = "物流公司名称")
    @Excel(name = "物流公司名称", height = 10, width = 20)
    private String  logisticsCompany;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号")
    @Excel(name = "物流单号", height = 10, width = 20)
    private String  logisticsNo;

    /**
     * 送货人
     */
    @ApiModelProperty(value = "送货人")
    @Excel(name = "送货人", height = 10, width = 20)
    private String  sender;

    /**
     * 送货人手机号码
     */
    @ApiModelProperty(value = "送货人手机号码")
    @Excel(name = "送货人手机号码", height = 10, width = 20)
    private String  senderPhone;



}
