package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.PopSkuExport;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ExcelTarget("SellerSkuExport")
@Data
public class SellerSkuExport extends PopSkuExport implements Serializable {

    @ApiModelProperty(value = "商品编码",example = "xx",required = false)
    @Excel(name = "商品编码", height = 10, width = 20)
    private String itemCode;

    @ApiModelProperty(value = "商品名称",example = "xx",required = false)
    @Excel(name = "商品名称", height = 10, width = 60)
    private String itemName;

    @ApiModelProperty(value = "品牌名称",example = "xx",required = false)
    @Excel(name = "品牌名称", height = 10, width = 20)
    private String brandName;

    @ApiModelProperty(value = "商品类目",example = "家用空调>家用空调>挂机>1.5P",required = false)
    @Excel(name = "商品类目", height = 10, width = 40)
    private String categoryPath;

    @ApiModelProperty(value = "规格",example = "xx",required = false)
    @Excel(name = "规格", height = 10, width = 60)
    private String skuAttributesName;

    @ApiModelProperty(value = "单位",example = "xx",required = false)
    @Excel(name = "单位", height = 10, width = 20)
    private String unitName;

    @ApiModelProperty(value = "sku编码",example = "xx",required = false)
    @Excel(name = "sku编码", height = 10, width = 20)
    private String skuCode;

    @ApiModelProperty(value = "可用库存")
    @Excel(name = "可用库存", height = 10, width = 20)
    private String useNum;

    @ApiModelProperty(value = "分销限价",example = "xx",required = false)
    @Excel(name = "分销限价", height = 10, width = 20)
    private String saleLimitedPrice;

    @ApiModelProperty(value = "销售价格",example = "xx",required = false)
    @Excel(name = "销售价格", height = 10, width = 20)
    private String price;

    @ApiModelProperty(value = "是否使用阶梯价",example = "xx",required = false)
    @Excel(name = "是否使用阶梯价", height = 10, width = 20)
    private String isUseLadderPrice;

    @ApiModelProperty(value = "是否使用销售区域价",example = "xx",required = false)
    @Excel(name = "是否使用销售区域价", height = 10, width = 20)
    private String isUseAreaPrice;

    @ApiModelProperty(value = "是否使用分组价",example = "xx",required = false)
    @Excel(name = "是否使用分组价", height = 10, width = 20)
    private String isUseGroupPrice;

    @ApiModelProperty(value = "发布时间",example = "xx",required = false)
    @Excel(name = "发布时间", height = 10, width = 20)
    private String createTime;

    @ApiModelProperty(value = "修改时间",example = "xx",required = false)
    @Excel(name = "修改时间", height = 10, width = 20)
    private String modifyTime;

    @ApiModelProperty(value = "店铺名称",example = "xx",required = false)
    @Excel(name = "店铺名称", height = 10, width = 50)
    private String shopName;

    @ApiModelProperty(value = "上下架",example = "0:下架1:上架",required = false)
    @Excel(name = "上下架", height = 10, width = 20)
    private String status;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"itemCode\":\"")
                .append(itemCode).append('\"');
        sb.append(",\"itemName\":\"")
                .append(itemName).append('\"');
        sb.append(",\"brandName\":\"")
                .append(brandName).append('\"');
        sb.append(",\"categoryPath\":\"")
                .append(categoryPath).append('\"');
        sb.append(",\"unitName\":\"")
                .append(unitName).append('\"');
        sb.append(",\"skuCode\":\"")
                .append(skuCode).append('\"');
        sb.append(",\"saleLimitedPrice\":")
                .append(saleLimitedPrice);
        sb.append(",\"price\":")
                .append(price);
        sb.append(",\"skuAttributesName\":\"")
                .append(skuAttributesName).append('\"');
        sb.append(",\"isUseLadderPrice\":")
                .append(isUseLadderPrice);
        sb.append(",\"isUseAreaPrice\":")
                .append(isUseAreaPrice);
        sb.append(",\"isUseGroupPrice\":")
                .append(isUseGroupPrice);
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append(",\"shopName\":\"")
                .append(shopName).append('\"');
        sb.append(",\"status\":")
                .append(status);
        sb.append('}');
        return sb.toString();
    }
}
