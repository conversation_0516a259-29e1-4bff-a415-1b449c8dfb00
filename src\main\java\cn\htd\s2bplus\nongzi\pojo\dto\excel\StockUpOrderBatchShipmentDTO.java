package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;

@Data
public class StockUpOrderBatchShipmentDTO implements Serializable {

    private static final long serialVersionUID = -483853376458919320L;

    @Excel(name = "提单号", width = 32, orderNum = "1")
    private String deliveryOrderNo;

    @Excel(name = "发货数量", width = 16, orderNum = "2")
    private String deliveryNumber;

    @Excel(name = "发货方式（1:快递公司承运）", width = 32, orderNum = "3")
    private String realDeliveryType;

    @Excel(name = "物流单号", width = 32, orderNum = "4")
    private String logisticNo;

    @Excel(name = "发货时间（yyyy-mm-dd）", width = 32, orderNum = "5")
    private String deliveryTime;

    @Excel(name = "附件文件名称", width = 128, orderNum = "6")
    private String appendixName;

    /**
     * 附件地址-OSSUrl
     */
    private String appendixUrl;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }

}
