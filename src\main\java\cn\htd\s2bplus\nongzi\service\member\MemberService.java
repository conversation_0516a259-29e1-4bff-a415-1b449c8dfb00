package cn.htd.s2bplus.nongzi.service.member;

import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.SellerPrivateDomainMemberExcelDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.MemberCompanyInfoDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.*;
import cn.htd.s2bplus.nongzi.pojo.vo.ImportMemberVO;
import cn.htd.s2bplus.nongzi.pojo.vo.MemberGroupRelationVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 会员服务
 *
 */
public interface MemberService {
	/**
	 * 导入会员接口-限购策略
	 * @param file
	 * @return
	 */
	Result<ImportMemberVO> importMemberList(MultipartFile file);

	/**
	 * 导出会员分组内容-指定分组
	 * @param groupId
	 * @return
	 */
	Result<String> exportMemberGroupMemberCodeList(String groupId, HttpServletResponse response);

	void exportMemberInfo(LoginUserDetail user,HttpServletResponse response,MemberSearchDTO memberSearch);

	/**
	 * oop - 会员管理 - 查询我的会员、担保会员列表
	 * @param memberSearch
	 * @return
	 */
	List<MyMemberVo> selectMemberList(MemberSearchDTO memberSearch, Long sellerId, String sellerType);

	/**
	 * 处理导入商家私域会员数据
	 * @param fileListImport 导入数据（去重后）
	 * @param userDetail 用户信息
	 */
	void handleImportSellerPrivateDomainMember(List<SellerPrivateDomainMemberExcelDTO> fileListImport,
											   Map<String, MemberCompanyInfoDTO> memberMap,
											   List<MemberGroupRelationVO> memberGroupRelationList,
											   LoginUserDetail userDetail);
}

