package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class MdmCategoryVO implements Serializable {
    private static final long serialVersionUID = -6759537651129004000L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty("categoryId")
    private String categoryId;

    @ApiModelProperty("类目编码")
    private String categoryCode;

    @ApiModelProperty("上级类目编码")
    private String categoryFatherCode;

    @ApiModelProperty("类目名称")
    private String categoryName;

    @ApiModelProperty("类目级次")
    private String categoryLevel;

    @ApiModelProperty("是否为末级类目")
    private String hasLeaf;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
