package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PurchaseOrderSkuInfo implements Serializable {
    private static final long serialVersionUID = 6614765800661952163L;

    private String name;
    private String unit;
    private BigDecimal quantity;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
