package cn.htd.s2bplus.nongzi.pojo.dto.membergroup;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SellerMemberGroupDTO implements Serializable {

    @ApiModelProperty(value = "商家id", example = "926388")
    private String sellerId;

    @ApiModelProperty(value = "会员id", example = "1")
    private String memberId;

    @ApiModelProperty(value = "商家编码", example = "926388")
    private String sellerCode;

    @ApiModelProperty(value = "会员编码", example = "1")
    private String memberCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
