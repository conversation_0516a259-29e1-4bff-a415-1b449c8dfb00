package cn.htd.s2bplus.nongzi.service.rebate;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.rebate.RebateCashConfigDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @title RebateService
 * @Date: 2024/5/11 15:23
 */
public interface RebateService {
    Result<Boolean> importRebateCashConfig(MultipartFile file);
}
