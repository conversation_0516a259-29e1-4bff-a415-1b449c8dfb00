package cn.htd.s2bplus.nongzi.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VirtualInventoryVO implements Serializable {

    private static final long serialVersionUID = -470774101629952593L;

    @ApiModelProperty(value = "店铺Id",example = "1314")
    private Long shopId;

    @ApiModelProperty(value = "skuCode集合",example = "3000021814")
    private List<String> skuCodeList;

}
