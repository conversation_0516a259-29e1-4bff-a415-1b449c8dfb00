package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class BatchGuestOrderRecordDTO implements Serializable {
    private static final long serialVersionUID = 7755886198495481576L;

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    @ApiModelProperty(value = "会员ID")
    private Long buyerId;
    @ApiModelProperty(value = "会员编号")
    private String buyerCode;
    @ApiModelProperty(value = "会员名称")
    private String buyerName;
    @ApiModelProperty(value = "会员类型0=未知， 1:非会员，2：担保会员，3：正式会员")
    private Integer buyerType;
    @ApiModelProperty(value = "卖家Id", hidden = true)
    private Long sellerId;
    @ApiModelProperty(value = "卖家编号", hidden = true)
    private String sellerCode;
    @ApiModelProperty(value = "卖家名称", hidden = true)
    private String sellerName;
    @ApiModelProperty(value = "1普通发票 2增值税专用发票")
    private String invoiceType;
    @ApiModelProperty(value = "发票ID")
    private Long invoiceId;
    @ApiModelProperty(value = "是否自提 0：否 1：是")
    private Integer pickupFlag;
    @ApiModelProperty(value = "收货人姓名")
    private String consigneeName;
    @ApiModelProperty(value = "收货人电话")
    private String consigneePhoneNum;
    @ApiModelProperty(value = "收货人身份证号")
    private String consigneeIdCardNo;
    @ApiModelProperty(value = "收货地址-省编码")
    private String consigneeAddressProvince;
    @ApiModelProperty(value = "收货地址-市编码")
    private String consigneeAddressCity;
    @ApiModelProperty(value = "收货地址-区编码")
    private String consigneeAddressDistrict;
    @ApiModelProperty(value = "收货地址-镇编码")
    private String consigneeAddressTown;
    @ApiModelProperty(value = "收货地址-省描述")
    private String consigneeAddressProvinceStr;
    @ApiModelProperty(value = "收货地址-市描述")
    private String consigneeAddressCityStr;
    @ApiModelProperty(value = "收货地址-区描述")
    private String consigneeAddressDistrictStr;
    @ApiModelProperty(value = "收货地址-镇描述")
    private String consigneeAddressTownStr;
    @ApiModelProperty(value = "收货地址-详细")
    private String consigneeAddressDetail;
    @ApiModelProperty(value = "身份证-正面")
    private String pictureIdFrontUrl;
    @ApiModelProperty(value = "身份证-反面")
    private String pictureIdBackUrl;
    @ApiModelProperty(value = "车牌号")
    private String carNo;
    @ApiModelProperty(value = "销售部门编码")
    private String salesDepartmentCode;
    @ApiModelProperty(value = "销售部门名称")
    private String salesDepartmentName;
    @ApiModelProperty(value = "客户经理编码")
    private String customerManagerCode;
    @ApiModelProperty(value = "客户经理名称")
    private String customerManagerName;
    @ApiModelProperty(value = "销售方式")
    private String saleType;
    @ApiModelProperty(value = "订单买家确认方式 0：否 1：通知到法人 2：通知到业务责任人")
    private String sendSmsType;
    @ApiModelProperty(value = "短信接收方姓名")
    private String sendSmsName;
    @ApiModelProperty(value = "短信接收方手机号")
    private String sendSmsTel;
    @ApiModelProperty(value = "归属平台担保证明")
    private String belongPlatformUrl;
    @ApiModelProperty(value = "订单商品总金额")
    private BigDecimal totalGoodsAmount;
    @ApiModelProperty(value = "提交订单结果编码:0 未提交 1：成功 2：失败")
    private Integer resultCode;
    @ApiModelProperty(value = "提交结果信息")
    private String resultMsg;
    @ApiModelProperty(value = "定时任务重试次数", hidden = true)
    private Integer retryTimes;
    @ApiModelProperty(value = "删除标志 0:未删除 1：删除")
    private Integer deleteFlag;
    @ApiModelProperty(value = "创建人ID", hidden = true)
    private Long createId;
    @ApiModelProperty(value = "创建人名称", hidden = true)
    private String createName;
    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @ApiModelProperty(value = "更新人ID", hidden = true)
    private Long modifyId;
    @ApiModelProperty(value = "更新人名称", hidden = true)
    private String modifyName;
    @ApiModelProperty(value = "更新时间", hidden = true)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @ApiModelProperty(value = "收货地址")
    private String consigneeAddress;

    @ApiModelProperty(value = "商品信息")
    private List<BatchGuestOrderItemDTO> itemDetailList;

    @ApiModelProperty(value = "店铺Id")
    private Long shopId;
    @ApiModelProperty(value = "店铺名称")
    private String shopName;


    @ApiModelProperty(value = "制单人Id")
    private String operatorId;

    @ApiModelProperty(value = "制单人名称")
    private String operatorName;

    @ApiModelProperty(value = "收货人电话")
    private String dsConsigneePhoneNum;
    @ApiModelProperty(value = "收货人身份证号")
    private String dsConsigneeIdCardNo;

    @ApiModelProperty(value = "收货地址-详细")
    private String dsConsigneeAddressDetail;

    @ApiModelProperty(value = "收货地址")
    private String dsConsigneeAddress;

    @ApiModelProperty(value = "短信接收方手机号")
    private String dsSendSmsTel;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
