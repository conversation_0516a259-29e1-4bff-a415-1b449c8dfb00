package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
public class CommissionRecordExportDTO implements Serializable {
    @Excel(name = "分销员Id")
    private Long memberId;
    @Excel(name = "分销账号")
    private String memberCode;
    @Excel(name = "分销账户名称",width = 50)
    private String businessPersonName;
    @Excel(name = "分销账户角色",width = 30)
    private String distributeType;

    @Excel(name = "更新时间")
    private String updateTime;

    @Excel(name = "变动类型")
    private String changeType;

    @Excel(name = "变动属性")
    private String changeAttr;

    @Excel(name = "关联订单号")
    private String orderNo;

    @Excel(name = "结算状态")
    private String provideStatus;
    /**
     * 品牌分销:分销佣金等于累计佣金
     */
    @Excel(name = "分销佣金")
    private BigDecimal distributeCommission;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
