package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ItemExtendDTO implements Serializable {
    private static final long serialVersionUID = 7442284212832584865L;

    @ApiModelProperty(
            value = "储藏温区 1:普通储存 2：冷藏储存 3：冷冻储存",
            example = "25"
    )
    private String storageTemperature;
    @ApiModelProperty(
            value = "采购人",
            example = "张三"
    )
    private String purchaseMan;
    @ApiModelProperty(
            value = "采购模式",
            example = "1"
    )
    private String purchaseMode;
    @ApiModelProperty(
            value = "供应商名称",
            example = "供应商名称"
    )
    private String supplierName;
    @ApiModelProperty(
            value = "供应商编码",
            example = "供应商名称"
    )
    private String supplierCode;
    @ApiModelProperty(
            value = "仓库",
            example = "仓库"
    )
    private String warehouse;
    @ApiModelProperty(
            value = "配送方式 1:普通配送 2：冷藏配送 3：冷冻配送",
            example = "配送方式"
    )
    private String deliveryType;
    @ApiModelProperty(
            value = "起订量",
            example = "起订量"
    )
    private String orderQuantity;
    @ApiModelProperty(
            value = "配料",
            example = "配料"
    )
    private String ingredient;
    @ApiModelProperty(
            value = "保质期",
            example = "保质期"
    )
    private String shelfLife;
    @ApiModelProperty(
            value = "生产商名称",

            example = "生产商名称"
    )
    private String manufacturerName;
    @ApiModelProperty(
            value = "生产商电话",
            example = "生产商电话"
    )
    private String manufacturerPhone;
    @ApiModelProperty(
            value = "生产商所在地省",
            example = "生产商所在地省"
    )
    private String manufacturerProvince;
    @ApiModelProperty(
            value = "生产商所在地市",
            example = "生产商所在地市"
    )
    private String manufacturerCity;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
