package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class SimpleCategoryAttrValueDTO implements Serializable {
    private static final long serialVersionUID = 4543983907039702999L;

    @ApiModelProperty(value = "属性值")
    private Long valueId;

    @ApiModelProperty(value = "属性值名称")
    private String valueName;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
