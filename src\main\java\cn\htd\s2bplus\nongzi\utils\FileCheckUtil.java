package cn.htd.s2bplus.nongzi.utils;

import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.ResultEnum;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @title FileCheckUtil
 * @Date: 2024/5/11 14:55
 */
public class FileCheckUtil {
    /**
     * 校验文件
     * @param file
     */
    public static void checkFile(MultipartFile file) {
        //文件非空校验
        if (ObjectUtils.isEmpty(file)) {
            throw new BusinessException(ResultEnum.IMPORT_FILE_IS_NULL_ERROR.getCode(),ResultEnum.IMPORT_FILE_IS_NULL_ERROR.getMsg());
        }
        //文件格式校验
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new BusinessException(ResultEnum.SYSTEM_ERROR.getCode(),ResultEnum.SYSTEM_ERROR.getMsg());
        }
        String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
        if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equalsIgnoreCase(substring) || StrConstant.XLSX.equalsIgnoreCase(substring))) {
            throw new BusinessException(ResultEnum.ONLY_SUPPORT_EXCEL_FORMAT_ERROR.getCode(),ResultEnum.ONLY_SUPPORT_EXCEL_FORMAT_ERROR.getMsg());
        }
    }
}
