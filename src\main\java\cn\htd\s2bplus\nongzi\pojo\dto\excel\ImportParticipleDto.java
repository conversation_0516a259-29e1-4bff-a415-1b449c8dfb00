package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: wangxuan
 * @description: 搜索分词导入封装
 * @date: 2023/3/1 17:22
 */
@Data
public class ImportParticipleDto {

    @ApiModelProperty(value = "分词类型 1:分词   2:同义词  3:过滤词")
    private Integer participleType;

    @ApiModelProperty(value = "分词")
    private String word;

    @ApiModelProperty(value = "查询分词名称")
    private String conversionWord;

    @ApiModelProperty(value = "行业")
    private String trade;

    @ApiModelProperty(value = "采集分词来源 1:爬虫 2:B2B")
    private String collectSource;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private Long createId;

    @ApiModelProperty(value = "更新时间")
    private Date modifyTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
