package cn.htd.s2bplus.nongzi.utils;

import org.dozer.DozerBeanMapper;

import java.util.ArrayList;
import java.util.List;

/**
 * DozerUtil工具类，用于两个对象复制数据
 *
 * <AUTHOR>
 * @date 2020-03-11
 */
public class DozerUtil {

    private static DozerBeanMapper mapper = new DozerBeanMapper();

    /**
     * 单个对象数据转换
     *
     * @param source           源对象
     * @param destinationClass 目标对象类
     * @param <T>              泛型
     * @return 目标对象
     */
    public static <T> T convert(Object source, Class<T> destinationClass) {
        return mapper.map(source, destinationClass);
    }

    /**
     * 列表数据转换
     *
     * @param sourceList       源集合
     * @param destinationClass 目标类
     * @param <T>              目标泛型
     * @param <S>              源泛型
     * @return 目标类集合
     */
    public static <T, S> List<T> convertList(List<S> sourceList, Class<T> destinationClass) {
        List<T> retList = new ArrayList<T>();
        for (S source : sourceList) {
            retList.add(mapper.map(source, destinationClass));
        }
        return retList;
    }
}
