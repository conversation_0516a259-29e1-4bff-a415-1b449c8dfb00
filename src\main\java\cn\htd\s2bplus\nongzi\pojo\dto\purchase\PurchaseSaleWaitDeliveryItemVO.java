package cn.htd.s2bplus.nongzi.pojo.dto.purchase;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class PurchaseSaleWaitDeliveryItemVO implements Serializable {
    @ApiModelProperty(value = "委托单号")
    private String entrustedOrderNo;

    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;
    /**
     * 分校但或要货单号
     */
    @ApiModelProperty(value = "分销单或要货单号")
    private String subOrderNo;

    @ApiModelProperty(value = "采购单唯一编号,ERP采购单号+平台公司代码")
    private String purchaseOrderUniqueNo;

    /**
     * 分校但或要货单号
     */
    @ApiModelProperty(value = "分销单或要货单行号")
    private String subOrderItemNo;

    /**
     * 配送方式 1:供应商配送,2:自提
     */
    @ApiModelProperty(value = "配送方式 1:供应商配送,2:自提")
    private String deliveryType;

    /**
     * 送货方式要求
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 商品单位
     */
    @ApiModelProperty(value = "商品单位")
    private String skuUnit;

    /**
     * 收货或自提详细信息
     */
    @ApiModelProperty(value = "收货或自提详细信息")
    private String receiverOrPickUpDetail;

    /**
     * 剩余待发货数量
     */
    @ApiModelProperty(value = "剩余待发货数量")
    private BigDecimal saleItemWaitDeliveryNumber;

    /**
     * 自提委托书URL
     */
    @ApiModelProperty(value = "自提委托书URL")
    private String powerAttorneyUrl;

    @ApiModelProperty(value = "待发货商品状态 1:申请发货 2:申请取消发货")
    private String skuStatus;

    @ApiModelProperty(value = "委托单状态 1:申请发货 3:申请取消发货")
    private String status;

    /**
     * 申请取消原因
     */
    @ApiModelProperty(value = "申请取消原因")
    private String cancelReason;

    @ApiModelProperty(value = "收货人")
    private String receiverName;

    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;

    @ApiModelProperty(value = "收货人电话")
    private String dsReceiverPhone;

    @ApiModelProperty(value = "收货地址")
    private String receiverAddress;

    @ApiModelProperty(value = "收货或自提详细信息脱敏")
    private String dsReceiverOrPickUpDetail;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
