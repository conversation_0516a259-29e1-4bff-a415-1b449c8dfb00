package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 物流订单导出列表
 * <AUTHOR>
 * @Date 2020-3-19
 */
@Data
@ExcelTarget("OrderLogisticsExcelDTO")
public class OrderLogisticsExcelDTO implements Serializable {
@ApiModelProperty(value = "")

    private static final long serialVersionUID = 7751855377193610231L;

    @Excel(name = "物流订单号", height = 10, width = 20)
    @ApiModelProperty(value = "物流订单号")
    private String erpSholesalerCode;

    @ApiModelProperty(value = "订单号")
    @Excel(name = "订单号", height = 10, width = 20)
    private String orderNo;

    @ApiModelProperty(value = "订单阶段号")
    @Excel(name = "订单阶段号", height = 10, width = 20)
    private String orderStageNo;

    @ApiModelProperty(value = "出库单号")
    @Excel(name = "出库单号", height = 10, width = 20)
    private String deliveryOrderCode;

    @ApiModelProperty(value = "下单时间")
    @Excel(name = "下单时间", height = 10, width = 20)
    private String createTimeString;

    @ApiModelProperty(value = "订单来源")
    @Excel(name = "订单来源", height = 10, width = 20)
    private String orderFrom;

    @ApiModelProperty(value = "会员编号")
    @Excel(name = "会员编号", height = 10, width = 20)
    private String buyerCode;

    @ApiModelProperty(value = "会员名称")
    @Excel(name = "会员名称", height = 10, width = 20)
    private String buyerName;

    @ApiModelProperty(value = "商品编号")
    @Excel(name = "商品编号", height = 10, width = 20)
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    @Excel(name = "商品名称", height = 10, width = 20)
    private String goodsName;

    @ApiModelProperty(value = "商品属性")
    @Excel(name = "商品属性", height = 10, width = 20)
    private String itemAttributes;

    @ApiModelProperty(value = "商品数量")
    @Excel(name = "商品数量", height = 10, width = 20)
    private Integer goodsCount;

    @ApiModelProperty(value = "仓库编号")
    @Excel(name = "仓库编号", height = 10, width = 20)
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    @Excel(name = "仓库名称", height = 10, width = 20)
    private String warehouseName;

    @ApiModelProperty(value = "供应商编号")
    @Excel(name = "供应商编号", height = 10, width = 20)
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    @Excel(name = "供应商名称", height = 10, width = 20)
    private String supplierName;

    @ApiModelProperty(value = "店铺名称")
    @Excel(name = "店铺名称", height = 10, width = 20)
    private String shopName;

    @ApiModelProperty(value = "货品编号")
    @Excel(name = "货品编号", height = 10, width = 20)
    private String cargoCode;

    @ApiModelProperty(value = "订单类型")
    @Excel(name = "订单类型", height = 10, width = 20)
    private String orderType;

    @ApiModelProperty(value = "订单状态")
    @Excel(name = "订单状态", height = 10, width = 20)
    private String deliveryStatus;

    @ApiModelProperty(value = "物流单号")
    @Excel(name = "物流单号", height = 10, width = 20)
    private String logisticsNo;

    @ApiModelProperty(value = "物流公司编号")
    @Excel(name = "物流公司编号", height = 10, width = 20)
    private String logisticsCompanyCode;

    @ApiModelProperty(value = "物流公司名称")
    @Excel(name = "物流公司名称", height = 10, width = 20)
    private String logisticsCompanyName;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"erpSholesalerCode\":\"")
                .append(erpSholesalerCode).append('\"');
        sb.append(",\"orderNo\":\"")
                .append(orderNo).append('\"');
        sb.append(",\"orderStageNo\":\"")
                .append(orderStageNo).append('\"');
        sb.append(",\"deliveryOrderCode\":\"")
                .append(deliveryOrderCode).append('\"');
        sb.append(",\"createTimeString\":\"")
                .append(createTimeString).append('\"');
        sb.append(",\"orderFrom\":\"")
                .append(orderFrom).append('\"');
        sb.append(",\"buyerCode\":\"")
                .append(buyerCode).append('\"');
        sb.append(",\"buyerName\":\"")
                .append(buyerName).append('\"');
        sb.append(",\"skuCode\":\"")
                .append(skuCode).append('\"');
        sb.append(",\"goodsName\":\"")
                .append(goodsName).append('\"');
        sb.append(",\"itemAttributes\":\"")
                .append(itemAttributes).append('\"');
        sb.append(",\"goodsCount\":")
                .append(goodsCount);
        sb.append(",\"warehouseCode\":\"")
                .append(warehouseCode).append('\"');
        sb.append(",\"warehouseName\":\"")
                .append(warehouseName).append('\"');
        sb.append(",\"supplierCode\":\"")
                .append(supplierCode).append('\"');
        sb.append(",\"supplierName\":\"")
                .append(supplierName).append('\"');
        sb.append(",\"shopName\":\"")
                .append(shopName).append('\"');
        sb.append(",\"cargoCode\":\"")
                .append(cargoCode).append('\"');
        sb.append(",\"orderType\":\"")
                .append(orderType).append('\"');
        sb.append(",\"deliveryStatus\":\"")
                .append(deliveryStatus).append('\"');
        sb.append(",\"logisticsNo\":\"")
                .append(logisticsNo).append('\"');
        sb.append(",\"logisticsCompanyCode\":\"")
                .append(logisticsCompanyCode).append('\"');
        sb.append(",\"logisticsCompanyName\":\"")
                .append(logisticsCompanyName).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
