package cn.htd.s2bplus.nongzi.pojo.dto.purchase;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description 发货订单行信息
 * <AUTHOR>
 * @Date 2021/9/26 17:47
 */
@Data
public class DeliveryOrderItemDTO implements Serializable {

    private static final long serialVersionUID = 2213531638353830132L;

    @ApiModelProperty(value = "分销单或提货单行号")
    @NotBlank
    private String itemNo;

    @ApiModelProperty(value = "行商品sku编码")
    @NotBlank
    private String skuCode;

    @ApiModelProperty(value = "行商品sku名称")
    private String skuName;

    @ApiModelProperty(value = "销售单号")
    private String deliveryOrderNo;

    @ApiModelProperty(value = "发货数量")
    @NotBlank
    private String deliveryNumber;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
