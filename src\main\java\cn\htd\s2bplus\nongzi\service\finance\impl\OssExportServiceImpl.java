package cn.htd.s2bplus.nongzi.service.finance.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.enums.ParamEnum;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.feign.order.OrderFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.finance.ExportSettlementOrderData;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.MemberShopInfoExcelVO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.MemberShopInfoReqDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.MemberShopInfoResDTO;
import cn.htd.s2bplus.nongzi.service.finance.OssExportService;
import cn.htd.s2bplus.nongzi.service.finance.StatementService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RefreshScope
public class OssExportServiceImpl implements OssExportService {

    @Autowired
    private MiddleGroundAPI middleGroundAPI;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private GoodsFeignService goodsFeignService;

    @Autowired
    private OrderFeignService orderFeignService;

    @Autowired
    private StatementService statementService;

    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;


    private static final String EXPORT_REPEATED_SUBMIT = "exportShopInfoRepeatedSubmit";


    @Override
    @Async
    public Result<Boolean> exportShopInfo(MemberShopInfoReqDTO memberShopInfoReqDTO,
                                          HttpServletRequest request,
                                          HttpServletResponse response) {
        //redis控制
//        String redisKey = this.getRedisKey(memberShopInfoReqDTO, request);
        try {
//            if (!this.checkRepeatedSubmit(redisKey)) {
//                return CommonResultUtil.error(cn.htd.s2bplus.nongzi.enums.ResultEnum.REPORT_IS_BEING_GENERATED_DO_NOT_RESUBMIT.getCode(),
//                        cn.htd.s2bplus.nongzi.enums.ResultEnum.REPORT_IS_BEING_GENERATED_DO_NOT_RESUBMIT.getMsg());
//            }
            Long start = System.currentTimeMillis();
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName("店铺信息列表");
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", MemberShopInfoExcelVO.class);
            // sheet中要填充得数据
//            List<ExportSettlementOrderData> exportDataList = new ArrayList<>();
            List<MemberShopInfoExcelVO> data = this.getListData(memberShopInfoReqDTO);
            if (CollectionUtils.isEmpty(data)) {
                return CommonResultUtil.error(ResultEnum.FAILURE.getCode(), "查询店铺信息列表异常");
            }

            // sheet中要填充得数据
            sheet1DataMap.put("data", data);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType("applicationnd.ms-excel"); // 改成输出excel文件
            String fileName = java.net.URLEncoder.encode("店铺信息列表", "UTF-8");
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            log.info("店铺列表导出时间：{}",System.currentTimeMillis() - start);
            return statementService.saveGeneration(memberShopInfoReqDTO.getUser().getUserId() + "", memberShopInfoReqDTO.getUser().getUserName(), ParamEnum.SHOP_LIST_EXPORT_OSS.getCode(), new ByteArrayInputStream(barray));
        } catch (Exception e) {
            log.error("生成店铺信息报表异常,error:", e);
            return CommonResultUtil.error(ResultEnum.FAILURE.getCode(), "生成店铺信息列表异常");
        } finally {
            //删除redis控制
//            redisTemplate.delete(redisKey);
        }
    }

    /**
     * @param memberShopInfoReqDTO
     * @return
     */
    private List<MemberShopInfoExcelVO> getListData(MemberShopInfoReqDTO memberShopInfoReqDTO) {
        List<MemberShopInfoExcelVO> list = new ArrayList<>();
        memberShopInfoReqDTO.setPage(1);
        memberShopInfoReqDTO.setRows(1);
        log.info("查询总店铺列表 入参:{}", memberShopInfoReqDTO);
        Result<DataGrid<MemberShopInfoResDTO>> dataGridResult = goodsFeignService.selectShopMemberList(memberShopInfoReqDTO);
        if (Objects.isNull(dataGridResult) || !dataGridResult.isSuccess() || Objects.isNull(dataGridResult.getData()) || CollectionUtils.isEmpty(dataGridResult.getData().getRows())) {
            return new ArrayList<>();
        }
        log.info("查询总店铺列表 出参:{}", dataGridResult);
        int exportRows = nongZiNacosConfig.getExportShopRows();
        long total = dataGridResult.getData().getTotal();
        int pages = (int) Math.ceil((double) total / exportRows);
        for (int i = 1; i <= pages; i++) {
            memberShopInfoReqDTO.setPage(i);
            memberShopInfoReqDTO.setRows(exportRows);
            log.info("查询店铺列表 入参:{},rows:{},req:{}", i, exportRows, memberShopInfoReqDTO);
            Result<DataGrid<MemberShopInfoResDTO>> listPageResult = goodsFeignService.selectShopMemberList(memberShopInfoReqDTO);
            log.info("查询店铺列表 出参:{},{}", i,listPageResult.toString());
            if (!listPageResult.isSuccess() || Objects.isNull(listPageResult.getData()) || CollectionUtils.isEmpty(listPageResult.getData().getRows())) {
                log.error("查询店铺列表 异常：{}", i);
                continue;
            }
            String settlementList = JSON.toJSONString(listPageResult.getData().getRows());
            List<MemberShopInfoExcelVO> data = JSON.parseArray(settlementList, MemberShopInfoExcelVO.class);
            list.addAll(data);
        }
        return list;

    }

    /**
     * redis控制
     *
     * @param redisKey
     * @return
     */
//    private Boolean checkRepeatedSubmit(String redisKey) {
//        if (ObjectUtils.isNotEmpty(redisTemplate.opsForValue().get(redisKey))) {
//            return Boolean.FALSE;
//        }
//        redisTemplate.opsForValue().set(redisKey, EXPORTING, timeout, TimeUnit.MINUTES);
//        return Boolean.TRUE;
//    }

    /**
     * 获取redis的key
     *
     * @param memberShopInfoReqDTO
     * @param request
     * @return
     */
    private String getRedisKey(MemberShopInfoReqDTO memberShopInfoReqDTO, HttpServletRequest request) {
//        String loginId = memberShopInfoReqDTO.getMemberCode();
//        if (ObjectUtils.isEmpty(loginId)) {
//            loginId = request.getParameter("loginId");
//        }
//        String loginId = memberShopInfoReqDTO.getUser().getLoginId();
        String loginId = "ioadmin";
        log.info("redis loginId:{}", loginId);
        return EXPORT_REPEATED_SUBMIT + loginId;
    }

}
