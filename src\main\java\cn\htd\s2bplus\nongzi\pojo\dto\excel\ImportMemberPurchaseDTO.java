package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;


@Data
@EqualsAndHashCode
public class ImportMemberPurchaseDTO implements Serializable {

	private static final long serialVersionUID = -3367121377753493508L;
	//会员编码
	private String memberCode;



	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
	}
}
