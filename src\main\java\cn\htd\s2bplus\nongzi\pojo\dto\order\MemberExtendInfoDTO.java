package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 * @Description :
 * @Date :2023/4/21 18:48
 */
@Data
public class MemberExtendInfoDTO {

    @ApiModelProperty(value = "代签标记：0-非代签，1-代签")
    private Integer paymentTag;

    @ApiModelProperty(value = " 公司名称", notes = "公司名称", example = "XX投资管理有限公司")
    private String companyName;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
