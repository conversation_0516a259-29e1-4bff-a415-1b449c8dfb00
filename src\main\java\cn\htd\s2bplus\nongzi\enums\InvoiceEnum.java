package cn.htd.s2bplus.nongzi.enums;

/**
 * <AUTHOR>
 * @Date 2021-10-18 17:15
 * @Description
 */
public enum InvoiceEnum {
    PLAIN_INVOICE("1", "普通发票"),
    VALUE_ADDED_TAX_INVOICE("2", "增值税发票"),
    OFFLINE_INVOICE("3", "线下开票")
    ;

    private String type;
    private String name;

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    InvoiceEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }
}
