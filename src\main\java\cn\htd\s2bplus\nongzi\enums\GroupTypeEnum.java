package cn.htd.s2bplus.nongzi.enums;

/**
 * 会员分组枚举
 */
public enum GroupTypeEnum {

    PEOPLE_MEMBER_GROUP(1,"指定人"),
    BUSINESS_MEMBER_GROUP(2,"指定行业"),
    WHITE_LIST_MEMBER_GROUP(3,"白名单行业"),
    LABEL_MEMBER_GROUP(4,"指定标签"),
    PRIVATE_MEMBER_GROUP(5,"私域会员分组"),

    ;

    private Integer code;
    private String msg;

    GroupTypeEnum(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
