package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OfflineSettlementInfoModifyDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Excel(name = "订单号",fixedIndex = 0,orderNum = "1")
    @ApiModelProperty(value = "订单编号", example = "")
    private String orderNo;

    /**
     * 结算佣金
     */
    @Excel(name = "结算金额",fixedIndex = 1,orderNum = "11")
    @ApiModelProperty(value = "结算佣金", example = "")
    private BigDecimal settleAmount;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "")
    private String modifyName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
