package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MemberShopInfoExcelVO {

    @ApiModelProperty(value = "店铺名称")
    @Excel(name = "店铺名称", width = 20, orderNum = "1")
    private String shopName;

    @ApiModelProperty(value = "开店时间")
    @Excel(name = "开店时间", width = 20, orderNum = "2", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date shopTime;


    @ApiModelProperty(value = "店铺状态(1：申请 2:通过 4:关闭 5：开通)")
    @Excel(name = "店铺状态", width = 20, orderNum = "3")
    private String storeStatusName;


    @ApiModelProperty(value = "店铺编码", example = "店铺编码")
    @Excel(name = "店铺编码", width = 20, orderNum = "4")
    private String shopUrl;

    @ApiModelProperty(value = "最近交易时间", example = "最近交易时间")
    @Excel(name = "最近交易时间", width = 20, orderNum = "5")
    private String orderTime;

    @ApiModelProperty(value = "商家名称")
    @Excel(name = "商家名称", width = 20, orderNum = "6")
    private String companyName;

    @ApiModelProperty(value = "店铺渠道")
    @Excel(name = "店铺渠道", width = 20, orderNum = "7")
    private String appName;

    @ApiModelProperty(value = "商家编码")
    @Excel(name = "商家编码", width = 20, orderNum = "8")
    private String memberCode;

    @ApiModelProperty(value = "卖家类型(1 自营  2:pop  3：pop)")
    @Excel(name = "卖家类型", width = 20, orderNum = "9")
    private String sellerTypeName;

    @ApiModelProperty(value = "入驻状态  0:未入驻 1：已入驻", example = "入驻状态")
    @Excel(name = "入驻状态", width = 20, orderNum = "10")
    private String entryStatusName;


    @ApiModelProperty(value = "经营范围 1、酒水，2、3C数码，3、交通出行，4、建材，5、农资农机，6、厨具卫浴，7、家用电器，")
    @Excel(name = "经营范围", width = 20, orderNum = "11")
    private String businessIndustryName;

    @ApiModelProperty(value = "经营地址")
    @Excel(name = "经营地址", width = 20, orderNum = "12")
    private String actualBusinessAddress;

    @ApiModelProperty(value = "入驻时间")
    @Excel(name = "入驻时间", width = 20, orderNum = "13", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    @ApiModelProperty(value = "统一信用代码")
    @Excel(name = "统一信用代码", width = 20, orderNum = "14")
    private String buyerBusinessLicenseId;

    @ApiModelProperty(value = "登记类型/市场主体类型")
    @Excel(name = "登记类型/市场主体类型", width = 20, orderNum = "15")
    private String customerNature;

    @ApiModelProperty(value = "登记机关", example = "登记机关")
    @Excel(name = "登记机关", width = 20, orderNum = "16")
    private String belongOrg;

    @ApiModelProperty(value = "登记状态")
    @Excel(name = "登记状态", width = 20, orderNum = "17")
    private String regStatus;

    @ApiModelProperty(value = "法定代表人/负责人姓名")
    @Excel(name = "法定代表人/负责人姓名", width = 20, orderNum = "18")
    private String artificialPersonName;

    @ApiModelProperty(value = "注册资本 0: 0 ~ 100万 1：100万 ~ 500万，2：大于等于500万")
    @Excel(name = "注册资本", width = 20, orderNum = "19")
    private String registerCapital;

    @ApiModelProperty(value = "负责人电话号码")
    @Excel(name = "负责人电话号码", width = 20, orderNum = "20")
    private String responsiblePersonMobile;

    @ApiModelProperty(value = "成立日期")
    @Excel(name = "成立日期", width = 20, orderNum = "21", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date establishTime;

    @ApiModelProperty(value = "营业期限自")
    @Excel(name = "营业期限自", width = 20, orderNum = "22", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date businessLicenseRegistrationDate;

    @ApiModelProperty(value = "营业期限至")
    @Excel(name = "营业期限至", width = 20, orderNum = "23", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date businessLicenseEndDate;

    @ApiModelProperty(value = "核准日期")
    @Excel(name = "核准日期", width = 20, orderNum = "24", exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    @ApiModelProperty(value = "详细地址-脱敏", example = "江苏省南京市玄武区孝陵卫街道钟灵街50号")
    @Excel(name = "经营场所/住所", width = 20, orderNum = "25")
    private String locationAddr;

    @ApiModelProperty(value = "年度交易额")
    @Excel(name = "年度交易额", width = 20, orderNum = "26")
    private BigDecimal orderTotalAmount;

    @ApiModelProperty(value = "行政区划代码")
    @Excel(name = "行政区划代码", width = 20, orderNum = "27")
    private String areaCode;

    @ApiModelProperty(value = "营业执照链接")
    @Excel(name = "营业执照链接", width = 20, orderNum = "28")
    private String buyerBusinessLicensePicSrc;

}
