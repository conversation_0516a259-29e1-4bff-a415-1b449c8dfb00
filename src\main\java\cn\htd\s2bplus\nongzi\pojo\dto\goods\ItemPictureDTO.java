package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ItemPictureDTO implements Serializable {
    private static final long serialVersionUID = 499500034794442067L;
    @ApiModelProperty(value = "图片url")
    private String pictureUrl;

    @ApiModelProperty(value = "是否是主图")
    private Integer isFirst;

    @ApiModelProperty(value = "排序号,排序号最小的作为主图，从1开始")
    private Integer sortNumber;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
