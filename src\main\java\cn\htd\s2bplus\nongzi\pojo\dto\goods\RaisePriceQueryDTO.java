package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class RaisePriceQueryDTO implements Serializable {

    @ApiModelProperty(value = "SKU编码", example = "SKU12345")
    private String skuCode;

    @ApiModelProperty(value = "店铺ID", example = "10001")
    private Long shopId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
