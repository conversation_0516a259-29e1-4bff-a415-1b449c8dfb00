package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class SimpleCategoryAttrDetailDTO implements Serializable {

    private static final long serialVersionUID = 6354086331537040376L;
    /**
     * 属性id
     */
    private Long categoryAttrTypeId;
    /**
     * 属性值
     */
    private String attrValue;
    /**
     * 类目属性详情列表
     */
    private List<SimpleCategoryAttrDTO> detailList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
