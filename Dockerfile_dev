FROM *************/b2b_p/centos6v5:latest

EXPOSE 8080


WORKDIR /home/<USER>

RUN rm -rf  tomcat7 tomcat8

ADD ./target/s2bplus-nongzi-api.jar ./

ENV nacos_config_server_addr=***********:8848 \
    nacos_discovery_server_addr=***********:8848 \
    nacos_config_namespace=6598ffa7-809f-4094-acc2-3cee452fb71e

ENV JAVA_OPTS="-Xms1G -Xmx1G -XX:PermSize=256M -XX:MaxPermSize=256m  -Dfile.encoding=UTF8 -Dsun.jnu.encoding=UTF8  -Denv=dev -Dapp.id=s2bplus-nongzi-api"

ENTRYPOINT java ${JAVA_OPTS}   -Dmaven.test.skip=true  -jar  s2bplus-nongzi-api.jar
