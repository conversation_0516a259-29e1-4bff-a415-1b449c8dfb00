package cn.htd.s2bplus.nongzi.pojo.dto.purchase;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

@Data
public class DeliveryAddressInfo {

    @ApiModelProperty(value = "主键ID，修改时传入")
    private Long id;

    @ApiModelProperty(value = "供应商ID", hidden = true)
    private Long sellerId;

    @ApiModelProperty(value = "供应商编码", hidden = true)
    private String sellerCode;

    @ApiModelProperty(value = "供应商名称", hidden = true)
    private String sellerName;

    @ApiModelProperty(value = "发货地址-省编码")
    private String provinceCode;

    @ApiModelProperty(value = "发货地址-市编码")
    private String cityCode;

    @ApiModelProperty(value = "发货地址-区编码")
    private String districtCode;

    @ApiModelProperty(value = "发货地址-镇编码")
    private String townCode;

    @ApiModelProperty(value = "发货地址-详细")
    private String detailAddress;

    @ApiModelProperty(value = "发货地址-详细加密")
    private String dsDetailAddress;

    @ApiModelProperty(value = "省名称")
    private String deliveryProvince;

    @ApiModelProperty(value = "市名称")
    private String deliveryCity;

    @ApiModelProperty(value = "区名称")
    private String deliveryDistrict;

    @ApiModelProperty(value = "镇名称")
    private String deliveryTown;

    @ApiModelProperty(value = "联系人")
    private String deliveryName;

    @ApiModelProperty(value = "联系手机")
    private String deliveryPhone;

    @ApiModelProperty(value = "联系手机加密")
    private String dsDeliveryPhone;

    @ApiModelProperty(value = "是否默认地址 0：否，1：是")
    private Integer defaultFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}