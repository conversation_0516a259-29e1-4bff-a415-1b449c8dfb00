package cn.htd.s2bplus.nongzi.pojo.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: 80333
 * @Description:
 * @Date: 2021/5/18 15:21
 */
@Data
public class ExportMemberReqDto implements Serializable {
    private static final long serialVersionUID = 5031119830835941480L;
    @ApiModelProperty(
            value = "登录会员号",
            example = "1",required = true
    )
    private String loginId;
    @ApiModelProperty(
            value = "会员编码集合",
            notes = "会员编码集合",
            example = "htd1001,htd1002",required = true
    )
    private List<String> memberCodes;

    @ApiModelProperty(
            value = "会员类型 1-非会员 2-交易会员 3-标准会员",
            notes = "会员类型 1-非会员 2-交易会员 3-标准会员",
            example = "1",required = true
    )
    private String memberFlag;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"memberCodes\":")
                .append(memberCodes);
        sb.append("\"loginId\":")
                .append(loginId);
        sb.append(",\"memberFlag\":\"")
                .append(memberFlag).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
