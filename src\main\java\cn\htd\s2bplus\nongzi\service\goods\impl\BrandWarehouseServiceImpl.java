package cn.htd.s2bplus.nongzi.service.goods.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.s2bplus.common.enums.PlatformEnum;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.OssNacosConfig;
import cn.htd.s2bplus.nongzi.enums.BusinessTypeEnum;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.DistributorCommissionDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.DistributorCommissionExportDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.*;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.CheckStockNumErrorVO;
import cn.htd.s2bplus.nongzi.pojo.vo.WarehouseVirtualApiDTO;
import cn.htd.s2bplus.nongzi.pojo.vo.WarehouseVirtualVO;
import cn.htd.s2bplus.nongzi.service.goods.BrandWarehouseService;
import cn.htd.s2bplus.nongzi.service.history.ReportHistoryService;
import cn.htd.s2bplus.nongzi.utils.CommonUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import cn.htd.s2bplus.nongzi.utils.XssFilter;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BrandWarehouseServiceImpl implements BrandWarehouseService {

    @Resource
    private GoodsFeignService goodsFeignService;
    @Resource
    private MiddleGroundAPI middleGroundAPI;
    @Autowired
    private ReportHistoryService reportHistoryService;
    @Autowired
    private OssNacosConfig ossNacosConfig;
    @Override
    public Result<String>  importOutputWarehouse(MultipartFile file, LoginUserDetail loginUser) {
        try {
            // 文件非空校验
            this.checkExcel(file);
            //XSS拦截
            new XssFilter().importFilter(file, null, BrandWarehouseOutPutExcelDTO.class.getCanonicalName());
            // 获取导入内容并校验模板
            List<BrandWarehouseOutPutDTO> importContent = this.getImportContent(file,loginUser);
            // 校验服务商和会员店编码
            int rows = 1;

            BatchOutWarehouseApiDTO batchOutDto = new BatchOutWarehouseApiDTO();
            batchOutDto.setSellerCode(loginUser.getMemberCode());
            batchOutDto.setModifyId(loginUser.getUserId());
            batchOutDto.setModifyName(loginUser.getUserName());
            batchOutDto.setBusinessType(1);

            BatchPutWarehouseApiDTO batchPutDto = new BatchPutWarehouseApiDTO();
            batchPutDto.setBusinessType(2);
            BatchOutWarehouseApiEntity outWarehouseEntity = null;
            BatchPutWarehouseApiEntity putWarehouseEntity = null;
            List<BatchOutWarehouseApiEntity> outWarehouseEntityList = new ArrayList<>();
            List<BatchPutWarehouseApiEntity> putWarehouseEntityList = new ArrayList<>();
            for (BrandWarehouseOutPutDTO brandWarehouseOutPutExcelDTO : importContent
            ) {
                if (brandWarehouseOutPutExcelDTO.getBusinessType().trim().equals("增加")) {
                    putWarehouseEntity = new BatchPutWarehouseApiEntity();
                    BeanUtils.copyProperties(brandWarehouseOutPutExcelDTO,putWarehouseEntity);
                    putWarehouseEntity.setUseNum(new BigDecimal(brandWarehouseOutPutExcelDTO.getUseNum()));
                    putWarehouseEntity.setSellerCode(loginUser.getMemberCode());
                    putWarehouseEntity.setCreateId(loginUser.getUserId());
                    putWarehouseEntity.setCreateName(loginUser.getUserName());
                    putWarehouseEntityList.add(putWarehouseEntity);

                } else if (brandWarehouseOutPutExcelDTO.getBusinessType().trim().equals("减少")) {
                    outWarehouseEntity = new BatchOutWarehouseApiEntity();
                    BeanUtils.copyProperties(brandWarehouseOutPutExcelDTO,outWarehouseEntity);
                    outWarehouseEntity.setUseNum(new BigDecimal(brandWarehouseOutPutExcelDTO.getUseNum()));
                    outWarehouseEntityList.add(outWarehouseEntity);
                }
            }
            if (!CollectionUtils.isEmpty(outWarehouseEntityList)) {
                // 出库时批量校验剩余库存
                BatchOutWarehouseApiDTO warehouseReqDTO = new BatchOutWarehouseApiDTO();
                List<BatchOutWarehouseEntity> outInfo;
                warehouseReqDTO.setOutInfo(outWarehouseEntityList);
                warehouseReqDTO.setSellerCode(loginUser.getMemberCode());
                Result<List<CheckStockNumErrorVO>> listResult = middleGroundAPI.checkSaleStockNum(warehouseReqDTO);
                if (!listResult.isSuccess()) {
                    throw new BusinessException(ResultEnum.FAILURE.getCode(),"出库数量校验异常");
                }
                if (!CollectionUtils.isEmpty(listResult.getData())) {
                    StringBuilder sb = new StringBuilder();
                    List<CheckStockNumErrorVO> errorVOList = listResult.getData();
                    for (CheckStockNumErrorVO vo : errorVOList
                         ) {
                        sb.append("商品库存编码：").append(vo.getStockCode()).append("库存编码：").append(vo.getWarehouseCode()).append("错误原因：").append(vo.getErrorMsg());
                    }
                    if (!ObjectUtils.isEmpty(sb)){
                        throw new BusinessException(ResultEnum.FAILURE.getCode(),sb.toString());
                    }
                }
            }


            log.info("批量导入入库 入参：{}",batchPutDto);
            log.info("批量导入出库 入参：{}",batchOutDto);
            Result<String> result = new Result<>();
            if (!CollectionUtils.isEmpty(putWarehouseEntityList)) {
                // 去重累加操作数量
                // 塞仓库名称
                Map<String, String> warehouseNameMap = putWarehouseEntityList.stream().collect(Collectors.toMap(BatchPutWarehouseApiEntity::getWarehouseCode, BatchPutWarehouseApiEntity::getWarehouseName,(v1,v2)->v1));
                List<BatchPutWarehouseApiEntity> putWarehouseApiEntities = BatchPutWarehouseApiEntity.aggregateBatchOutWarehouseEntities(putWarehouseEntityList);
                batchPutDto.setPutInfo(putWarehouseApiEntities);
                for (BatchPutWarehouseApiEntity putWarehouse : putWarehouseApiEntities
                     ) {
                    putWarehouse.setSellerCode(loginUser.getMemberCode());
                    putWarehouse.setSellerName(loginUser.getUserName());
                    putWarehouse.setCreateId(loginUser.getUserId());
                    putWarehouse.setCreateName(loginUser.getUserName());
                    putWarehouse.setWarehouseName(warehouseNameMap.get(putWarehouse.getWarehouseCode()));
                }
                result = goodsFeignService.batchPutWarehouse(batchPutDto);
            }
            if (!CollectionUtils.isEmpty(outWarehouseEntityList)) {
                // 去重累加操作数量
                List<BatchOutWarehouseApiEntity> outWarehouseApiEntities = BatchOutWarehouseApiEntity.aggregateBatchOutWarehouseEntities(outWarehouseEntityList);
                batchOutDto.setOutInfo(outWarehouseApiEntities);
                result = goodsFeignService.batchOutWarehouse(batchOutDto);
            }

            log.info("批量导入出入库 出参：{}",result);
            return result;
        }catch (BusinessException bx){
            log.error("批量导入出入库失败：",bx);
            return CommonResultUtil.error(bx.getCode(),bx.getMessage());
        }catch (Exception e){
            log.error("批量导入出入库异常：",e);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    @Override
    public void exportWarehouseGoodsDetail(WarehouseGoodsApiDTO requestDTO, HttpServletResponse response, LoginUserDetail loginUser) {

        Result<List<WarehouseGoodsDetailApiVO>> queryResult = middleGroundAPI.warehouseGoodsDetailByParam(requestDTO);
        log.info("查询仓库商品明细列表:{}", ObjectUtils.isEmpty(queryResult.getData()) ? null : queryResult.getData().size());
        if (!queryResult.isSuccess() || ObjectUtils.isEmpty(queryResult.getData())) {
            log.info("查询仓库商品明细列表 出错:{}", queryResult);
            return;
        }
        List<WarehouseGoodsDetailApiVO> dataList = queryResult.getData();
        List<WarehouseGoodsDetailExcel> dataExportList = new ArrayList<>(dataList.size());
        for (WarehouseGoodsDetailApiVO dto : dataList) {
            WarehouseGoodsDetailExcel exportDTO = new WarehouseGoodsDetailExcel();
            BeanUtils.copyProperties(dto, exportDTO);
            exportDTO.setUseNum(dto.getUseNum().stripTrailingZeros().toPlainString());
            exportDTO.setReserveNum(dto.getReserveNum().stripTrailingZeros().toPlainString());
            dataExportList.add(exportDTO);
        }

        OssUtils ossUtils = new OssUtils();
        String fileName = BusinessTypeEnum.EXPORT_WAREHOUSE_GOODS_DETAIL.getMsg();
        //生成excel文件，并上传的阿里云服务器返回文件地址路径
        String downloadUrl = ossUtils.getDownloadUrl(dataExportList, WarehouseGoodsDetailExcel.class, fileName, ossNacosConfig.getBucket(), ossNacosConfig.getEndpoint(), ossNacosConfig.getAccessKeyId(), ossNacosConfig.getAccessKeySecret(), response);
        if (StringUtils.isBlank(downloadUrl)) {
            log.info("获取上传文件地址出错:{}", JSON.toJSONString(requestDTO));
            return;
        }
        //保存报表生成下载历史
        reportHistoryService.saveReportHistory(downloadUrl, BusinessTypeEnum.EXPORT_WAREHOUSE_GOODS_DETAIL.getCode(),loginUser);
    }

    private List<BrandWarehouseOutPutDTO> getImportContent(MultipartFile file,LoginUserDetail loginUser) {

        List<BrandWarehouseOutPutDTO> resWarehouseList = new ArrayList<>();
        BrandWarehouseOutPutDTO outPutDTO = null;
        // 获取excel内容
        List<BrandWarehouseOutPutExcelDTO> importCommodityDTOList = this.getExcelInfo(file);
        if (CollectionUtils.isEmpty(importCommodityDTOList)) {
            throw new BusinessException(ResultEnum.ERROR.getCode(), "导入的模板错误或内容为空");
        }
        StringBuilder sb = new StringBuilder();
        //excel行数
        int rows = 1;
        if (importCommodityDTOList.size() > 1000){
            throw new BusinessException(ResultEnum.FAILURE.getCode(),"导入数据不能超过1000条");
        }
        Set<Long> shopIdSet = new HashSet<>();
        for (BrandWarehouseOutPutExcelDTO fileList : importCommodityDTOList) {
            rows++;
            outPutDTO =  new BrandWarehouseOutPutDTO();
            BeanUtils.copyProperties(fileList,outPutDTO);
            //判断空行
            this.checkNotNull(outPutDTO, sb, rows);
            // 判断参数
            this.checkRequiredParam(outPutDTO,loginUser.getMemberCode(), sb, rows);
            resWarehouseList.add(outPutDTO);
        }

        if (!ObjectUtils.isEmpty(sb)){
            throw new BusinessException(ResultEnum.FAILURE.getCode(),sb.toString());
        }
        return resWarehouseList;
    }

    private void checkExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.info("上传文件为空");
            throw new BusinessException(ResultEnum.FAILURE.getCode(), "上传文件为空");
        }
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isNotBlank(originalFilename)) {
            String substring = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            if (!StringUtils.isEmpty(substring)) {
                if (!("xls".equals(substring) || "xlsx".equals(substring))) {
                    throw new BusinessException(ResultEnum.FAILURE.getCode(), "仅支持Excel格式文件！");
                }
            }
        } else {
            throw new BusinessException(ResultEnum.FAILURE.getCode(), "上传文件名不能为空！");
        }
    }

    /**
     * 判断空行
     * @param fileList
     */
    private void checkNotNull(BrandWarehouseOutPutDTO fileList,StringBuilder sb,int rows){
        if (StringUtils.isEmpty(fileList.getStockCode())){
            sb.append("第").append(rows).append("行:商品库存编码为空");
        } else {
            fileList.setStockCode(fileList.getStockCode().trim());
        }
        if (StringUtils.isEmpty(fileList.getBusinessType())){
            sb.append("第").append(rows).append("行:出入库类型为空");
        } else {
            fileList.setBusinessType(fileList.getBusinessType().trim());
        }

        if (StringUtils.isEmpty(fileList.getWarehouseCode())){
            sb.append("第").append(rows).append("行:仓库编码为空");
        } else {
            fileList.setWarehouseCode(fileList.getWarehouseCode().trim());
        }

    }

    /**
     * 必传字段校验
     * @param fileList
     * @param sb
     * @param rows
     */
    private void checkRequiredParam(BrandWarehouseOutPutDTO fileList,String memberCode, StringBuilder sb,int rows){

        if (StringUtils.isBlank(fileList.getBusinessType()) || (!fileList.getBusinessType().equals("增加") && !fileList.getBusinessType().equals("减少"))) {
            sb.append("第").append(rows).append("行出入库类型只可填写 增加/减少!");
        }
        if (StringUtils.isEmpty(fileList.getUseNum()) || !CommonUtil.isPriceNumber(fileList.getUseNum()) || CommonUtil.getNumberDigit(fileList.getUseNum().trim()) > 9) {
            sb.append("第").append(rows).append("行数量必填，且只可填写数字，整数位最多9位，小数点后最多两位!");
        }
        // 入库校验 仓库编码查询仓库信息
        if (StringUtils.isNotBlank(fileList.getBusinessType()) && (fileList.getBusinessType().equals("增加"))) {
            WarehouseVirtualApiDTO reqDTO = new WarehouseVirtualApiDTO();
            reqDTO.setWarehouseCode(fileList.getWarehouseCode());
            reqDTO.setSellerCode(memberCode);
            PageResult<List<WarehouseVirtualVO>> warehouseInfo = middleGroundAPI.pageWarehouseByParam(reqDTO,1,10);
            if (!warehouseInfo.isSuccess() || CollectionUtils.isEmpty(warehouseInfo.getData())) {
                sb.append("第").append(rows).append("行").append("仓库编码未查询到有效仓库信息，请先维护仓库!");
            } else {
                fileList.setWarehouseName(warehouseInfo.getData().get(0).getWarehouseName());
            }
            // 入库校验 校验商品库存编码是否已被其他品牌方绑定
            if (!StringUtils.isEmpty(fileList.getStockCode())) {
                CheckStockCodeApiDTO codeReqDTO = new CheckStockCodeApiDTO();
                codeReqDTO.setSellerCode(memberCode);
                codeReqDTO.setStockCode(fileList.getStockCode());
                Result<Integer> checkStockCode = middleGroundAPI.checkStockCode(codeReqDTO);
                if (!checkStockCode.isSuccess() || (checkStockCode.getData() != null && checkStockCode.getData() > 0)) {
                    sb.append("第").append(rows).append("行").append("商品仓库编码已被使用！");
                }
            }

        }
    }
    /**
     * 获取excel内容
     */
    private List<BrandWarehouseOutPutExcelDTO> getExcelInfo(MultipartFile file) {
        List<BrandWarehouseOutPutExcelDTO> importCommodityDTOList;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setHeadRows(1);
            importCommodityDTOList = ExcelImportUtil.importExcel(inputStream, BrandWarehouseOutPutExcelDTO.class, params);
            if (!CollectionUtils.isEmpty(importCommodityDTOList)) {
                return importCommodityDTOList;
            }
        } catch (Exception e) {
            log.error("解析出入库数据异常 error:", e);
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("解析出入库数据异常 error:", e);
            }
        }
        return null;
    }
}
