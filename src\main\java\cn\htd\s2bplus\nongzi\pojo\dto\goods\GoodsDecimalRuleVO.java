package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title GoodsDecimalRuleVO
 * @Date: 2023/11/15 10:13
 */
@Data
public class GoodsDecimalRuleVO implements Serializable {
    @ApiModelProperty(value = "商品Id")
    private Long itemId;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "规则类型 0-不允许 1-允许填写任意小数 2-允许填写小数的规格")
    private Integer ruleType;

    @ApiModelProperty(value = "支持输入小数位数")
    private Integer decimalPlaces;

    @ApiModelProperty(value = "支持输入小数点最小起订数量")
    private BigDecimal decimalNumber;

    @ApiModelProperty(value = "商品基数规格")
    private String decimalSpecs;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
