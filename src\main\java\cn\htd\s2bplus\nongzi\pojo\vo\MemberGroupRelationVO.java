package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class MemberGroupRelationVO implements Serializable {

    @ApiModelProperty(value = "分组id", example = "926388")
    private Long groupId;

    @ApiModelProperty(value = "商家编码", example = "926388")
    private String sellerId;

    @ApiModelProperty(value = "新增删除会员编码拼接字符串", example = "1")
    private String buyerId;

    @ApiModelProperty(value = "分组类型", example = "空默认、1指定人、2按行业")
    private Integer groupType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
