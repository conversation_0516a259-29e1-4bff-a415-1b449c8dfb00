package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单信息(各种订单状态)
 */
@Data
@ApiModel
public class OrderDetailResDTO implements Serializable  {

    private static final long serialVersionUID = -6853573294922337794L;


    @ApiModelProperty(value = "事业部类别 0 农资事业部 1 消费电子事业部 2 智能家电事业部 3新兴产业事业部",example = "")
    private Integer departmentType;

    /**
     * 配送方式1:供应商配送  2:自提
     */
    @ApiModelProperty(value = "配送方式1:供应商配送  2:自提")
    private String deliveryType;
    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 买家编号
     */
    @ApiModelProperty(value = "买家编号")
    private String buyerCode;

    /**
     * 买家名称
     */
    @ApiModelProperty(value = "买家名称")
    private String buyerName;

    /**
     * 卖家编号
     */
    @ApiModelProperty(value = "卖家编号")
    private String sellerCode;

    /**
     * 卖家名称
     */
    @ApiModelProperty(value = "卖家名称")
    private String sellerName;

    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "店铺编号")
    private Long shopId;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private String orderFrom;

    /**
     * 销售类型
     */
    @ApiModelProperty(value = "销售类型")
    private String salesType;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private String orderType;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单退款状态")
    private String orderRefundStatus;

    /**
     * 买家确认订单状态
     */
    @ApiModelProperty(value = "买家确认订单状态")
    private String buyerCheckStatus;

    /**
     * 卖家确认订单状态
     */
    @ApiModelProperty(value = "卖家确认订单状态")
    private String sellerCheckStatus;

    /**
     * 云原生 订单类型
     */
    @ApiModelProperty(value = "云原生 订单类型")
    private String  cloudOrderType;

    @ApiModelProperty(value = "云场订单子类型：0:默认   1字头表示撮合   10：来自商城的撮合订单    11：来自经理人的撮合订单")
    private String subOrderFrom;

    /**
     * 阶段单号
     */
    @ApiModelProperty(value = "阶段单号")
    private String orderStageNo;
    /**
     * 阶段单状态
     */
    @ApiModelProperty(value = "阶段单状态")
    private String stageStatus;

    /**
     * 支付状态
     */
    @ApiModelProperty(value = "支付状态")
    private String payStatus;

    /**
     * 物流状态
     */
    @ApiModelProperty(value = "物流状态")
    private String logisticsStatus ;

    /**
     * 收付款下行 erp 状态
     */
    @ApiModelProperty(value = "收付款下行 erp 状态")
    private String paymentDownErpStatus;

    /**
     * 结算状态 000:空值 2101:未结算 2102:已结算
     *
     */
    @ApiModelProperty(value = "结算状态 000:空值 2101:未结算 2102:已结算")
    private String settlementStatus;

    /**
     * 拆单状态
     */
    @ApiModelProperty(value = "拆单状态 ")
    private String spillStatus;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String consigneeName;

    /**
     * 收货人联系电话
     */
    @ApiModelProperty(value = "收货人联系电话")
    private String consigneePhoneNum;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    private String consigneeAddress;

    /**
     * 收货地址-省
     */
    @ApiModelProperty(value = "收货地址-省")
    private String  consigneeAddressProvince;

    /**
     * 收货地址-市
     */
    @ApiModelProperty(value = "收货地址-市")
    private String  consigneeAddressCity;

    /**
     * 收货地址-区
     */
    @ApiModelProperty(value = "收货地址-区")
    private String  consigneeAddressDistrict;

    /**
     * 收货地址-镇
     */
    @ApiModelProperty(value = "收货地址-镇")
    private String  consigneeAddressTown;

    /**
     * 收货地址-详细
     */
    @ApiModelProperty(value = "收货地址-详细")
    private String  consigneeAddressDetail;

    /**
     * 城市站编码 市的地址编码
     */
    @ApiModelProperty(value = "城市站编码 市的地址编码")
    private String  site;

    /**
     * 订单商品总数量
     */
    @ApiModelProperty(value = "订单商品总数量")
    private Integer  totalGoodsCount;

    /**
     * 订单商品总金额
     */
    @ApiModelProperty(value = "订单商品总金额")
    private BigDecimal totalGoodsAmount;

    /**
     * 用券优惠总金额
     */
    @ApiModelProperty(value = "用券优惠总金额")
    private BigDecimal  totalDiscountAmount;

    /**
     * 订单总价
     */
    @ApiModelProperty(value = "订单总价")
    private BigDecimal  orderTotalAmount;

    /**
     * 订单实付金额
     */
    @ApiModelProperty(value = "订单实付金额")
    private BigDecimal  orderPayAmount;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private String  payType;

    /**
     * 买家留言
     */
    @ApiModelProperty(value = "买家留言")
    private String  buyerRemarks;

    /**
     * 订单备注
     */
    @ApiModelProperty(value = "订单备注")
    private String  orderRemarks;

    /**
     * 普通发票抬头
     */
    @ApiModelProperty(value = "普通发票抬头")
    private String  invoiceNotify;

    /**
     * 增值税发票公司名称
     */
    @ApiModelProperty(value = "增值税发票公司名称")
    private String  invoiceCompanyName;

    /**
     * 发票邮寄地址-详细
     */
    @ApiModelProperty(value = "发票邮寄地址-详细")
    private String  invoiceAddress;

    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    private String  payChannel;

    /**
     * VIP折扣活动优惠总金额
     */
    @ApiModelProperty(value = "VIP折扣活动优惠总金额")
    private BigDecimal  totalVipDiscountAmount;

    /**
     * 运费总金额
     */
    @ApiModelProperty(value = "运费总金额")
    private BigDecimal totalFreight;

    /**
     * 平台总承担费用
     */
    @ApiModelProperty(value = "平台总承担费用")
    private BigDecimal  totalPlatformUndertakeAmount;

    /**
     * 商家总承担费用
     */
    @ApiModelProperty(value = "商家总承担费用")
    private BigDecimal  totalSellerUndertakeAmount;

    /**
     * 是否要发票
     */
    @ApiModelProperty(value = "是否要发票")
    private Integer isNeedInvoice;

    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型")
    private String invoiceType;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(value = "纳税人识别号")
    private String taxManId;

    /**
     * 开户行名称
     */
    @ApiModelProperty(value = "开户行名称")
    private String bankName;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;


    /**
     * 订单创建时间
     */
    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 订单取消标记
     */
    @ApiModelProperty(value = "订单取消标记")
    private Integer isCancelOrder;

    @ApiModelProperty(value = "订单行集合")
    private List<OrderItemsDTO> orderItemsDTOS;

    /**
     * 议价金额
     */
    @ApiModelProperty(value="议价金额", example = "议价金额")
    private BigDecimal bargainPrice;

    /**
     * 计息金额
     */
    @ApiModelProperty(value="计息金额", example = "计息金额")
    private BigDecimal interestBearPrice;

    /**
     * 提货人信息
     */
    @ApiModelProperty(value = "提货人信息")
    private OrderDeliverySelfPickupDTO orderDeliverySelfPickupDTO;

    /**
     * 是否展示发送确认订单短信按钮 true展示，false不展示
     */
    @ApiModelProperty(value="是否展示发送信息按钮 true展示，false不展示")
    private boolean showSendMsg;

    /**
     * 是否展示取消订单按钮 true展示，false不展示
     */
    @ApiModelProperty(value="是否展示取消订单按钮 true展示，false不展示")
    private boolean showCancelOrder;

    /**
     * 是否展示发送确认收货短信按钮 true展示，false不展示
     */
    @ApiModelProperty(value="是否展示发送信息按钮 true展示，false不展示")
    private boolean showSendConfirmationMsg;

    @ApiModelProperty(value="服务费")
    private BigDecimal serviceFree;

    @ApiModelProperty(value="撮合订单号")
    private String matchOrderNo;

    @ApiModelProperty(value = "订单支付时间", example = "")
    @JsonFormat(locale="zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payOrderTime;

    @ApiModelProperty(value="推荐人")
    private String recommender;


    /**
     * 支付系统流水号
     */
    @ApiModelProperty(value = "支付系统流水号")
    private String paySerialNo;

    /**
     * 收货人联系电话加密
     */
    @ApiModelProperty(value = "收货人联系电话加密")
    private String dsConsigneePhoneNum;

    /**
     * 收货地址加密
     */
    @ApiModelProperty(value = "收货地址加密")
    private String dsConsigneeAddress;

    @ApiModelProperty(value="邮箱地址")
    private String emailAddress;

    @ApiModelProperty(value="发票收件人姓名")
    private String invoiceReceiverName;

    @ApiModelProperty(value="发票收件人手机号码加密")
    private String dsInvoiceReceiverPhone;

    @ApiModelProperty(value="发票接收地址信息加密")
    private String dsInvoiceAddressDetail;

    /**
     * 银行账号加密
     */
    @ApiModelProperty(value = "银行账号加密")
    private String dsBankAccount;

    /**
     * 联系电话加密
     */
    @ApiModelProperty(value = "联系电话加密")
    private String dsContactPhone;

    /**
     * 发票邮寄地址-详细加密
     */
    @ApiModelProperty(value = "发票邮寄地址-详细加密")
    private String  dsInvoiceAddress;

    @ApiModelProperty(value = "用于区分已支付-待审核状态,1-待审核 2-通过，3-驳回")
    private String orderAuthorizationStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
