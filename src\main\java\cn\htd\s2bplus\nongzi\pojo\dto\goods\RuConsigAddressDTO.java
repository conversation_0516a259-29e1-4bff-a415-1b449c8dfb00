
package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class RuConsigAddressDTO implements Serializable {

    @ApiModelProperty(value = "会员ID",example = "1(可不传)",hidden = true)
    private Long memberId;

    @ApiModelProperty(value = "地址ID(修改必传，新增不传)",example = "1(修改必传，新增不传)")
    private Long addressId;

    @ApiModelProperty(value = "收货人姓名",example = "张三",required = true)
    @NotBlank(message = "收货人姓名必传")
    @Length(max = 20,message = "收货人姓名超出长度啦")
    private String consigneeName;

    @ApiModelProperty(value = "收货人手机号码",example = "13100000001",required = true)
    @NotBlank(message = "收货人手机号码必传")
    private String consigneeMobile;
    private String dsConsigneeMobile;


    @ApiModelProperty(value = "收货地址-省编码",example = "1",required = true)
    @NotBlank(message = "收货地址-省编码 必传")
    private String consigneeAddressProvince;

    @ApiModelProperty(value = "收货地址-市编码",example = "23",required = true)
    @NotBlank(message = "收货地址-市编码 必传")
    private String consigneeAddressCity;

   @ApiModelProperty(value = "收货地址-区编码",example = "12",required = true)
   @NotBlank(message = "收货地址-区编码 必传")
    private String consigneeAddressDistrict;

   @ApiModelProperty(value = "收货地址-镇编码",example = "42",required = true)
   //@NotBlank(message = "收货地址-镇编码 必传") 配合小程序去除必填校验
    private String consigneeAddressTown;

    @ApiModelProperty(value = "收货地址-省",example = "江苏省",required = false,hidden = true)
    private String consigneeAddressProvinceStr;

    @ApiModelProperty(value = " 收货地址-市",example = "南京市",required = false,hidden = true)
    private String consigneeAddressCityStr;

    @ApiModelProperty(value = "收货地址-区",example = "玄武区",required = false,hidden = true)
    private String consigneeAddressDistrictStr;

    @ApiModelProperty(value = "收货地址-镇",example = "麒麟镇",required = false,hidden = true)
    private String consigneeAddressTownStr;

    @ApiModelProperty(value = "收货地址-详细",example = "柳营西路50号",required = true)
    @NotBlank(message = "收货地址-详细 必传")
    @Length(max = 100,message = "收货地址-详细超出长度啦")
    private String consigneeAddressDetail;
    private String dsConsigneeAddressDetail;

    @ApiModelProperty(value = "收货地址",example = "江苏省南京市玄武区柳营西路50号")
    private String consigneeAddress;
    private String dsConsigneeAddress;

   @ApiModelProperty(value = "设置默认地址标记(0为不默认，1为默认)",example = "0")
    private String defaultFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
