package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ItemBrandDTO implements Serializable {

    private static final long serialVersionUID = 2202413930402004140L;
    @ApiModelProperty("品牌ID")
    private Long brandId;


    @ApiModelProperty("品牌名称")
    private String brandName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}

