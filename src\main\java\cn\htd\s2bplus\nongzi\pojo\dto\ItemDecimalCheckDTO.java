package cn.htd.s2bplus.nongzi.pojo.dto;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title ItemDecimalCheckDTO
 * @Date: 2023/11/15 16:55
 */
@Data
public class ItemDecimalCheckDTO implements Serializable {

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsCount;

    @ApiModelProperty(value = "是否校验规格 0-校验 1-不校验")
    private Integer isCheckSpecification;

    @ApiModelProperty(value = "0-不支持 1-支持")
    private String isSupportDecimal;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
