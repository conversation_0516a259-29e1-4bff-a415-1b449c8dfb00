package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2020/11/27 16:22
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class SkuQueryDTO implements Serializable {
    private static final long serialVersionUID = 7394885749361249358L;

    @ApiModelProperty(value = "商品编码", hidden = true)
    private Long itemId;

    @ApiModelProperty(value = "店铺ID", hidden = true)
    private Long shopId;

    @ApiModelProperty(value = "店铺名称", hidden = true)
    private String shopName;

    @ApiModelProperty(value = "代课下单：商品编码",notes = "商品编码",example = "10275855")
    private String itemCode;

    @ApiModelProperty(
            value = "外部商品编码",
            notes = "外部商品编码",
            example = "10000000"
    )
    private String outerItemCode;

    @ApiModelProperty(
            value = "品牌名称",
            notes = "品牌名称",
            example = "苹果"
    )
    private String brandName;
    @ApiModelProperty(
            value = "商品标题",
            notes = "商品名称",
            example = "创维电视65M9"
    )
    private String itemName;

    @ApiModelProperty(
            value = "商品上下架状态",
            notes = "0:下架1:上架",
            example = "1",
            hidden = true
    )
    private Integer status;


    @ApiModelProperty(
            value = "商品sku编码",
            notes = "商品sku编码",
            example = "1000276046"
    )
    private String skuCode;

    @ApiModelProperty(
            value = "商家ID",
            notes = "商家ID",
            example = "155"
    )
    private Long sellerId;

    @ApiModelProperty(
            value = "渠道id列表",
            notes = "渠道id列表",
            example = "0"
    )
    private List<Long> appIds;

    @ApiModelProperty(
            value = "erp编码",
            notes = "erp编码",
            example = "222860"
    )
    private String erpCode;

    @ApiModelProperty(
            value = "sku编码列表"
    )
    private List<String> skuCodes;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"itemId\":")
                .append(itemId);
        sb.append(",\"shopId\":")
                .append(shopId);
        sb.append(",\"shopName\":\"")
                .append(shopName).append('\"');
        sb.append(",\"itemCode\":\"")
                .append(itemCode).append('\"');
        sb.append(",\"outerItemCode\":\"")
                .append(outerItemCode).append('\"');
        sb.append(",\"brandName\":\"")
                .append(brandName).append('\"');
        sb.append(",\"itemName\":\"")
                .append(itemName).append('\"');
        sb.append(",\"status\":")
                .append(status);
        sb.append(",\"skuCode\":\"")
                .append(skuCode).append('\"');
        sb.append(",\"sellerId\":")
                .append(sellerId);
        sb.append(",\"appIds\":")
                .append(appIds);
        sb.append(",\"erpCode\":\"")
                .append(erpCode).append('\"');
        sb.append(",\"skuCodes\":\"")
                .append(skuCodes).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
