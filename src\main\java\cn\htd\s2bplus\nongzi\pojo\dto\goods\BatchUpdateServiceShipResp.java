package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title BatchUpdateServiceShipResp
 * @Date: 2025/4/14 16:07
 */
@Data
public class BatchUpdateServiceShipResp implements Serializable {
    private static final long serialVersionUID = 5422471619142090961L;

    @ApiModelProperty(value = "服务商编码")
    private String serviceProviderCode;

    @ApiModelProperty(value = "代收客户编码")
    private String buyerCode;

    @ApiModelProperty(value = "代收客户代收客户Apple ID")
    private String appleId;

    @ApiModelProperty(value = "更新结果")
    private String updateResult;

    @ApiModelProperty(value = "失败原因")
    private String failedReason;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
