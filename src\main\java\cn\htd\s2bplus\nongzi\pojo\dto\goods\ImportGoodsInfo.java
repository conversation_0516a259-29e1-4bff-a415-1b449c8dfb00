package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ImportGoodsInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务类型 1-货品商品发布
     */
    private String businessType;

    /**
     * 业务处理状态 0-初始状态 1-成功 2-失败
     */
    private String businessStatus;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 条形码
     */
    private String eanCode;

    /**
     * 单位
     */
    private String unit;

    /**
     * 成本价
     */
    private BigDecimal cost;

    /**
     * 销售价
     */
    private BigDecimal retailPrice;

    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 商家编码
     */
    private String sellerCode;

    /**
     * 子账号的商家编码
     */
    private String subSellerCode;

    /**
     * 创建人ID
     */
    private String createId;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * JSON格式的扩展信息-用于记录导入的商品类目、品牌等信息
     */
    private String extendInfo;

    /**
     * 商品导入批次号
     */
    private String batchNo;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
