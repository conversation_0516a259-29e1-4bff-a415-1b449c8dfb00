package cn.htd.s2bplus.nongzi.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
public class LocalStringUtil {

    public LocalStringUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 校验字符串只包含数字或字母
     * @param str 待校验字符串
     * @return
     */
    public static boolean isAlphabetOrNumeric(String... str) {
        boolean matches = true;
        if (str == null) {
            return false;
        }

        Pattern p = Pattern.compile("[0-9a-zA-Z]+");
        for (String s : str) {
            Matcher m = p.matcher(s);
            matches = m.matches();
            if (!matches) {
                break;
            }
        }
        return matches;
    }
}
