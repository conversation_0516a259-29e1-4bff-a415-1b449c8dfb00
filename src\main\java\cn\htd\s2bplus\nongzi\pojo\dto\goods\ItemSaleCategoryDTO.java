package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ItemSaleCategoryDTO implements Serializable {
    private static final long serialVersionUID = 2269299347228576854L;

    @ApiModelProperty(value = "一级销售类目ID")
    private Long firstCid;

    @ApiModelProperty(value = "二级销售类目ID")
    private Long secondCid;

    @ApiModelProperty(value = "三级销售类目ID")
    private Long thirdCid;

    @ApiModelProperty(value = "一级销售类目名称")
    private String firstCname;

    @ApiModelProperty(value = "二级销售类目名称")
    private String secondCname;

    @ApiModelProperty(value = "三级销售类目名称")
    private String thirdCname;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
