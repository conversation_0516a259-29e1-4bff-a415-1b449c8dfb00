package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title DecimalRuleParamDTO
 * @Date: 2023/11/22 15:05
 */
@Data
public class DecimalRuleParamDTO implements Serializable {
    @ApiModelProperty(value = "商品id集合")
    private List<Long> itemIds;

    @ApiModelProperty(value = "商品编码集合")
    private List<String> itemCodes;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
