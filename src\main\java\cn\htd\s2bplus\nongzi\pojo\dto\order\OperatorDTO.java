package cn.htd.s2bplus.nongzi.pojo.dto.order;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020-05-28 10:58
 * @Version 1.0
 */
public class OperatorDTO implements Serializable {


    private static final long serialVersionUID = -934662945353073754L;
    protected String operateId;
    protected String operateName;
    protected Date operateTime;

    public OperatorDTO() {
    }

    public OperatorDTO(String operateId, String operateName, Date operateTime) {
        this.operateId = operateId;
        this.operateName = operateName;
        this.operateTime = operateTime;
    }

    public String getOperateId() {
        return this.operateId;
    }

    public String getOperateName() {
        return this.operateName;
    }

    public Date getOperateTime() {
        return this.operateTime;
    }

    public void setOperateId(String operateId) {
        this.operateId = operateId;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"operateId\":\"")
                .append(operateId).append('\"');
        sb.append(",\"operateName\":\"")
                .append(operateName).append('\"');
        sb.append(",\"operateTime\":\"")
                .append(operateTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
