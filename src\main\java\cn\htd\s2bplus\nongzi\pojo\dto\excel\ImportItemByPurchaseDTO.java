package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;


@Data
@EqualsAndHashCode
public class ImportItemByPurchaseDTO implements Serializable {

	private static final long serialVersionUID = 7391369826388360680L;
	//商品/SKU编码
	private String code;
	//限购数量
	private Integer purchaseCount;



	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
	}
}
