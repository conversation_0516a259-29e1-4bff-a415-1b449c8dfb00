package cn.htd.s2bplus.nongzi.enums;

/**
 * 报表业务类型  ，0=未知，1=销售单报表，2=结算单报表
 * <AUTHOR>
 * date: 2020/5/9
 */
public enum BusinessTypeEnum {

    /**
     * 未处理
     */
    Unknown(0, "未知"),

    /**
     * 销售单报表
     */
    SALE_ORDER(1, "销售单报表"),


    /**
     * 结算单报表
     */
    SETTLEMENT_ORDER(2, "结算单报表"),

    /**
     * 线下结算单报表
     */
    OFFLINE_SETTLEMENT_ORDER(3, "线下付款结算报表"),

    /**
     * 导入发货信息表
     */
    IMPORT_SEND_ORDER(4, "导入发货信息表"),

    /**
     * 导入发货信息表
     */
    AUTHENTICATION_RESULT_ERROR(5, "实名制验证失败"),

    /**
     * 商品价格报表
     */
    SALE_GOODS(6, "商品价格报表"),

    /**
     * 会员分组内容
     */
    MEMBER_GROUP_CONTENT(7, "会员分组内容"),

    /**
     * 批量线下开单表
     */
    // order-service
    EXPORT_GUEST_ORDER(8, "批量线下开单表"),

    // order-service
    EXPORT_PURCHASER_CENTER_SALE_ORDER(9, "供货中心销售订单报表"),
    /**
     * 发券结余信息
     */
    EXPORT_BALANCE_INFORMATION(9, "发券结余信息"),
    /**
     * 发券结余信息-活动
     */
    EXPORT_PROMOTION_BALANCE_INFORMATION(10, "发券结余信息-活动"),
    /**
     * 发券结余信息-会员
     */
    EXPORT_MEMBER_BALANCE_INFORMATION(11, "发券结余信息-会员"),

    EXPORT_DISTRIBUTOR_COMMISSION(12, "分销员佣金汇总报表"),
    EXPORT_COMMISSION_RECORD(13, "分销员佣金明细报表"),
    EXPORT_WAREHOUSE_GOODS_DETAIL(14, "库存商品明细列表"),
    // goods-service
    GOODS_SERVICE_IMPORT_GOODS_INFO_RECORD(15, "橙意采货盘商品导入记录表"),
    // goods-service
    GOODS_SERVICE_EXPORT_GOODS_INFO_RECORD(16, "橙意采货盘商品导出记录表"),
    // goods-service
    GOODS_SERVICE_PUBLISH_GOODS_FAILURE_RECORD(17, "橙意采货盘商品发布失败记录表"),
    EXPORT_CYC_USER_LIST(18, "橙易采会员列表"),
    EXPORT_SETTLEMENT_OFFLINE(5, "佣金订单明细（线下结算）"),

    OFFLINE_REMIT(1,"线下汇款"),
     DEPOSIT_PAY(0,"账存支付"),
    OFFLINE_SETTLEMENT_STATUS_0(0,"待线下结算"),
    OFFLINE_SETTLEMENT_STATUS_1(1,"已线下结算"),
    MEMBER_TYPE_DEALER(1,"自营商家"),

    BATCH_SHIPMENT_ERR_LIST(19, "批量发货-错误列表"),
    // goods-service
    BATCH_IMPORT_PUBLISH_GOODS_ERROR_RECORD(20, "批量导入商品发布-数据校验不通过列表"),
    // goods-service
    BATCH_IMPORT_PUBLISH_GOODS_UPLOAD_RECORD(21, "批量导入商品发布-上传excel记录"),

    ;


    private Integer code;
    private String msg;

    BusinessTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
