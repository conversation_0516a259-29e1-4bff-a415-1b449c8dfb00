package cn.htd.s2bplus.nongzi.service.goods;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.WarehouseGoodsApiDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.ImportItemOrSkuListVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * created by b<PERSON><PERSON><PERSON> on 2024/9/7
 */
public interface BrandWarehouseService {

    /**
     * 批量导入出入库
     * @param file
     * @return
     */
    Result<String> importOutputWarehouse(MultipartFile file, LoginUserDetail loginUser);

    /**
     * 异步导出文件
     * @return
     */
    void exportWarehouseGoodsDetail(WarehouseGoodsApiDTO requestDTO, HttpServletResponse response, LoginUserDetail loginUser);
}
