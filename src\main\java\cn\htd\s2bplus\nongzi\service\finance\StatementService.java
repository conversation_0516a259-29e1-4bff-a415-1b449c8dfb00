package cn.htd.s2bplus.nongzi.service.finance;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.finance.OfflineSettlementInfoQueryDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.finance.SettlementOrderDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.ReportHistoryDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
public interface StatementService {

    /**
     * 结算单生成报表
     *
     * @param settlementOrderDTO
     * @return
     */
    Result<Boolean> settlementReportGeneration(SettlementOrderDTO settlementOrderDTO,
                                               HttpServletRequest request,
                                               HttpServletResponse response);


    Result<Boolean> saveGeneration(String createId, String createName, String type, InputStream excelStream);

    /**
     * 批量修改线下账存结算
     * @param file 导入文件
     * @param userDetail 当前登录用户信息
     * @return
     */
    Result<String> batchModify(MultipartFile file, LoginUserDetail userDetail);

    /**
     * 导出线下账存结算列表
     * @param req 入参
     * @param response
     * @return
     */
    Result<String> exportData(OfflineSettlementInfoQueryDTO req, HttpServletResponse response);

}
