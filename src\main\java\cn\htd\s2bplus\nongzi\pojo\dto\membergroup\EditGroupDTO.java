package cn.htd.s2bplus.nongzi.pojo.dto.membergroup;

import cn.htd.s2bplus.nongzi.pojo.dto.user.MemberBaseInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EditGroupDTO implements Serializable {


    @ApiModelProperty(value = "分组ID",example = "1",required = true)
    @NotNull(message = "分组ID不能为空")
    private Long groupId;



    @ApiModelProperty(value = "分组名称",example = "美的组",required = false)
    @NotBlank(message = "分组名称不能为空")
    @Size(min = 0,max = 20,message = "分组名称长度不符合要求,要求在20个字符之内")
    private String name;

    @ApiModelProperty(value = "分组类型",example = "1指定人、2按行业",required = false)
    private Integer groupType;

    @ApiModelProperty(value = "备注",example = "备注",required = false)
    @Size(min = 0,max = 30,message = "备注字符长度不符合要求,要求在30个字符之内")
    private String comment;

    @ApiModelProperty(value = "新增删除会员编码拼接字符串",example = "1,2,3,4",required = false)
    private String buyerIds;


    @ApiModelProperty(value = "操作人编码",example = "操作人编码",required = false,hidden = true)
    public String operateId;

    @ApiModelProperty(value = "操作人姓名",example = "操作人姓名",required = false,hidden = true)
    public String operateName;

    @ApiModelProperty(value = "修改人ID",example = "修改人ID",required = false,hidden = true)
    private String modifyId;

    @ApiModelProperty(value = "修改人名称",example = "修改人名称",required = false,hidden = true)
    private String modifyName;

    @ApiModelProperty(value = "删除分组编码拼接字符串",example = "1 多个用,拼接",required = false,hidden = true)
    private String groupIds;

    @ApiModelProperty(value = "商家编码",example = "1213",required = false,hidden = true)
    private String sellerId;

    @ApiModelProperty(value = "商家编码",example = "1213",required = false,hidden = true)
    private String sellerCode;

    @ApiModelProperty(value = "行业名称,更新行业编码名称拼接字符串",example = "酒水,家电,交通出行")
    private String industryNames;

    @ApiModelProperty(value = "登录运营人员Id",example = "登录运营人员Id",required = false,hidden = true)
    public String operationId;

    @ApiModelProperty(value = "会员信息集合")
    private Map<Long,MemberBaseInfoDTO> buyerInfoMap;

    public String getIndustryNames() {
        return industryNames;
    }

    public void setIndustryNames(String industryNames) {
        this.industryNames = industryNames;
    }

    public Long getGroupId() {
        return groupId;
    }

    public EditGroupDTO setGroupId(Long groupId) {
        this.groupId = groupId;
        return this;
    }

    public String getName() {
        return name;
    }

    public EditGroupDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public EditGroupDTO setGroupType(Integer groupType) {
        this.groupType = groupType;
        return this;
    }

    public String getComment() {
        return comment;
    }

    public EditGroupDTO setComment(String comment) {
        this.comment = comment;
        return this;
    }

    public String getBuyerIds() {
        return buyerIds;
    }

    public EditGroupDTO setBuyerIds(String buyerIds) {
        this.buyerIds = buyerIds;
        return this;
    }

    public String getOperateId() {
        return operateId;
    }

    public EditGroupDTO setOperateId(String operateId) {
        this.operateId = operateId;
        return this;
    }

    public String getOperateName() {
        return operateName;
    }

    public EditGroupDTO setOperateName(String operateName) {
        this.operateName = operateName;
        return this;
    }

    public String getModifyId() {
        return modifyId;
    }

    public EditGroupDTO setModifyId(String modifyId) {
        this.modifyId = modifyId;
        return this;
    }

    public String getModifyName() {
        return modifyName;
    }

    public EditGroupDTO setModifyName(String modifyName) {
        this.modifyName = modifyName;
        return this;
    }

    public String getGroupIds() {
        return groupIds;
    }

    public EditGroupDTO setGroupIds(String groupIds) {
        this.groupIds = groupIds;
        return this;
    }

    public String getSellerId() {
        return sellerId;
    }

    public EditGroupDTO setSellerId(String sellerId) {
        this.sellerId = sellerId;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"groupId\":")
                .append(groupId);
        sb.append(",\"name\":\"")
                .append(name).append('\"');
        sb.append(",\"groupType\":")
                .append(groupType);
        sb.append(",\"comment\":\"")
                .append(comment).append('\"');
        sb.append(",\"buyerIds\":\"")
                .append(buyerIds).append('\"');
        sb.append(",\"operateId\":\"")
                .append(operateId).append('\"');
        sb.append(",\"operateName\":\"")
                .append(operateName).append('\"');
        sb.append(",\"modifyId\":\"")
                .append(modifyId).append('\"');
        sb.append(",\"modifyName\":\"")
                .append(modifyName).append('\"');
        sb.append(",\"groupIds\":\"")
                .append(groupIds).append('\"');
        sb.append(",\"sellerId\":\"")
                .append(sellerId).append('\"');
        sb.append(",\"sellerCode\":\"")
                .append(sellerCode).append('\"');
        sb.append(",\"industryNames\":")
                .append(industryNames);
        sb.append('}');
        return sb.toString();
    }
}
