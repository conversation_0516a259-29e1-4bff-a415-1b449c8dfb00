package cn.htd.s2bplus.nongzi.pojo.dto.erp;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

@Data
public class PurchasingDepartmentDTO {


    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "采购部门编码")
    private String departmentCode;
    @ApiModelProperty(value = "采购部门名称")
    private String departmentName;
    @ApiModelProperty(value = "仓库编码")
    private String wareHouseCode;
    @ApiModelProperty(value = "仓库名称")
    private String wareHouseName;
    @ApiModelProperty(value = "公司编码")
    private String companyCode;
    @ApiModelProperty(value = "商品id")
    private Long itemId;
    @ApiModelProperty(value = "商品code")
    private String itemCode;
    @ApiModelProperty(value = "实际库存数量")
    private String storeNum;
    @ApiModelProperty(value = "商品属性")
    private String productAttribute;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
