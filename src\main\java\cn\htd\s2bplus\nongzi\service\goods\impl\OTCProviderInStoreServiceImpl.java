package cn.htd.s2bplus.nongzi.service.goods.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.htd.rdc.base.development.framework.core.cache.IRedisService;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.config.OssNacosConfig;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.RedisConstants;
import cn.htd.s2bplus.nongzi.enums.OrderResultCodeEnum;
import cn.htd.s2bplus.nongzi.enums.ParamCheckResultEnum;
import cn.htd.s2bplus.nongzi.enums.ParamEnum;
import cn.htd.s2bplus.nongzi.feign.finance.TradeOrderFeignService;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.feign.user.UserService;
import cn.htd.s2bplus.nongzi.pojo.dto.ItemDecimalCheckDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.erp.PurchasingDepartmentDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ExportBatchAddServiceAddressResp;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.*;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.Constant;
import cn.htd.s2bplus.nongzi.pojo.dto.order.FinanceReportformEntity;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.dto.user.MemberBaseInfoDTO;
import cn.htd.s2bplus.nongzi.pojo.vo.ItemVO;
import cn.htd.s2bplus.nongzi.service.goods.GoodsService;
import cn.htd.s2bplus.nongzi.service.goods.OTCProviderInStoreService;
import cn.htd.s2bplus.nongzi.utils.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OTCProviderInStoreServiceImpl implements OTCProviderInStoreService {
    @Resource
    private GoodsFeignService goodsFeignService;

    @Autowired
    private OssNacosConfig ossNacosConfig;

    @Autowired
    private IRedisService redisServiceImpl;

    @Autowired
    private UserService userService;

    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;

    @Autowired
    private MiddleGroundAPI middleGroundAPI;

    @Autowired
    private TradeOrderFeignService tradeOrderFeignService;

    @Autowired
    private GoodsService goodsService;

    @Override
    @Async
    public Result<Boolean> exportIntentionAddress(QueryIntentionAddressPageDTO queryIntentionAddressPageDTO, HttpServletResponse response,LoginUserDetail loginUser) {
        try {
            log.info("导出分销商地址关系 入参：{}", queryIntentionAddressPageDTO);
            queryIntentionAddressPageDTO.setPage(1);
            queryIntentionAddressPageDTO.setSize(10000);
            if (Constant.TRY_EXPORT.equals(queryIntentionAddressPageDTO.getConsigneeType())) {
                queryIntentionAddressPageDTO.setSellerCode(loginUser.getLoginId());
            }
            PageResult<List<IntentionAddressWarehouseDTO>> listPageResult = goodsFeignService.queryIntentionAddressByPage(queryIntentionAddressPageDTO);
            log.info("查询分销商地址对应关系列表 出参：{}", listPageResult);
            if (listPageResult.getData() == null || listPageResult.getData().size() == 0){
                return CommonResultUtil.error(ResultEnum.FAILURE.getCode(), "查询分销商地址对应关系列表异常");
            }
            if (!listPageResult.isSuccess()){
                return CommonResultUtil.error(listPageResult.getCode(),listPageResult.getMsg());
            }
            List<IntentionAddressWarehouseDTO> data = listPageResult.getData();
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName("分销商地址关系列表");
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", ExportIntentionProviderAddressDTO.class);
            // sheet中要填充得数据
            List<ExportIntentionProviderAddressDTO> exportDataList = new ArrayList<>();
            for (IntentionAddressWarehouseDTO intentionAddressWarehouseDTO : data) {
                if (Constant.APPLE_DATA.equals(intentionAddressWarehouseDTO.getConsigneeType()) && Constant.WAIT_DEAL.equals(intentionAddressWarehouseDTO.getStatus())) {
                    intentionAddressWarehouseDTO.setBuyerCode(StringUtils.isNotBlank(intentionAddressWarehouseDTO.getBuyerCode()) && intentionAddressWarehouseDTO.getBuyerCode().contains("htd") ? intentionAddressWarehouseDTO.getBuyerCode() : StringUtils.EMPTY);
                }
                ExportIntentionProviderAddressDTO export = new ExportIntentionProviderAddressDTO();
                //组装主要数据
                BeanUtil.copy(intentionAddressWarehouseDTO,export);
                exportDataList.add(export);
            }
            if (CommonConstants.TRY_EXPORT_ADDRESS.equals(queryIntentionAddressPageDTO.getConsigneeType())) {
                sheet1DataMap.put("entity", TryExportProviderAddressDTO.class);
                List<TryExportProviderAddressDTO> tryExportDataList = new ArrayList<>();
                for (ExportIntentionProviderAddressDTO export : exportDataList) {
                    TryExportProviderAddressDTO tryExport = new TryExportProviderAddressDTO();
                    BeanUtils.copyProperties(export, tryExport);
                    tryExportDataList.add(tryExport);
                }
                // sheet中要填充得数据
                sheet1DataMap.put("data", tryExportDataList);

            } else {
                // sheet中要填充得数据
                sheet1DataMap.put("data", exportDataList);
            }
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType("applicationnd.ms-excel"); // 改成输出excel文件
            String fileName = java.net.URLEncoder.encode("结算单列表", "UTF-8");
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            return saveAddressGeneration(loginUser, new ByteArrayInputStream(barray));
        }catch (Exception e){
            log.error("导出分销商地址关系异常",e);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    /**
     * 生成报表上传
     * @param
     * @return
     */
    private Result<Boolean> saveAddressGeneration(LoginUserDetail loginUser, InputStream excelStream) {
        try {
            OssUtils ossUtils = new OssUtils();
            String fileName = getfileName("xls");
            String downloadUrl = ossUtils.upload(excelStream, fileName, ossNacosConfig.getBucket(),ossNacosConfig.getEndpoint(),ossNacosConfig.getAccessKeyId(),ossNacosConfig.getAccessKeySecret());
            if (StringUtils.isEmpty(downloadUrl)) {
                return CommonResultUtil.error(ResultEnum.FAILURE.getCode(), "OSS上传失败");
            }

            // 保存报表url
            FinanceReportformEntity financeReportformEntity = new FinanceReportformEntity();
            financeReportformEntity.setType(ParamEnum.EXPORT_INTENTION_ADDRESS.getCode());
            financeReportformEntity.setCreateId(loginUser.getUserId() + "");
            financeReportformEntity.setCreateName(loginUser.getUserName());
            financeReportformEntity.setCreateDate(new Date());
            financeReportformEntity.setDownUrl(downloadUrl);
            financeReportformEntity.setFileName(fileName);
            Result<Boolean> saveFinanceReportForm = tradeOrderFeignService.saveFinanceReportForm(financeReportformEntity);
            if (saveFinanceReportForm.isSuccess() && saveFinanceReportForm.getData()) {
                return ResultUtil.success(ResultEnum.SUCCESS.getCode());
            } else {
                return ResultUtil.error(ResultEnum.FAILURE.getCode(), "保存报表地址失败");
            }
        } catch (Exception e) {
            log.error("保存报表地址异常",e);
            return CommonResultUtil.error(ResultEnum.FAILURE.getCode(), "保存报表地址异常");
        }
    }

    @Override
    public Result<String> exportSalesInStore(QuerySalesInStoreDTO querySalesInStoreDTO, HttpServletResponse response,LoginUserDetail loginUser) {
        try {
            log.info("查询销售计划详情 入参:{}",querySalesInStoreDTO);
            querySalesInStoreDTO.setPage(1);
            querySalesInStoreDTO.setRows(10000);
            QuerySalesInStorePageDTO querySalesInStorePageDTO = new QuerySalesInStorePageDTO();
            BeanUtils.copyProperties(querySalesInStoreDTO,querySalesInStorePageDTO);
            querySalesInStorePageDTO.setSellerCode(loginUser.getLoginId());
            PageResult<List<IntentionAddressDTO>> listPageResult = goodsFeignService.queryIntentionAddressRecordByPage(querySalesInStorePageDTO);
            log.info("分页查询销售计划 返回结果:{}", JSON.toJSONString(listPageResult));
            if (!listPageResult.isSuccess()){
                return CommonResultUtil.errorPage(listPageResult.getCode(),listPageResult.getMsg());
            }
            List<IntentionAddressDTO> data = listPageResult.getData();
            List<SalesInStoreDetailDTO> addressDetail = new ArrayList<>();
            for (IntentionAddressDTO intentionAddressDTO : data) {
                SalesInStoreDetailDTO salesInStoreDetailDTO = new SalesInStoreDetailDTO();
                BeanUtils.copyProperties(intentionAddressDTO,salesInStoreDetailDTO);
                salesInStoreDetailDTO.setTotalPrice(intentionAddressDTO.getGoodsPrice().multiply(intentionAddressDTO.getGoodsCount()));
                salesInStoreDetailDTO.setTotalCount(intentionAddressDTO.getGoodsCount());
                addressDetail.add(salesInStoreDetailDTO);
            }
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName("销售计划详情");
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", ExportSalesInStoreDetailDTO.class);
            // sheet中要填充得数据
            List<ExportSalesInStoreDetailDTO> exportDataList = new ArrayList<>();
            for (SalesInStoreDetailDTO salesInStoreDetailDTO : addressDetail) {
                ExportSalesInStoreDetailDTO export = new ExportSalesInStoreDetailDTO();
                //组装主要数据
                BeanUtil.copy(salesInStoreDetailDTO,export);
                exportDataList.add(export);
            }
            // sheet中要填充得数据
            sheet1DataMap.put("data", exportDataList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType("applicationnd.ms-excel"); // 改成输出excel文件
            String fileName = java.net.URLEncoder.encode("销售计划列表", "UTF-8");
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            return saveGeneration(new ByteArrayInputStream(barray));
        }catch (Exception e){
            log.error("导出分销商地址关系异常",e);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    @Override
    public Result<String> exportIntentionInStore(String intentionNo, HttpServletResponse response) {
        try {
            log.info("查询销售计划详情 入参:{}",intentionNo);
            Result<List<IntentionAddressDTO>> intentionAddressListResult = goodsFeignService.getIntentionAddressList(intentionNo);
            log.info("查询销售计划详情 出参:{}",intentionAddressListResult);
            if (!intentionAddressListResult.isSuccess()){
                return CommonResultUtil.errorPage(intentionAddressListResult.getCode(),intentionAddressListResult.getMsg());
            }
            List<IntentionAddressDTO> intentionAddressList = intentionAddressListResult.getData();
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName("到店地址列表");
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", ExportIntentionAddressDTO.class);
            // sheet中要填充得数据
            List<ExportIntentionAddressDTO> exportDataList = new ArrayList<>();
            for (IntentionAddressDTO intentionAddressDTO : intentionAddressList) {
                ExportIntentionAddressDTO export = new ExportIntentionAddressDTO();
                //组装主要数据
                BeanUtil.copy(intentionAddressDTO,export);
                StringBuilder snList = new StringBuilder();
                if (!CollectionUtils.isEmpty(intentionAddressDTO.getSnList())){
                    for (String s : intentionAddressDTO.getSnList()) {
                        snList.append(s).append(",");
                    }
                }
                export.setSnList(snList.toString());
                // 计算金额并且保留两位小数
                export.setTotalPrice(intentionAddressDTO.getGoodsPrice().multiply(intentionAddressDTO.getGoodsCount()).setScale(2, BigDecimal.ROUND_HALF_UP));
                exportDataList.add(export);
            }
            // sheet中要填充得数据
            sheet1DataMap.put("data", exportDataList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType("applicationnd.ms-excel"); // 改成输出excel文件
            String fileName = java.net.URLEncoder.encode("到店地址列表", "UTF-8");
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            return saveGeneration(new ByteArrayInputStream(barray));
        }catch (Exception e){
            log.error("导出分销商地址关系异常",e);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    @Override
    public Result<Boolean> batchImportSalesInStoreNew(MultipartFile file, LoginUserDetail loginUser) {
        try {
            // 文件非空校验
            this.checkExcel(file);
            //XSS拦截
            new XssFilter().importFilter(file, null, ImportSalesInStoreDTO.class.getCanonicalName());
            OssUtils ossUtils = new OssUtils();
            String suffix = StringUtils.substringAfterLast(file.getOriginalFilename(), ".");
            String fileName = getfileName(suffix);
            String downloadUrl = ossUtils.upload(file.getInputStream(), fileName, ossNacosConfig.getBucket(),ossNacosConfig.getEndpoint(),ossNacosConfig.getAccessKeyId(),ossNacosConfig.getAccessKeySecret());
            log.info("批量新增到店销售计划, downloadUrl={},loginId:{}", downloadUrl,BaseContextHandler.getLoginId());
            // 获取excel内容
            List<ImportSalesInStoreDTO> importSalesInStoreDTOList = this.getExcelInfo(file);
            if (CollectionUtils.isEmpty(importSalesInStoreDTOList)){
                throw new BusinessException(ResultEnum.ERROR.getCode(), "导入的模板错误或内容为空");
            }
            // 校验导入内容(无接口查询)
            this.checkImportSalesInStore(importSalesInStoreDTOList);
            List<IntentionAddressRecordAddDTO> addressRecordAddDTOS = new ArrayList<>();
            for (ImportSalesInStoreDTO importSalesInStoreDTO : importSalesInStoreDTOList) {
                IntentionAddressRecordAddDTO intentionAddressRecordAddDTO = new IntentionAddressRecordAddDTO();
                BeanUtil.copy(importSalesInStoreDTO, intentionAddressRecordAddDTO);
                intentionAddressRecordAddDTO.setSellerCode(loginUser.getLoginId());
                intentionAddressRecordAddDTO.setCreateId(loginUser.getMemberId());
                intentionAddressRecordAddDTO.setCreateName(loginUser.getUserName());
                intentionAddressRecordAddDTO.setModifyId(loginUser.getMemberId());
                intentionAddressRecordAddDTO.setModifyName(loginUser.getUserName());
                intentionAddressRecordAddDTO.setShopId(String.valueOf(importSalesInStoreDTO.getShopId()));
                addressRecordAddDTOS.add(intentionAddressRecordAddDTO);
            }
            log.info("批量新增到店销售计划 入参：{}",addressRecordAddDTOS);
            Result<Boolean> result = goodsFeignService.batchAddSalesInStore(addressRecordAddDTOS);
            log.info("批量新增到店销售计划 出参：{}",result);
            if (result.isSuccess()) {
                goodsService.updateConsigneeInfo(addressRecordAddDTOS);
            }
            return result;
        }catch (BusinessException bx){
            log.info("导入销售计划入库失败：",bx);
            return CommonResultUtil.error(bx.getCode(),bx.getMessage());
        }catch (Exception e){
            log.error("导入销售计划入库异常：",e);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    /**
     * 到店销售计划-校验导入内容(无接口查询)
     */
    private void checkImportSalesInStore(List<ImportSalesInStoreDTO> importSalesInStoreDTOList) {
        if (importSalesInStoreDTOList.size() > nongZiNacosConfig.getImportOTCProviderInStoreMaxCount()){
            throw new BusinessException(ResultEnum.ERROR.getCode(), "导入销售计划数量不能超过"+nongZiNacosConfig.getImportOTCProviderInStoreMaxCount()+"条");
        }
        Set<Long> shopIdSet = new HashSet<>();
        Set<String> providerServiceCodeSet = new HashSet<>();
        Set<String> appleIdSet = new HashSet<>();
        StringBuilder sb = new StringBuilder();
        Map<String, List<String>> appleIdSkuListMap = new HashMap<>();
        Map<String,BigDecimal> skuPriceMap = new HashMap<>();
        Map<String,String> consigneeInfoMap = new HashMap<>();
        for (int i = 0; i < importSalesInStoreDTOList.size(); i++) {
            int rows = i + 2;
            ImportSalesInStoreDTO salesInStoreDTO = importSalesInStoreDTOList.get(i);
            ParamCheckResultEnum checkResultEnum = CommonUtil.isAllParamEmpty(
                    salesInStoreDTO.getDepartmentCode(),
                    salesInStoreDTO.getDepartmentName(),
                    salesInStoreDTO.getWarehouseCode(),
                    salesInStoreDTO.getSupplierCode());
            if (ParamCheckResultEnum.OTHER.getCode().equals(checkResultEnum.getCode())){
                sb.append("第").append(rows).append("行采购部门名称和供应商编码以及仓库编码必须同时为空或同时不为空.");
            }
            this.checkNotNull(salesInStoreDTO, sb, rows);
            // 判断参数
            this.checkRequiredParam(salesInStoreDTO, sb, rows);
            shopIdSet.add(salesInStoreDTO.getShopId());
            providerServiceCodeSet.add(salesInStoreDTO.getServiceProviderCode());
            appleIdSet.add(salesInStoreDTO.getAppleId());
            this.checkConsigneeInfo(rows, consigneeInfoMap, salesInStoreDTO,sb);
            // 相同地址内(同appleId) 不能有重复的sku
            if (appleIdSkuListMap.containsKey(salesInStoreDTO.getAppleId())){
                if (appleIdSkuListMap.get(salesInStoreDTO.getAppleId()).contains(salesInStoreDTO.getSkuCode())){
                    sb.append("第").append(rows).append("行同appleId").append(salesInStoreDTO.getAppleId()).append("的SKU编码").append(salesInStoreDTO.getSkuCode()).append("不能重复.");
                }else {
                    appleIdSkuListMap.get(salesInStoreDTO.getAppleId()).add(salesInStoreDTO.getSkuCode());
                }
            } else {
                appleIdSkuListMap.put(salesInStoreDTO.getAppleId(),new ArrayList<>(Collections.singletonList(salesInStoreDTO.getSkuCode())));
            }
            // 相同sku价格必须相同
            if (skuPriceMap.containsKey(salesInStoreDTO.getSkuCode())){
                if (skuPriceMap.get(salesInStoreDTO.getSkuCode()).compareTo(salesInStoreDTO.getGoodsPrice()) != 0){
                    sb.append("第").append(rows).append("行同SKU编码").append(salesInStoreDTO.getSkuCode()).append("价格必须相同.");
                }
            } else {
                skuPriceMap.put(salesInStoreDTO.getSkuCode(),salesInStoreDTO.getGoodsPrice());
            }
        }
        if (shopIdSet.size() > 1){
            sb.append("店铺ID只能为同一店铺");
        }
        if (providerServiceCodeSet.size() > 1){
            sb.append("导入数据必须为同一服务商");
        }
        if (appleIdSet.size() > nongZiNacosConfig.getImportInStoreMaxOrderCount()){
            sb.append("导入销售计划appleId数量不能超过").append(nongZiNacosConfig.getImportInStoreMaxOrderCount());
        }
        if (!ObjectUtils.isEmpty(sb)){
            throw new BusinessException(ResultEnum.FAILURE.getCode(),sb.toString());
        }
    }

    @Override
    public Result<Boolean> batchImportSalesInStore(MultipartFile file, LoginUserDetail loginUser) {
        try {
            // 文件非空校验
            this.checkExcel(file);
            //XSS拦截
            new XssFilter().importFilter(file, null, ImportSalesInStoreDTO.class.getCanonicalName());
            // 获取导入内容并校验模板
            List<ImportSalesInStoreDTO> importContent = this.getImportContent(file);
            // 校验服务商和会员店编码
            int rows = 1;
            Map<String,MemberBaseInfoDTO> serviceProviderMap = new HashMap<>();
            List<ShopSkuQueryDTO> skuQueryDTOList = new ArrayList<>();
            Map<String, Integer> memberSkuMap = new HashMap<>();
            Map<String, List<BigDecimal>> skuPriceCheckMap = new HashMap<>();
            for (ImportSalesInStoreDTO sale : importContent) {
                String key = sale.getServiceProviderCode() + sale.getAppleId() + sale.getSkuCode();
                String skuPriceKey = sale.getServiceProviderCode() + sale.getSkuCode();
                if (memberSkuMap.containsKey(key)) {
                    memberSkuMap.put(key, 1);
                } else {
                    memberSkuMap.put(key, 0);
                }
                if (skuPriceCheckMap.containsKey(skuPriceKey)) {
                    List<BigDecimal> priceList = new ArrayList<>();
                    priceList.addAll(skuPriceCheckMap.get(skuPriceKey));
                    priceList.add(sale.getGoodsPrice());
                    skuPriceCheckMap.put(skuPriceKey, priceList);
                } else {
                    skuPriceCheckMap.put(skuPriceKey, Arrays.asList(sale.getGoodsPrice()));
                }
            }
            Map<String,String> consigneeInfoMap = new HashMap<>();
            for (ImportSalesInStoreDTO importSalesInStoreDTO : importContent) {
                rows++;
                String key = importSalesInStoreDTO.getServiceProviderCode() + importSalesInStoreDTO.getAppleId() + importSalesInStoreDTO.getSkuCode();
                String skuPriceKey = importSalesInStoreDTO.getServiceProviderCode() + importSalesInStoreDTO.getSkuCode();
                // 服务商编码
                if ( !ObjectUtils.isEmpty(memberSkuMap.get(key)) && memberSkuMap.get(key)== 1) {
                    throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rows + "行" + importSalesInStoreDTO.getServiceProviderCode() +"和" + importSalesInStoreDTO.getAppleId() + "对应的SKU编码:" + importSalesInStoreDTO.getSkuCode() + "存在数据重复" );
                }
                this.checkConsigneeInfo(rows, consigneeInfoMap, importSalesInStoreDTO,null);
                // sku价格校验
                if (!CollectionUtils.isEmpty(skuPriceCheckMap.get(skuPriceKey))) {
                    List<BigDecimal> list = skuPriceCheckMap.get(skuPriceKey);
                    BigDecimal price = list.get(0);
                    for (BigDecimal a : list) {
                        if (a.compareTo(price) != 0) {
                            throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rows + "行，" + "服务商" + importSalesInStoreDTO.getServiceProviderCode() + "下相同商品" + importSalesInStoreDTO.getSkuCode() + "价格必须一致");
                        }
                    }
                }
                if (ObjectUtils.isEmpty(serviceProviderMap.get(importSalesInStoreDTO.getServiceProviderCode()))) {
                    MemberBaseInfoDTO memberBaseInfoDTO = this.checkMemberCodeInfo(importSalesInStoreDTO.getServiceProviderCode(), rows, Constant.SERVICE_PROVIDER_TYPE);
                    serviceProviderMap.put(importSalesInStoreDTO.getServiceProviderCode(),memberBaseInfoDTO);
                }
                // 校验地址和收获范围
                if(nongZiNacosConfig.getCheckAddressFlag()){
                    this.checkAddressAndGoods(importSalesInStoreDTO,rows,loginUser.getMemberCode());
                }
                ShopSkuQueryDTO shopSkuQueryDTO = new ShopSkuQueryDTO();
                shopSkuQueryDTO.setShopId(importSalesInStoreDTO.getShopId());
                shopSkuQueryDTO.setSkuCode(importSalesInStoreDTO.getSkuCode());
                shopSkuQueryDTO.setSellerId(loginUser.getMemberId());
                skuQueryDTOList.add(shopSkuQueryDTO);
            }
            // 校验商品
            BaseDTO baseDto = new BaseDTO();
            baseDto.setUser(loginUser);
            Result<List<SkuOutDTO>> dealImportGoodsData = this.dealImportGoodsData(importContent, baseDto);
            if (!dealImportGoodsData.isSuccess() || dealImportGoodsData.getData() == null) {
                throw new BusinessException(ApiResultEnum.BATCH_QUERY_ITEM_INFO_ERROR.getCode(), ApiResultEnum.BATCH_QUERY_ITEM_INFO_ERROR.getMsg());
            }
            // 组装仓库信息+采购部门参数
            List<String> skuCodes = this.buildWareInfo(skuQueryDTOList,loginUser.getLoginId(),importContent);
            List<IntentionAddressRecordAddDTO> addressRecordAddDTOS = new ArrayList<>();
            for (ImportSalesInStoreDTO importSalesInStoreDTO : importContent) {
                if (!CollectionUtils.isEmpty(skuCodes) && skuCodes.contains(importSalesInStoreDTO.getSkuCode())) {
                    IntentionAddressRecordAddDTO intentionAddressRecordAddDTO = new IntentionAddressRecordAddDTO();
                    BeanUtil.copy(importSalesInStoreDTO, intentionAddressRecordAddDTO);
                    intentionAddressRecordAddDTO.setSellerCode(loginUser.getLoginId());
                    intentionAddressRecordAddDTO.setCreateId(loginUser.getMemberId());
                    intentionAddressRecordAddDTO.setCreateName(loginUser.getUserName());
                    intentionAddressRecordAddDTO.setModifyId(loginUser.getMemberId());
                    intentionAddressRecordAddDTO.setModifyName(loginUser.getUserName());
                    intentionAddressRecordAddDTO.setShopId(String.valueOf(importSalesInStoreDTO.getShopId()));
                    addressRecordAddDTOS.add(intentionAddressRecordAddDTO);
                }
            }
            log.info("批量新增到店销售 入参：{}",addressRecordAddDTOS);
            Result<Boolean> result = goodsFeignService.batchAdd(addressRecordAddDTOS);
            log.info("批量新增到店销售 出参：{}",result);
            if (result.isSuccess()) {
                goodsService.updateConsigneeInfo(addressRecordAddDTOS);
            }
            return result;
        }catch (BusinessException bx){
            log.error("导入销售入库失败：",bx);
            return CommonResultUtil.error(bx.getCode(),bx.getMessage());
        }catch (Exception e){
            log.error("导入销售入库异常：",e);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    /**
     * 校验收货人信息
     *
     * @param importSalesInStoreDTO
     * @param rows
     * @param consigneeInfoMap
     */
    private void checkConsigneeInfo(int rows, Map<String, String> consigneeInfoMap, ImportSalesInStoreDTO importSalesInStoreDTO,StringBuilder sb) {
        if (StringUtils.isBlank(importSalesInStoreDTO.getConsigneeName()) && StringUtils.isBlank(importSalesInStoreDTO.getConsigneeMobile())) {
            return;
        }
        // 收货信息校验
        String consigneeInfoKey = importSalesInStoreDTO.getAppleId();
        StringBuilder consigneeInfoValue = new StringBuilder();
        if(StringUtils.isNotBlank(importSalesInStoreDTO.getConsigneeName())) {
            consigneeInfoValue.append(importSalesInStoreDTO.getConsigneeName().trim());
        }
        if (StringUtils.isNotBlank(importSalesInStoreDTO.getConsigneeMobile())) {
            consigneeInfoValue.append(importSalesInStoreDTO.getConsigneeMobile().trim());
        }
        if (consigneeInfoMap.containsKey(consigneeInfoKey)) {
            if (!consigneeInfoValue.toString().equals(consigneeInfoMap.get(consigneeInfoKey))) {
                if (!ObjectUtils.isEmpty(sb)) {
                    sb.append("第").append(rows).append("收货人姓名和收货人电话在同一个商家和appleId下存在数据不一致");
                } else {
                    throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rows + "行" + ":appleId存在不同的收货人和联系电话");
                }
            }
        } else {
            consigneeInfoMap.put(consigneeInfoKey,consigneeInfoValue.toString());
        }
    }

    /**
     * 批量导入分销商地址对应关系
     *
     * @param file
     * @param loginUser
     * @return
     */
    @Override
    public Result<String> batchImportIntentionAddressWarehouseRecord(MultipartFile file, LoginUserDetail loginUser) {
        try {
            // 文件非空校验
            this.checkExcel(file);
            //XSS拦截
            new XssFilter().importFilter(file, null, ImportServiceAddressDTO.class.getCanonicalName());
            // 获取导入内容并校验模板
            List<ImportServiceAddressDTO> importContent = this.getServiceAddressImportContent(file);
            // 校验服务商和会员店编码
            List<BatchAddServiceAddressDTO> batchAddServiceAddressDTOS = new ArrayList<>();
            for (ImportServiceAddressDTO commonExcelDTO : importContent) {
                BatchAddServiceAddressDTO batchAddServiceAddressDTO = new BatchAddServiceAddressDTO();
                BeanUtil.copy(commonExcelDTO,batchAddServiceAddressDTO);
                // 商家编码
                batchAddServiceAddressDTO.setSellerCode(loginUser.getLoginId());
                batchAddServiceAddressDTO.setCreateId(loginUser.getMemberId());
                batchAddServiceAddressDTO.setCreateName(loginUser.getUserName());
                batchAddServiceAddressDTO.setModifyId(loginUser.getMemberId());
                batchAddServiceAddressDTO.setModifyName(loginUser.getUserName());
                batchAddServiceAddressDTOS.add(batchAddServiceAddressDTO);
            }
            log.info("批量新增分销商地址对应关系 入参：{}",batchAddServiceAddressDTOS);
            Result<List<BatchAddServiceAddressResp>> result = goodsFeignService.batchAddServiceAddress(batchAddServiceAddressDTOS);
            log.info("批量新增分销商地址对应关系 出参：{}",result);
            if (!result.isSuccess() || result.getData() == null) {
                throw new BusinessException(result.getCode(), result.getMsg());
            }
            // sheet中要填充得数据
            List<ExportBatchAddServiceAddressResp> exportDataList = new ArrayList<>();
            boolean haveFailed = false;
            for (BatchAddServiceAddressResp dto : result.getData()) {
                if (StringUtils.isNotBlank(dto.getFailedReason())) {
                    haveFailed = true;
                }
                ExportBatchAddServiceAddressResp exportDTO = new ExportBatchAddServiceAddressResp();
                BeanUtil.copy(dto,exportDTO);
                exportDataList.add(exportDTO);
            }
            if (!haveFailed) {
                return CommonResultUtil.success("导入成功");
            }
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName("批量导入分销商地址对应关系结果");
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", ExportBatchAddServiceAddressResp.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data", exportDataList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] byteArray = bos.toByteArray();
            Result<String> urlResult = saveGeneration(new ByteArrayInputStream(byteArray));
            urlResult.setSuccess(false);
            return urlResult;
        }catch (BusinessException bx){
            log.error("批量导入分销商地址对应关系失败：",bx);
            return CommonResultUtil.error(bx.getCode(),bx.getMessage());
        }catch (Exception e){
            log.error("批量导入分销商地址对应关系异常：",e);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    /**
     * 批量更新服务商和客户对应关系
     *
     * @param file
     * @param loginUser
     * @return
     */
    @Override
    public Result<String> batchImportUpdateServiceShip(MultipartFile file, LoginUserDetail loginUser) {
        try {
            // 文件非空校验
            this.checkExcel(file);
            //XSS拦截
            new XssFilter().importFilter(file, null, ImportUpdateServiceShipDTO.class.getCanonicalName());

            Result<List<BatchUpdateServiceShipResp>> result = this.updateServiceShip(file, loginUser);
            List<ExportBatchUpdateServiceShipResp> exportDataList = new ArrayList<>();
            boolean haveFailed = false;
            for (BatchUpdateServiceShipResp dto : result.getData()) {
                if (StringUtils.isNotBlank(dto.getFailedReason())) {
                    haveFailed = true;
                }
                ExportBatchUpdateServiceShipResp exportDTO = new ExportBatchUpdateServiceShipResp();
                BeanUtil.copy(dto,exportDTO);
                exportDataList.add(exportDTO);
            }
            if (!haveFailed) {
                return CommonResultUtil.success("导入成功");
            }
            String name = "批量更新服务商和客户对应关系结果";
            List<Map<String, Object>> sheetsList = CommonUtil.getExportDataMap(exportDataList, name, ExportBatchUpdateServiceShipResp.class);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] byteArray = bos.toByteArray();
            Result<String> urlResult = this.saveGeneration(new ByteArrayInputStream(byteArray));
            urlResult.setSuccess(false);
            return urlResult;
        }catch (BusinessException bx){
            log.error("批量更新服务商和客户对应关系失败:",bx);
            return CommonResultUtil.error(bx.getCode(),bx.getMessage());
        }catch (Exception e){
            log.error("批量更新服务商和客户对应关系异常:",e);
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),ResultEnum.ERROR.getMsg());
        }
    }

    private Result<List<BatchUpdateServiceShipResp>> updateServiceShip(MultipartFile file, LoginUserDetail loginUser) {
        List<ImportUpdateServiceShipDTO> importUpdateServiceShipDTOS = this.getUpdateServiceShipDTOS(file);

        List<BatchUpdateServiceShipDTO> batchUpdateServiceShipDTOS = new ArrayList<>();
        for (ImportUpdateServiceShipDTO importUpdateServiceShipDTO : importUpdateServiceShipDTOS) {
            BatchUpdateServiceShipDTO batchUpdateServiceShipDTO = new BatchUpdateServiceShipDTO();
            BeanUtils.copyProperties(importUpdateServiceShipDTO,batchUpdateServiceShipDTO);
            batchUpdateServiceShipDTO.setCreateId(loginUser.getMemberId());
            batchUpdateServiceShipDTO.setCreateName(loginUser.getUserName());
            batchUpdateServiceShipDTO.setModifyId(loginUser.getMemberId());
            batchUpdateServiceShipDTO.setModifyName(loginUser.getUserName());
            batchUpdateServiceShipDTOS.add(batchUpdateServiceShipDTO);
        }
        log.info("批量更新服务商和客户对应关系 入参：{}",batchUpdateServiceShipDTOS);
        Result<List<BatchUpdateServiceShipResp>> result = goodsFeignService.batchUpdateServiceShip(batchUpdateServiceShipDTOS);
        log.info("批量更新服务商和客户对应关系 出参：{}",result);
        if (!result.isSuccess() || result.getData() == null) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        return result;
    }

    /**
     * 获取更新数据
     *
     * @param file
     * @return
     */
    private List<ImportUpdateServiceShipDTO> getUpdateServiceShipDTOS(MultipartFile file) {
        // 获取excel内容
        List<ImportUpdateServiceShipDTO> importUpdateServiceShipDTOS = CommonUtil.importExcel(file, ImportUpdateServiceShipDTO.class, 0, 1);
        if (CollectionUtils.isEmpty(importUpdateServiceShipDTOS)) {
            throw new BusinessException(ResultEnum.ERROR.getCode(), "导入的模板错误或内容为空");
        }
        StringBuilder sb = new StringBuilder();
        //excel行数
       int rows = 1;

       for (ImportUpdateServiceShipDTO dto : importUpdateServiceShipDTOS) {
           //判断空行
           this.checkUpdateServiceShipNotNull(dto, sb, rows);
           rows++;
       }
        if (rows > 1000) {
            throw new BusinessException(ResultEnum.FAILURE.getCode(), "导入数据不能超过1000条");
        }
        if (!ObjectUtils.isEmpty(sb)) {
            throw new BusinessException(ResultEnum.FAILURE.getCode(), sb.toString());
        }
       return importUpdateServiceShipDTOS;
    }

    /**
     * 更新服务商和客户数据非空行校验
     *
     * @param dto
     * @param sb
     * @param rows
     */
    private void checkUpdateServiceShipNotNull(ImportUpdateServiceShipDTO dto, StringBuilder sb, int rows) {
        if (ObjectUtils.isEmpty(dto.getServiceProviderCode())) {
            sb.append("第").append(rows).append("行:服务商编码(htd)为空");
        } else {
            dto.setServiceProviderCode(dto.getServiceProviderCode().trim());
        }
        if (ObjectUtils.isEmpty(dto.getBuyerCode())) {
            sb.append("第").append(rows).append("行:代收客户编码(htd)为空");
        } else {
            dto.setBuyerCode(dto.getBuyerCode().trim());
        }
        if (ObjectUtils.isEmpty(dto.getAppleId())) {
            sb.append("第").append(rows).append("行:代收客户Apple ID为空");
        } else {
            dto.setAppleId(dto.getAppleId().trim());
        }
    }

    private List<ImportServiceAddressDTO> getServiceAddressImportContent(MultipartFile file) {
        // 获取excel内容
        List<ImportServiceAddressDTO> serviceAddressList = this.getServiceAddressExcelInfo(file);
        if (CollectionUtils.isEmpty(serviceAddressList)) {
            throw new BusinessException(ResultEnum.ERROR.getCode(), "导入的模板错误或内容为空");
        }
        StringBuilder sb = new StringBuilder();
        //excel行数
        int rows = 1;
        for (ImportServiceAddressDTO dto : serviceAddressList) {
            //判断空行
            this.checkServiceAddressNotNull(dto, sb, rows);
            rows++;
        }
        if (rows > 1000){
            throw new BusinessException(ResultEnum.FAILURE.getCode(),"导入数据不能超过1000条");
        }
        if (!ObjectUtils.isEmpty(sb)){
            throw new BusinessException(ResultEnum.FAILURE.getCode(),sb.toString());
        }
        return serviceAddressList;
    }

    private void checkServiceAddressNotNull(ImportServiceAddressDTO dto, StringBuilder sb, int rows) {
        if (ObjectUtils.isEmpty(dto.getServiceProviderCode())) {
            sb.append("第").append(rows).append("行:服务商编码(htd)为空");
        } else {
            dto.setServiceProviderCode(dto.getServiceProviderCode().trim());
        }
        if (ObjectUtils.isEmpty(dto.getBuyerCode())) {
            sb.append("第").append(rows).append("行:代收客户编码(htd)为空");
        } else {
            dto.setBuyerCode(dto.getBuyerCode().trim());
        }
        if (ObjectUtils.isEmpty(dto.getAppleId())) {
            sb.append("第").append(rows).append("行:代收客户Apple ID为空");
        } else {
            dto.setAppleId(dto.getAppleId().trim());
        }
        // AppleId必须为纯数字
        if (!StringUtils.isNumeric(dto.getAppleId())) {
            sb.append("第").append(rows).append("行:代收客户Apple ID必须为纯数字");
        }
        if (ObjectUtils.isEmpty(dto.getReceiverName())) {
            sb.append("第").append(rows).append("行:收货人姓名为空");
        } else {
            dto.setReceiverName(dto.getReceiverName().trim());
        }
        if (ObjectUtils.isEmpty(dto.getReceiverPhone())) {
            sb.append("第").append(rows).append("行:收货人电话为空");
        } else {
            dto.setReceiverPhone(dto.getReceiverPhone().trim());
        }
        if (ObjectUtils.isEmpty(dto.getProvinceName())) {
            sb.append("第").append(rows).append("行:省名称为空");
        } else {
            dto.setProvinceName(dto.getProvinceName().trim());
        }
        if (ObjectUtils.isEmpty(dto.getCityName())) {
            sb.append("第").append(rows).append("行:市名称为空");
        } else {
            dto.setCityName(dto.getCityName().trim());
        }
        if (ObjectUtils.isEmpty(dto.getAreaName())) {
            sb.append("第").append(rows).append("行:区名称为空");
        } else {
            dto.setAreaName(dto.getAreaName().trim());
        }
        if (ObjectUtils.isEmpty(dto.getTownName())) {
            sb.append("第").append(rows).append("行:镇名称为空");
        } else {
            dto.setTownName(dto.getTownName().trim());
        }
        if (ObjectUtils.isEmpty(dto.getAddressDetail())) {
            sb.append("第").append(rows).append("行:详细地址为空");
        } else {
            dto.setAddressDetail(dto.getAddressDetail().trim());
        }
    }

    private List<ImportServiceAddressDTO> getServiceAddressExcelInfo(MultipartFile file) {
        List<ImportServiceAddressDTO> importDTOList;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setHeadRows(1);
            importDTOList = ExcelImportUtil.importExcel(inputStream, ImportServiceAddressDTO.class, params);
            if (!CollectionUtils.isEmpty(importDTOList)) {
                return importDTOList;
            }
        } catch (Exception e) {
            log.error("解析商品数据异常 error:", e);
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("解析商品数据异常 error:", e);
            }
        }
        return null;
    }

    /**
     *
     */
    private void checkAddressAndGoods(ImportSalesInStoreDTO importSalesInStoreDTO,int rowIndex,String sellerCode){
        // 查询收获地址
        Result<Long> longResult = goodsFeignService.queryConsigneeIdByBuyerAndServiceProvider(importSalesInStoreDTO.getServiceProviderCode(), importSalesInStoreDTO.getAppleId(),sellerCode);
        if (!longResult.isSuccess() || longResult.getData() == null) {
            throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rowIndex + "行服务商和appleId没有绑定关系");
        }
        // 查询地址
        log.info("查询用户收货地址信息：{}",longResult.getData());
        Result<RuConsigAddressDTO> addressDTOResult = userService.addressInfo(longResult.getData());
        log.info("查询用户收货地址信息：{}",addressDTOResult.toString());
        if (!addressDTOResult.isSuccess() || addressDTOResult.getData() == null) {
            log.error("查询用户收货地址信息异常：{}", addressDTOResult.toString());
            throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rowIndex + "行收货地址输入错误");
        }
        List<String> skuCodeList = new ArrayList<>();
        skuCodeList.add(importSalesInStoreDTO.getSkuCode());
        SalesAreaSkuQueryDTO salesAreaSkuQueryDTO = new SalesAreaSkuQueryDTO();
        salesAreaSkuQueryDTO.setSkuCodes(skuCodeList);
        salesAreaSkuQueryDTO.setCountyCode(addressDTOResult.getData().getConsigneeAddressDistrict());
        //地区需要传递
        log.info("根据sku编码列表查询商品列表 request:{}", salesAreaSkuQueryDTO.toString());
        Result<List<ItemVO>> itemListBySkuCodes = goodsFeignService.getItemListIgnoreSalesAreaBySkuCodes(salesAreaSkuQueryDTO);
        log.info("根据sku编码列表查询商品列表返回:{}", itemListBySkuCodes.toString());
        if (!itemListBySkuCodes.isSuccess()) {
            throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rowIndex + "行，该地区未查询到SKU");
        }
        boolean flag = false;
        for (ItemVO item : itemListBySkuCodes.getData()) {
            sign:
            for (String skuCodeTemp : skuCodeList) {
                List<ItemSkuDTO> itemSkuDTOList = item.getItemSkuDTOList();
                if (item.getIsInSalesArea()) {
                    for (ItemSkuDTO itemSkuDTO : itemSkuDTOList) {
                        if (skuCodeTemp.equals(itemSkuDTO.getItemSkuCode())) {
                            flag = true;
                            break sign;
                        }
                    }
                }
            }
        }
        if (!flag){
            throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rowIndex + "行，SKU不在销售范围内");
        }
    }

    /**
     * 校验会员信息
     * @param memberCode
     * @param rowIndex
     * @param type
     */
    private MemberBaseInfoDTO checkMemberCodeInfo(String memberCode,int rowIndex,String type) {
        log.info("查询会员基本信息入参:{}",memberCode);
        Result<MemberBaseInfoDTO> result = userService.memberBaseInfo(memberCode);
        log.info("查询会员基本信息出参:{}",result);
        if (!result.isSuccess() || ObjectUtils.isEmpty(result.getData()) || StringUtils.isEmpty(result.getData().getCompanyName())) {
            String msg = Constant.MEMBER_TYPE.equals(type) ? "会员店编码" : "服务商编码";
            throw new BusinessException(ResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + msg + "输入错误");
        }
        return result.getData();
    }

    /**
     * 处理导入商品数据
     */
    private Result<List<SkuOutDTO>> dealImportGoodsData(List<ImportSalesInStoreDTO> importCommodityDTOList, BaseDTO baseDto) {
        log.info("处理导入商品总条数:{},baseDto:{}", importCommodityDTOList.size(), baseDto);
        Long totalCount = (long) importCommodityDTOList.size();
        //校验店铺信息,成功则返回shopId
        Long shopId = this.checkShopGetId(importCommodityDTOList, baseDto, totalCount);
        // 获取sku信息
        List<SkuOutDTO> skuDetailList = this.getSkuOutList(importCommodityDTOList, baseDto, shopId);
        // 小数点校验
        this.checkDecimal(skuDetailList,importCommodityDTOList);
        // 处理导入信息
        return CommonResultUtil.success(skuDetailList);
    }


    private void checkDecimal(List<SkuOutDTO> skuDetailList,List<ImportSalesInStoreDTO> importCommodityDTOList) {
        List<ItemDecimalCheckDTO> itemDecimalCheckList = new ArrayList<>();
        for (SkuOutDTO skuOutDTO : skuDetailList) {
            for (ImportSalesInStoreDTO importSalesInStoreDTO : importCommodityDTOList) {
                if (skuOutDTO.getSkuCode().equals(importSalesInStoreDTO.getSkuCode())){
                    ItemDecimalCheckDTO itemDecimalCheckDTO = new ItemDecimalCheckDTO();
                    itemDecimalCheckDTO.setItemId(skuOutDTO.getItemId());
                    itemDecimalCheckDTO.setGoodsCount(importSalesInStoreDTO.getGoodsCount());
                    itemDecimalCheckList.add(itemDecimalCheckDTO);
                }
            }
        }
        log.info("校验商品小数规则 入参:{}", itemDecimalCheckList);
        Result<List<ItemDecimalCheckDTO>> result = goodsFeignService.checkIsItemDecimal(itemDecimalCheckList);
        log.info("校验商品小数规则 出参:{}", result);
        if (!result.isSuccess()) {
            log.info("校验商品小数规则 失败:{}", result);
            throw new BusinessException(result.getCode(), result.getMsg());
        }
    }
    /**
     * 查询sku集合，校验商品销售数量和库存
     */
    private List<SkuOutDTO> getSkuOutList(List<ImportSalesInStoreDTO> importCommodityDTOList, BaseDTO baseDto, Long shopId) {
        //sku编码集合
        List<String> skuList = new ArrayList<>();
        importCommodityDTOList.forEach(goods -> skuList.add(goods.getSkuCode()));
        //获取商品信息列表
        List<SkuOutDTO> skuDetailList = this.getSkuDetailList(baseDto.getUser().getMemberId(), shopId, skuList);
        if (!CollectionUtils.isEmpty(skuDetailList)) {
            this.checkBuyNum(importCommodityDTOList,skuDetailList);
        }
        return skuDetailList;
    }

    /**
     * 校验商品销售数量和库存
     */
    private void checkBuyNum(List<ImportSalesInStoreDTO> importCommodityDTOList, List<SkuOutDTO> skuDetailList) {
        StringBuilder sb = new StringBuilder();
        //excel行数
        if (importCommodityDTOList.size() > skuDetailList.size()){
            List<String> hasSkuCodes = skuDetailList.stream().map(SkuOutDTO::getSkuCode).collect(Collectors.toList());
            List<ImportSalesInStoreDTO> noHasSkuCode = importCommodityDTOList.stream().filter(o -> !hasSkuCodes.contains(o.getSkuCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(noHasSkuCode)){
                int rows = 1;
                for (ImportSalesInStoreDTO commodityDTO : noHasSkuCode) {
                    rows++;
                    sb.append("第").append(rows).append("行商品skuCode不存在或已下架:").append(commodityDTO.getSkuCode()).append("!");
                }
            }
        }
        if (StringUtils.isNotBlank(sb.toString())) {
            throw new BusinessException(ResultEnum.ERROR.getCode(), sb.toString());
        }
    }

    /**
     * 获取Sku详情列表
     */
    private List<SkuOutDTO> getSkuDetailList(Long sellerId, Long shopId, List<String> skuList) {
        SkuQueryDTO skuQueryDTO = new SkuQueryDTO();
        skuQueryDTO.setSellerId(sellerId);
        //去重以减少查询时间
        List<String> reqSkuList = skuList.stream().distinct().collect(Collectors.toList());
        skuQueryDTO.setSkuCodes(reqSkuList);
        skuQueryDTO.setShopId(shopId);
        skuQueryDTO.setStatus(1);
        log.info("batchImportCommodity.getSkuDetail req:{}", skuQueryDTO);
        Result<DataGrid<SkuOutDTO>> result = goodsFeignService.pageListUpShelfSku(skuQueryDTO, 0, 0);
        log.info("batchImportCommodity.getSkuDetail resp:{}", result.toString());
        if (!result.isSuccess() || null == result.getData() || CollectionUtils.isEmpty(result.getData().getRows())) {
            throw new BusinessException(OrderResultCodeEnum.SKU_CODE_IS_NULL_ERROR.getCode(),
                    OrderResultCodeEnum.SKU_CODE_IS_NULL_ERROR.getMsg());
        }
        return result.getData().getRows();
    }

    /**
     * 校验店铺获取店铺id
     */
    private Long checkShopGetId(List<ImportSalesInStoreDTO> importCommodityDTOList, BaseDTO baseDto, Long totalCount) {
        Set<Long> shopSet = new HashSet<>();
        importCommodityDTOList.forEach(goods -> shopSet.add(goods.getShopId()));
        //校验店铺信息,成功则返回shopId
        return this.checkShopInfo(shopSet, baseDto, totalCount);
    }

    /**
     * 校验店铺信息
     */
    private Long checkShopInfo(Set<Long> shopSet, BaseDTO baseDto, Long totalCount) {
        //判断店铺是否全部为空
        if (shopSet.iterator().hasNext() && ObjectUtils.isEmpty(shopSet.iterator().next())) {
            throw new BusinessException(OrderResultCodeEnum.SHOP_ID_IS_NULL_ERROR.getCode(),
                    "共导入数据：" + totalCount + "条，" + OrderResultCodeEnum.SHOP_ID_IS_NULL_ERROR.getMsg());
        }
        //判断店铺是否相同
        if (shopSet.size() > 1) {
            log.info("所选商品必须为同一店铺 error:{}", shopSet);
            throw new BusinessException(OrderResultCodeEnum.MUST_SAME_SHOP_ERROR.getCode(),
                    "共导入数据：" + totalCount + "条，" + OrderResultCodeEnum.MUST_SAME_SHOP_ERROR.getMsg());
        }
        Long shopId = shopSet.iterator().next();
        ShopInfoDTO shopInfoDTO = new ShopInfoDTO();
        shopInfoDTO.setSellerId(baseDto.getUser().getMemberId());
        shopInfoDTO.setShopId(shopId);
        shopInfoDTO.setStatus("2");
        shopInfoDTO.setRunStatus("1");
        shopInfoDTO.setDataTag(2);
        log.info("checkShopInfo.queryShopInfoByCondition req:{}", shopInfoDTO);
        long costTimeCreateBegin = System.currentTimeMillis();
        log.info("queryShopInfoByCondition startTime:{} 毫秒", costTimeCreateBegin);
        Result<List<ShopInfoDTO>> listResult = goodsFeignService.queryShopInfoByCondition(shopInfoDTO);
        Long costTimeCreate = System.currentTimeMillis() - costTimeCreateBegin;
        log.info("queryShopInfoByCondition endTime:{} 毫秒", costTimeCreate);
        log.info("checkShopInfo.queryShopInfoByCondition resp:{}", listResult.toString());
        if (!listResult.isSuccess() || null == listResult.getData()) {
            throw new BusinessException(OrderResultCodeEnum.SHOP_NAME_NOT_EXIST.getCode(),
                    OrderResultCodeEnum.SHOP_NAME_NOT_EXIST.getMsg());
        }
        return shopId;
    }

    /**
     * 生成报表上传
     * @param
     * @return
     */
    private Result<String> saveGeneration(InputStream excelStream) {
        try {
            OssUtils ossUtils = new OssUtils();
            String fileName = getfileName("xls");
            String downloadUrl = ossUtils.upload(excelStream, fileName, ossNacosConfig.getBucket(),ossNacosConfig.getEndpoint(),ossNacosConfig.getAccessKeyId(),ossNacosConfig.getAccessKeySecret());
            if (StringUtils.isEmpty(downloadUrl)) {
                return CommonResultUtil.error(ResultEnum.FAILURE.getCode(), "OSS上传失败");
            }
            return CommonResultUtil.success(downloadUrl);
        } catch (Exception e) {
            log.error("保存报表地址异常",e);
            return CommonResultUtil.error(ResultEnum.FAILURE.getCode(), "保存报表地址异常");
        }
    }
    /**
     * 获取文件名称
     * @return
     */
    private String getfileName(String suffix) {
        SimpleDateFormat datetime = new SimpleDateFormat("yyyyMMddHHmmss");
        //根据时间获取文件名
        return datetime.format(new Date()) + "." + suffix;
    }

    /**
     * 文件非空校验
     * @param file
     */
    private void checkExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.info("上传文件为空");
            throw new BusinessException(ResultEnum.FAILURE.getCode(), "上传文件为空");
        }
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isNotBlank(originalFilename)) {
            String substring = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            if (!StringUtils.isEmpty(substring)) {
                if (!("xls".equals(substring) || "xlsx".equals(substring))) {
                    throw new BusinessException(ResultEnum.FAILURE.getCode(), "仅支持Excel格式文件！");
                }
            }
        } else {
            throw new BusinessException(ResultEnum.FAILURE.getCode(), "上传文件名不能为空！");
        }
    }

    /**
     * 获取导入内容,校验
     */
    private List<ImportSalesInStoreDTO> getImportContent(MultipartFile file) {
        // 获取excel内容
        List<ImportSalesInStoreDTO> importCommodityDTOList = this.getExcelInfo(file);
        if (CollectionUtils.isEmpty(importCommodityDTOList)) {
            throw new BusinessException(ResultEnum.ERROR.getCode(), "导入的模板错误或内容为空");
        }
        StringBuilder sb = new StringBuilder();
        //excel行数
        int rows = 1;
        Set<Long> shopIdSet = new HashSet<>();
        for (ImportSalesInStoreDTO fileList : importCommodityDTOList) {
            rows++;
            //判断空行
            this.checkNotNull(fileList, sb, rows);
            // 判断参数
            this.checkRequiredParam(fileList, sb, rows);
            shopIdSet.add(fileList.getShopId());
        }
        if (--rows > nongZiNacosConfig.getImportOTCProviderInStoreMaxCount()){
            throw new BusinessException(ResultEnum.FAILURE.getCode(),"导入数据不能超过"+nongZiNacosConfig.getImportOTCProviderInStoreMaxCount()+"条");
        }
        boolean containsNull = shopIdSet.stream().anyMatch(obj -> obj == null);
        if (shopIdSet.size() > 1 && !containsNull){
            sb.append("店铺ID只能为同一店铺");
        }
        if (!ObjectUtils.isEmpty(sb)){
            throw new BusinessException(ResultEnum.FAILURE.getCode(),sb.toString());
        }
        return importCommodityDTOList;
    }

    /**
     * 必传字段校验
     * @param fileList
     * @param sb
     * @param rows
     */
    private void checkRequiredParam(ImportSalesInStoreDTO fileList,StringBuilder sb,int rows){
        if (ObjectUtils.isEmpty(fileList.getAppleId()) || !CommonUtil.isPriceNumber(fileList.getAppleId())) {
            sb.append("第").append(rows).append("行appleId只可填写数字!");
        }
        if(!StringUtils.isEmpty(fileList.getConsigneeMobile()) && !fileList.getConsigneeMobile().matches(Constant.PHONE_REGEX)) {
            sb.append("第").append(rows).append("行收货人手机号只可填11位数字!");
        }
        if (StringUtils.isNotBlank(fileList.getConsigneeName()) && fileList.getConsigneeName().trim().length() > 20) {
            sb.append("第").append(rows).append("收货人姓名输入超长!");
        }
        if (StringUtils.isBlank(fileList.getSkuCode()) || fileList.getSkuCode().trim().length() > 20) {
            sb.append("第").append(rows).append("行商品sku为空或输入超长!");
        }
        if (ObjectUtils.isEmpty(fileList.getShopId()) || fileList.getShopId().toString().trim().length() > 10) {
            sb.append("第").append(rows).append("行店铺Id为空或输入超长!");
        }
        if (ObjectUtils.isEmpty(fileList.getGoodsPrice()) || !CommonUtil.isPriceNumber(fileList.getGoodsPrice().toString()) || CommonUtil.getNumberDigit(fileList.getGoodsPrice().toString().trim()) > 10) {
            sb.append("第").append(rows).append("行价格必填，且只可填写数字，整数位最多十位，小数点后最多两位!");
        }
        if (ObjectUtils.isEmpty(fileList.getGoodsCount()) || !CommonUtil.isCountNumber(fileList.getGoodsCount().toString()) || CommonUtil.getNumberDigit(fileList.getGoodsCount().toString().trim()) > 10) {
            sb.append("第").append(rows).append("行数量必填，只可填写大于0的数字，整数位最多十位，如需填写小数需对商品进行配置，小数点后最多四位!");
        }

        // 供应商ID、仓库ID、采购部门编码必填且长度不超过30
        String supplierId = fileList.getSupplierCode();
        String warehouseId = fileList.getWarehouseCode();
        String departmentCode = fileList.getDepartmentCode();
        // 904版本改为非必填
        if (StringUtils.isNotEmpty(supplierId)) {
            if (supplierId.trim().length() > 30
                    || !LocalStringUtil.isAlphabetOrNumeric(supplierId.replace("_", ""))) {
                sb.append("第").append(rows).append("行供应商Id输入超长或存在非数字、字母!");
            }
        }
        if (StringUtils.isNotEmpty(warehouseId)) {
            if (warehouseId.trim().length() > 30) {
                sb.append("第").append(rows).append("行仓库Id输入超长!");
            }
        }
        if (StringUtils.isNotEmpty(departmentCode)) {
            if (departmentCode.trim().length() > 30
                    || !LocalStringUtil.isAlphabetOrNumeric(departmentCode.replace("_", ""))) {
                sb.append("第").append(rows).append("行采购部门编码输入超长或存在非数字、字母!");
            }
        }
    }

    /**
     * 判断空行
     * @param fileList
     */
    private void checkNotNull(ImportSalesInStoreDTO fileList,StringBuilder sb,int rows){
        if (StringUtils.isEmpty(fileList.getAppleId())){
            sb.append("第").append(rows).append("行:appleId编码为空");
        } else {
            fileList.setAppleId(fileList.getAppleId().trim());
        }
        if (ObjectUtils.isEmpty(fileList.getServiceProviderCode())){
            sb.append("第").append(rows).append("行:服务商编码为空");
        } else {
            fileList.setServiceProviderCode(fileList.getServiceProviderCode().trim());
        }
        if (ObjectUtils.isEmpty(fileList.getShopId())){
            sb.append("第").append(rows).append("行:店铺ID为空");
        }
        if (ObjectUtils.isEmpty(fileList.getSkuCode())){
            sb.append("第").append(rows).append("行:SKU编码为空");
        } else {
            fileList.setSkuCode(fileList.getSkuCode().trim());
        }
        if (StringUtils.isNotBlank(fileList.getSupplierCode())){
            fileList.setSupplierCode(fileList.getSupplierCode().trim());
        }
        if (StringUtils.isNotBlank(fileList.getDepartmentName())){
            fileList.setDepartmentName(fileList.getDepartmentName().trim());
        }
        if (StringUtils.isNotBlank(fileList.getWarehouseCode())){
            fileList.setWarehouseCode(fileList.getWarehouseCode().trim());
        }
        if (ObjectUtils.isEmpty(fileList.getGoodsCount())){
            sb.append("第").append(rows).append("行:数量为空");
        }
        if (ObjectUtils.isEmpty(fileList.getGoodsPrice())){
            sb.append("第").append(rows).append("行:单价（元）为空");
        }
        if (ObjectUtils.isEmpty(fileList.getTotalPrice())){
            sb.append("第").append(rows).append("行:合计金额为空");
        }
    }
    /**
     * 获取excel内容
     */
    private List<ImportSalesInStoreDTO> getExcelInfo(MultipartFile file) {
        List<ImportSalesInStoreDTO> importCommodityDTOList;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setHeadRows(1);
            importCommodityDTOList = ExcelImportUtil.importExcel(inputStream, ImportSalesInStoreDTO.class, params);
            if (!CollectionUtils.isEmpty(importCommodityDTOList)) {
                return importCommodityDTOList;
            }
        } catch (Exception e) {
            log.error("解析商品数据异常 error:", e);
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("解析商品数据异常 error:", e);
            }
        }
        return null;
    }

    /**
     * 校验采购部门
     * @param skuQueryDTOList
     * @return
     */
    private List<String> buildWareInfo(List<ShopSkuQueryDTO> skuQueryDTOList,String ownerCode,List<ImportSalesInStoreDTO> importContent) {
        Result<List<SkuOutDTO>> result = new Result();
        if (!ObjectUtils.isEmpty(skuQueryDTOList)) {
            List<ShopSkuQueryDTO> list = skuQueryDTOList.stream().distinct().collect(Collectors.toList());
            log.info("批量查询店铺商品信息入参,req={}", list);
            result = goodsFeignService.batchQuerySkuInfo(list);
            log.info("批量查询店铺商品信息出参,result={}", result);
            if (!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
                log.error("批量查询店铺商品信息失败,req={},result={}", list, result);
                throw new BusinessException(OrderResultCodeEnum.BATCH_QUERY_SKU_SHOP_ERROR.getCode(), OrderResultCodeEnum.BATCH_QUERY_SKU_SHOP_ERROR.getMsg());
            }
        }
        List<SkuOutDTO> skuOutDTOList = result.getData();
        // 仓库塞值
        skuOutDTOList = this.setSupplierAndWarehouse(skuOutDTOList,importContent);
        int count = 1;
        for (SkuOutDTO skuOutDTO : skuOutDTOList) {
            String cargoCode = skuOutDTO.getCargoCode();
            count++;
            // 缓存key
            String key =  RedisConstants.CHECK_WARE_INFO +
                    ownerCode + skuOutDTO.getCargoCode() + skuOutDTO.getSupplierCode() + skuOutDTO.getWarehouseCode()+skuOutDTO.getDepartmentName();
            // 先判断缓存是否已经存在，不存在则放入缓存
            List<PurchasingDepartmentDTO> purchasingDepartmentDTOList = (List<PurchasingDepartmentDTO>) redisServiceImpl.get(key);
            if (CollectionUtils.isEmpty(purchasingDepartmentDTOList)) {
                purchasingDepartmentDTOList = this.queryPurchasingDepartment(ownerCode, cargoCode);
                if (CollectionUtils.isEmpty(purchasingDepartmentDTOList)){
                    throw new BusinessException(ResultEnum.FAILURE.getCode(), "第" + count + "行未查询到仓库信息");
                }
                redisServiceImpl.set(key, purchasingDepartmentDTOList, RedisConstants.DAY_TIME);
            }

            boolean flag = true;
            for (PurchasingDepartmentDTO purchasingDepartmentDTO : purchasingDepartmentDTOList) {
                if (!StringUtils.isEmpty(skuOutDTO.getDepartmentName())){
                    if (skuOutDTO.getDepartmentName().equals(purchasingDepartmentDTO.getDepartmentName())
                            &&skuOutDTO.getSupplierCode().equals(purchasingDepartmentDTO.getSupplierCode())
                            && skuOutDTO.getWarehouseCode().equals(purchasingDepartmentDTO.getWareHouseCode())){
                        this.setSkuOutDTO(skuOutDTO, purchasingDepartmentDTO);
                        flag = false;
                        break;
                    }
                }
            }
            if (flag){
                throw new BusinessException(ResultEnum.FAILURE.getCode(), "第" + count + "行未查询到仓库信息");
            }
        }
        // 获取所有skuCode
        List<String> skuList = skuOutDTOList.stream().map(SkuOutDTO::getSkuCode).collect(Collectors.toList());

        return skuList;
    }

    private List<SkuOutDTO> setSupplierAndWarehouse(List<SkuOutDTO> skuOutList, List<ImportSalesInStoreDTO> fileList) {

        List<SkuOutDTO> list = Lists.newArrayList();

        for (ImportSalesInStoreDTO excelDTO : fileList) {
            if (Objects.isNull(skuOutList)) {
                return list;
            }

            Optional<SkuOutDTO> first = skuOutList.stream().filter(e -> Objects.equals(e.getSkuCode(), excelDTO.getSkuCode())
                    && Objects.equals(e.getShopId(), excelDTO.getShopId())
            ).findFirst();

            if (first.isPresent()) {
                SkuOutDTO skuOutDTO = first.get();
                SkuOutDTO tempSkuOutDTO = new SkuOutDTO();
                BeanUtil.copy(skuOutDTO, tempSkuOutDTO);
                tempSkuOutDTO.setDepartmentName(excelDTO.getDepartmentName());
                tempSkuOutDTO.setSupplierCode(excelDTO.getSupplierCode());
                tempSkuOutDTO.setWarehouseCode(excelDTO.getWarehouseCode());
                list.add(tempSkuOutDTO);
            }
        }

        return list;
    }

    private void setSkuOutDTO(SkuOutDTO skuOutDTO, PurchasingDepartmentDTO purchasingDepartmentDTO) {
        skuOutDTO.setWarehouseCode(purchasingDepartmentDTO.getWareHouseCode());
        skuOutDTO.setWarehouseName(purchasingDepartmentDTO.getWareHouseName());
        skuOutDTO.setSupplierCode(purchasingDepartmentDTO.getSupplierCode());
        skuOutDTO.setSupplierName(purchasingDepartmentDTO.getSupplierName());
        skuOutDTO.setExactInventory(new BigDecimal(purchasingDepartmentDTO.getStoreNum()));
        skuOutDTO.setDepartmentCode(purchasingDepartmentDTO.getDepartmentCode());
        skuOutDTO.setDepartmentName(purchasingDepartmentDTO.getDepartmentName());
    }

    /**
     * 查询采购部门
     * @param supplierCode
     * @param productCode
     * @return
     */
    private List<PurchasingDepartmentDTO> queryPurchasingDepartment(String supplierCode, String productCode) {
        log.info("查询采购部门入参：{},{}", supplierCode, productCode);
        Result<List<PurchasingDepartmentDTO>> listResult = goodsFeignService.queryPurchaseDepartment(supplierCode, productCode);
        log.info("查询采购部门出参：{}", listResult.toString());
        if (listResult.isSuccess() && null != listResult.getData()) {
            return listResult.getData();
        }
        return null;
    }
}
