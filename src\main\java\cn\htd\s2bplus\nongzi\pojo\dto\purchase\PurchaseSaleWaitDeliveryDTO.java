package cn.htd.s2bplus.nongzi.pojo.dto.purchase;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class PurchaseSaleWaitDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "请求链路ID 用于问题日志排查",hidden = true)
    private Long requestId = System.currentTimeMillis();

    /**
     * 委托函单号
     */
    @ApiModelProperty(value = "委托单号")
    private String entrustedOrderNo;

    /**
     * 送货方式要求
     */
    @ApiModelProperty(value = "送货方式要求")
    private String deliveryType;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "待发货商品状态 1:申请发货 2:申请取消发货")
    private String skuStatus;

    /**
     * 采购单号
     */
    @NotNull
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;

    /**
     * 采购单唯一编号,ERP采购单号+平台公司代码(4位)
     */
    @NotNull
    @ApiModelProperty(value = "采购单唯一编号,ERP采购单号+平台公司代码(4位)")
    private String purchaseOrderUniqueNo;


    @ApiModelProperty(value = "待发货商品状态 1:申请发货 3:申请取消发货")
    private String status;

    /**
     * 当前页数
     */
    @ApiModelProperty(value = "当前页数")
    private Integer page;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数")
    private Integer rows;

    private Date modifyStartTime;


    private Date modifyEndTime;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "要货单号/提货单号")
    private String tenderNo;

    @ApiModelProperty(value = "订单类型-囤货or直发")
    private String orderType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
