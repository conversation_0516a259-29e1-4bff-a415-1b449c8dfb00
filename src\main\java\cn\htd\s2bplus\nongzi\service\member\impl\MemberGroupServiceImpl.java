package cn.htd.s2bplus.nongzi.service.member.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.enums.BusinessTypeEnum;
import cn.htd.s2bplus.nongzi.enums.GroupTypeEnum;
import cn.htd.s2bplus.nongzi.enums.PurchaserEnum;
import cn.htd.s2bplus.nongzi.enums.ResultEnum;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.feign.user.UserService;
import cn.htd.s2bplus.nongzi.mapper.ReportHistoryMapper;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.SellerPrivateDomainMemberExcelDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.MemberCompanyInfoDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.QueryMemberGroupRelationDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.SellerMemberGroupDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.SaleOrderDetailDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.*;
import cn.htd.s2bplus.nongzi.pojo.vo.MemberGroupRelationVO;
import cn.htd.s2bplus.nongzi.service.member.MemberGroupService;
import cn.htd.s2bplus.nongzi.service.member.MemberService;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.DozerUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import cn.htd.s2bplus.nongzi.utils.XssFilter;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class MemberGroupServiceImpl implements MemberGroupService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;


    @Autowired
    private UserService userService;

    @Autowired
    private ReportHistoryMapper reportHistoryMapper;

    @Autowired
    @Lazy
    private MemberService memberService;

    @Autowired
    private MiddleGroundAPI middleGroundAPI;


    @Async
    @Override
    public void saveGroupReportHistory(String groupId,Integer current, Integer pageSize, PageResult<List<MemberGroupResponseDTO>> listPageResult ,LoginUserDetail loginUser,HttpServletResponse response) {
        List<MemberGroupResponseDTO> memberGroupResponseDTOList = listPageResult.getData();
        if (listPageResult.getPage().getTotal() > pageSize){
            List<String> groupList = new ArrayList<>();
            groupList.add(groupId);
            //调用过一次，所以从2页开始
            current++;
            //计算还需要调用几次接口 拼接所有分组内容
            double ceil = Math.ceil((float) listPageResult.getPage().getTotal() / pageSize);
            while (current<=ceil){
                MemberGroupRequest memberGroupRequestAsync = new MemberGroupRequest();
                memberGroupRequestAsync.setGroupList(groupList);
                memberGroupRequestAsync.setCurrent(current);
                memberGroupRequestAsync.setSize(pageSize);
                logger.info("异步查询分组会员信息 入参:{}", memberGroupRequestAsync);
                PageResult<List<MemberGroupResponseDTO>> listPageResult1 = userService.selectMemberGroupListInfo(memberGroupRequestAsync);
                logger.info("异步查询分组会员信息 出参:{}", listPageResult1.isSuccess());
                if (!listPageResult1.isSuccess()) {
                    throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_MEMBER_GROUP_MEMBER_ERROR.getMsg());
                }
                if (ObjectUtils.isEmpty(listPageResult1) || ObjectUtils.isEmpty(listPageResult1.getData())){
                    throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_MEMBER_GROUP_MEMBER_NULL.getMsg());
                }
                memberGroupResponseDTOList.addAll(listPageResult1.getData());
                current++;
            }
        }
        //会员分组内容导出
        String downloadUrl = this.getMemberGroupMemberDownloadUrl(memberGroupResponseDTOList, response, groupId + "_会员分组内容");
        if (StringUtils.isEmpty(downloadUrl)) {
            logger.error("获取上传文件地址出错:{}", downloadUrl);
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.UPLOAD_OSS_ERROR.getMsg());
        }
        //保存报表生成下载历史
        this.saveReportHistory(downloadUrl,loginUser);
    }

    @Override
    public Result<String> importSellerPrivateDomainMember(MultipartFile file,LoginUserDetail userDetail) {
        try {
            //文件非空校验
            if (file == null || file.isEmpty()) {
                logger.info("上传文件为空");
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(),"上传文件为空!");
            }
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(".") + 1,originalFilename.length());
            if (StringUtils.isEmpty(substring) && (!"xls".equals(substring) || !"xlsx".equals(substring))) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(),"仅支持Excel格式文件!");
            }
            //XSS拦截
            new XssFilter().importFilter(file,null, SellerPrivateDomainMemberExcelDTO.class.getCanonicalName());
            //获取excel内容
            List<SellerPrivateDomainMemberExcelDTO> fileListImport = this.getExcelInfo(file);
            if (CollectionUtils.isEmpty(fileListImport)) {
                throw new BusinessException(ResultEnum.ERROR.getCode(),"导入的模板错误或内容为空");
            }
            if (1000 < fileListImport.size()) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(),"一次最多导入1000条数据");
            }
            //去空格
            for (SellerPrivateDomainMemberExcelDTO dto : fileListImport) {
                dto.setSellerCode(StringUtils.isNotBlank(dto.getSellerCode()) ? dto.getSellerCode().trim() : dto.getSellerCode());
                dto.setMemberCode(StringUtils.isNotBlank(dto.getMemberCode()) ? dto.getMemberCode().trim() : dto.getMemberCode());
            }
            //错误信息集合
            StringBuilder errorMsgBuilder = new StringBuilder();
            this.checkDataNull(fileListImport,errorMsgBuilder);
            if (StringUtils.isNotBlank(errorMsgBuilder)) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(),errorMsgBuilder.toString());
            }
            //数据去重
            List<SellerPrivateDomainMemberExcelDTO> list = fileListImport.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(SellerPrivateDomainMemberExcelDTO::getSellerCode)
                            .thenComparing(SellerPrivateDomainMemberExcelDTO::getMemberCode))),ArrayList::new));
            logger.info("原始数据记录数:{},去重后数据记录数:{}",fileListImport.size(),list.size());
            if (list.size() < fileListImport.size()) {
                throw new BusinessException(ResultEnum.ERROR.getCode(),"存在相同数据，请检查导入文件");
            }
            //批量查询商家/会员信息
            Map<String,MemberCompanyInfoDTO> memberMap = this.getSellerPrivateDomainMemberMap(fileListImport);
            //批量查询商家会员私域会员关系
            List<QueryMemberGroupRelationDTO> memberGroupDTOList = this.getMemberGroupDTOList(fileListImport, memberMap);
            logger.info("批量查询查询会员分组信息,rep:{}",memberGroupDTOList);
            Result<List<MemberGroupRelationVO>> memberGroupResult = userService.queryMemberGroupInfoList(this.getMemberGroupDTOList(fileListImport,memberMap));
            logger.info("批量查询查询会员分组信息,resp:{}",memberGroupResult);
            if (!memberGroupResult.isSuccess()) {
                throw new BusinessException(ResultEnum.ERROR.getCode(),"批量查询商家会员私域会员关系异常");
            }
            //导入商家私域会员校验
            this.checkSellerPrivateDomainMember(fileListImport,errorMsgBuilder,memberMap,memberGroupResult.getData());
            if (StringUtils.isNotBlank(errorMsgBuilder)) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(),errorMsgBuilder.toString());
            }
            memberService.handleImportSellerPrivateDomainMember(fileListImport,memberMap,memberGroupResult.getData(),userDetail);
            return CommonResultUtil.success(String.valueOf(ResultEnum.SUCCESS.getCode()));
        } catch (BusinessException b) {
            logger.info("导入商家私域会员失败:",b);
            return CommonResultUtil.error(b.getCode(),b.getMessage());
        } catch (Exception e) {
            logger.error("导入商家私域会员异常,error:",e);
            return CommonResultUtil.error(ResultEnum.SYSTEM_ERROR.getCode(),ResultEnum.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 保存报表生成下载历史
     * @param downloadUrl
     */
    private void saveReportHistory(String downloadUrl,LoginUserDetail loginUser) {
        ReportHistory reportHistoryDTO = new ReportHistory();
        reportHistoryDTO.setBusinessType((BusinessTypeEnum.MEMBER_GROUP_CONTENT.getCode().byteValue()));
        reportHistoryDTO.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
        reportHistoryDTO.setDownloadUrl(downloadUrl);
        reportHistoryDTO.setFinishTime(new Date());
        reportHistoryDTO.setBeginTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
        reportHistoryDTO.setEndTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
        reportHistoryDTO.setCreateId(loginUser.getUserId());
        reportHistoryDTO.setCreateName(loginUser.getUserName());
        reportHistoryDTO.setCreateTime(new Date());
        reportHistoryDTO.setModifyId(loginUser.getUserId());
        reportHistoryDTO.setModifyName(loginUser.getUserName());
        reportHistoryDTO.setModifyTime(new Date());

        // 保存报表生成下载历史
        logger.info("保存报表生成下载历史 入参:{}",reportHistoryDTO);
        boolean insertSelectiveBoolean = reportHistoryMapper.insertSelective(reportHistoryDTO);
        logger.info("保存报表生成下载历史 出参:{}",insertSelectiveBoolean);
        if (!insertSelectiveBoolean){
            logger.info("保存报表生成下载历史出错");
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.REPORT_HISTORY_ERROR.getMsg());
        }
    }


    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     */
    private String  getMemberGroupMemberDownloadUrl(List<MemberGroupResponseDTO> saleOrderDetailDTOList, HttpServletResponse response, String sheetNameStart){

        String downloadUrl = "";
        try{
            String time = DateUtil.getCurrentDateFull();
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", MemberGroupResponseDTO.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(saleOrderDetailDTOList) ?
                    new ArrayList<SaleOrderDetailDTO>() : saleOrderDetailDTOList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType(CommonConstants.CONTENT_TYPE_MS_EXCEL); // 改成输出excel文件
            String fileName = sheetNameStart +CommonConstants.UNDERLINE+ time;
            response.setHeader(CommonConstants.CONTENT_DISPOSITION,
                    CommonConstants.EXCEL_ATTACHMENT + fileName + CommonConstants.EXCEL_XLS);// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            OssUtils ossUtils = new OssUtils();
            String ossFileName = fileName+CommonConstants.EXCEL_XLS;
            downloadUrl = ossUtils.upload(excelStream,ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        }catch (Exception e){
            logger.error("生成会员分组内容excel异常:",e);
        }
        return downloadUrl;
    }

    /**
     * 获取excel内容
     * @param file 导入文件
     * @return 导入数据
     */
    private List<SellerPrivateDomainMemberExcelDTO> getExcelInfo(MultipartFile file) {
        List<SellerPrivateDomainMemberExcelDTO> importCommodityDTOList;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setHeadRows(1);
            importCommodityDTOList = ExcelImportUtil.importExcel(inputStream,SellerPrivateDomainMemberExcelDTO.class,params);
            if (!CollectionUtils.isEmpty(importCommodityDTOList)) {
                return importCommodityDTOList;
            }
        } catch (Exception e) {
            logger.error("解析导入商家私域会员据异常,error:",e);
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (IOException e) {
                logger.error("解析导入商家私域会员据异常,error:",e);
            }
        }
        return null;
    }

    /**
     * 批量查询商家/会员信息
     * @param fileListImport 导入数据（去重后）
     * @return 商家/会员信息集合
     */
    private Map<String,MemberCompanyInfoDTO> getSellerPrivateDomainMemberMap(List<SellerPrivateDomainMemberExcelDTO> fileListImport) {
        List<SellerMemberGroupDTO> sellerMemberGroupList = DozerUtil.convertList(fileListImport,SellerMemberGroupDTO.class);
        List<MemberBaseInfoDTO> memberBaseInfoList = this.getMemberBaseInfoList(sellerMemberGroupList);
        List<MemberCompanyInfoDTO> memberCompanyList = this.batchQuerySellerOrMemberInfo(memberBaseInfoList);
        return memberCompanyList.stream().collect(Collectors.toMap(i -> i.getMemberCode() + "-" + i.getBuyerSellerType(),Function.identity(),(var1,var2) -> var2));
    }

    /**
     * 批量查询商家/会员信息
     * @param memberBaseInfoList 商家/会员信息查询入参
     * @return 商家/会员信息集合
     */
    private List<MemberCompanyInfoDTO> batchQuerySellerOrMemberInfo(List<MemberBaseInfoDTO> memberBaseInfoList) {
        logger.info("批量查询商家/会员信息,rep:{}",memberBaseInfoList);
        Result<List<MemberCompanyInfoDTO>> memberCompanyResult = middleGroundAPI.batchQueryMemCompanyInfo(memberBaseInfoList);
        logger.info("批量查询商家/会员信息,resp:{}",memberCompanyResult);
        if (!memberCompanyResult.isSuccess() || CollectionUtils.isEmpty(memberCompanyResult.getData())) {
            throw new BusinessException(ResultEnum.ERROR.getCode(),"批量查询商家/会员信息异常");
        }
        return memberCompanyResult.getData();
    }

    /**
     * 组装批量查询商家会员私域会员关系入参
     * @param fileListImport 导入参数（去重后）
     * @param memberMap 商家/会员信息集合
     * @return 批量查询商家会员私域会员关系入参
     */
    private List<QueryMemberGroupRelationDTO> getMemberGroupDTOList(List<SellerPrivateDomainMemberExcelDTO> fileListImport,Map<String,MemberCompanyInfoDTO> memberMap) {
        List<QueryMemberGroupRelationDTO> memberGroupDTOList = new ArrayList<>();
        fileListImport.forEach(i -> {
            MemberCompanyInfoDTO seller = memberMap.get(i.getSellerCode() + "-" + PurchaserEnum.SET_BUYER_SELLER_TYPE_SELLER.getCode());
            if (!org.springframework.util.ObjectUtils.isEmpty(seller)) {
                QueryMemberGroupRelationDTO memberGroupDTO = new QueryMemberGroupRelationDTO();
                memberGroupDTO.setSellerId(String.valueOf(seller.getMemberId()));
                memberGroupDTO.setGroupType(GroupTypeEnum.PRIVATE_MEMBER_GROUP.getCode());
                memberGroupDTOList.add(memberGroupDTO);
            }
        });
        return memberGroupDTOList;
    }

    /**
     * 组装批量查询商家入参
     * @param fileListImport 导入数据（去重后）
     * @return 批量查询商家入参
     */
    private List<MemberBaseInfoDTO> getMemberBaseInfoList(List<SellerMemberGroupDTO> fileListImport) {
        List<MemberBaseInfoDTO> memberBaseInfoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fileListImport)) {
            return memberBaseInfoList;
        }
        for (SellerMemberGroupDTO sellerMemberGroupDTO : fileListImport) {
            MemberBaseInfoDTO seller = new MemberBaseInfoDTO();
            if (!org.springframework.util.ObjectUtils.isEmpty(sellerMemberGroupDTO.getSellerId())) {
                seller.setId(Long.valueOf(sellerMemberGroupDTO.getSellerId()));
            }
            if (!StringUtils.isBlank(sellerMemberGroupDTO.getSellerCode())) {
                seller.setMemberCode(sellerMemberGroupDTO.getSellerCode());
            }
            seller.setBuyerSellerType(PurchaserEnum.SET_BUYER_SELLER_TYPE_SELLER.getCode());
            memberBaseInfoList.add(seller);
            MemberBaseInfoDTO member = new MemberBaseInfoDTO();
            if (!org.springframework.util.ObjectUtils.isEmpty(sellerMemberGroupDTO.getMemberId())) {
                member.setId(Long.valueOf(sellerMemberGroupDTO.getMemberId()));
            }
            if (!StringUtils.isBlank(sellerMemberGroupDTO.getMemberCode())) {
                member.setMemberCode(sellerMemberGroupDTO.getMemberCode());
            }
            member.setBuyerSellerType(PurchaserEnum.SET_BUYER_SELLER_TYPE_BUYER.getCode());
            memberBaseInfoList.add(member);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(fileListImport.get(0).getMemberId())) {
            memberBaseInfoList = memberBaseInfoList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(MemberBaseInfoDTO::getId)
                            .thenComparing(MemberBaseInfoDTO::getBuyerSellerType))),ArrayList::new));
        } else {
            memberBaseInfoList = memberBaseInfoList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(MemberBaseInfoDTO::getMemberCode)
                            .thenComparing(MemberBaseInfoDTO::getBuyerSellerType))),ArrayList::new));
        }
        return memberBaseInfoList;
    }

    /**
     * 空数据校验
     * @param list 导入数据
     * @param errorMsgBuilder 错误信息
     */
    private void checkDataNull(List<SellerPrivateDomainMemberExcelDTO> list, StringBuilder errorMsgBuilder) {
        for (int i = 0; i < list.size(); i++) {
            int index = i + 2;
            SellerPrivateDomainMemberExcelDTO sellerPrivateDomainMemberExcelDTO = list.get(i);
            //必填字段非空校验
            if (StringUtils.isBlank(sellerPrivateDomainMemberExcelDTO.getSellerCode())) {
                this.appendError(errorMsgBuilder,"第" + index + "行，商家编码为空");
            }
            if (StringUtils.isBlank(sellerPrivateDomainMemberExcelDTO.getMemberCode())) {
                this.appendError(errorMsgBuilder,"第" + index + "行，会员编码为空");
            }
        }
    }

    /**
     * 导入商家私域会员校验
     * @param fileListImport 导入数据（去重后）
     * @param errorMsgBuilder 错误信息集合
     */
    private void checkSellerPrivateDomainMember(List<SellerPrivateDomainMemberExcelDTO> fileListImport,
                                                StringBuilder errorMsgBuilder,
                                                Map<String,MemberCompanyInfoDTO> memberMap,
                                                List<MemberGroupRelationVO> memberGroupList) {
        Map<String,MemberGroupRelationVO> memberGroupMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(memberGroupList)) {
            memberGroupMap = memberGroupList.stream().collect(Collectors.toMap(i -> i.getSellerId() + "-" + i.getBuyerId(),Function.identity(),(var1,var2) -> var2));
        }
        for (int i = 0; i < fileListImport.size(); i++) {
            int index = i + 2;
            SellerPrivateDomainMemberExcelDTO sellerPrivateDomainMemberExcelDTO = fileListImport.get(i);
            //1.是否存在校验
            MemberCompanyInfoDTO seller = memberMap.get(sellerPrivateDomainMemberExcelDTO.getSellerCode() + "-" + PurchaserEnum.SET_BUYER_SELLER_TYPE_SELLER.getCode());
            if (ObjectUtils.isEmpty(seller)) {
                this.appendError(errorMsgBuilder,"第" + index + "行，商家编码不存在");
            }
            MemberCompanyInfoDTO member = memberMap.get(sellerPrivateDomainMemberExcelDTO.getMemberCode() + "-" + PurchaserEnum.SET_BUYER_SELLER_TYPE_BUYER.getCode());
            if (ObjectUtils.isEmpty(member)) {
                this.appendError(errorMsgBuilder,"第" + index + "行，会员编码不存在");
            }
            //2.校验分组关系是否已经存在
            if (!ObjectUtils.isEmpty(seller) && !ObjectUtils.isEmpty(member)
                    && !ObjectUtils.isEmpty(memberGroupMap.get(seller.getMemberId() + "-" + member.getMemberId()))) {
                this.appendError(errorMsgBuilder,"第" + index + "行，" + seller.getMemberCode() + "和" + member.getMemberCode() + "，绑定关系已存在");
            }
        }
    }

    /**
     * 错误信息追加
     * @param errorMsgBuilder 错误信息
     * @param error 追加错误信息
     */
    private void appendError(StringBuilder errorMsgBuilder, String error) {
        if (errorMsgBuilder.length() > 0) {
            errorMsgBuilder.append(";");
        }
        errorMsgBuilder.append(error);
    }
}

