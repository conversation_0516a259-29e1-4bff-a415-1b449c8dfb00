package cn.htd.s2bplus.nongzi.enums;


/**
 * 导入文件接口的业务类型
 */
public enum ImportBusinessTypeEnum {

    PUBLISH_IMPORT_GOODS_TYPE("1","货盘商品发布"),
    BATCH_IMPORT_PUBLISH_GOODS("2","批量导入商品发布"),
    ;
    private String type;
    private String desc;

    ImportBusinessTypeEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
