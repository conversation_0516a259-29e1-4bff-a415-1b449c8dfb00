package cn.htd.s2bplus.nongzi.pojo.dto.common;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class MerchantOperationLogDTO implements Serializable {
    private static final long serialVersionUID = 4749100100159499090L;

    @ApiModelProperty(value = "卖家编码")
    private String sellerCode;

    @ApiModelProperty(value = "日志业务类型")
    private Integer businessType;

    @ApiModelProperty(value = "操作类型")
    private Integer operatorType;

    @ApiModelProperty(value = "业务关键字")
    private String businessKey;

    @ApiModelProperty(value = "操作记录")
    private String operationRecord;

    @ApiModelProperty(value = "操作人编码")
    private String operatorCode;

    @ApiModelProperty(value = "操作人名称")
    private String operatorName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
