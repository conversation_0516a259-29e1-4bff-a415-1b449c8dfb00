package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class UpShelfDTO implements Serializable{

	private static final long serialVersionUID = -3793348805286660998L;

    /**
     * 创建人ID
     */
    @NotNull(message = "operateId不能为null")
    @ApiModelProperty(value = "操作人id")
    private Long operateId;

    /**
     * 创建人名称
     */
    @NotNull(message = "operateName不能为空")
    @ApiModelProperty(value = "操作人名称")
    private String operateName;

    @ApiModelProperty(value = "上下架状态")
    @NotNull(message = "isVisable上下架状态不能为空")
    private Integer isVisable;

    @ApiModelProperty(value = "上下架店铺标识 0否 1是")
    private String isMark;

    @ApiModelProperty(value = "卖家编码")
    private String sellerCode;

    @ApiModelProperty(value = "商家类型 1内部2外部3分销商")
    private String sellerType;

    private List<ItemsUpShelfDTO> itemsUpShelfList;
}
