package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.rdc.base.development.framework.core.mp.support.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WarehouseGoodsApiDTO extends Query implements Serializable {


	@ApiModelProperty(name = "品牌方编码")
	private String sellerCode;

	@ApiModelProperty(name = "sku编码")
	private String skuCode;

	@ApiModelProperty(name = "商品库存编码")
	private String stockCode;

	@ApiModelProperty(name = "仓库编码")
	private String warehouseCode;

	@ApiModelProperty(name = "商品库存编码")
	private List<String> stockCodeList;

}
