package cn.htd.s2bplus.nongzi.enums;

/**
 * 发货方式枚举类
 */
public enum DeliveryTypeEnum {
    DISTRIBUTE("1","供应商配送"),
    SELF_PICK_UP("2","自提"),
    REAL_DISTRIBUTE("1","快递公司承运"),
    REAL_CAR_SEND("2","派车配送"),
    REAL_OTHER("3","其他"),
    REAL_ONLINE_CAR_APP("4","汇送达")
    ;
    private String type;
    private String name;

    DeliveryTypeEnum(String type, String name){
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return this.type;
    }

    public String getName() {
        return this.name;
    }
}
