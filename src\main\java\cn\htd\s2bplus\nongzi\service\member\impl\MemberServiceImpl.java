package cn.htd.s2bplus.nongzi.service.member.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.*;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.feign.user.UserService;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportMemberPurchaseDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.SellerPrivateDomainMemberExcelDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.*;
import cn.htd.s2bplus.nongzi.pojo.dto.user.*;
import cn.htd.s2bplus.nongzi.pojo.vo.ImportMemberVO;
import cn.htd.s2bplus.nongzi.pojo.vo.MemberGroupRelationVO;
import cn.htd.s2bplus.nongzi.service.file.FileUtil;
import cn.htd.s2bplus.nongzi.service.member.MemberGroupService;
import cn.htd.s2bplus.nongzi.service.member.MemberService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RefreshScope
@Slf4j
public class MemberServiceImpl implements MemberService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FileUtil fileUtil;

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;

    @Value("${export.memberGroupPageSize}")
    private String exportPageSize;

    @Autowired
    private UserService userService;

    @Autowired
    private MemberGroupService memberGroupService;

    @Autowired
    private GoodsFeignService goodsFeignService;

    @Override
    public Result<ImportMemberVO> importMemberList(MultipartFile file) {
        logger.info("解析会员-限购策略开始");
        Result<ImportMemberVO> result = new Result();
        List<ImportMemberPurchaseDTO> list = new ArrayList();
        List<String> errorCodeList = new ArrayList();
        List<String> codeList = new ArrayList();
        ImportMemberVO importMemberVO=new ImportMemberVO();
        OssUtils ossUtils = new OssUtils();
        String downloadUrl="";
        String time = DateUtil.getCurrentDateFull();
        int importMaxCount = 30;

        try {
            //校验文件类型
            if (ObjectUtils.isEmpty(file) || StringUtils.isEmpty(file.getOriginalFilename())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
            if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equals(substring) || StrConstant.XLSX.equals(substring))) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_FILE_ERROR.getMsg());
            }
            long start = System.currentTimeMillis();
            EasyExcel.read(file.getInputStream(), ImportMemberPurchaseDTO.class, new ReadListener<ImportMemberPurchaseDTO>() {
                public static final int BATCH_COUNT = 100;
                private final List<ImportMemberPurchaseDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

                @Override
                public void invoke(ImportMemberPurchaseDTO data, AnalysisContext context) {
                    int rowNumber = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
                    int rowIndex = context.readSheetHolder().getRowIndex() + 1;
                    if (rowNumber == 0) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
                    }
                    if (rowNumber > importMaxCount) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_MAX_ERROR.getMsg() + importMaxCount);
                    }
                    if (StringUtils.isEmpty(data.getMemberCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_ITEMCODE_NULL.getMsg());
                    }
                    if (codeList.contains(data.getMemberCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "编码:" + data.getMemberCode() + ApiResultEnum.IMPORT_MEMBER_REPEAT.getMsg());
                    }
                    codeList.add(data.getMemberCode());
                    cachedDataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (!CollectionUtils.isEmpty(cachedDataList)) {
                        list.addAll(cachedDataList);
                    }
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) {
                    logger.info("解析会员-限购策略失败 :{}", exception.getMessage());
                    if (exception instanceof BusinessException) {
                        throw new BusinessException(ResultEnum.ERROR.getCode(), exception.getMessage());
                    } else if (exception instanceof ExcelDataConvertException) {
                        ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                        String errorMsg = ApiResultEnum.IMPORT_ERROR_LINE_COLUMN.getMsg() + "解析异常";
                        Integer rowIndex = excelDataConvertException.getRowIndex() + 1;
                        Integer columnIndex = excelDataConvertException.getColumnIndex() + 1;
                        logger.info("解析会员-限购策略,第{}行，第{}列解析异常，数据为:{}", rowIndex, columnIndex, JSON.toJSONString(excelDataConvertException.getCellData()));
                        throw new BusinessException(ResultEnum.ERROR.getCode(), String.format(errorMsg, rowIndex, columnIndex));
                    }
                }
            }).sheet().doRead();
            logger.info("解析会员-限购策略出来的数据量：{}", list.size());
            logger.info("解析会员-限购策略excel时间占用 :{}ms", System.currentTimeMillis() - start);
            logger.info("会员-限购策略数据完毕数据:{}", list);
            if (list.size() == 0) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            logger.info("根据商品/sku编码、店铺id，批量查询商品/sku信息 入参:{}", codeList);
            Result<List<BatchMemberVO>> listResult = userService.batchMemberNameByCodeList(codeList);
            logger.info("根据商品/sku编码、店铺id，批量查询商品/sku信息 出参:{}", listResult);
            if (!listResult.isSuccess()) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.BATCH_QUERY_MEMBER_INFO_ERROR.getMsg());
            }
            if (ObjectUtils.isEmpty(listResult.getData())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), codeList.get(0) + ApiResultEnum.IMPORT_MEMBER_ERROR.getMsg());
            }
            errorCodeList = codeList.stream().filter(
                    code -> listResult.getData().stream().noneMatch(
                            each -> code.equals(each.getMemberCode()))).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(errorCodeList)) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), errorCodeList.get(0) + ApiResultEnum.IMPORT_MEMBER_ERROR.getMsg());
            }
            downloadUrl = ossUtils.upload(file.getInputStream(),"批量导入会员_限购策略"+time+CommonConstants.EXCEL_XLSX, bucket, endpoint, accessKeyId, accessKeySecret);
            if (StringUtils.isEmpty(downloadUrl)){
                throw new BusinessException(ApiResultEnum.ERROR.getCode(),ApiResultEnum.UPLOAD_OSS_ERROR.getMsg());
            }
            importMemberVO.setDownloadUrl(downloadUrl);
            importMemberVO.setBatchMemberVOList(listResult.getData());
            result.setData(importMemberVO);
            result.setCode(ApiResultEnum.SUCCESS.getCode());
            result.setMsg("限购批量导入会员成功");
        } catch (BusinessException e) {
            logger.info("限购批量导入会员失败:{}", e.getMessage());
            result.setCode(e.getCode());
            result.setMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("限购批量导入会员异常", e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("限购批量导入会员异常");
        }
        return result;
    }

    @Override
    public Result<String> exportMemberGroupMemberCodeList(String groupId, HttpServletResponse response) {
        Result<String> result = new Result<>();
        try {
            logger.info("会员分组详情  入参 {}", groupId);
            Result<MemberGroupDTO> memberGroupDetail = userService.selectCusBuyerGroupById(groupId);
            logger.info("会员分组详情  出参 {}", memberGroupDetail);
            if (!memberGroupDetail.isSuccess()) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_MEMBER_GROUP_ERROR.getMsg());
            }
            if (ObjectUtils.isEmpty(memberGroupDetail.getData())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_MEMBER_GROUP_NULL.getMsg());
            }
            //只支持指定会员类型导出
            if (!CommonConstants.MEMBER_GROUP_TYPE_ASSIGN.equals(memberGroupDetail.getData().getGroupType())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.SUPPORT_THE_SPECIFIED_TYPE_ERROR.getMsg());
            }
            //首次同步查询会员分组内容  异步补全会员分组内容
            this.saveMemberGroupList(groupId, response);
        } catch (BusinessException e) {
            logger.info("会员分组会员导出失败:{}", e.getMessage());
            result.setCode(e.getCode());
            result.setMsg(e.getMessage());
            return result;
        } catch (Exception e) {
            logger.error("会员分组会员导出异常", e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("会员分组会员导出异常");
            return result;
        }
        result.setData(ApiResultEnum.EXPORT_CURRENT_PLAYING_SONG.getMsg());
        result.setMsg(ApiResultEnum.EXPORT_CURRENT_PLAYING_SONG.getMsg());
        result.setCode(ApiResultEnum.SUCCESS.getCode());
        return result;
    }

    @Override
    @Async
    public void exportMemberInfo(LoginUserDetail user, HttpServletResponse response,MemberSearchDTO memberSearch) {

        logger.info("查询我的会员、非会员列表 入参：{},{},{}",memberSearch,user.getMemberId(),user.getSellerType());
        memberSearch.setSysFlag(SellerStatusEnum.SYS_FLAG01.getType());
        List<MyMemberVo> myMemberVoList = this.selectMemberList(memberSearch,user.getMemberId(),user.getSellerType());
        logger.info("查询我的会员、非会员列表 出参：{}",myMemberVoList.toString());
        if(CollectionUtils.isEmpty(myMemberVoList)){
            logger.info("未查询到数据");
            return;
        }
        memberSearch.setSysFlag(SellerStatusEnum.SYS_FLAG01.getType());
        if(CollectionUtils.isEmpty(myMemberVoList) || myMemberVoList.size() == 0){
           return;
        }
        SimpleDateFormat simpleDateFormatName = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
        List<MemberExPortDTO> memberExPortDTOS = new ArrayList<>();
        for (MyMemberVo infoDTO : myMemberVoList) {
            MemberExPortDTO memberExPortDTO = new MemberExPortDTO();
            memberExPortDTO.setArtificialPersonMobile(infoDTO.getArtificialPersonMobile());
            memberExPortDTO.setArtificialPersonName(infoDTO.getArtificialPersonName());
            memberExPortDTO.setCompanyName(infoDTO.getCompanyName());
            memberExPortDTO.setCustomerManagerName(infoDTO.getBelongManagerName());
            memberExPortDTO.setMemberCode(infoDTO.getMemberCode());
            memberExPortDTO.setReGistTime(infoDTO.getRegistTime());
            memberExPortDTOS.add(memberExPortDTO);
        }
        String date = simpleDateFormatName.format(new Date());
        String sheetNameStart = "会员/非会员" + "_" + date;
        this.exportReverseOrderHandle(memberExPortDTOS, response, sheetNameStart,user);
    }

    private int getTotalPage(int totalCount,int rows){
        int totalPage = 1;
        totalPage = (int) (totalCount % rows == 0 ? totalCount / rows : (totalCount / rows + 1));
        return totalPage;
    }

    @Override
    public List<MyMemberVo> selectMemberList(MemberSearchDTO memberSearch, Long sellerId, String sellerType) {
        String TYPE_ONE = "1";
        String TYPE_TWO = "2";
        List<MyMemberVo> allMemberList = new ArrayList<>();
        //循环查询数据,每页60条
        int page = 1;
        int rows = 60;
        int totalPage = 1;

        //查询会员列表
        if (memberSearch.getMemberType().equals(TYPE_TWO)) {
            memberSearch.setStart(page);
            memberSearch.setLength(rows);
            memberSearch.setStatus("1");
            //每页100条查询
            PageResult<List<MyMemberVo>> list = userService.selectMemberList(memberSearch, sellerId, sellerType);
            if(list == null || CollectionUtils.isEmpty(list.getData())){
                return allMemberList;
            }
            logger.info("非会员列表:{}", list);
            allMemberList.addAll(list.getData());
            totalPage = this.getTotalPage(list.getPage().getTotal().intValue(),rows);
            if(totalPage > 1 ){
                logger.info("会员页数超过1页：{}",totalPage);
                //分页条数大于1条
                for (int i = 2; i <= totalPage; i++ ){
                    logger.info("分页查询导出订单列表:{}",i);
                    memberSearch.setStart(page * i);
                    memberSearch.setLength(rows);
                    list = userService.selectMemberList(memberSearch, sellerId, sellerType);
                    logger.info("非会员列表:{}", list);
                    if(list != null && !CollectionUtils.isEmpty(list.getData())){
                        allMemberList.addAll(list.getData());
                    }
                }
            }

        }else if(memberSearch.getMemberType().equals(TYPE_ONE)) {
            memberSearch.setStart(page);
            memberSearch.setLength(rows);
            PageResult<List<MyNoMemberVo>> listPageResult = userService.selectNoMemberList(memberSearch, sellerId, sellerType);
            logger.info("非会员列表:{}", listPageResult);
            if (listPageResult.isSuccess() && listPageResult.getData() != null) {
                List<MyNoMemberVo> data = listPageResult.getData();
                for (MyNoMemberVo myNoMemberVo : data) {
                    MyMemberVo myMemberVo = new MyMemberVo();
                    BeanUtil.copy(myNoMemberVo,myMemberVo);
                    allMemberList.add(myMemberVo);
                }
                totalPage = this.getTotalPage(listPageResult.getPage().getTotal().intValue(),rows);
                if(totalPage > 1 ){
                    logger.info("非会员页数超过1页：{}",totalPage);
                    //分页条数大于1条
                    for (int i = 2; i <= totalPage; i++ ){
                        logger.info("分页查询导出列表:{}",i);
                        memberSearch.setStart(page * i);
                        memberSearch.setLength(rows);
                        listPageResult = userService.selectNoMemberList(memberSearch, sellerId, sellerType);
                        if(listPageResult != null && !CollectionUtils.isEmpty(listPageResult.getData())){
                            for (MyNoMemberVo myNoMemberVo : listPageResult.getData()) {
                                MyMemberVo myMemberVo = new MyMemberVo();
                                BeanUtil.copy(myNoMemberVo,myMemberVo);
                                allMemberList.add(myMemberVo);
                            }
                        }
                    }
                }
            }
            //logger.info("查询非会员列表出:{}", JSON.toJSONString(allMemberList));
        }
        return allMemberList;
    }

    @Override
    @Async
    public void handleImportSellerPrivateDomainMember(List<SellerPrivateDomainMemberExcelDTO> fileListImport,
                                                      Map<String,MemberCompanyInfoDTO> memberMap,
                                                      List<MemberGroupRelationVO> memberGroupRelationList,
                                                      LoginUserDetail userDetail) {
        try {
            //将导入数据,根据商家编码分组
            Map<String,List<SellerPrivateDomainMemberExcelDTO>> sellerGroupMap = fileListImport.stream().collect(Collectors.groupingBy(SellerPrivateDomainMemberExcelDTO::getSellerCode));
            List<Long> sellerIds = sellerGroupMap.keySet().stream().map(i -> {
                MemberCompanyInfoDTO memberCompanyInfoDTO = memberMap.get(i + "-" + PurchaserEnum.SET_BUYER_SELLER_TYPE_SELLER.getCode());
                if (org.springframework.util.ObjectUtils.isEmpty(memberCompanyInfoDTO)) {
                    return null;
                }
                return memberCompanyInfoDTO.getMemberId();
            }).filter(org.apache.commons.lang3.ObjectUtils::isNotEmpty).collect(Collectors.toList());
            Result<List<MemberGroupInfo>> listResult = userService.queryGroupBySellerIds(sellerIds);
            if (!listResult.isSuccess()) {
                throw new BusinessException(ResultEnum.ERROR.getCode(),"根据商家id查询分组信息异常");
            }
            Map<Long,MemberGroupInfo> sellerIdMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(listResult.getData())) {
                sellerIdMap = listResult.getData().stream().collect(Collectors.toMap(i -> Long.valueOf(i.getSellerId()),Function.identity(),(var1,var2) -> var2));
            }
            //将原始私域会员关系根据商家id分组
            Map<String,List<MemberGroupRelationVO>> sellerMemberGroupRelationMap = memberGroupRelationList.stream().collect(Collectors.groupingBy(MemberGroupRelationVO::getSellerId));
            Map<Long,MemberCompanyInfoDTO> memberIdMap = memberMap.values().stream().filter(i -> PurchaserEnum.SET_BUYER_SELLER_TYPE_BUYER.getCode().equals(String.valueOf(i.getBuyerSellerType())))
                    .collect(Collectors.toMap(MemberCompanyInfoDTO::getMemberId, Function.identity(),(var1,var2) -> var2));
            //需要创建的私域会员分组
            List<AddGroupDTO> addGroupList = new ArrayList<>();
            //需要更新的私域会员分组
            List<EditGroupDTO> editGroupList = new ArrayList<>();
            for (Map.Entry<String,List<SellerPrivateDomainMemberExcelDTO>> entry : sellerGroupMap.entrySet()) {
                MemberCompanyInfoDTO seller = memberMap.get(entry.getKey() + "-" + PurchaserEnum.SET_BUYER_SELLER_TYPE_SELLER.getCode());
                if (org.springframework.util.ObjectUtils.isEmpty(seller)) {
                    continue;
                }
                //原私域会员分组
                List<MemberGroupRelationVO> originalPrivateDomainMemberList = sellerMemberGroupRelationMap.get(String.valueOf(seller.getMemberId()));
                //新私域会员分组
                List<SellerPrivateDomainMemberExcelDTO> newPrivateDomainMemberList = entry.getValue();
                if (CollectionUtils.isEmpty(originalPrivateDomainMemberList) && (CollectionUtils.isEmpty(sellerIdMap)
                        || org.springframework.util.ObjectUtils.isEmpty(sellerIdMap.get(seller.getMemberId())))) {
                    //场景一：导入商家原先未创建分组，则创建商家私域会员分组
                    addGroupList.add(this.getAddGroupParam(newPrivateDomainMemberList,memberMap,userDetail));
                    continue;
                }
                List<Long> newMemberIds = newPrivateDomainMemberList.stream().map(i -> {
                    MemberCompanyInfoDTO member = memberMap.get(i.getMemberCode() + "-" + PurchaserEnum.SET_BUYER_SELLER_TYPE_BUYER.getCode());
                    if (org.springframework.util.ObjectUtils.isEmpty(member)) {
                        return null;
                    }
                    return member.getMemberId();
                }).filter(org.apache.commons.lang3.ObjectUtils::isNotEmpty).collect(Collectors.toList());
                List<Long> difference;
                if (CollectionUtils.isEmpty(originalPrivateDomainMemberList)) {
                    difference = new ArrayList<>(newMemberIds);
                } else {
                    difference = newMemberIds.stream().filter(item -> !originalPrivateDomainMemberList.stream().map(MemberGroupRelationVO::getBuyerId)
                            .map(Long::valueOf).collect(Collectors.toList()).contains(item)).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(difference)) {
                    //场景二：导入商家已存在分组，原始分组数据和新分组数据有差异，将差异数据更新到该商家私域会员分组下（原始分组数据和新分组数据相同，则不做任何处理）
                    MemberGroupInfo memberGroupInfo = sellerIdMap.get(seller.getMemberId());
                    editGroupList.add(this.getEditGroupParam(seller.getCompanyName(),memberGroupInfo,originalPrivateDomainMemberList,difference,memberIdMap,userDetail));
                }
            }
            if (!CollectionUtils.isEmpty(addGroupList)) {
                addGroupList.forEach(this::insertOssMemberGroupInfo);
            }
            if (!CollectionUtils.isEmpty(editGroupList)) {
                editGroupList.forEach(this::editOssMemberGroupInfo);
            }
        } catch (BusinessException b) {
            log.info("处理导入商家私域会员数据失败:",b);
        } catch (Exception e) {
            log.error("处理导入商家私域会员数据异常,error:",e);
        }
    }

    private void exportReverseOrderHandle(List<MemberExPortDTO> memberExPortDTOS, HttpServletResponse response, String sheetNameStart,
                                          LoginUserDetail user) {
        try {
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", MemberExPortDTO.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(memberExPortDTOS) ?
                    new ArrayList<MemberExPortDTO>() : memberExPortDTOS);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> maps = new ArrayList<>();
            maps.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(maps, ExcelType.HSSF);
            response.setContentType("applicationnd.ms-excel"); // 改成输出excel文件
            String fileName = java.net.URLEncoder.encode("用户列表", "UTF-8");
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            fileUtil.saveFileGeneration(user.getUserId() + "", user.getUserName(), ExportFileTypeEnum.EXPORT_MEMBER_LIST_INFORMATION.getCode(), new ByteArrayInputStream(barray));
        } catch (Exception e) {
            logger.error("memberServiceImpl exportReverseOrderHandle error:{}", e);
        }

    }

    /**
     * 首次同步查询会员分组内容  异步补全会员分组内容
     * @param groupId
     * @param response
     */
    private void saveMemberGroupList(String groupId, HttpServletResponse response) {
        MemberGroupRequest memberGroupRequest = new MemberGroupRequest();
        List<String> groupList = new ArrayList<>();
        LoginUserDetail loginUser = BaseContextHandler.getLoginUser();
        Integer current = 1;
        Integer pageSize = Integer.valueOf(exportPageSize);
        groupList.add(groupId);
        memberGroupRequest.setGroupList(groupList);
        memberGroupRequest.setCurrent(current);
        memberGroupRequest.setSize(pageSize);
        logger.info("查询分组会员信息 入参:{}", memberGroupRequest);
        PageResult<List<MemberGroupResponseDTO>> listPageResult = userService.selectMemberGroupListInfo(memberGroupRequest);
        logger.info("查询分组会员信息 出参:{}", listPageResult.isSuccess());
        if (!listPageResult.isSuccess()) {
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_MEMBER_GROUP_MEMBER_ERROR.getMsg());
        }
        if (ObjectUtils.isEmpty(listPageResult) || ObjectUtils.isEmpty(listPageResult.getData())){
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_MEMBER_GROUP_MEMBER_NULL.getMsg());
        }
        //查询会员分组内容，保存历史报表
        memberGroupService.saveGroupReportHistory(groupId,current ,pageSize ,listPageResult,loginUser, response);
    }

    /**
     * 获取新增分组入参
     * @param newPrivateDomainMemberList 导入参数（去重后）
     * @param memberCompanyMap 商家/会员信息集合
     * @param userDetail 用户信息
     * @return 新增分组入参
     */
    private AddGroupDTO getAddGroupParam(List<SellerPrivateDomainMemberExcelDTO> newPrivateDomainMemberList,Map<String,MemberCompanyInfoDTO> memberCompanyMap,LoginUserDetail userDetail) {
        AddGroupDTO addGroupDTO = new AddGroupDTO();
        List<String> buyerIds = new ArrayList<>();
        Map<Long,MemberBaseInfoDTO> buyerInfoMap = new HashMap<>();
        for (SellerPrivateDomainMemberExcelDTO sellerPrivateDomainMemberExcelDTO : newPrivateDomainMemberList) {
            MemberCompanyInfoDTO seller = memberCompanyMap.get(sellerPrivateDomainMemberExcelDTO.getSellerCode() + "-" + PurchaserEnum.SET_BUYER_SELLER_TYPE_SELLER.getCode());
            MemberCompanyInfoDTO member = memberCompanyMap.get(sellerPrivateDomainMemberExcelDTO.getMemberCode() + "-" + PurchaserEnum.SET_BUYER_SELLER_TYPE_BUYER.getCode());
            addGroupDTO.setSellerId(String.valueOf(seller.getMemberId()));
            addGroupDTO.setSellerCode(seller.getMemberCode());
            addGroupDTO.setSellerName(seller.getCompanyName());
            addGroupDTO.setComment("");
            addGroupDTO.setName(seller.getCompanyName() + "的私域会员分组");
            buyerIds.add(String.valueOf(member.getMemberId()));
            MemberBaseInfoDTO memberBaseInfoDTO = new MemberBaseInfoDTO();
            memberBaseInfoDTO.setId(member.getMemberId());
            memberBaseInfoDTO.setCompanyName(member.getCompanyName());
            buyerInfoMap.put(member.getMemberId(),memberBaseInfoDTO);
        }
        addGroupDTO.setGroupType(GroupTypeEnum.PRIVATE_MEMBER_GROUP.getCode());
        addGroupDTO.setBuyerIds(StringUtils.join(buyerIds,","));
        addGroupDTO.setBuyerInfoMap(buyerInfoMap);
        addGroupDTO.setOperateId(String.valueOf(userDetail.getMemberId()));
        addGroupDTO.setOperateName(userDetail.getUserName());
        return addGroupDTO;
    }

    /**
     * 组装更新会员分组参数
     * @param companyName 公司名称
     * @param memberGroupInfo 会员分组信息
     * @param originalPrivateDomainMemberList 原始分组数据
     * @param difference 差集
     * @param memberIdMap 会员id集合
     * @param userDetail 用户信息
     * @return 更新会员分组参数
     */
    private EditGroupDTO getEditGroupParam(String companyName,MemberGroupInfo memberGroupInfo,List<MemberGroupRelationVO> originalPrivateDomainMemberList,
                                           List<Long> difference,Map<Long,MemberCompanyInfoDTO> memberIdMap,LoginUserDetail userDetail) {
        EditGroupDTO editGroupDTO = new EditGroupDTO();
        editGroupDTO.setGroupId(memberGroupInfo.getGroupId());
        editGroupDTO.setGroupType(GroupTypeEnum.PRIVATE_MEMBER_GROUP.getCode());
        editGroupDTO.setComment("");
        List<String> buyerIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(originalPrivateDomainMemberList)) {
            buyerIds = originalPrivateDomainMemberList.stream().map(MemberGroupRelationVO::getBuyerId).collect(Collectors.toList());
        }
        buyerIds.addAll(difference.stream().map(String::valueOf).collect(Collectors.toList()));
        editGroupDTO.setBuyerIds(StringUtils.join(buyerIds,","));
        editGroupDTO.setName(companyName + "的私域会员分组");
        editGroupDTO.setComment("私域会员分组");
        editGroupDTO.setOperateId(String.valueOf(userDetail.getMemberId()));
        editGroupDTO.setOperateName(userDetail.getUserName());
        editGroupDTO.setModifyId(String.valueOf(userDetail.getMemberId()));
        editGroupDTO.setModifyName(userDetail.getUserName());
        editGroupDTO.setGroupIds(String.valueOf(memberGroupInfo.getGroupId()));
        editGroupDTO.setSellerId(memberGroupInfo.getSellerId());
        List<MemberCompanyInfoDTO> memberCompanyInfoList = buyerIds.stream().map(i -> memberIdMap.get(Long.valueOf(i)))
                .filter(org.apache.commons.lang3.ObjectUtils::isNotEmpty).collect(Collectors.toList());
        editGroupDTO.setBuyerInfoMap(this.handleBuyerInfoMap(memberCompanyInfoList));
        return editGroupDTO;
    }

    /**
     * 处理会员信息集合
     * @param memberCompanyInfoList 会员信息集合
     * @return 会员信息集合
     */
    private Map<Long,MemberBaseInfoDTO> handleBuyerInfoMap(List<MemberCompanyInfoDTO> memberCompanyInfoList) {
        Map<Long,MemberBaseInfoDTO> map = new HashMap<>();
        if (CollectionUtils.isEmpty(memberCompanyInfoList)) {
            return map;
        }
        for (MemberCompanyInfoDTO memberCompanyInfoDTO : memberCompanyInfoList) {
            MemberBaseInfoDTO memberBaseInfoDTO = new MemberBaseInfoDTO();
            memberBaseInfoDTO.setId(memberCompanyInfoDTO.getMemberId());
            memberBaseInfoDTO.setMemberCode(memberCompanyInfoDTO.getMemberCode());
            memberBaseInfoDTO.setCompanyName(memberCompanyInfoDTO.getCompanyName());
            map.put(memberCompanyInfoDTO.getMemberId(),memberBaseInfoDTO);
        }
        return map;
    }

    /**
     * 新增OSS私域会员分组
     * @param addGroupDTO 新增OSS私域会员分组入参
     * @return 新增结果
     */
    public Result<Boolean> insertOssMemberGroupInfo(AddGroupDTO addGroupDTO) {
        Integer groupType = addGroupDTO.getGroupType();
        if (GroupTypeEnum.BUSINESS_MEMBER_GROUP.getCode().equals(groupType)){
            Result<Boolean> result = estimateMemberWhite(addGroupDTO.getSellerCode());
            if (!result.isSuccess() || result.getData() == null ){
                return ResultUtil.error(ResultEnum.ERROR.getCode(),"查询当前用户是否在白名单失败");
            }
            //如果不是那就设置归属为当前用户
            Boolean data = result.getData();
            if (data){
                addGroupDTO.setGroupType(GroupTypeEnum.WHITE_LIST_MEMBER_GROUP.getCode());
            }
        }
        MemberGroupInfo build = MemberGroupInfo.builder().sellerId(addGroupDTO.getSellerId()).name(addGroupDTO.getName())
                .memberCode(addGroupDTO.getSellerCode()).build();
        build.setCurrent(1);
        build.setSize(100);
        logger.info("  新增会员分组校验 memberGroupList  查询是否有相同名字分组存在  rep {} ",build.toString());
        PageResult<List<MemberGroupInfo>> memberGroupList = userService.memberGroupList(build);
        logger.info("  新增会员分组校验 memberGroupList  查询是否有相同名字分组存在  result {} ",JSON.toJSONString(memberGroupList));
        if (memberGroupList.isSuccess() && CollectionUtils.isEmpty(memberGroupList.getData())){
            logger.info(" marker 新增会员分组  rep {}",addGroupDTO.toString());
            Result<Long> listPageResult = userService.insertMemberGroupInfo(addGroupDTO);
            logger.info(" marker 新增会员分组  result {}",JSON.toJSONString(listPageResult));
            if (listPageResult.isSuccess() && listPageResult.getData() != null){
                return CommonResultUtil.success(true,"新增会员分组成功");
            }
        }else {
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),"新增会员分组失败,或已经存在相同名字的会员分组");
        }
        return CommonResultUtil.error(ResultEnum.ERROR.getCode(),"新增会员分组失败");
    }

    /**
     * 更新OSS私域会员分组
     * @param editGroupDTO  更新OSS私域会员分组入参
     * @return 更新结果
     */
    private Result<Boolean> editOssMemberGroupInfo(EditGroupDTO editGroupDTO){
        Result<MemberGroupDTO> memberGroupDetail = userService.selectCusBuyerGroupById(editGroupDTO.getGroupIds());
        logger.info("editOssMemberGroupInfo,查询会员分组详情,resp:{}",memberGroupDetail);
        if (memberGroupDetail.isSuccess() && org.springframework.util.ObjectUtils.isEmpty(memberGroupDetail.getData())){
            throw new BusinessException(ResultEnum.ERROR.getCode(),"查询会员分组接口失败");
        }
        MemberGroupInfo build = MemberGroupInfo.builder().sellerId(editGroupDTO.getSellerId()).name(editGroupDTO.getName()).build();
        build.setCurrent(1);
        build.setSize(100);
        logger.info("editOssMemberGroupInfo,编辑会员分组校验,查询是否有相同名字分组存在,rep:{}",build);
        PageResult<List<MemberGroupInfo>> memberGroupList = userService.memberGroupList(build);
        logger.info("editOssMemberGroupInfo,编辑会员分组校验,查询是否有相同名字分组存在,resp:{}",memberGroupList);
        if (memberGroupList.isSuccess()){
            List<MemberGroupInfo> data = memberGroupList.getData();
            if (!CollectionUtils.isEmpty(data)){
                Boolean flag = false;
                for (MemberGroupInfo memberGroupInfo : data){
                    if (memberGroupInfo.getGroupId().longValue() == Long.valueOf(editGroupDTO.getGroupIds()).longValue()){
                        flag = true;
                        break;
                    }
                }
                if (!flag){
                    throw new BusinessException(ResultEnum.ERROR.getCode(),"修改会员分组失败,或已经存在相同名字的会员分组");
                }
            }
            Integer groupType = editGroupDTO.getGroupType();
            if (GroupTypeEnum.BUSINESS_MEMBER_GROUP.getCode().equals(groupType)){
                Result<Boolean> result = estimateMemberWhite(editGroupDTO.getSellerCode());
                if (!result.isSuccess() || result.getData() == null ){
                    throw new BusinessException(ResultEnum.ERROR.getCode(),"查询当前用户是否在白名单失败");
                }
                //如果不是那就设置归属为当前用户
                if (result.getData()){
                    editGroupDTO.setGroupType(GroupTypeEnum.WHITE_LIST_MEMBER_GROUP.getCode());
                }
            }
            if (!StringUtils.isEmpty(editGroupDTO.getBuyerIds())){
                String buyerIds = editGroupDTO.getBuyerIds();
                if (buyerIds.contains(",")){
                    StringBuffer stringBuffer = new StringBuffer("");
                    String[] split = buyerIds.split(",");
                    Set<String> strings = new HashSet<>(Arrays.asList(split));
                    strings.forEach(buyerId -> stringBuffer.append(buyerId+","));
                    editGroupDTO.setBuyerIds(stringBuffer.toString().substring(0,stringBuffer.toString().length()-1));
                }
            }
            logger.info("editOssMemberGroupInfo,编辑会员分,rep:{}",editGroupDTO);
            Result<Boolean> result = userService.updateMemberGroupInfo(editGroupDTO);
            logger.info("editOssMemberGroupInfo,编辑会员分,resp:{}",result);
            /*if (result.isSuccess()) {
                //更新会员分组和商品关系表
                this.getEditGroupDTO(editGroupDTO);
                logger.info("editOssMemberGroupInfo,更新商品和会员分组的关系,req:{}",editGroupDTO);
                Result<Boolean> updateResult = goodsFeignService.updateItemMemberGroup(editGroupDTO);
                logger.info("editOssMemberGroupInfo,更新商品和会员分组的关系,resp:{}",updateResult);
            }*/
            return result;
        }else {
            return CommonResultUtil.error(ResultEnum.ERROR.getCode(),"查询会员分组接口失败");
        }
    }

    /**
     * @desc 指定会员分组：获取会员的数量
     * @param editGroupDTO
     */
    private void getEditGroupDTO(EditGroupDTO editGroupDTO){
        if (GroupTypeEnum.PEOPLE_MEMBER_GROUP.getCode().equals(editGroupDTO.getGroupType())){
            if (!StringUtils.isEmpty(editGroupDTO.getBuyerIds())){
                String buyerIds = editGroupDTO.getBuyerIds();
                if (buyerIds.contains(",")){
                    String[] split = buyerIds.split(",");
                    Set<String> strings = new HashSet<>(Arrays.asList(split));
                    editGroupDTO.setBuyerIds(String.valueOf(strings.size()));
                }
            }
        }
    }

    /**
     * 校验会员是否属于白名单
     * @param memberCode 会员编码
     * @return 校验结果
     */
    public Result<Boolean> estimateMemberWhite(String memberCode) {
        Result<Boolean> result = new Result<>();
        logger.info(" estimateMemberWhite 查询会员是否属于白名单  rep {}",memberCode);
        result = userService.estimateMemberWhite(memberCode);
        logger.info(" estimateMemberWhite 查询会员是否属于白名单  result {}",JSON.toJSONString(result));
        if (result.isSuccess()){
            result = CommonResultUtil.success(result.getData(),"查询会员是否属于白名单成功");
        }else {
            result = CommonResultUtil.error(ResultEnum.ERROR.getCode(),"查询会员是否属于白名单失败");
        }
        return result;
    }
}

