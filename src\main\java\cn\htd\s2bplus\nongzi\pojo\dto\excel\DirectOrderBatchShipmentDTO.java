package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 直发订单-批量发货DTO
 */
@Data
public class DirectOrderBatchShipmentDTO implements Serializable {

    private static final long serialVersionUID = -323155362427125679L;

    @NotBlank(message = "提单号不能为空")
    @Excel(name = "提单号", width = 32, orderNum = "1")
    @ApiModelProperty(value = "分销订单行号")
    private String deliveryOrderNo;

    @Excel(name = "配送方式（1:供应商配送）", width = 16, orderNum = "2")
    @ApiModelProperty(value = "配送方式 1-供应商配送 2-自提")
    private String deliveryType;

    @Excel(name = "发货方式（1:快递公司承运）", width = 32, orderNum = "3")
    @ApiModelProperty(value = "发货方式，1:快递公司承运,2:派车配送 3:其他")
    private String realDeliveryType;

    @Excel(name = "物流公司编码", width = 33, orderNum = "4")
    @ApiModelProperty(value = "物流公司编码")
    private String logisticsCompanyCode;

    @Excel(name = "货运方式（1:快递2:汽配3:火车4:轮船）", width = 34, orderNum = "5")
    @ApiModelProperty(value = "货运方式 1-快递 2-汽配 3-火车 4-轮船")
    private String freightMethod;

    @Excel(name = "物流单号", width = 35, orderNum = "6")
    @ApiModelProperty(value = "物流单号")
    private String logisticNo;

    @Excel(name = "发货时间（yyyy-mm-dd）", width = 32, orderNum = "7")
    @ApiModelProperty(value = "发货时间")
    private String deliveryTime;

    @Excel(name = "发货地址ID", width = 32, orderNum = "8")
    @ApiModelProperty(value = "发货地址ID")
    private String deliveryAddressId;

    @Excel(name = "附件文件名称", width = 128, orderNum = "9")
    @ApiModelProperty(value = "附件文件名称")
    private String appendixName;

    /**
     * 附件地址-OSSUrl
     */
    @ExcelIgnore
    private String appendixUrl;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }

}
