package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: wangxuan
 * @description: 搜索采集分词excel模板
 * @date: 2023/3/1 17:09
 */
@Data
@ExcelTarget("ParticipleExcelDto")
public class ParticipleExcelDto {

    @ApiModelProperty(value = "分词类型 1:分词   2:同义词  3:过滤词")
    @Excel(name = "分词类型")
    private String participleType;

    @ApiModelProperty(value = "分词")
    @Excel(name = "分词")
    private String word;

    @ApiModelProperty(value = "查询分词名称")
    @Excel(name = "同义词")
    private String conversionWord;

    @ApiModelProperty(value = "行业")
    @Excel(name = "行业")
    private String trade;

    @ApiModelProperty(value = "采集分词来源 1:爬虫 2:B2B")
    @Excel(name = "采集分词来源")
    private String collectSource;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"participleType\":")
                .append(participleType);
        sb.append(",\"word\":\"")
                .append(word).append('\"');
        sb.append(",\"conversionWord\":\"")
                .append(conversionWord).append('\"');
        sb.append(",\"trade\":\"")
                .append(trade).append('\"');
        sb.append(",\"collectSource\":\"")
                .append(collectSource).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
