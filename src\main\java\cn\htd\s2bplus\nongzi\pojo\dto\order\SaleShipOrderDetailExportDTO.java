package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * date: 2020/10/16
 */
@ExcelTarget("SaleShipOrderDetailExportDTO")
@Data
@ToString
public class SaleShipOrderDetailExportDTO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @Excel(name = "订单编号", height = 10, width = 20)
    private String orderNo;

    /**
     * 子订单号
     */
    @ApiModelProperty(value = "子订单号")
    @Excel(name = "子订单号", height = 10, width = 22)
    private String orderItemNo;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @Excel(name = "下单时间", height = 10, width = 20)
    private String createTime;

    /**
     * 会员店编码
     */
    @ApiModelProperty(value = "会员店编码")
    @Excel(name = "会员店编码", height = 10, width = 20)
    private String buyerCode;

    /**
     * 会员店名称
     */
    @ApiModelProperty(value = "会员店名称")
    @Excel(name = "会员店名称", height = 10, width = 20)
    private String buyerName;

    /**
     * sku编码
     */
    @ApiModelProperty(value = "sku编码")
    @Excel(name = "sku编码", height = 10, width = 20)
    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @Excel(name = "商品名称", height = 10, width = 20)
    private String goodsName;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    @Excel(name = "规格名称", height = 10, width = 20)
    private String itemAttributes;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    @Excel(name = "品牌", height = 10, width = 20)
    private String brandName;

    /**
     * 商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价
     */
    @ApiModelProperty(value = "商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价")
    @Excel(name = "销售单价", height = 10, width = 20)
    private BigDecimal goodsPrice;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    @Excel(name = "销售数量", height = 10, width = 20)
    private BigDecimal goodsCount;

    /**
     * 运费金额
     */
    @ApiModelProperty(value = "运费金额")
    @Excel(name = "运费", height = 10, width = 20)
    private String goodsFreight;

    /**
     * 订单行实付金额
     */
    @ApiModelProperty(value = "订单行实付金额")
    @Excel(name = "实付金额", height = 10, width = 20)
    private BigDecimal orderItemPayAmount;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    @Excel(name = "收货人姓名", height = 10, width = 20)
    private String consigneeName;

    /**
     * 收货人联系电话
     */
    @ApiModelProperty(value = "收货人联系电话")
    @Excel(name = "收货人联系电话", height = 10, width = 20)
    private String consigneePhoneNum;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    @Excel(name = "收货地址", height = 10, width = 20)
    private String consigneeAddress;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    @Excel(name = "订单类型", height = 10, width = 20)
    private String orderType;

    /**
     * 订单状态
     */
    @Excel(name = "订单状态", height = 10, width = 20)
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * 订单备注
     */
    @ApiModelProperty(value = "订单备注")
    @Excel(name = "商家备注", height = 10, width = 20)
    private String  orderRemarks;

    @ApiModelProperty(value="发票收件人姓名")
    @Excel(name = "发票收件人姓名", width = 20)
    private String invoiceReceiverName;

    @ApiModelProperty(value="发票收件人手机号码")
    @Excel(name = "发票收件人手机号码", width = 20)
    private String invoiceReceiverPhone;

    @ApiModelProperty(value="发票接收地址信息")
    @Excel(name = "发票接收地址信息", width = 80)
    private String invoiceAddressDetail;

    @ApiModelProperty(value="邮箱地址")
    @Excel(name = "邮箱地址", width = 30)
    private String emailAddress;

    @ApiModelProperty(value = "发票类型")
    @Excel(name = "发票类型", width = 30)
    private String invoiceType;

    @ApiModelProperty(value = "纳税人识别号")
    @Excel(name = "纳税人识别号", width = 30)
    private String taxManId;

    @ApiModelProperty(value = "开户行名称")
    @Excel(name = "开户行名称", width = 30)
    private String bankName;

    @ApiModelProperty(value = "银行账号")
    @Excel(name = "银行账号", width = 30)
    private String bankAccount;

    @ApiModelProperty(value = "联系电话")
    @Excel(name = "联系电话", width = 30)
    private String contactPhone;

    @ApiModelProperty(value = "发票抬头")
    @Excel(name = "发票抬头", width = 30)
    private String  invoiceNotify;

    @ApiModelProperty(value="注册地址")
    @Excel(name = "注册地址", width = 80)
    private String invoiceAddress;

    @ApiModelProperty(value="推荐人")
    @Excel(name = "推荐人", height = 10, width = 20)
    private String recommender;

    @ApiModelProperty(value="交易店铺")
    @Excel(name = "交易店铺", width = 30)
    private String shopName;

    @ApiModelProperty(value = "支付系统流水号")
    @Excel(name = "支付系统流水号", width = 30)
    private String paySerialNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
