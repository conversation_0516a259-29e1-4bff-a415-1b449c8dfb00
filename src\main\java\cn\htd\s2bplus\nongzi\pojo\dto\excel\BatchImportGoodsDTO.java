package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class BatchImportGoodsDTO implements Serializable {
    private static final long serialVersionUID = 6266817088396731758L;

    @Excel(name = "商品货号（选填）",width = 30,orderNum = "1")
    private String productNo;

    @Excel(name = "四级管理类目ID",width = 10,orderNum = "2")
    private String managerFourCid;

    @Excel(name = "三级销售类目ID",width = 10,orderNum = "3")
    private Long saleThirdCid;

    @Excel(name = "品牌ID",width = 10,orderNum = "4")
    private Long brandId;

    @Excel(name = "型号",width = 30,orderNum = "5")
    private String modelType;

    @Excel(name = "单位",width = 10,orderNum = "6")
    private String unit;

    @Excel(name = "商品名称",width = 30,orderNum = "7")
    private String itemName;

    @Excel(name = "规格名称1",width = 30,orderNum = "8")
    private String attrName;

    @Excel(name = "规格值1",width = 30,orderNum = "9")
    private String valueName;

    @Excel(name = "规格名称2（选填）",width = 30,orderNum = "10")
    private String secondAttrName;

    @Excel(name = "规格值2（选填）",width = 30,orderNum = "11")
    private String secondValueName;

    @Excel(name = "规格名称3（选填）",width = 30,orderNum = "12")
    private String thirdAttrName;

    @Excel(name = "规格值3（选填）",width = 30,orderNum = "13")
    private String thirdValueName;

    @Excel(name = "库存数量",width = 10,orderNum = "14")
    private BigDecimal inventory;

    @Excel(name = "商品条形码（选填）",width = 10,orderNum = "15")
    private String eanCode;

    @Excel(name = "商品图片文件夹",width = 10,orderNum = "16")
    private String itemPicFile;

    @Excel(name = "商品图片名称",width = 30,orderNum = "17")
    private String itemPicName;

    @Excel(name = "规格图片名称（选填）",width = 30,orderNum = "18")
    private String skuPicName;

    @Excel(name = "详情图片名称（选填）",width = 30,orderNum = "19")
    private String itemDescribePicName;

    @Excel(name = "商品详情",width = 50,orderNum = "20")
    private String itemDescribeContent;

    @Excel(name = "发布店铺名称",width = 10,orderNum = "21")
    private String shopName;

    @Excel(name = "基础售价",width = 10,orderNum = "22")
    private BigDecimal retailPrice;

    // 错误的规格标识
    private String errAttrFlag;

    // 错误信息
    private List<String> errMsgList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }



}
