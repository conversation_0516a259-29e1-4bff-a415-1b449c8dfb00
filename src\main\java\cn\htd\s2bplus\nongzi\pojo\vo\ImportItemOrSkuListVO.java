package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportItemByPurchaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class ImportItemOrSkuListVO implements Serializable {

    private static final long serialVersionUID = -4707741022629952593L;
    /**
     * 文件url
     */
    @ApiModelProperty(value = "文件url")
    private String downloadUrl;


    /**
     * 商品/sku信息上传信息
     */
    @ApiModelProperty(value = "商品/sku信息上传信息")
    private List<ImportItemByPurchaseDTO> importItemByPurchaseList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
