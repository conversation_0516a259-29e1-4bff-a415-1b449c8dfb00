package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MemberGroupDTO implements Serializable {


    private static final long serialVersionUID = 4199898685985865171L;
    @ApiModelProperty(value = "主键", example = "1")
    private String id;

    @ApiModelProperty(value = "会员店等级", example = "会员店等级(1:一星会员,2:二星会员,3:三星会员,4:四星会员,5:五星会员,6:VIP会员)")
    private String buyerGrade;

    @ApiModelProperty(value = "分组ID", example = "1")
    private Long groupId;

    @ApiModelProperty(value = "会员编码", example = "61")
    private Long buyerId;

    @ApiModelProperty(value = "分组名称", example = "美的组")
    private String name;

    @ApiModelProperty(value = "区分会员和分组类型", example = "区分会员和分组类型")
    private String type;

    @ApiModelProperty(value = "备注", example = "备注")
    private String comment;

    @ApiModelProperty(value = "商家编码", example = "926388")
    private String sellerId;

    @ApiModelProperty(value = "展示会员数量", example = "11")
    private Integer memberCount;

    @ApiModelProperty(value = "会员编码", example = "926388")
    private String memberCode;

    @ApiModelProperty(value = "操作的会员编码", example ="['926416']")
    private String[] memberIdArr;

    @ApiModelProperty(value = "删除分组编码拼接字符串", example = "1")
    private String groupIds;

    @ApiModelProperty(value = "新增删除会员编码拼接字符串", example = "1")
    private String buyerIds;

    @ApiModelProperty(value = "会员所在省", example = "32")
    private String locationProvince;

    @ApiModelProperty(value = "会员所在城市", example = "3202")
    private String locationCity;

    @ApiModelProperty(value = "会员所在区", example = "320201")
    private String locationCounty;

    @ApiModelProperty(value = "公司名称", example = "滨海县东坎镇四方家电经营部")
    private String companyName;

    @ApiModelProperty(value = "删除标志", example = "删除标志 1是，0否")
    private String deleteFlag;

    @ApiModelProperty(value = "创建人ID", example = "0")
    private String createId;

    @ApiModelProperty(value = "创建人名称", example = "sys")
    private String createName;

    @ApiModelProperty(value = "创建时间", example = "2015-12-18 14:16:51")
    private Date createTime;

    @ApiModelProperty(value = "修改人ID", example = "0")
    private String modifyId;

    @ApiModelProperty(value = "修改人名称", example = "sys")
    private String modifyName;

    @ApiModelProperty(value = "修改时间", example = "2019-04-25 21:53:23")
    private Date modifyTime;

    @ApiModelProperty(value = "会员等级编码集合", example = "{'1','2'}")
    private List<String> gradeList;

    @ApiModelProperty(value = "会员分组编码集合", example = "{'1','2'}")
    private List<String> groupList;

    @ApiModelProperty(value = "新vms标志 1:新系统", example = "1")
    private String newFlag;

    @ApiModelProperty(value = "分组名称", example = "界首市")
    private String groupName;

    @ApiModelProperty(value = "分组类型", example = "空默认、1指定人、2按行业")
    private Integer groupType;

    @ApiModelProperty(value = "组中被关联对象集合")
    private List<MemberGroupInfoDTO> memberGroupInfoDTOS;

    @ApiModelProperty(value = "会员分组关系ID")
    private Long buyerGroupId;

    @ApiModelProperty(value = "组中被关联对象的类型")
    private String refObjType;

    @ApiModelProperty(value = "组中被关联对象的类型名称")
    private String refObjName;

    /**
     * 分组类型集合
     */
    @ApiModelProperty(value = "分组类型集合", example = "空默认、1 指定人、2 按行业,3 白名单行业,4 标签")
    private List<Integer> groupTypeList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
