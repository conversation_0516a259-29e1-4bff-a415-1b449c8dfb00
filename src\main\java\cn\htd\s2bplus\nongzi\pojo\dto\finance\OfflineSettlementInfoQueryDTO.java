package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;
@Data
public class OfflineSettlementInfoQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "买家名称", example = "")
    private String buyerName;

    @ApiModelProperty(value = "卖家编码", example = "", hidden = true)
    private String sellerCode;

    @ApiModelProperty(value = "订单创建时间-开始时间", example = "")
    private Date createOrderTimeStart;

    @ApiModelProperty(value = "订单创建时间-结束时间", example = "")
    private Date createOrderTimeEnd;

    @ApiModelProperty(value = "结算状态：0-未结算，1-已结算", example = "")
    private Integer settleStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
