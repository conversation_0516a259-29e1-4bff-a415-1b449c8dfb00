package cn.htd.s2bplus.nongzi.utils;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.enums.ParamCheckResultEnum;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/3/2 11:13
 * @Version 1.0
 */
public class CommonUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(CommonUtil.class);
    public static String randomNum16(){
        //毫秒为13位数
        String str = String.valueOf(System.currentTimeMillis());
        Random random = new Random();
        //随机生成一个4位数
        int ran = random.nextInt(9999 - 1000 + 1 ) + 1000;
        if(str.length()<16){
            for(int i = 0;i<15-str.length();i++){
                str = str + ran;
            }
        }else if(str.length()>16){
            str = str.substring(0, 16);
        }
        //返回一个17位字符串
        return str;
    }

    public static String getOrderNoByType(int type){
        String randomNum = randomNum16();
        String prefix = "50";
        switch(type){
            case 1 :
                //江苏移动订单
                prefix="51";
                break;
            case 2 :
                //云链订单
                prefix="52";
                break;
            case 3 :
                prefix="53";
                break;
            case 4 :
                prefix="54";
                break;
            case 5 :
                prefix="55";
                break;
            case 6 :
                // 支付流水号
                prefix="6A";
                break;
            default :
                break;
        }
        return prefix + randomNum;
    }

    /**
     * 判断一个字符串是否是数字。
     *
     * @param string
     * @return
     */
    public static boolean isNumber(String string) {
        if (string == null)
            return false;
        Pattern pattern = Pattern.compile("^-?\\d+(\\.\\d+)?$");
        return pattern.matcher(string).matches();
    }

    /**
     * 判断一个字符串是否是手机号（存数字）
     *
     * @param mobile
     * @return
     */
    public static boolean isMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)){
            return false;
        }
        if(mobile.length() != 11){
            return false;
        }
        return isNumber(mobile);
    }

    /**
     * 异步通知请求参数转map
     *
     * @return
     */
    public static Map<String, String> getNotifyParameters(ServletRequest request) {
        Map<String, String> params = new TreeMap<>();
        Enumeration<String> enumeration = request.getParameterNames();
        while (enumeration.hasMoreElements()) {
            String name = enumeration.nextElement();
            String[] values = request.getParameterValues(name);
            if (values == null || values.length == 0) {
                continue;
            }
            String value = values[0];
            // 注意：这里是判断不为null,没有包括空字符串的判断。
            if (value != null) {
                params.put(name, value);
            }
        }
        return params;
    }

    /**
     * 校验数量，正数，不为0，小数点后最多四位
     *
     * @param str
     * @return
     */
    public static boolean isCountNumber(String str) {
        // 判断小数点后4位的数字的正则表达式
        Pattern pattern = Pattern.compile("(0*[.]((?!0)\\d|(?!00)\\d{2}|(?!000)\\d{3}|(?!0000)\\d{4}))|(\\d*[1-9]\\d*([.]\\d{1,4})?)");
        Matcher match = pattern.matcher(str);
        return match.matches();

    }

    /**
     * 校验金额，大于或等于0，小数点后最多两位
     *
     * @param str
     * @return
     */
    public static boolean isPriceNumber(String str) {
        // 判断小数点后4位的数字的正则表达式
        Pattern pattern = Pattern.compile("(0*[.]((?!0)\\d|(?!00)\\d{2}))|(\\d*[1-9]\\d*([.]\\d{1,2})?)");
        Matcher match = pattern.matcher(str);
        return match.matches();
    }
    /**
     * 校验金额，大于或等于0，小数点后最多六位
     *
     * @param str
     * @return
     */
    public static boolean isPriceNumberSix(String str) {
        // 判断小数点后4位的数字的正则表达式
        Pattern pattern = Pattern.compile("(0*[.]((?!0)\\d|(?!00)\\d{2}))|(\\d*[1-9]\\d*([.]\\d{1,6})?)");
        Matcher match = pattern.matcher(str);
        return match.matches();
    }


    /**
     * 判断整数位数
     * @param number
     * @return
     */
    public static int  getNumberDigit(String number) {
        if(number.contains(CommonConstants.SPLIT_POINT)){
            // 截取小数点前面的数
            number = number.substring(0,number.indexOf(CommonConstants.SPLIT_POINT));
        }
       return number.length();
    }

    /**
     * 判断是否为小数
     *
     * @param count
     * @return true-为小数 false-为整数
     */
    public static boolean judgeDecimalPoint(BigDecimal count) {
        if (ObjectUtils.isEmpty(count)) {
            return false;
        }
        double number = Double.valueOf(String.valueOf(count));
        if (number % 1 ==0) {
            return false;
        }
        return true;
    }

    public static <T> List<T> importExcel(MultipartFile file, Class<T> clazz, int titleRows, int headRows) {
        try (InputStream inputStream = file.getInputStream()) {
            ImportParams params = new ImportParams();
            params.setTitleRows(titleRows);
            params.setHeadRows(headRows);

            // 调用 ExcelImportUtil 的导入方法
            List<T> dataList = ExcelImportUtil.importExcel(inputStream, clazz, params);

            if (!CollectionUtils.isEmpty(dataList)) {
                return dataList;
            }
        } catch (Exception e) {
            LOGGER.error("解析 Excel 数据异常 error:", e);
        }
        return null;
    }

    public static <T> List<Map<String, Object>> getExportDataMap(List<T> exportDataList, String sheetName, Class<T> entityClass) {
        // 创建导出参数对象
        ExportParams sheetParams = new ExportParams();
        // 设置sheet的名称
        sheetParams.setSheetName(sheetName);

        // 创建用于存储导出数据的map
        Map<String, Object> sheetDataMap = new HashMap<>();

        // 设置title参数为ExportParams类型
        sheetDataMap.put("title", sheetParams);

        // 设置模版导出对应的实体类型
        sheetDataMap.put("entity", entityClass);

        // 设置sheet中要填充的数据
        sheetDataMap.put("data", exportDataList);

        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(sheetDataMap);
        return sheetsList;
    }

    /**
     * 判断所有参数是否为空 全部非空返回ALL_NOT_EMPTY 全部为空返回ALL_EMPTY 其他返回OTHER
     */
    public static ParamCheckResultEnum isAllParamEmpty(String ... params) {
        // 如果数组为空，则所有参数为空
        if (ArrayUtils.isEmpty(params)) {
            return ParamCheckResultEnum.ALL_EMPTY;
        }
        // 检查是否有非空参数
        boolean hasNonEmpty = false;
        // 检查是否有空参数
        boolean hasEmpty = false;
        for (String param : params) {
            if (org.apache.commons.lang3.StringUtils.isBlank(param)) {
                hasEmpty = true;
            } else {
                hasNonEmpty = true;
            }
            // 一旦发现既有非空又有空，则可以直接返回OTHER
            if (hasNonEmpty && hasEmpty) {
                return ParamCheckResultEnum.OTHER;
            }
        }
        // 根据hasNonEmpty和hasEmpty的结果直接返回
        if (hasNonEmpty) {
            // 全部非空返回ALL_NOT_EMPTY
            return ParamCheckResultEnum.ALL_NOT_EMPTY;
        } else {
            // 全部为空返回ALL_EMPTY
            return ParamCheckResultEnum.ALL_EMPTY;
        }
    }


    public static void main(String[] args) {
        System.out.println(CommonUtil.isAllParamEmpty("","",null," "));
    }
}
