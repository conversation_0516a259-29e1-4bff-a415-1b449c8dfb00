package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class StatementModelItemDTO implements Serializable {
    private static final long serialVersionUID = -5065918773362054727L;
    @ApiModelProperty(value = "自增id")
    private Long id;
    @ApiModelProperty(value = "模型ID")
    private Long modelId;
    @ApiModelProperty(value = "清分层级")
    private Long lev;
    @ApiModelProperty(value = "角色编码")
    private String roleCode;
    @ApiModelProperty(value = "分佣方式 1：固定 2 百分比")
    private String distributionType;
    @ApiModelProperty(value = "分佣系数  分佣方式1-固定值 2-百分比系数")
    private BigDecimal distributionValue;
    @ApiModelProperty(value = "状态 0启用 1 停用")
    private String status;
    @ApiModelProperty(value = "是否删除 0否 1是")
    private String deleteFlag;
    @ApiModelProperty(value = "创建人ID")
    private String createId;
    @ApiModelProperty(value = "创建人")
    private String createName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改人id")
    private String modifyId;
    @ApiModelProperty(value = "修改人姓名")
    private String modifyName;
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    @ApiModelProperty(value = "商家编码")
    private String memberCode;
    @ApiModelProperty(value = "商家名称")
    private String memberName;
    @ApiModelProperty(value = "类目名称", example = "4.5L")
    private String categoryName;
    @ApiModelProperty(value = "类目编码", example = "A8270501")
    private String categoryCode;
    @ApiModelProperty(value = "品牌名称", example = "曼瑞德")
    private String brandName;

    public StatementModelItemDTO() {
    }

    public Long getId() {
        return this.id;
    }

    public Long getModelId() {
        return this.modelId;
    }

    public Long getLev() {
        return this.lev;
    }

    public String getRoleCode() {
        return this.roleCode;
    }

    public String getDistributionType() {
        return this.distributionType;
    }

    public BigDecimal getDistributionValue() {
        return this.distributionValue;
    }

    public String getStatus() {
        return this.status;
    }

    public String getDeleteFlag() {
        return this.deleteFlag;
    }

    public String getCreateId() {
        return this.createId;
    }

    public String getCreateName() {
        return this.createName;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public String getModifyId() {
        return this.modifyId;
    }

    public String getModifyName() {
        return this.modifyName;
    }

    public Date getModifyTime() {
        return this.modifyTime;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    public void setLev(Long lev) {
        this.lev = lev;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public void setDistributionType(String distributionType) {
        this.distributionType = distributionType;
    }

    public void setDistributionValue(BigDecimal distributionValue) {
        this.distributionValue = distributionValue;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getMemberCode() {
        return memberCode;
    }

    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    @Override
    public String toString() {
        return "StatementModelItemDTO{" +
                "id=" + id +
                ", modelId=" + modelId +
                ", lev=" + lev +
                ", roleCode='" + roleCode + '\'' +
                ", distributionType='" + distributionType + '\'' +
                ", distributionValue=" + distributionValue +
                ", status='" + status + '\'' +
                ", deleteFlag='" + deleteFlag + '\'' +
                ", createId='" + createId + '\'' +
                ", createName='" + createName + '\'' +
                ", createTime=" + createTime +
                ", modifyId='" + modifyId + '\'' +
                ", modifyName='" + modifyName + '\'' +
                ", modifyTime=" + modifyTime +
                ", memberCode='" + memberCode + '\'' +
                ", memberName='" + memberName + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", categoryCode='" + categoryCode + '\'' +
                ", brandName='" + brandName + '\'' +
                '}';
    }
}
