package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PurchaserOrderGoodsListDTO implements Serializable {
    private static final long serialVersionUID = -2985528089410486253L;

    @ApiModelProperty(value = "采购单唯一键")
    private String purchaseOrderUniqueNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "数量")
    private BigDecimal goodsNumber;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayment;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
