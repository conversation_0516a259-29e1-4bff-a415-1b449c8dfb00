package cn.htd.s2bplus.nongzi.feign.auth;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.config.FeignConfiguration;
import cn.htd.s2bplus.nongzi.pojo.dto.auth.ApproveDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.ProxyOperateShopAuthDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.ShopAuthSubAccountVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/3/9
 */
@Component
@FeignClient(value="s2bplus-auth-service",configuration = FeignConfiguration.class)
public interface AuthServiceAPI {
	/* 验证接口*/
	@PostMapping(value = "/inspect/approve")
	Result<LoginUserDetail> approve(@RequestBody ApproveDTO approveDTO);

	/**
	 * 查询子账号店铺信息
	 *
	 * @param loginId 登录id
	 * @return 响应
	 */
	@GetMapping("/newSubAccountIncludeRole/querySubAccountShop")
	Result<List<ShopAuthSubAccountVO>> querySubAccountShop(@RequestParam(value = "loginId", required = true) String loginId, @RequestParam(value = "shopName", required = false) String shopName,
														   @RequestParam(value = "memberCode", required = true) String memberCode);

	/**
	 * 查询oss销售管理平台店铺授权配置列表
	 * @param proxyOperateShopAuthDTO
	 * @return
	 */
	@PostMapping(value = "/proxyOperateShopAuth/queryAuthorizationList")
	Result<List<ProxyOperateShopAuthDTO>> queryAuthorizationList(@RequestBody ProxyOperateShopAuthDTO proxyOperateShopAuthDTO);

	@GetMapping(value="/account/queryUserInfo")
	Result<LoginUserDetail> queryUserInfo(@RequestParam("authorization") String authorization);
}
