package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class CheckStockCodeReqDTO implements Serializable {


	@ApiModelProperty(value = "品牌方编码")
	@NotBlank(message ="品牌方编码 不可为空")
	private String brandCode;

	@ApiModelProperty(value="商品库存编码")
	@NotBlank(message ="商品库存编码 不可为空")
	private String stockCode;

}
