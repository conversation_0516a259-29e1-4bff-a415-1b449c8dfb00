package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ShopDTO implements Serializable {
    private static final long serialVersionUID = -8344221588052771958L;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}

