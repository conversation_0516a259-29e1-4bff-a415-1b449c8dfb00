package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
@ExcelTarget("ImportRemoveItemDTO")
public class ImportRemoveItemDTO implements Serializable {


    private static final long serialVersionUID = 6266817088396731758L;

    @ExcelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty(value = "下架枚举value 1:经营资质证书未上传 2:价格异常 3:商品信息不符合标准 4:其他 ")
    @ExcelProperty("下架原因")
    private String delistingTypeName;

    @ApiModelProperty(value = "下架枚举key 1:经营资质证书未上传 2:价格异常 3:商品信息不符合标准 4:其他 ")
    private Integer delistingType;

    @ApiModelProperty(value = "备注")
    @ExcelProperty("备注")
    private String delistingRemark;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}