package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * created by b<PERSON><PERSON><PERSON> on 2024/9/5
 */
@Data
public class WarehouseGoodsDetailExcel implements Serializable {

    @Excel(name = "商品sku编码", width = 30)
    private String skuCode;

    @Excel(name = "商品库存编码", width = 30)
    private String stockCode;

    @Excel(name = "商品名称", width = 30)
    private String itemName;

    @Excel(name = "仓库编码", width = 30)
    private String warehouseCode;

    @Excel(name = "仓库名称", width = 30)
    private String warehouseName;

    @Excel(name = "仓库类型", width = 30)
    private String warehouseTypeStr;

    @Excel(name = "可用库存", width = 30)
    private String useNum;

    @Excel(name = "订单占用", width = 30)
    private String reserveNum;

}
