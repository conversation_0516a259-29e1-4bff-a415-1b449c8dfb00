package cn.htd.s2bplus.nongzi.utils;

import java.util.HashMap;
import java.util.Map;

import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.pojo.dto.auth.ApproveDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import org.apache.commons.lang3.StringUtils;
import cn.htd.s2bplus.common.S2bPlusCommonConstants;


/**
 * <AUTHOR>
 * @desc
 * @date 2020/2/25
 */
public class BaseContextHandler {
    BaseContextHandler() {
    }



	public static final ThreadLocal<Map<String, Object>> THREADLOCAL = new ThreadLocal<>();

    public static void set(String key, Object value) {
		Map<String, Object> map = THREADLOCAL.get();
        if (map == null) {
			map = new HashMap<>();
			THREADLOCAL.set(map);
        }
        map.put(key, value);
    }

    public static Object get(String key){
		Map<String, Object> map = THREADLOCAL.get();
        if (map == null) {
			map = new HashMap<>();
			THREADLOCAL.set(map);
        }
        return map.get(key);
    }

    public static Long getUserID(){
        Object value = get(CommonConstants.CONTEXT_KEY_USER_ID);
        return returnLongValue(value);
    }

    public static void setUserID(Long userID){
        set(CommonConstants.CONTEXT_KEY_USER_ID,userID);
    }

    public static String getUserName(){
        Object value = get(CommonConstants.CONTEXT_KEY_USER_NAME);
        return returnObjectValue(value);
    }

    public static void setUserName (String userName){
        set(CommonConstants.CONTEXT_KEY_USER_NAME,userName);
    }

    public static Long getAccountID(){
        Object value = get(CommonConstants.CURRENT_KEY_ACCOUNT_ID);
        return returnLongValue(value);
    }

    public static void setAccountID(Long accountId){
        set(CommonConstants.CURRENT_KEY_ACCOUNT_ID,accountId);
    }

    public static String getMemberCode(){
        Object value = get(CommonConstants.CONTEXT_KEY_MEMBER_CODE);
        return returnObjectValue(value);
    }

    public static void setMemberCode(String memberCode){
        set(CommonConstants.CONTEXT_KEY_MEMBER_CODE,memberCode);
    }

    public static LoginUserDetail getLoginUser(){
        return (LoginUserDetail)get(CommonConstants.CONTEXT_KEY_LOGIN_USER);
    }

    public static void setLoginUser(LoginUserDetail loginUser){
        set(CommonConstants.CONTEXT_KEY_LOGIN_USER,loginUser);
    }

    public static ApproveDTO getApprove(){
        return (ApproveDTO)get(CommonConstants.CONTEXT_KEY_APPROVE);
    }

    public static void setApprove(ApproveDTO approve){
        set(CommonConstants.CONTEXT_KEY_APPROVE,approve);
    }

    private static String returnObjectValue(Object value) {
        return value==null?"":value.toString();
    }

    private static Long returnLongValue(Object value){
        return value==null?0:Long.parseLong(value.toString());
    }

    public static void remove(){
		THREADLOCAL.remove();
    }

    public static String getLoginId() {
        LoginUserDetail loginUserDetail = (LoginUserDetail) get(CommonConstants.CONTEXT_KEY_LOGIN_USER);
        if (loginUserDetail != null && StringUtils.isNotEmpty(loginUserDetail.getLoginId())) {
            return loginUserDetail.getLoginId();
        }
        return S2bPlusCommonConstants.APP_NAME_OOP;
    }

}
