package cn.htd.s2bplus.nongzi.interceptor;


import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.s2bplus.common.AppNacosConfig;
import cn.htd.s2bplus.common.enums.PlatformEnum;
import cn.htd.s2bplus.nongzi.enums.OssPlatformEnum;
import cn.htd.s2bplus.nongzi.feign.user.UserService;
import cn.htd.s2bplus.nongzi.pojo.dto.auth.ApproveDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.feign.auth.AuthServiceAPI;
import cn.htd.s2bplus.nongzi.utils.AuthRuntimeException;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import cn.htd.s2bplus.nongzi.utils.SpringUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 * @date 2019/10
 */
@Component
@Slf4j
public class AuthInterceptor extends HandlerInterceptorAdapter {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 跨域问题在gateway统一处理
        response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept,authorization,uniqueId,loginId");

        // 如果是OPTIONS则结束请求
        if (HttpMethod.OPTIONS.toString().equals(request.getMethod())) {
            response.setStatus(HttpStatus.OK.value());
            return true;
        }
        String ossAuthorization = request.getHeader("ossAuthorization");
        if (!StringUtils.isEmpty(ossAuthorization) && !"undefined".equals(ossAuthorization)) {
            ApplicationContext applicationContext = SpringUtils.getApplicationContext();
            AuthServiceAPI authServiceAPI = applicationContext.getBean(AuthServiceAPI.class);
            log.info("oss获取登录信息入参：{}", ossAuthorization);
            Result<LoginUserDetail> loginUserDetail = authServiceAPI.queryUserInfo(ossAuthorization);
            log.info("oss获取登录信息出参：{}", loginUserDetail);
            if (!loginUserDetail.isSuccess()) {
                throw new AuthRuntimeException(loginUserDetail.getCode(), loginUserDetail.getMsg());
            }
            LoginUserDetail user = loginUserDetail.getData();
            BaseContextHandler.setAccountID(user.getUserId());
            BaseContextHandler.setUserID(user.getUserId());
            BaseContextHandler.setUserName(user.getUserName());
            BaseContextHandler.setMemberCode(String.valueOf(user.getUserId()));
            this.setSubAccountInfo(user);
            BaseContextHandler.setLoginUser(user);
            return super.preHandle(request, response, handler);
        }
        boolean flag = false;
        String requestUri = request.getRequestURI();
        String httpMethod = request.getMethod().toUpperCase();
        String authorization = request.getHeader("uniqueId") != null ? request.getHeader("uniqueId") : request.getParameter("uniqueId");
        if (ObjectUtils.isEmpty(authorization) || "null".equals(authorization) || "NULL".equals(authorization)) {
            authorization = request.getHeader("authorization") != null ? request.getHeader("authorization") : request.getParameter("authorization");
            flag = true;
            if (ObjectUtils.isEmpty(authorization)) {
                authorization = request.getHeader("loginId") != null ? request.getHeader("loginId") : request.getParameter("loginId");
                flag = false;
                if (ObjectUtils.isEmpty(authorization)) {
                    throw new AuthRuntimeException(ResultEnum.UN_AUTHORIZED.getCode(), ResultEnum.UN_AUTHORIZED.getMsg());
                }
            }
        }
        log.info("请求头authorization为空，请求头authorization为空的值:{}",authorization);
        if (!flag) {
            ApplicationContext applicationContext = SpringUtils.getApplicationContext();
            UserService userService = applicationContext.getBean(UserService.class);
            log.info("userService.getUserByLoginId req:{}", authorization);
            Result<LoginUserDetail> loginId = userService.getUserByLoginId(authorization);
            log.info("userService.getUserByLoginId res:{}", JSON.toJSONString(loginId));
            if (!loginId.isSuccess()) {
                throw new AuthRuntimeException(ResultEnum.UN_AUTHORIZED.getCode(), ResultEnum.UN_AUTHORIZED.getMsg());
            }
            LoginUserDetail user = loginId.getData();
            BaseContextHandler.setAccountID(user.getUserId());
            BaseContextHandler.setUserID(user.getUserId());
            BaseContextHandler.setUserName(user.getUserName());
            BaseContextHandler.setMemberCode(String.valueOf(user.getUserId()));
            this.setSubAccountInfo(user);
            BaseContextHandler.setLoginUser(user);
        }else {
            log.info("authorization值：{}", authorization);
            ApproveDTO approveDTO = ApproveDTO.builder().url(requestUri).method(httpMethod).authorization(authorization).build();
            BaseContextHandler.setApprove(approveDTO);
            ApplicationContext applicationContext = SpringUtils.getApplicationContext();
            AuthServiceAPI authServiceAPI = applicationContext.getBean(AuthServiceAPI.class);
            Result<LoginUserDetail> approveResult = authServiceAPI.approve(approveDTO);
            log.info("authServiceAPI.approve response:{}", JSON.toJSONString(approveResult));

            if (!approveResult.isSuccess()) {
                throw new AuthRuntimeException(approveResult.getCode(), approveResult.getMsg());
            }
            LoginUserDetail user = approveResult.getData();
            BaseContextHandler.setAccountID(user.getAccountId());
            BaseContextHandler.setUserID(user.getUserId());
            BaseContextHandler.setUserName(user.getUserName());
            BaseContextHandler.setMemberCode(user.getMemberCode());
            this.setSubAccountInfo(user);
            BaseContextHandler.setLoginUser(user);
        }
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        BaseContextHandler.remove();
        super.afterCompletion(request, response, handler, ex);
    }

    /**
     * 设置子账号信息
     * @param user 登录人信息
     */
    private void setSubAccountInfo(LoginUserDetail user) {
        if(user.getParentAccount() != null){
            // 子账号id
            user.setSubAccountLoginId(user.getLoginId());
            user.setSubAccountMemberId(user.getMemberId());
            user.setSubAccountUserId(user.getUserId());
            // 子账号登录暂赋主账户信息
            LoginUserDetail loginParent = user.getParentAccount();
            BaseContextHandler.setAccountID(loginParent.getAccountId());
            user.setLoginId(loginParent.getLoginId());
            user.setAccountId(loginParent.getAccountId());
            user.setUserId(loginParent.getUserId());
            user.setMemberId(loginParent.getMemberId());
            user.setMemberCode(loginParent.getMemberCode());
            user.setSellerType(loginParent.getSellerType());
        }
    }
}
