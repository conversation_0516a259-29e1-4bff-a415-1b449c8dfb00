package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2020/11/27 16:22
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class ShopSkuQueryDTO implements Serializable {
    private static final long serialVersionUID = 7394885749361249358L;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "卖家ID")
    private Long sellerId;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"shopId\":")
                .append(shopId);
        sb.append(",\"skuCode\":\"")
                .append(skuCode).append('\"');
        sb.append(",\"sellerId\":")
                .append(sellerId);
        sb.append('}');
        return sb.toString();
    }
}
