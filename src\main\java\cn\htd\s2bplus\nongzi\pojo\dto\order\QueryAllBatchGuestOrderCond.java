package cn.htd.s2bplus.nongzi.pojo.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class QueryAllBatchGuestOrderCond implements Serializable {
    private static final long serialVersionUID = 7755886198495481576L;

    @ApiModelProperty(value = "商品skuCode")
    private String skuCode;
    @ApiModelProperty(value = "第三方单号")
    private String thirdOrderNo;
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    @ApiModelProperty(value = "会员编号")
    private String buyerCode;
    @ApiModelProperty(value = "是否自提 0：否 1：是")
    private Integer pickupFlag;
    @ApiModelProperty(value = "收货人姓名")
    private String consigneeName;
    @ApiModelProperty(value = "收货人电话")
    private String consigneePhoneNum;
    @ApiModelProperty(value = "提交订单结果编码:0 未提交 1：成功 2：失败")
    private Integer resultCode;
    @ApiModelProperty(value = "删除标志 0:未删除 1：删除")
    private Integer deleteFlag;
    @ApiModelProperty(value = "创建起始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createBeginTime;
    @ApiModelProperty(value = "创建结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createEndTime;
    @ApiModelProperty(value = "卖家Id")
    private Long sellerId;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"skuCode\":\"")
                .append(skuCode).append('\"');
        sb.append(",\"thirdOrderNo\":\"")
                .append(thirdOrderNo).append('\"');
        sb.append(",\"batchNo\":\"")
                .append(batchNo).append('\"');
        sb.append(",\"orderNo\":\"")
                .append(orderNo).append('\"');
        sb.append(",\"buyerCode\":\"")
                .append(buyerCode).append('\"');
        sb.append(",\"pickupFlag\":")
                .append(pickupFlag);
        sb.append(",\"consigneeName\":\"")
                .append(consigneeName).append('\"');
        sb.append(",\"consigneePhoneNum\":\"")
                .append(consigneePhoneNum).append('\"');
        sb.append(",\"resultCode\":")
                .append(resultCode);
        sb.append(",\"deleteFlag\":")
                .append(deleteFlag);
        sb.append(",\"createBeginTime\":\"")
                .append(createBeginTime).append('\"');
        sb.append(",\"createEndTime\":\"")
                .append(createEndTime).append('\"');
        sb.append(",\"sellerId\":")
                .append(sellerId);
        sb.append('}');
        return sb.toString();
    }
}
