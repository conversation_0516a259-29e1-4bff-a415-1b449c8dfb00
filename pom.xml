<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <!--继承基础架构部父类，其中统一指定了springcloud和springboot版本-->
    <parent>
        <groupId>cn.htd.development</groupId>
        <artifactId>base-development</artifactId>
        <version>1.0.2-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>cn.htd.s2bplus</groupId>
    <artifactId>s2bplus-nongzi-api</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>s2bplus-nongzi-api</name>
    <description>S2BPLUS农资事业部开放运营API</description>


    <!--统一设置参数变量-->
    <properties>
        <java.version>1.8</java.version>
        <alibaba.version>0.9.0.RELEASE</alibaba.version>
        <sso-version>1.0.0-RELEASES</sso-version>
        <netease-version>1.0.0-SNAPSHOT</netease-version>
        <dozer>5.5.1</dozer>
        <poi.version>4.1.2</poi.version>
        <easyexcel.version>3.1.1</easyexcel.version>
    </properties>

    <dependencies>
        <!-- s2bPlus通用基础包 包含base-development-framework && development-framework-log && development-framework-tool -->
        <dependency>
            <groupId>cn.htd.s2bplus</groupId>
            <artifactId>s2bplus-common</artifactId>
            <version>4.0.20-release</version>
        </dependency>
        <dependency>
            <groupId>cn.htd.development</groupId>
            <artifactId>base-development-framework-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
            <version>2.2.2.RELEASE</version>
        </dependency>
        <!--excel POI类-->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>${poi.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>

        <!--以下为产品线项目依赖包-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--阿里云OSS存储-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.8.0</version>
        </dependency>

        <!-- 影响系统WebServices 调用 -->
        <dependency>
            <groupId>axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.4</version>
        </dependency>




        <!-- 身份权限认证 -->
        <dependency>
            <groupId>cn.htd</groupId>
            <artifactId>sso</artifactId>
            <version>${sso-version}</version>
        </dependency>
        <!--拖拽验证码-->
        <dependency>
            <groupId>cn.htd.netease</groupId>
            <artifactId>netease-captcha</artifactId>
            <version>${netease-version}</version>
        </dependency>

        <dependency>
            <groupId>com.yiji.openapi</groupId>
            <artifactId>yiji-openapi-tool</artifactId>
            <version>1.3</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.4</version>
        </dependency>

        <dependency>
            <groupId>net.sf.dozer</groupId>
            <artifactId>dozer</artifactId>
            <version>${dozer}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


    <build>
        <finalName>s2bplus-nongzi-api</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <!-- 1、设置jar的入口类 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>cn.htd.s2bplus.nongzi.S2bplusNongziApiApplication</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <!--2、把附属的jar打到jar内部的lib目录中 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
            </plugin>

            <!-- 3、打包过程忽略Junit测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.17</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
