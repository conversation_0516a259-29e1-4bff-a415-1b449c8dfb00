FROM test-dockimgws.htd.cn/b2b_p/centos6v5-ty364-msah:latest

RUN sed -i '0,/htdty/s//nongzi-api-cyc-prod/' /home/<USER>/tingyun/tingyun.properties

EXPOSE 8080

WORKDIR /home/<USER>

RUN rm -rf  tomcat7 tomcat8

ADD ./target/s2bplus-nongzi-api.jar ./

ENV nacos_config_server_addr=*************:8848 \
    nacos_discovery_server_addr=*************:8848 \
    nacos_config_namespace=422f88cf-fae6-4a86-8e76-717eb856aee1

ENV JAVA_OPTS="-Xms2G -Xmx2G -XX:PermSize=512M -XX:MaxPermSize=512M  -Dfile.encoding=UTF8 -Dsun.jnu.encoding=UTF8   -Dapp.id=s2bplus-nongzi-api"

ENTRYPOINT java ${JAVA_OPTS}  -javaagent:/home/<USER>/tingyun/tingyun-agent-java.jar   -Dmaven.test.skip=true  -jar  s2bplus-nongzi-api.jar
