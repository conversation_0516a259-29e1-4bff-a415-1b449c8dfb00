package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ShelfData extends BaseDTO {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * skuCode
	 */
	@ApiModelProperty(value = "skuCode")
	private String skuCode;

	/**
	 * itemCode
	 */
	@ApiModelProperty(value = "商品编码")
	private String itemCode;

	/**
	 * 上下架状态,0 下架 1 上架
	 */
	@ApiModelProperty(value = "上下架状态,0 下架 1 上架")
	private String shelfStatus;

	@ApiModelProperty(value = "店铺Id")
	private Long shopId;

}
