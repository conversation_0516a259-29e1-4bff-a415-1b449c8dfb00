package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;

/**
 * 直发订单-批量发货记录
 */
@Data
public class DirectOrderBatchShipmentRecordDTO extends DirectOrderBatchShipmentDTO implements Serializable {

    private static final long serialVersionUID = -567351274477896126L;

    @Excel(name = "报错原因", height = 20, width = 30, orderNum = "10")
    private String errorReason;;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }

}
