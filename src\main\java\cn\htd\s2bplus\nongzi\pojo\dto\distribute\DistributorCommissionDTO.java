package cn.htd.s2bplus.nongzi.pojo.dto.distribute;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
public class DistributorCommissionDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "会员Id")
    private Long memberId;
    @ApiModelProperty("推客编码")
    private String memberCode;
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "经营人姓名")
    private String businessPersonName;
    @ApiModelProperty(value = "推客类型", example = "1：推客/分销员，2：合伙人")
    private Integer distributeType;
    @ApiModelProperty(value = "申请时间")
    private Date createTime;
    @ApiModelProperty(value = "累计佣金")
    private BigDecimal totalCommission;
    @ApiModelProperty(value = "待结算佣金")
    private BigDecimal unsettledCommission;
    @ApiModelProperty(value = "已结算佣金")
    private BigDecimal settledCommission;
    @ApiModelProperty(value = "可提现佣金")
    private BigDecimal canWithdrawCommission;
    /**
     * 品牌分销:分销佣金等于累计佣金
     */
    @ApiModelProperty(value = "分销佣金")
    private BigDecimal distributeCommission;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
