package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory;
import cn.htd.s2bplus.nongzi.utils.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReportHistoryVo implements Serializable {

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Long total;


    /**
     * 总页数
     */
    @ApiModelProperty(value = "总页数")
    private Integer pages;

    /**
     * 下载列表
     */
    @ApiModelProperty(value = "下载列表")
    List<ReportHistory> reportHistoryList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
