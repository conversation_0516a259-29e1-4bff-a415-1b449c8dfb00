package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import java.io.Serializable;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020-12-11 14:02
 * @Version 1.0
 */
public class SalesAreaSkuQueryDTO implements Serializable {
    private static final long serialVersionUID = 5395345710312946595L;
    /**
     * sku编码
     */
    private List<String> skuCodes;

    /**
     * 区县编码
     */
    private String countyCode;


    public SalesAreaSkuQueryDTO() {
    }

    public List<String> getSkuCodes() {
        return this.skuCodes;
    }

    public String getCountyCode() {
        return this.countyCode;
    }

    public void setSkuCodes(List<String> skuCodes) {
        this.skuCodes = skuCodes;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"skuCodes\":")
                .append(skuCodes);
        sb.append(",\"countyCode\":\"")
                .append(countyCode).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
