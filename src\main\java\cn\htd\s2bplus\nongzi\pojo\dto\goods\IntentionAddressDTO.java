package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.user.MemberConsigAddressDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class IntentionAddressDTO implements Serializable {
    private static final long serialVersionUID = -3492142780004376761L;

    @ApiModelProperty(value = "活动编码")
    private String activityCode;

    @ApiModelProperty(value = "服务商编码")
    private String serviceProviderCode;

    @ApiModelProperty(value = "服务商名称")
    private String serviceProviderName;

    @ApiModelProperty(value = "意向单编号")
    private String intentionNo;

    @ApiModelProperty(value = "卖家编号")
    private String sellerCode;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "采购部门名称")
    private String departmentName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "收货地址ID")
    private Long consigneeId;

    @ApiModelProperty(value = "收货地址")
    private MemberConsigAddressDTO memberConsigAddressDTO;

    @ApiModelProperty(value = "买家编码")
    private String buyerCode;

    @ApiModelProperty(value = "会员店名称")
    private String buyerName;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "apple_id")
    private String appleId;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsCount;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "活动状态:0 智能报价中；1 待推送报价")
    private Integer pushStatus;

    @ApiModelProperty(value = "销售属性集合名称 颜色:白色;内存:16G;")
    private String attributesName;

    @ApiModelProperty(value = "串码集合")
    private List<String> snList;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建类型：0=人工创建，1=系统创建")
    private Integer createType;

    @ApiModelProperty(value = "订单创建失败原因")
    private String failedReason;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
