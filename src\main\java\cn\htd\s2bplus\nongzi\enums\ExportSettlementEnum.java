package cn.htd.s2bplus.nongzi.enums;

/**
 * <AUTHOR>
 */
public enum ExportSettlementEnum {

    SETTLE_STATUS_PENDING_FINANCIAL_CONFIRMATION("10", "待财务确认"),
    SETTLE_STATUS_AWAITING_MERCHANT_WITHDRAWAL("11","待商家提款"),
    SETTLE_STATUS_MERCHANT_WITHDRAWAL_PROCESSING("12","商家提款处理中"),
    SETTLE_STATUS_SETTLEMENT_COMPLETED("13","结算完成"),
    SETTLE_STATUS_SETTLEMENT_FAILED("14","结算失败"),

    PAY_TYPE_CASH("1","现金"),
    PAY_TYPE_WECHAT("2","微信"),
    PAY_TYPE_ALIPAY("3","支付宝"),
    PAY_TYPE_BALANCE("4","余额"),
    PAY_TYPE_LARGE_TRANSFER("5","大额转账"),
    PAY_TYPE_OTHER("6","其他"),

    ;

    private String code;
    private String msg;

    ExportSettlementEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
