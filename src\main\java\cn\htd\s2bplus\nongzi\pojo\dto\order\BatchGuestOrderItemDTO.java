package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.math.BigDecimal;

/**
 * @description:
 * @author: 08011507
 * @date: 2021/5/24
 */
@Data
public class BatchGuestOrderItemDTO {

    @ApiModelProperty(value = "商品skuCode")
    private String skuCode;
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    @ApiModelProperty(value = "店铺Id")
    private Long shopId;
    @ApiModelProperty(value = "店铺名称", hidden = true)
    private String shopName;
    @ApiModelProperty(value = "商品单价")
    private BigDecimal goodPrice;
    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodCount;
    @ApiModelProperty(value = "第三方单号")
    private String thirdOrderNo;
    @ApiModelProperty(value = "分销限价")
    private BigDecimal saleLimitPrice;
    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(
            value = " 货品编码 ",
            notes = "货品编码",
            example = "400000273"
    )
    private String cargoCode;




    /**
     * 仓库代码
     */
    @ApiModelProperty(value = "仓库代码")
    private String warehouseCode;
    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 供货商编码
     */
    @ApiModelProperty(value = "供货商编码")
    private String supplierCode;

    /**
     * 供货商名称
     */
    @ApiModelProperty(value = "供货商名称")
    private String supplierName;
    /**
     * 采购部门编码
     */
    @ApiModelProperty(value = "采购部门编码")
    private String purchaseDepartmentCode;

    /**
     * 采购部门名称
     */
    @ApiModelProperty(value = "采购部门名称")
    private String purchaseDepartmentName;

    @ApiModelProperty(value = "商品属性")
    private String productAttribute;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
