package cn.htd.s2bplus.nongzi.pojo.dto.contract;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title DetailTableInfo
 * @Date: 2024/12/16 9:59
 */
@Data
public class DetailTableInfo implements Serializable {
    private static final long serialVersionUID = 2609304036168157071L;
    @ApiModelProperty(value = "表格定义名称",example = "dt1")
    private String detailType;

    @ApiModelProperty(value = "表格具体信息")
    private List<SelectContractInParamDTO> info;

    @ApiModelProperty(value = "表格控件名称",example = "dt1")
    private String controlName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
