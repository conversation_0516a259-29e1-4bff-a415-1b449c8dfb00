package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ReportHistory implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID",hidden = true)
    private Long id;

    /**
     * 业务类型，0=未知，1=销售单报表，2=结算单报表， 3=待发货订单导出记录表，4=pop待发货导入表
     */
    @ApiModelProperty(value = "业务类型",hidden = true)
    private Byte businessType;

    /**
     * 报表状态,0=生成中，1=已生成，2=生成异常
     */
    @ApiModelProperty(value = "报表状态")
    private Byte reportStatus;

    /**
     * 文件下载路径
     */
    @ApiModelProperty(value = "文件下载路径")
    private String downloadUrl;

    /**
     * 报表生成完成时间
     */
    @ApiModelProperty(value = "报表生成完成时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    /**
     * 查询条件开始时间
     */
    @ApiModelProperty(value = "查询条件开始时间",hidden = true)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 查询条件结束时间
     */
    @ApiModelProperty(value = "查询条件结束时间",hidden = true)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID",hidden = true)
    private Long createId;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称",hidden = true)
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID",hidden = true)
    private Long modifyId;

    /**
     * 更新人名称
     */
    @ApiModelProperty(value = "更新人名称",hidden = true)
    private String modifyName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间",hidden = true)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"id\":")
                .append(id);
        sb.append(",\"businessType\":")
                .append(businessType);
        sb.append(",\"reportStatus\":")
                .append(reportStatus);
        sb.append(",\"downloadUrl\":\"")
                .append(downloadUrl).append('\"');
        sb.append(",\"finishTime\":\"")
                .append(finishTime).append('\"');
        sb.append(",\"beginTime\":\"")
                .append(beginTime).append('\"');
        sb.append(",\"endTime\":\"")
                .append(endTime).append('\"');
        sb.append(",\"createId\":")
                .append(createId);
        sb.append(",\"createName\":\"")
                .append(createName).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append(",\"modifyId\":")
                .append(modifyId);
        sb.append(",\"modifyName\":\"")
                .append(modifyName).append('\"');
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
