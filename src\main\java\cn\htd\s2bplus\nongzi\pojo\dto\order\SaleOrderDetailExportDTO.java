package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description :
 * @Date :2023/4/21 18:39
 */
@ExcelTarget("SaleOrderDetailDTO")
@Data
public class SaleOrderDetailExportDTO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @Excel(name = "订单编号", height = 10, width = 20)
    private String orderNo;


    /**
     * 子订单号
     */
    @ApiModelProperty(value = "子订单号")
    @Excel(name = "子订单号", height = 10, width = 22)
    private String orderItemNo;


    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @Excel(name = "下单时间", height = 10, width = 20)
    private String createTime;


    /**
     * 会员店编码
     */
    @ApiModelProperty(value = "会员店编码")
    @Excel(name = "会员店编码", height = 10, width = 20)
    private String buyerCode;

    /**
     * 会员店名称
     */
    @ApiModelProperty(value = "会员店名称")
    @Excel(name = "会员店名称", height = 10, width = 20)
    private String buyerName;


    /**
     * sku编码
     */
    @ApiModelProperty(value = "sku编码")
    @Excel(name = "sku编码", height = 10, width = 20)
    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @Excel(name = "商品名称", height = 10, width = 20)
    private String goodsName;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    @Excel(name = "品牌", height = 10, width = 20)
    private String brandName;

    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位")
    @Excel(name = "销售单位", height = 10, width = 20)
    private String salesUnit;

    /**
     * 商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价
     */
    @ApiModelProperty(value = "商品单价 销售价，阶梯价，区域价，会员分组价，会员等级价")
    @Excel(name = "销售单价", height = 10, width = 20)
    private BigDecimal goodsPrice;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    @Excel(name = "销售数量", height = 10, width = 20)
    private BigDecimal goodsCount;


    /**
     * 商品总金额
     */
    @ApiModelProperty(value = "商品总金额")
    @Excel(name = "商品总金额", height = 10, width = 20)
    private BigDecimal goodsAmount;

    /**
     * 运费金额
     */
    @ApiModelProperty(value = "运费金额")
    @Excel(name = "运费", height = 10, width = 20)
    private String goodsFreight;

    /**
     * 优惠总金额  包含店铺优惠、平台优惠和使用返利的合计
     */
    @ApiModelProperty(value = "优惠总金额  包含店铺优惠、平台优惠和使用返利的合计")
    @Excel(name = "优惠总金额", height = 10, width = 20)
    private String totalDiscountAmount;


    /**
     * 平台优惠金额   分担优惠券金额中，平台优惠金额
     */
    @ApiModelProperty(value = "平台优惠金额   分担优惠券金额中，平台优惠金额")
    @Excel(name = "平台补贴金额", height = 10, width = 20)
    private String platformDiscountAmount;

    /**
     * 店铺优惠金额   分担优惠券金额中，店铺优惠金额
     */
    @ApiModelProperty(value = "店铺优惠金额   分担优惠券金额中，店铺优惠金额")
    @Excel(name = "商家补贴金额", height = 10, width = 20)
    private String shopDiscountAmount;

    /**
     * 议价金额
     */
    @ApiModelProperty(value = "议价金额")
    @Excel(name = "议价金额", height = 10, width = 20)
    private String bargainingAmount;



    /**
     * 计息金额
     */
    @ApiModelProperty(value = "计息金额")
    @Excel(name = "计息金额", height = 10, width = 20)
    private String interestBearingAmount;




    /**
     * 订单行实付金额
     */
    @ApiModelProperty(value = "订单行实付金额")
    @Excel(name = "实付金额", height = 10, width = 20)
    private BigDecimal orderItemPayAmount;


    /**
     * 配送方式
     */
    @ApiModelProperty(value = "配送方式")
    @Excel(name = "配送方式", height = 10, width = 20)
    private String deliveryType;


    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名/自提货人")
    @Excel(name = "收货人姓名/自提货人", height = 10, width = 20)
    private String consigneeName;

    /**
     * 收货人联系电话
     */
    @ApiModelProperty(value = "收货人联系电话/自提联系电话")
    @Excel(name = "收货人联系电话/自提联系电话", height = 10, width = 20)
    private String consigneePhoneNum;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址/自提点")
    @Excel(name = "收货地址/自提点", height = 10, width = 20)
    private String consigneeAddress;


    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    @Excel(name = "车牌号", height = 10, width = 20)
    private String carNumber;


    /**
     * 身份证编号
     */
    @ApiModelProperty(value = "身份证编号")
    @Excel(name = "身份证编号", height = 10, width = 20)
    private String identityCard ;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    @Excel(name = "支付方式", height = 10, width = 20)
    private String  payType;

    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    @Excel(name = "支付渠道", height = 10, width = 20)
    private String  payChannel;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @Excel(name = "支付时间", height = 10, width = 20)
    private String payOrderTime;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    @Excel(name = "订单类型", height = 10, width = 20)
    private String orderType;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    @Excel(name = "订单状态", height = 10, width = 20)
    private String orderStatus;


    /**
     * 订单备注
     */
    @ApiModelProperty(value = "订单备注")
    @Excel(name = "商家备注", height = 10, width = 20)
    private String  orderRemarks;

    /**
     * 买家留言
     */
    @ApiModelProperty(value = "买家留言")
    @Excel(name = "会员备注", height = 10, width = 20)
    private String  buyerRemarks;

    @ApiModelProperty(value="发票收件人姓名")
    @Excel(name = "发票收件人姓名", width = 20)
    private String invoiceReceiverName;

    @ApiModelProperty(value="发票收件人手机号码")
    @Excel(name = "发票收件人手机号码", width = 20)
    private String invoiceReceiverPhone;

    @ApiModelProperty(value="发票接收地址信息")
    @Excel(name = "发票接收地址信息", width = 80)
    private String invoiceAddressDetail;

    @ApiModelProperty(value="邮箱地址")
    @Excel(name = "邮箱地址", width = 30)
    private String emailAddress;

    @ApiModelProperty(value = "发票类型")
    @Excel(name = "发票类型", width = 30)
    private String invoiceType;

    @ApiModelProperty(value = "纳税人识别号")
    @Excel(name = "纳税人识别号", width = 30)
    private String taxManId;

    @ApiModelProperty(value = "开户行名称")
    @Excel(name = "开户行名称", width = 30)
    private String bankName;

    @ApiModelProperty(value = "银行账号")
    @Excel(name = "银行账号", width = 30)
    private String bankAccount;

    @ApiModelProperty(value = "联系电话")
    @Excel(name = "联系电话", width = 30)
    private String contactPhone;

    @ApiModelProperty(value = "发票抬头")
    @Excel(name = "发票抬头", width = 30)
    private String  invoiceNotify;

    @ApiModelProperty(value="注册地址")
    @Excel(name = "注册地址", width = 80)
    private String invoiceAddress;

    @ApiModelProperty(value="推荐人")
    @Excel(name = "推荐人", width = 30)
    private String recommender;

    @ApiModelProperty(value="交易店铺")
    @Excel(name = "交易店铺", width = 30)
    private String shopName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
