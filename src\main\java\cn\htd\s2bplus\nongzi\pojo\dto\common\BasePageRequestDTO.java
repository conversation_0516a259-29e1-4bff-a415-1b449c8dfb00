package cn.htd.s2bplus.nongzi.pojo.dto.common;

import cn.htd.rdc.base.development.framework.core.mp.support.Query;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 基础请求体(含requestId,分页参数)
 * <AUTHOR>
 * @Date 2021/10/20 10:59
 */
@Data
public class BasePageRequestDTO extends Query implements Serializable {
    @ApiModelProperty(value = "请求用户信息",hidden = true)
    private LoginUserDetail user;
}
