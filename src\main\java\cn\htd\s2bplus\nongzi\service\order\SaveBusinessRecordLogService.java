package cn.htd.s2bplus.nongzi.service.order;


import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.dto.common.SaveOperationLogParamDTO;

public interface SaveBusinessRecordLogService {
    /**
     * 保存业务操作日志
     *
     * @param saveOperationLogParamDTO 入参
     * @param loginUser 操作人信息
     * @throws Exception 异常
     */
    void saveRecord(SaveOperationLogParamDTO saveOperationLogParamDTO, LoginUserDetail loginUser) throws Exception;

    /**
     * 获取关键打印日志
     *
     * @param businessKey 业务关键字
     * @param businessType 业务类型
     * @param operationType 操作类型
     * @return 响应
     */
    String getPrintLog(String businessKey, Integer businessType, Integer operationType);
}
