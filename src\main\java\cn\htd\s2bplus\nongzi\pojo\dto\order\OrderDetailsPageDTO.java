package cn.htd.s2bplus.nongzi.pojo.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019-3-4
 */
@Data
@ApiModel
public class OrderDetailsPageDTO implements Serializable {

    private static final long serialVersionUID = -4121168586147398809L;

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Long total;


    /**
     * 总页数
     */
    @ApiModelProperty(value = "总页数")
    private Integer pages;

    /**
     * 订单详情信息
     */
    @ApiModelProperty(value = "订单详情信息")
    private List<OrderDetailResDTO> orderDetailResDTOS;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"total\":")
                .append(total);
        sb.append(",\"pages\":")
                .append(pages);
        sb.append(",\"orderDetailResDTOS\":")
                .append(orderDetailResDTOS);
        sb.append('}');
        return sb.toString();
    }
}
