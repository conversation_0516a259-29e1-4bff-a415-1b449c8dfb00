package cn.htd.s2bplus.nongzi.pojo.dto.yc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "新建店铺类目和品牌")
public class S2BShopCategoryBrandDTO implements Serializable {

    @ApiModelProperty(
            value = "ID",required = false,hidden = true
    )
    private Long id;
    @ApiModelProperty(
            value = "入参用店铺ID组",required = false,hidden = true
    )
    private Long[] shopIds;
    @ApiModelProperty(
            value = "品牌id列表",required = true
    )
    private List<Long> brandIds;
    @ApiModelProperty(
            value = "店铺Id",required = true,hidden = true
    )
    private Long shopId;
    @ApiModelProperty(
            value = "类目Id",required = false,hidden = true
    )
    private Long cid;
    @ApiModelProperty(
            value = "父级类目id  0：无父id  其他为父级类目ID",required = false,hidden = true
    )
    private Long parentCid;
    @ApiModelProperty(
            value = "类目级别",required = false,hidden = true
    )
    private Integer categoryLevel;
    @ApiModelProperty(
            value = "商户Id",required = false,hidden = true
    )
    private Long sellerId;
    @ApiModelProperty(
            value = "类目状态：1.是关闭 2.是开通    -1.是删除",required = false,hidden = true
    )
    private String status;
    @ApiModelProperty(
            value = "评论",required = false,hidden = true
    )
    private String comment;
    @ApiModelProperty(
            value = "通过时间",required = false,hidden = true
    )
    private Date passTime;
    @ApiModelProperty(
            value = "删除标记",required = false,hidden = true
    )
    private Integer deleteFlag;
    @ApiModelProperty(
            value = "创建人Id",required = true,hidden = true
    )
    private Long createId;
    @ApiModelProperty(
            value = "创建人名称",required = true,hidden = true
    )
    private String createName;
    @ApiModelProperty(
            value = "创建时间",required = true,hidden = true
    )
    private Date createTime;
    @ApiModelProperty(
            value = "修改人Id",required = false,hidden = true
    )
    private Long modifyId;
    @ApiModelProperty(
            value = "修改人名称",required = false,hidden = true
    )
    private String modifyName;
    @ApiModelProperty(
            value = "修改时间",required = false,hidden = true
    )
    private Date modifyTime;
    @ApiModelProperty(
            value = "租户ID",required = false,hidden = true
    )
    private Long tenementId;
    @ApiModelProperty(
            value = "是否根据店铺IDgroupBy",required = false,hidden = true
    )
    private Long isGroupBy;
    @ApiModelProperty(
            value = "店铺品牌列表",required = false,hidden = true
    )
    private List<S2BShopBrandDTO> shopBrandDTOList;

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("S2BShopCategoryBrandDTO{");
        sb.append("id=").append(id);
        sb.append(", shopIds=").append(shopIds == null ? "null" : Arrays.asList(shopIds).toString());
        sb.append(", brandIds=").append(brandIds);
        sb.append(", shopId=").append(shopId);
        sb.append(", cid=").append(cid);
        sb.append(", parentCid=").append(parentCid);
        sb.append(", categoryLevel=").append(categoryLevel);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", status='").append(status).append('\'');
        sb.append(", comment='").append(comment).append('\'');
        sb.append(", passTime=").append(passTime);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", createId=").append(createId);
        sb.append(", createName='").append(createName).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyId=").append(modifyId);
        sb.append(", modifyName='").append(modifyName).append('\'');
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", tenementId=").append(tenementId);
        sb.append(", isGroupBy=").append(isGroupBy);
        sb.append(", shopBrandDTOList=").append(shopBrandDTOList);
        sb.append('}');
        return sb.toString();
    }
}
