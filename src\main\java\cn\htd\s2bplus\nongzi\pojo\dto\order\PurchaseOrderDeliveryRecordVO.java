package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class PurchaseOrderDeliveryRecordVO implements Serializable {
    private static final long serialVersionUID = -8303454990707903916L;

    @ApiModelProperty(value = "入库单记录ID")
    private String id;

    @ApiModelProperty(value = "委托函单号")
    private String entrustedOrderNo;

    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "1:提货单 2:销售单")
    private Integer deliveryOrderType;

    @ApiModelProperty(value = "销售单订单号或提货单单号")
    private String deliveryOrderNo;

    @ApiModelProperty(value = "销售单或提货单中自提或配送信息")
    private String deliveryInfo;

    @ApiModelProperty(value = "商品名称,计量单位,发货数量;商品名称,计量单位,发货数量")
    private String skuInfo;

    @ApiModelProperty(value = "发货方式 1:快递公司承运,2:派车配送 3:其他")
    private Integer realDeliveryType;

    @ApiModelProperty(value = "签收状态 10:待签收（待接单） 20:已签收 30:驳回 40:取消 50 待配送 60配送中")
    private String logisticStatus;

    @ApiModelProperty(value = "驳回原因")
    private String dismissReason;

    @ApiModelProperty(value = "物流公司承运(1):物流单号;派车配送(2):车牌号,联系人信息;其他(3):无")
    private String logisticInfo;

    @ApiModelProperty(value = "发货时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;

    @ApiModelProperty(value = "收货人电话密文")
    private String dsReceiverPhone;

    @ApiModelProperty(value = "自提人电话密文")
    private String dsPickUpPhone;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
