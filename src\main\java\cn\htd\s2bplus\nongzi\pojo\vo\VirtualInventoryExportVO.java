package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class VirtualInventoryExportVO implements Serializable {

    @Excel(name = "商品SKU编码",width = 30,orderNum = "1")
    private String skuCode;

    @Excel(name = "商品名称",width = 30,orderNum = "2")
    private String itemName;

    @Excel(name = "规格",width = 30,orderNum = "3")
    private String attributesName;

    @Excel(name = "剩余可卖虚拟库存",width = 30,orderNum = "4")
    private BigDecimal saleVirtualInventory;

    @Excel(name = "已售数量",width = 30,orderNum = "5")
    private BigDecimal soldVirtualInventory;

    @Excel(name = "虚拟库存总数",width = 30,orderNum = "6")
    private BigDecimal totalVirtualInventory;

    @Excel(name = "生效时间",width = 30,orderNum = "7")
    private String effectiveTime;

    @Excel(name = "创建时间",width = 30,orderNum = "8")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 库存状态 1:未开始,2:生效中,3:已过期
     */
    @Excel(name = "状态",width = 30,orderNum = "9")
    private String inventoryStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
