package cn.htd.s2bplus.nongzi.pojo.dto.yc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "品牌ID列表")
public class S2BShopBrandDTO implements Serializable {

    private static final long serialVersionUID = 6571264524514172455L;
    @ApiModelProperty(
            value = "店铺类目品牌ID",required = false
    )
    private Long id;
    @ApiModelProperty(
            value = "入参用店铺ID组",required = false
    )
    private Long[] shopIds;
    @ApiModelProperty(
            value = "店铺id",required = true
    )
    private Long shopId;
    @ApiModelProperty(
            value = "商家ID",required = false
    )
    private Long sellerId;
    @ApiModelProperty(
            value = "品类ID",required = false
    )
    private Long categoryId;
    @ApiModelProperty(
            value = "品牌ID",required = false
    )
    private Long brandId;
    @ApiModelProperty(
            value = "状态;1：申请，2：通过，3：驳回，4：申请删除，5：已删除",required = false
    )
    private String status;
    @ApiModelProperty(
            value = "审核通过时间",required = false
    )
    private Date passTime;
    @ApiModelProperty(
            value = "0、未删除，1、已删除",required = false
    )
    private Integer deleteFlag;
    @ApiModelProperty(
            value = "创建人ID"
    )
    private Long createId;
    @ApiModelProperty(
            value = "创建人名称"
    )
    private String createName;
    @ApiModelProperty(
            value = "创建时间"
    )
    private Date createTime;
    @ApiModelProperty(
            value = "更新人ID",required = false
    )
    private Long modifyId;
    @ApiModelProperty(
            value = "更新人名称",required = false
    )
    private String modifyName;
    @ApiModelProperty(
            value = "更新时间",required = false
    )
    private Date modifyTime;
    @ApiModelProperty(
            value = "租户ID",required = false
    )
    private Long tenementId;
    @ApiModelProperty(
            value = "是否根据店铺IDgroupBy",required = false
    )
    private Long isGroupBy;
    @ApiModelProperty(
            value = "品牌Id集合",required = false
    )
    private List<Long> brandIds;
    @ApiModelProperty(
            value = "上级类目ID",required = false
    )
    private Long parentCid;
    @ApiModelProperty(
            value = "品牌名称",required = false
    )
    private String brandName;
    @ApiModelProperty(
            value = "三级类目名称",required = false
    )
    private String categoryName;
    @ApiModelProperty(
            value = "品类编码集合 - 目前供新系统VMS用",required = false
    )
    private List<Long> categoryIdList;
    @ApiModelProperty(
            value = "品牌编码集合 - 目前供新系统VMS用",required = false
    )
    private List<Long> brandIdList;
    @ApiModelProperty(
            value = "排序类型 空：老系统；1：新vms",required = false
    )
    private int orderByType;
    @ApiModelProperty(
            value = "申请经营类目品牌信息",required = false
    )
    private Map<String, List<Long>> maps;

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("S2BShopBrandDTO{");
        sb.append("id=").append(id);
        sb.append(", shopIds=").append(shopIds == null ? "null" : Arrays.asList(shopIds).toString());
        sb.append(", shopId=").append(shopId);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", brandId=").append(brandId);
        sb.append(", status='").append(status).append('\'');
        sb.append(", passTime=").append(passTime);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", createId=").append(createId);
        sb.append(", createName='").append(createName).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyId=").append(modifyId);
        sb.append(", modifyName='").append(modifyName).append('\'');
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", tenementId=").append(tenementId);
        sb.append(", isGroupBy=").append(isGroupBy);
        sb.append(", brandIds=").append(brandIds);
        sb.append(", parentCid=").append(parentCid);
        sb.append(", brandName='").append(brandName).append('\'');
        sb.append(", categoryName='").append(categoryName).append('\'');
        sb.append(", categoryIdList=").append(categoryIdList);
        sb.append(", brandIdList=").append(brandIdList);
        sb.append(", orderByType=").append(orderByType);
        sb.append(", maps=").append(maps);
        sb.append('}');
        return sb.toString();
    }
}
