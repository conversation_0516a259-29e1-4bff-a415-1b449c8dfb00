package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
public class BuyerUserExportDTO implements Serializable {
    private static final long serialVersionUID = -6326390892558746630L;

    @Excel(name = "会员ID")
    private Long memberId;

    @Excel(name = "会员编码")
    private String memberCode;

    @Excel(name = "公司名称")
    private String companyName;

    @Excel(name = "法人姓名")
    private String artificialPersonName;

    @Excel(name = "会员营业执照注册号")
    private String buyerBusinessLicenseId;

    @Excel(name = "联系人/姓名")
    private String businessPersonName;

    @Excel(name = "注册手机号-脱敏")
    private String businessPersonMobile;

    @Excel(name = "注册手机号-加密")
    private String dsBusinessPersonMobile;

    @Excel(name = "一级分类 门店会员：MD、云店会员：YD、政企客户：ZQ")
    private String firstClassCode;

    @Excel(name = "二级分类"+
            " 会员门店：A1、授权门店：A2、经销代理：A3" +
            "网店店主:B1、社群团长：B2、主播达人：B3"+
            "政府/中大型企业:C1、普通企业:C2")
    private String secondClassCode;

    @Excel(name = "客户性质分类：" +
            "MD-A1：门店会员-会员门店，" +
            "MD-A2：门店会员-授权门店，" +
            "MD-A2：门店会员-经销代理，" +
            "YD-B1：云店会员-网店店主，" +
            "YD-B2：云店客户-社群团长，" +
            "YD-B3：云店客户-主播达人，" +
            "ZQ-C1：政企客户-政府/中大型企业，"+
            "ZQ-C2：政企客户-普通企业")
    private String customerClassCode;

    @Excel(name = "会员认证状态：0-未认证，1-已认证")
    private String isRealNameAuthenticated;

    @Excel(name = "实名认证状态：0-未实名，1-个人实名，2-企业单位实名")
    private String realNameStatus;

    @Excel(name = "审核状态 1审核中、2审核通过、3审核驳回")
    private String verifyStatus;

    @Excel(name = "注册时间")
    private String registerTime;

    @Excel(name = "分销员标识：0-不是，1-是")
    private String distributorRoleFlag;

    @Excel(name = "橙意采:客户分类是否确认(1-确认 2-未确认),其他平台:客户标签来源(不再维护)")
    private String customerLabelSource;

    @Excel(name = "实际经营地址-省市区镇详细地址 实际经营地址-省市区镇详细地址")
    private String actualBusinessAddress;
    @Excel(name = "实际经营地址-省市区镇详细地址 实际经营地址-省市区镇详细地址")
    private String dsActualBusinessAddress;

    @Excel(name = "推荐人编码")
    private String recommenderCode;

    @Excel(name = "会员注册终端来源")
    private String memberFromStr;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
