package cn.htd.s2bplus.nongzi.service.goods;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ExportGoodsInfoReqVO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;


public interface ImportAndExportGoodsService {

    /**
     * 导入货盘商品
     * @param file
     */
    Result<Boolean> importGoodsInfo(MultipartFile file, String businessType,LoginUserDetail user, HttpServletResponse response);

    /**
     * 	导出商品
     *
     */
    void exportGoodsInfo(ExportGoodsInfoReqVO exportGoodsInfoDTO, LoginUserDetail user,HttpServletResponse response);

    /**
     * 	导出发布失败商品
     *
     */
    void exportPublishFailureRecord(LoginUserDetail user,HttpServletResponse response);

}
