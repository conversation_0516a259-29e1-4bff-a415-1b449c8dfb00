package cn.htd.s2bplus.nongzi.service.coupon;

import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.coupon.AutoSendCouponRecordDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.coupon.BalanceInformationDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.coupon.BalanceInformationVO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.CouponRuleVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/8 10:46
 */
public interface CouponService {


    /**
     * 导出会员结余历史信息列表
     * @param balanceInformationDTO
     * @param response
     * @return
     */
    Result<String> exportBalanceInformationByMemberPageList(BalanceInformationDTO balanceInformationDTO, HttpServletResponse response);

    /**
     * 导出结余信息列表查询
     * @param balanceInformationDTO
     * @param response
     * @return
     */
    Result<String> exportBalanceInformationPageList(BalanceInformationDTO balanceInformationDTO, HttpServletResponse response);
    /**
     * 导出结余信息列表查询
     * @param balanceInformationDTO
     * @param response
     * @return
     */
    void getDownUrlAndSaveHistoryReport(Integer current, Integer pageSize, PageResult<List<BalanceInformationVO>> listPageResult, LoginUserDetail loginUser, BalanceInformationDTO balanceInformationDTO,HttpServletResponse response);

    /**
     * 导入批量创建券规则
     * @param file
     * @return
     */
    Result<List<AutoSendCouponRecordDTO>> importBatchCreateCouponRule(MultipartFile file);
}
