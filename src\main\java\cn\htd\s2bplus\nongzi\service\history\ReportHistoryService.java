package cn.htd.s2bplus.nongzi.service.history;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.ReportHistoryVo;


/**
 * 报表历史数据服务
 *
 */
public interface ReportHistoryService {

    /**
     * 分页查询报表生成下载历史
     * @param finishBeginTime
     * @param finishEndTime
     * @param memberId
     * @param businessType
     * @param page
     * @param rows
     * @return
     */
    Result<ReportHistoryVo> queryReportHistoryPage(String finishBeginTime, String finishEndTime, Long memberId, Integer businessType, int page, int rows);


    /**
     * 保存报表生成下载历史
     * @param downloadUrl 下载链接
     * @param businessType 业务类型
     * @param loginUser 登录人信息
     */
    void saveReportHistory(String downloadUrl,Integer businessType,LoginUserDetail loginUser);
}

