package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> gm
 * @create 2024/2/29
 */
@Data
@ApiModel
public class CommissionOrderListDTO implements Serializable {


    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "买家编号")
    private String buyerCode;

    @ApiModelProperty(value = "买家名称")
    private String buyerName;

    @ApiModelProperty(value = "卖家编号")
    private String sellerCode;

    @ApiModelProperty(value = "卖家名称")
    private String sellerName;

    @ApiModelProperty(value = "店铺编号")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "订单商品总数量")
    private BigDecimal totalGoodsCount;

    @ApiModelProperty(value = "订单实付金额")
    private BigDecimal orderPayAmount;

    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "确认收货时间", example = "")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderReceiptTime;

    @ApiModelProperty(value = "线下结算状态")
    private Integer offlineSettlementStatus;

    @ApiModelProperty(value = "渠道编码")
    private String appCode;

    @ApiModelProperty(value = "渠道名称")
    private String appName;

    @ApiModelProperty(value = "商城支付方式  0：在线支付 1：线下汇款")
    private Integer mallPayType;

    @ApiModelProperty(value = "商家类型")
    private Integer sellerType;

    @ApiModelProperty(value = "商家类型名称")
    private String sellerTypeName;

    @ApiModelProperty(value = "运营佣金总金额")
    private BigDecimal totalCommissionAmount;

    @ApiModelProperty(value = "技术佣金金额")
    private BigDecimal totalTechnologyAmount;

    @ApiModelProperty(value = "账存支付方式 ：账存支付 线下汇款")
    private String depositPayType;

    @ApiModelProperty(value = "线下结算状态描述")
    private String offlineSettlementStatusDesc;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
