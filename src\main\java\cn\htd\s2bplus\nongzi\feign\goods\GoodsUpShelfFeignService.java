package cn.htd.s2bplus.nongzi.feign.goods;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.BatchSkuUpShelfRespDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.ImportGoodsInfo;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.SkuUpShelfDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.UpShelfDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * goods feignclent
 * 
 * <AUTHOR>
 *
 */
@FeignClient(name = "s2bplus-goods-service")
public interface GoodsUpShelfFeignService {

	@RequestMapping(value = "/upShelf/importBatchUpdatePrice", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	Result<String> importBatchUpdatePrice(@RequestPart("file") MultipartFile file,
													   @RequestParam(value = "modifyName", required = false) String modifyName,
													   @RequestParam(value = "modifyId", required = false) Long modifyId,
													   @RequestParam(value = "modifyCode", required = false) String modifyCode,
													   @RequestParam(value = "sellerType", required = false) String sellerType,
										  			   @RequestParam(value = "subAccountLoginId", required = false) String subAccountLoginId);



	@PostMapping("/upShelf/batchSkuUpShelf")
	@ApiOperation(value = "sku批量上下架", notes = "sku批量上下架")
	Result<BatchSkuUpShelfRespDTO> batchSkuUpShelf(@RequestBody SkuUpShelfDTO skuUpShelfDTO);

	@PostMapping(value = "/upShelf/batchUpShelfItems")
	@ApiOperation(value = "上下架管理-批量上下架", notes = "上下架管理-批量上下架")
	Result<String> batchUpShelfItems(@RequestBody UpShelfDTO upShelfDTO);

}
