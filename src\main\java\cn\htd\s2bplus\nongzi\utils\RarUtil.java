package cn.htd.s2bplus.nongzi.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
public class RarUtil {
    /**
     * 使用 unrar 命令解压 rar 文件到指定目录
     *
     * @param rarFilePath RAR 文件路径
     * @param destDir 解压目标目录
     * @throws IOException 如果执行命令失败或发生 I/O 错误
     */
    public static void unrar(String rarFilePath, String destDir) throws IOException {
        // 检查 unrar 是否安装
        String[] checkCommand = {"which", "unrar"};
        ProcessBuilder checkBuilder = new ProcessBuilder(checkCommand);
        Process checkProcess = checkBuilder.start();
        try {
            if (checkProcess.waitFor() != 0) {
                throw new IOException("未找到 unrar 命令，请确保已安装 unrar 工具");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("检查 unrar 命令时被中断", e);
        }

        // 创建目标目录（如果不存在）
        Path destPath = Paths.get(destDir);
        if (!Files.exists(destPath)) {
            Files.createDirectories(destPath);
        }

        // 构建 unrar 命令
        String[] command = {"unrar", "x", "-o+", rarFilePath, destDir};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true); // 合并标准错误和标准输出

        // 执行命令
        Process process = processBuilder.start();

        // 处理命令输出
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            log.info("call unrar resp: {}", line); // 这里简单打印到控制台，实际应用中可以进行日志记录等操作
        }
        reader.close();

        // 等待命令执行完成并检查退出状态码
        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new IOException("unrar 命令执行失败，退出代码：" + exitCode);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("unrar 命令执行过程中被中断", e);
        }

        // 删除压缩文件
        deleteFile(rarFilePath);
    }

    private static void deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            boolean deleted = file.delete();
            if (deleted) {
                log.info("压缩文件删除成功：" + filePath);
            } else {
                log.info("压缩文件删除失败：" + filePath);
            }
        } else {
            log.info("压缩文件不存在：" + filePath);
        }
    }

    public static void main(String[] args) {
        try {
            String rarFilePath = "D:\\project\\master\\SpringBoot_MybatisPlus-master\\src\\main\\resources\\test.rar";
            String destDir = "D:\\project\\master\\SpringBoot_MybatisPlus-master\\src\\main\\resources\\output\\";

            unrar(rarFilePath, destDir);
            System.out.println("RAR 文件解压完成！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
