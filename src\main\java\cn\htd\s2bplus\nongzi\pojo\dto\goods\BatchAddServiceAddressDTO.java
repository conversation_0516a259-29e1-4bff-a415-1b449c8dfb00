package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class BatchAddServiceAddressDTO implements Serializable {
    @ApiModelProperty(value = "服务商编码")
    private String serviceProviderCode;

    @ApiModelProperty(value = "服务商名称")
    private String serviceProviderName;

    @ApiModelProperty(value = "服务商会员id")
    private Long serviceProviderMemberId;

    @ApiModelProperty(value = "代收客户编码")
    private String buyerCode;

    @ApiModelProperty(value = "代收客户名称")
    private String buyerName;

    @ApiModelProperty(value = "代收客户Apple id")
    private String appleId;

    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;

    @ApiModelProperty(value = "省")
    private String provinceName;

    @ApiModelProperty(value = "市")
    private String cityName;

    @ApiModelProperty(value = "区")
    private String areaName;

    @ApiModelProperty(value = "镇")
    private String townName;

    @ApiModelProperty(value = "详细")
    private String addressDetail;

    @ApiModelProperty(value = "商家编码")
    private String sellerCode;

    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Long modifyId;

    @ApiModelProperty(value = "更新人名称")
    private String modifyName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
