package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class BatchAddServiceAddressResp extends BatchAddServiceAddressDTO implements Serializable {

    @ApiModelProperty(value = "导入结果")
    private String importResult;

    @ApiModelProperty(value = "失败原因")
    private String failedReason;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
