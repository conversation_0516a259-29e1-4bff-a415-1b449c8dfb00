package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
@ExcelTarget("SellerSkuExport")
public class SubmitAssignmentGoodsDTO implements Serializable {
    private static final long serialVersionUID = 856555174439156384L;

    @ApiModelProperty(value = "商品编码")
    @Excel(name = "商品编码", width = 20)
    private String itemCode;

    @ApiModelProperty(value = "商品ID")
    @Excel(name = "商品ID", width = 20)
    private Long itemId;

    @ApiModelProperty(value = "商品名称")
    @Excel(name = "商品名称", width = 30)
    private String itemName;

    @Excel(name = "商家编码", width = 20)
    @ApiModelProperty(value = "商家编码")
    private String sellerCode;

    @Excel(name = "商家名称", width = 20)
    @ApiModelProperty(value = "商家名称")
    private String sellerName;

    @ApiModelProperty(value = "店铺ID")
    @Excel(name = "店铺ID", width = 20)
    private Long shopId;

    @ApiModelProperty(value = "归属店铺")
    @Excel(name = "归属店铺", width = 20)
    private String shopName;

    @ApiModelProperty(value = "归属渠道")
    private Long appId;

    @ApiModelProperty(value = "渠道名称")
    @Excel(name = "渠道名称", width = 20)
    private String appName;

    @ApiModelProperty(value = "商品sku上架状态：0-未上架 1-已上架")
    @Excel(name = "上架状态", width = 20)
    private String shelfStatus;

    @ApiModelProperty(value = "提报人工号")
    private String createId;

    @ApiModelProperty(value = "提报人名称")
    @Excel(name = "提报人", width = 30)
    private String createName;

    @ApiModelProperty(value = "提报时间")
    @Excel(name = "提报时间", width = 20,databaseFormat = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
