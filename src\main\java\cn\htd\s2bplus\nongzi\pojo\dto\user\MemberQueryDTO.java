package cn.htd.s2bplus.nongzi.pojo.dto.user;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/2/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberQueryDTO implements Serializable {
	private static final long serialVersionUID = -8238739856727020936L;

	/** 会员ID*/
	private Long id;

	/** 唯一标识码*/
	private String memberCode;

	private Long sellerId;

	/** 会员名称*/
	private String companyName;

	/**
	 * 会员/商家类型 1：会员，2：商家
	 */
	private String buyerSellerType;

	/**空白：审核中未成为正式会员，0：无效，1：有效*/
	private String status;
	/**
	 * 空:会员店,1:内部供应商，2:外部供应商 ,3:分销商
	 */
	private String sellerType;

	@Override
	public String toString() {
		final StringBuilder sb = new StringBuilder("{");
		sb.append("\"id\":")
				.append(id);
		sb.append(",\"memberCode\":\"")
				.append(memberCode).append('\"');
		sb.append(",\"companyName\":\"")
				.append(companyName).append('\"');
		sb.append(",\"buyerSellerType\":\"")
				.append(buyerSellerType).append('\"');
		sb.append(",\"status\":\"")
				.append(status).append('\"');
		sb.append(",\"sellerId\":\"")
				.append(sellerId).append('\"');
		sb.append(",\"sellerType\":\"")
				.append(sellerType).append('\"');
		sb.append('}');
		return sb.toString();
	}
}
