package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BatchPutWarehouseEntity implements Serializable {

	@ApiModelProperty(value = "仓库编码")
	@NotBlank(message = "仓库编码不可为空")
	private String warehouseCode;

	@ApiModelProperty(value = "仓库名称")
	private String warehouseName;

	@ApiModelProperty(value = "品牌方编码")
	@NotBlank(message = "品牌方编码不可为空")
	private String brandCode;

	private String brandName;

	private String itemName;

	private String weightUnit;

	private String qualityPeriod;

	private String producer;

	private String batchNumber;

	private Date addTime;

	@ApiModelProperty(value = "库存数量")
	@NotBlank(message = "库存数量不可为空")
	private BigDecimal saleStockNum;

	private String stockArea;

	private String stockLocation;

	@ApiModelProperty(value = "商品库存编码")
	@NotBlank(message = "商品库存编码不可为空")
	private String stockCode;

	private String supplierCode;

	private String supplierName;

	private String standards;

	private String temperatureRange;

	private Long createId;

	private String createName;

	private String orderNo;

	private String skuCode;

	@ApiModelProperty(value = "采购价格")
	private String supplierPrice;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "采购方式")
	private String supplierModel;
	/**
	 * 订单退货用到字段 用于退到批次内
	 * */
	private Long warehouseVirtualGoodsId;
}
