package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * date: 2020/11/19
 */
@Data
public class ExternalChannelManagementDTO implements Serializable {

    private static final long serialVersionUID = 2608948324394337317L;

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    @ApiModelProperty(value = "外部渠道名称")
    private String externalChannelName;
    @ApiModelProperty(value = "渠道类型")
    private Integer appType;
    @ApiModelProperty(value = "创建人id")
    private Long createId;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改人id")
    private Long modifyId;
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    @ApiModelProperty(value = "删除标志 0：未删除 1：已删除")
    private Byte deleteFlag;
    @ApiModelProperty(value = "渠道状态 0:可用 1:不可用")
    private Byte effectiveStatus;

    @ApiModelProperty(value = "是否支持独立模板，0：不支持，1：支持")
    private Byte independentOperationType;

    @ApiModelProperty(value = "渠道图标")
    private String channelIcon;

    @ApiModelProperty(value = "中台渠道id")
    private Long appId;

    @ApiModelProperty(value = "归属公司名称")
    private String belongCompanyName;

    @ApiModelProperty(value = "应用信息列表")
    private List<ExternalChannelApplicationDTO> applicationDTOList;

    @ApiModelProperty(value = "OP登录账号")
    private String loginId;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"id\":")
                .append(id);
        sb.append(",\"channelCode\":\"")
                .append(channelCode).append('\"');
        sb.append(",\"externalChannelName\":\"")
                .append(externalChannelName).append('\"');
        sb.append(",\"appType\":")
                .append(appType);
        sb.append(",\"createId\":")
                .append(createId);
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append(",\"modifyId\":")
                .append(modifyId);
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append(",\"deleteFlag\":")
                .append(deleteFlag);
        sb.append(",\"effectiveStatus\":")
                .append(effectiveStatus);
        sb.append(",\"independentOperationType\":")
                .append(independentOperationType);
        sb.append(",\"channelIcon\":")
                .append(channelIcon);
        sb.append(",\"appId\":")
                .append(appId);
        sb.append(",\"applicationDTOList\":")
                .append(applicationDTOList);
        sb.append(",\"loginId\":")
                .append(loginId);
        sb.append('}');
        return sb.toString();
    }
}