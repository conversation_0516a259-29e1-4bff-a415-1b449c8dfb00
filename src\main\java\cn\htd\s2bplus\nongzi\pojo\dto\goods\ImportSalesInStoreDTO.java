package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ImportSalesInStoreDTO implements Serializable {
    @Excel(name = "服务商编码",width = 20, orderNum = "1")
    @ApiModelProperty(value = "服务商编码")
    private String serviceProviderCode;

    @Excel(name = "appleId编码",width = 20, orderNum = "2")
    @ApiModelProperty(value = "appleId编码")
    private String appleId;

    @Excel(name = "收货人姓名",width = 20, orderNum = "3")
    @ApiModelProperty(value = "收货人姓名")
    private String consigneeName;

    @Excel(name = "收货人电话",width = 20, orderNum = "4")
    @ApiModelProperty(value = "收货人电话")
    private String consigneeMobile;

    @Excel(name = "店铺ID",width = 20, orderNum = "5")
    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @Excel(name = "SKU编码",width = 20, orderNum = "6")
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @Excel(name = "供应商编码",width = 20, orderNum = "7")
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @Excel(name = "仓库编码",width = 20, orderNum = "8")
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "采购部门编码")
    @Excel(name = "采购部门编码",width = 20, orderNum = "9")
    private String departmentCode;

    @Excel(name = "采购部门名称",width = 20, orderNum = "10")
    @ApiModelProperty(value = "采购部门名称")
    private String departmentName;

    @Excel(name = "单价（元）",width = 20, orderNum = "11")
    @ApiModelProperty(value = "商品价格")
    private BigDecimal goodsPrice;

    @Excel(name = "数量",width = 20, orderNum = "12")
    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsCount;

    @Excel(name = "合计金额",width = 20, orderNum = "13")
    @ApiModelProperty(value = "合计金额")
    private BigDecimal totalPrice;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
