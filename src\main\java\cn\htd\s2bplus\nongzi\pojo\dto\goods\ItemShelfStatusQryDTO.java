package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ItemShelfStatusQryDTO implements Serializable {


	private static final long serialVersionUID = 8038646206602711432L;

	/**
	 * 商品参数列表集合
	 */
	@ApiModelProperty(value = "商品参数列表集合")
	private List<ItemSkuPublishStatusDTO> itemSkuInfoList;

	/**
	 * 查询标志 0-根据skuCode查询 1-根据itemCode查询
	 */
	@ApiModelProperty(value = "查询标志 0-根据skuCode查询 1-根据itemCode查询")
	private String queryFlag;
}
