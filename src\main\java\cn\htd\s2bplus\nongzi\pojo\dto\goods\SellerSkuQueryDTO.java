package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @notesription:
 * @date 2022/5/20 14:36
 */
@Data
public class SellerSkuQueryDTO implements Serializable {
    private static final long serialVersionUID = 5922281565922152155L;

    @ApiModelProperty(
            value = "商品编码",
            notes = "商品编码",
            example = "10275855"
    )
    private String itemCode;
    @ApiModelProperty(
            value = "商品名称",
            notes = "商品名称",
            example = "创维电视65M9"
    )
    private String itemName;
    @ApiModelProperty(
            value = "商品sku编码",
            notes = "商品sku编码",
            example = "1000276046"
    )
    private String skuCode;
    @NotNull(
            message = "商家id不能为空"
    )
    @ApiModelProperty(
            value = "商家ID",
            notes = "商家ID",
            example = "155"
    )
    private Long sellerId;
    @ApiModelProperty(
            value = "商品上下架状态列表",
            notes = "0:下架，1：上架",
            example = "1"
    )
    private List<Integer> statusList;
    @ApiModelProperty(
            value = "店铺ID",
            notes = "店铺ID",
            example = "1296"
    )
    private Long shopId;
    @ApiModelProperty(
            value = "店铺名称",
            notes = "店铺名称",
            example = "京东店"
    )
    private String shopName;
    @ApiModelProperty(
            value = "类目ID",
            notes = "类目ID",
            example = "259"
    )
    private Long categoryId;
    @ApiModelProperty(
            value = "类目名称",
            notes = "类目名称",
            example = "白酒"
    )
    private String categoryName;
    @ApiModelProperty(
            value = "品牌ID",
            notes = "品牌ID",
            example = "600"
    )
    private Long brandId;
    @ApiModelProperty(
            value = "品牌名称",
            notes = "品牌名称",
            example = "苹果"
    )
    private String brandName;
    @ApiModelProperty(
            value = "商品ERP编码",
            notes = "商品ERP编码",
            example = "115754"
    )
    private String erpCode;
    @ApiModelProperty(
            value = "基础价(开始)",
            notes = "基础价(开始)",
            example = "2021-09-01 00:00:00"
    )
    private BigDecimal baseMinPrice;
    @ApiModelProperty(
            value = "基础价(结束)",
            notes = "开始时间",
            example = "2021-09-01 00:00:00"
    )
    private BigDecimal baseMaxPrice;
    @ApiModelProperty(
            value = "创建开始时间",
            notes = "开始时间",
            example = "2021-09-01 00:00:00"
    )
    private Date createStartTime;
    @ApiModelProperty(
            value = "创建结束时间",
            notes = "结束时间",
            example = "2021-09-01 00:00:00"
    )
    private Date createEndTime;
    @ApiModelProperty(
            value = "数据标记",
            notes = "0:默认值 1:老中台  2:云原生",
            example = "2"
    )
    private Integer dataTag;
    @ApiModelProperty(
            value = "店铺ID列表",
            notes = "店铺ID列表"
    )
    private List<Long> shopIds;
    @ApiModelProperty(value = "是否直接返回,true-直接返回",notes = "是否直接返回",hidden = true)
    private Boolean flag = false;

    @ApiModelProperty(value = "条形码")
    private String eanCode;

    @ApiModelProperty(value = "上下架状态 0:下架，1：上架")
    private Integer status;

    @ApiModelProperty(value = "商品的类型 1：货盘商品导入",hidden = true)
    private String createItemScene;

    @Override
    public String toString() {
        ReflectionToStringBuilder builder = new ReflectionToStringBuilder(this, MyJsonStyle.JSON_STYLE);
        return builder.toString();
    }
}
