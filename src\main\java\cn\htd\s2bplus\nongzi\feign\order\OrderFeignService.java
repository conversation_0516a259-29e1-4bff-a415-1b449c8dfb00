package cn.htd.s2bplus.nongzi.feign.order;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.common.Pager;
import cn.htd.s2bplus.nongzi.pojo.dto.order.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@FeignClient(name = "s2bplus-order-service")
public interface OrderFeignService {
	/**
	 * 新增报表生成下载历史
	 * @param reportHistoryDTO
	 * @return
	 */
	@RequestMapping(value = "/order/createReportHistory", method = RequestMethod.POST)
	Result<ReportHistoryDTO> createReportHistory(@RequestBody ReportHistoryDTO reportHistoryDTO);


	/**
	 * 查订单及商品行信息
	 * @param orderAndAllStatus
	 * @param current
	 * @param size
	 * @return
	 */
	@PostMapping("/order/orderList")
	Result<OrderDetailsPageDTO> orderList(@RequestBody OrderAndAllStatusReqDTO orderAndAllStatus, @RequestParam(value = "current")int current,
                                          @RequestParam(value = "size") int size);



	/**
	 * 根据订单编号查询订单自提信息
	 * @param orderNo
	 * @return
	 */
	@RequestMapping(value = "/order/queryOrderDeliverySelfPickup", method = RequestMethod.GET)
	Result<OrderDeliverySelfPickupDTO> queryOrderDeliverySelfPickup(@RequestParam(value = "orderNo") String orderNo);


	/**
	 * 确认发货
	 * @param inShipmentDTO
	 * @return
	 */
	@RequestMapping(value = "/order/deliverGood",method = RequestMethod.POST)
	Result deliverGood(@RequestBody InShipmentDTO inShipmentDTO);

	/**
	 * 查询订单信息
	 * @param orderNo
	 * @return
	 */
	@RequestMapping(value = "/order/queryOrderInfo", method = RequestMethod.GET)
	Result<QueryOrderDTO> queryOrderInfo(@RequestParam(value = "orderNo") String orderNo);

	/**
	 * 议价或发货之后修改业务单据
	 * @return
	 */
	@PostMapping(value = "/order/modifyTradeOrderAfterBargain")
	Result<Integer> modifyTradeOrderAfterBargain(@RequestBody TradeOrderBargainDTO dto);

	/**
	 * 分页查询报表生成下载历史
	 * @param page 页数
	 * @param rows 每页记录数
	 * @return 报表生成下载历史
	 */
	@RequestMapping(value = "/order/queryReportHistoryPage", method = RequestMethod.GET)
	Result<Pager<ReportHistoryDTO>> queryReportHistoryPage(
			@RequestParam(value = "finishBeginTime") String finishBeginTime,
			@RequestParam(value = "finishEndtime") String finishEndtime,
			@RequestParam(value = "memberId") Long memberId,
			@RequestParam(value = "page") int page, @RequestParam(value = "rows") int rows);

	/**
	 * 查询批量代客下单结果列表
	 *
	 * @param cond
	 * @param page
	 * @param rows
	 * @return
	 */
	@PostMapping(value = "/batchGuestOrder/queryAllBatchGuestOrderList")
	Result<DataGrid<BatchGuestOrderRecordDTO>> queryAllBatchGuestOrderList(@RequestBody QueryAllBatchGuestOrderCond cond,
																		   @RequestParam(value = "page") Integer page,
																		   @RequestParam(value = "rows") Integer rows);

	@PostMapping(value = "/oss/statement/queryCommissionOrderList")
	@ApiOperation(value = "查询佣金订单明细列表（线下结算）", notes = "查询佣金订单明细列表（线下结算）")
	Result<List<CommissionOrderListDTO>> queryCommissionOrderList(@RequestBody QueryCommissionOrderDTO req);


	@PostMapping(value = "/order/queryCreateTimeByShopList")
	@ApiOperation(value = "根据店铺id查询订单创建时间", notes = "查询佣金订单明细列表（线下结算）")
	Result<Map<Long, Date>> queryCreateTimeByShopList(@RequestBody List<Long> shopIdList);
}
