package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: 80333
 * @Description: 会员非会员导出dto
 * @Date: 2021/5/21 14:59
 */
@Data
@ExcelTarget("MemberExPortDTO")
public class MemberExPortDTO implements Serializable {

    @ApiModelProperty(value = "会员账号")
    @Excel(name = "会员账号", height = 10, width = 30)
    private String memberCode;

    @ApiModelProperty(value = "公司名称")
    @Excel(name = "公司名称", height = 10, width = 30)
    private String companyName;

    @ApiModelProperty(value="法人姓名")
    @Excel(name = "法人姓名",height = 10,width = 30)
    private String artificialPersonName;

    @ApiModelProperty(value = " 法人电话", notes = "法人电话", example = "13899999XXX")
    @Excel(name = "法人电话",height = 10,width = 30)
    private String artificialPersonMobile;

    @ApiModelProperty(value = "客户经理名称")
    @Excel(name = "客户经理名称",height = 10,width = 30)
    private String customerManagerName;

    @ApiModelProperty(value = "注册日期")
    @Excel(name = "注册日期",height = 10,width = 30)
    private String reGistTime;


}
