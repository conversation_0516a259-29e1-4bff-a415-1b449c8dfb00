package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ExternalChannelApplicationDTO implements Serializable {
    private static final long serialVersionUID = -87263205959712469L;

    @ApiModelProperty(value = "id，新增时无需传入")
    private Long id;

    @ApiModelProperty(value = "渠道ID",hidden = true)
    private Long channelId;

    @ApiModelProperty(value = "应用类别编码")
    @NotBlank
    private String applicationTypeCode;

    @ApiModelProperty(value = "应用类别名称，例如：微信小程序")
    @NotBlank
    private String applicationTypeName;

    @ApiModelProperty(value = "应用名称，例如：超级老板pro")
    @NotBlank
    private String applicationName;

    @ApiModelProperty(value = "小程序appid,例如：wx76f715e6b4634431")
    @NotBlank
    private String appletId;

    @ApiModelProperty(value = "应用直播权限设置 0：未开启 1：已开启")
    private Integer applicationLivePermission;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
