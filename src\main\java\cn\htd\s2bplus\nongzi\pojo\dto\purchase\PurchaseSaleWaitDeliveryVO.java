package cn.htd.s2bplus.nongzi.pojo.dto.purchase;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class PurchaseSaleWaitDeliveryVO implements Serializable {

    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "采购单唯一编号,ERP采购单号+平台公司代码(4位)")
    private String purchaseOrderUniqueNo;

    @ApiModelProperty(value = "委托单号")
    private String entrustedOrderNo;

    @ApiModelProperty(value = "配送或自提信息")
    private String receiverOrPickUpDetail;

    /**
     * 分校但或要货单号
     */
    @ApiModelProperty(value = "分销单或要货单号")
    private String subOrderNo;

    @ApiModelProperty(value = "收货人电话密文")
    private String dsReceiverPhone;

    @ApiModelProperty(value = "自提人电话密文")
    private String dsPickUpPhone;

    /**
     * 分销单或要货单号行信息
     */
    @ApiModelProperty(value = "分销单或要货单号行信息")
    private List<PurchaseSaleWaitDeliveryItemVO> purchaseSaleWaitDeliveryItemList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
