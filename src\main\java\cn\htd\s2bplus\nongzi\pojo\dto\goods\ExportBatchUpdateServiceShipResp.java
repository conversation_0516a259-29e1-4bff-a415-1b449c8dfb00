package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title ExportBatchUpdateServiceShipResp
 * @Date: 2025/4/14 16:28
 */
@Data
public class ExportBatchUpdateServiceShipResp implements Serializable {
    private static final long serialVersionUID = -4481179858338632343L;

    @Excel(name = "服务商编码(htd)",width = 20, orderNum = "1")
    @ApiModelProperty(value = "服务商编码")
    private String serviceProviderCode;

    @Excel(name = "代收客户编码(htd)",width = 20, orderNum = "2")
    @ApiModelProperty(value = "代收客户编码")
    private String buyerCode;

    @Excel(name = "代收客户Apple ID",width = 20, orderNum = "3")
    @ApiModelProperty(value = "代收客户代收客户Apple ID")
    private String appleId;

    @Excel(name = "更新关系结果",width = 20, orderNum = "4")
    @ApiModelProperty(value = "更新结果")
    private String updateResult;

    @Excel(name = "失败原因",width = 20, orderNum = "5")
    @ApiModelProperty(value = "失败原因")
    private String failedReason;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
