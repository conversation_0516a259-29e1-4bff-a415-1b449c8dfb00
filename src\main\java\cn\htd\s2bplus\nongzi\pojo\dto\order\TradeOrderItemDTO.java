package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.nongzi.pojo.dto.finance.TradeOrderItemDistributionDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class TradeOrderItemDTO implements Serializable {
    private static final long serialVersionUID = -8326069152514643218L;
    private Long id;
    @ApiModelProperty(value = "单据号 单据号",
            example = "JY10001"
    )
    private String finOrderNo;
    @ApiModelProperty(value = "订单行ID 订单行ID",
            example = "TD1000101"
    )
    private String orderItemId;
    @ApiModelProperty(value = "商品id 商品id",
            example = "1"
    )
    private Long itemId;
    @ApiModelProperty(value = "财务凭证产品编码 财务凭证产品编码",
            example = "10166640"
    )
    private String certProductCode;
    @ApiModelProperty(value = "商品编码 商品编码",
            example = "10166640"

    )
    private String itemCode;
    @ApiModelProperty(value = "商品名称 商品名称",
            example = "澳柯玛空调KFR-35GW/HTD01-A3"
    )
    private String itemName;
    @ApiModelProperty(value = "商品描述 商品描述",
            example = "空凋电器商品"
    )
    private String itemDes;
    @ApiModelProperty(value = "类目编码 类目编码",
            example = "A8270501"
    )
    private String categoryCode;
    @ApiModelProperty(value = "类目名称 类目名称",
            example = "4.5L"
    )
    private String categoryName;
    @ApiModelProperty(value = "品牌编码 品牌编码",
            example = "1"
    )
    private String brandCode;
    @ApiModelProperty(value = "品牌名称 品牌名称",
            example = "曼瑞德"
    )
    private String brandName;
    @ApiModelProperty(value = "价格 价格",
            example = "10.00"
    )
    private BigDecimal price;
    @ApiModelProperty(value = "数量 数量",
            example = "4"
    )
    private BigDecimal quantity;
    @ApiModelProperty(value = "交易金额 交易金额",
            example = "100.00"
    )
    private BigDecimal amount;
    @ApiModelProperty(value = "实际交易金额 实际交易金额",
            example = "100.00"
    )
    private BigDecimal realAmount;
    @ApiModelProperty(value = "其他费用 其他费用",
            example = "10"
    )
    private BigDecimal otherPrice;
    @ApiModelProperty(value = "单位 单位",
            example = "tun"
    )
    private String unit;
    @ApiModelProperty(value = "单据分佣干系人信息 单据分佣干系人信息",
            example = ""
    )
    private List<TradeOrderItemDistributionDTO> distributions;

    public Long getId() {
        return this.id;
    }

    public String getFinOrderNo() {
        return this.finOrderNo;
    }

    public String getOrderItemId() {
        return this.orderItemId;
    }

    public Long getItemId() {
        return this.itemId;
    }

    public String getCertProductCode() {
        return this.certProductCode;
    }

    public String getItemCode() {
        return this.itemCode;
    }

    public String getItemName() {
        return this.itemName;
    }

    public String getItemDes() {
        return this.itemDes;
    }

    public String getCategoryCode() {
        return this.categoryCode;
    }

    public String getCategoryName() {
        return this.categoryName;
    }

    public String getBrandCode() {
        return this.brandCode;
    }

    public String getBrandName() {
        return this.brandName;
    }

    public BigDecimal getPrice() {
        return this.price;
    }

    public BigDecimal getQuantity() {
        return this.quantity;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public BigDecimal getRealAmount() {
        return this.realAmount;
    }

    public BigDecimal getOtherPrice() {
        return this.otherPrice;
    }

    public String getUnit() {
        return this.unit;
    }

    public List<TradeOrderItemDistributionDTO> getDistributions() {
        return this.distributions;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setFinOrderNo(String finOrderNo) {
        this.finOrderNo = finOrderNo;
    }

    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public void setCertProductCode(String certProductCode) {
        this.certProductCode = certProductCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public void setItemDes(String itemDes) {
        this.itemDes = itemDes;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public void setOtherPrice(BigDecimal otherPrice) {
        this.otherPrice = otherPrice;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setDistributions(List<TradeOrderItemDistributionDTO> distributions) {
        this.distributions = distributions;
    }

    public String toString() {
        return "TradeOrderItemDTO(id=" + this.getId() + ", finOrderNo=" + this.getFinOrderNo() + ", orderItemId=" + this.getOrderItemId() + ", itemId=" + this.getItemId() + ", certProductCode=" + this.getCertProductCode() + ", itemCode=" + this.getItemCode() + ", itemName=" + this.getItemName() + ", itemDes=" + this.getItemDes() + ", categoryCode=" + this.getCategoryCode() + ", categoryName=" + this.getCategoryName() + ", brandCode=" + this.getBrandCode() + ", brandName=" + this.getBrandName() + ", price=" + this.getPrice() + ", quantity=" + this.getQuantity() + ", amount=" + this.getAmount() + ", realAmount=" + this.getRealAmount() + ", otherPrice=" + this.getOtherPrice() + ", unit=" + this.getUnit() + ", distributions=" + this.getDistributions() + ")";
    }

    public TradeOrderItemDTO() {
    }
}
