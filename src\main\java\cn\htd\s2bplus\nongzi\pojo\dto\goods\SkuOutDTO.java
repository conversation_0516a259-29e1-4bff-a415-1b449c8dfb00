package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ProjectName: s2bplus-goods-service
 * @Package: cn.htd.s2bplus.goods.pojo.dto.goods
 * @ClassName: SkuOutDTO
 * @Author: 80333
 * @Description: 代课下单商品
 * @Date: 2021/1/19 11:29
 */
@Data
public class SkuOutDTO implements Serializable {

    @ApiModelProperty(
            value = "商品id",
            notes = "商品id",
            example = "118185"
    )
    private Long itemId;
    @ApiModelProperty(
            value = "商品编码",
            notes = "商品编码",
            example = "5000080171"
    )
    private String itemCode;
    @ApiModelProperty(
            value = "skuId",
            notes = "skuId",
            example = "384264"
    )
    private Long skuId;
    @ApiModelProperty(
            value = "sku编码",
            notes = "sku编码",
            example = "1000167108"
    )
    private String skuCode;
    @ApiModelProperty(
            value = "商品名称",
            notes = "商品编码",
            example = "iphone 8"
    )
    private String itemName;
    @ApiModelProperty(
            value = "sku属性",
            notes = "sku属性",
            example = "5860:29010;5861:29012;"
    )
    private String skuAttributes;
    @ApiModelProperty(
            value = "sku属性名称",
            notes = "sku属性",
            example = "颜色:白色;内存:32G;"
    )
    private String skuAttributesName;
    @ApiModelProperty(
            value = "品牌Id",
            notes = "品牌Id",
            example = "822"
    )
    private Long brandId;
    @ApiModelProperty(
            value = "品牌名称",
            notes = "品牌名称",
            example = "乐视"
    )
    private String brandName;
    @ApiModelProperty(
            value = "类目id",
            notes = "类目id",
            example = "352"
    )
    private Long categoryId;
    @ApiModelProperty(
            value = "类目路径",
            notes = "类目路径",
            example = "家用空调>家用空调>挂机>1.5P"
    )
    private String categoryPath;
    @ApiModelProperty(
            value = "卖家id",
            notes = "卖家id",
            example = "2779"
    )
    private Long sellerId;
    @ApiModelProperty(
            value = "上架店铺id",
            notes = "上架店铺id",
            example = "386"
    )
    private Long shopId;
    @ApiModelProperty(
            value = "店铺名称",
            notes = "店铺名称",
            example = "386"
    )
    private String shopName;
    @ApiModelProperty(
            value = "商品上下架状态",
            notes = "0:下架1:上架",
            example = "1"
    )
    private Integer status;
    @ApiModelProperty(
            value = "商品主图",
            notes = "商品主图",
            example = "/8811379294238.jpg"
    )
    private String pictureUrl;
    @ApiModelProperty(
            value = "上下架时间",
            notes = "上下架时间",
            example = "2017-03-29 18:04:00"
    )
    private Date modifyTime;
    @ApiModelProperty(
            value = "销售价格",
            notes = "销售价格",
            example = "100"
    )
    private BigDecimal price;
    @ApiModelProperty(
            value = "库存数量",
            notes = "库存数量",
            hidden = true
    )
    private BigDecimal inventory;
    @ApiModelProperty(
            value = "数据标记 ",
            notes = "0:默认值 1:老中台  2:云原生",
            example = "2"
    )
    private Integer dataTag;

    @ApiModelProperty(
            value = "分销限价",
            notes = "分销限价",
            example = "100"
    )
    private BigDecimal saleLimitedPrice;

    @ApiModelProperty(
            value = "上架操作时间",
            notes = "上架操作时间",
            example = "2017-03-29 18:04:00"
    )
    private Date visableTime;

    @ApiModelProperty(value = "商品单位是否需要转换dto")
    private StockNeedConvertOutDTO stockNeedConvertOutDTO;

    @ApiModelProperty(
            value = "精确库存数量",
            notes = "精确库存数量",
            example = "100"
    )
    private BigDecimal exactInventory;

    @ApiModelProperty(value = "erp编码", notes = "erp编码", example = "222860")
    private String erpCode;

    @ApiModelProperty(value = "仓库编码", notes = "仓库编码", example = "htd2018093232")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称", notes = "仓库名称", example = "测试仓库")
    private String warehouseName;

    @ApiModelProperty(value = "采购部门代码", notes = "采购部门代码", example = "htd202")
    private String departmentCode;

    @ApiModelProperty(value = "采购部门名称", notes = "采购部门名称", example = "采购测试部门")
    private String departmentName;

    @ApiModelProperty(value = "供货商代码", notes = "供应商编码", example = "htd1298119")
    private String supplierCode;

    @ApiModelProperty(value = "供货商名称", notes = "供货商名称", example = "测试供应商")
    private String supplierName;

    @ApiModelProperty(
            value = " 货品编码 ",
            notes = "货品编码",
            example = "400000273"
    )
    private String cargoCode;

    /**
     * 库存对象
     */
    @ApiModelProperty(value = "可用数量", example = "1000")
    private BigDecimal useNumDecimal;

    @ApiModelProperty(value = "占用数量", example = "1000")
    private BigDecimal reserveNumDecimal;

    @ApiModelProperty(value = "库存总数量", example = "1000")
    private BigDecimal totalNumDecimal;

    /**
     * 库存对象
     */
    @ApiModelProperty(value = "可用数量", example = "1000")
    private BigDecimal useNum;

    @ApiModelProperty(value = "库存类型 1共享库存 2专项库存")
    private Integer inventoryType;
    @ApiModelProperty(value = "sku名称")
    private String skuName;
    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"itemId\":")
                .append(itemId);
        sb.append(",\"itemCode\":\"")
                .append(itemCode).append('\"');
        sb.append(",\"skuId\":")
                .append(skuId);
        sb.append(",\"skuCode\":\"")
                .append(skuCode).append('\"');
        sb.append(",\"itemName\":\"")
                .append(itemName).append('\"');
        sb.append(",\"skuAttributes\":\"")
                .append(skuAttributes).append('\"');
        sb.append(",\"skuAttributesName\":\"")
                .append(skuAttributesName).append('\"');
        sb.append(",\"brandId\":")
                .append(brandId);
        sb.append(",\"brandName\":\"")
                .append(brandName).append('\"');
        sb.append(",\"categoryId\":")
                .append(categoryId);
        sb.append(",\"categoryPath\":\"")
                .append(categoryPath).append('\"');
        sb.append(",\"sellerId\":")
                .append(sellerId);
        sb.append(",\"shopId\":")
                .append(shopId);
        sb.append(",\"shopName\":\"")
                .append(shopName).append('\"');
        sb.append(",\"status\":")
                .append(status);
        sb.append(",\"pictureUrl\":\"")
                .append(pictureUrl).append('\"');
        sb.append(",\"modifyTime\":\"")
                .append(modifyTime).append('\"');
        sb.append(",\"price\":")
                .append(price);
        sb.append(",\"inventory\":")
                .append(inventory);
        sb.append(",\"dataTag\":")
                .append(dataTag);
        sb.append(",\"saleLimitedPrice\":")
                .append(saleLimitedPrice);
        sb.append(",\"visableTime\":\"")
                .append(visableTime).append('\"');
        sb.append(",\"stockNeedConvertOutDTO\":")
                .append(stockNeedConvertOutDTO);
        sb.append(",\"exactInventory\":")
                .append(exactInventory);
        sb.append(",\"erpCode\":\"")
                .append(erpCode).append('\"');
        sb.append(",\"warehouseCode\":\"")
                .append(warehouseCode).append('\"');
        sb.append(",\"warehouseName\":\"")
                .append(warehouseName).append('\"');
        sb.append(",\"departmentCode\":\"")
                .append(departmentCode).append('\"');
        sb.append(",\"departmentName\":\"")
                .append(departmentName).append('\"');
        sb.append(",\"supplierCode\":\"")
                .append(supplierCode).append('\"');
        sb.append(",\"supplierName\":\"")
                .append(supplierName).append('\"');
        sb.append(",\"cargoCode\":\"")
                .append(cargoCode).append('\"');
        sb.append(",\"useNumDecimal\":")
                .append(useNumDecimal);
        sb.append(",\"reserveNumDecimal\":")
                .append(reserveNumDecimal);
        sb.append(",\"totalNumDecimal\":")
                .append(totalNumDecimal);
        sb.append(",\"inventoryType\":")
                .append(inventoryType);
        sb.append('}');
        return sb.toString();
    }
}
