package cn.htd.s2bplus.nongzi.pojo.dto.promotion;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title MerchantInfoDTO
 * @Date: 2024/7/20 13:46
 */
@Data
public class MerchantInfoDTO implements Serializable {
    private static final long serialVersionUID = 7075944092078497880L;

    @ExcelProperty("商家账号")
    @ApiModelProperty(value = "商家账号")
    private String sellerCode;

    @ExcelProperty("商家名称")
    @ApiModelProperty(value = "商家名称")
    private String sellerName;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
    }
}
