package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WarehouseVirtualReqDTO implements Serializable {

	@ApiModelProperty(value="仓库编码")
	private String warehouseCode;

	@ApiModelProperty(value = "仓库名称")
	private String warehouseName;

	@ApiModelProperty(value = "品牌方编码")
	private String brandCode;


	@ApiModelProperty(value = "页面")
	private  Integer page;

	@ApiModelProperty(value = "行数")
	private  Integer rows;
}
