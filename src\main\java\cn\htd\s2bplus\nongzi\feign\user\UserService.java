package cn.htd.s2bplus.nongzi.feign.user;

import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.config.FeignConfiguration;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.RuConsigAddressDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.AddGroupDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.EditGroupDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.MemberGroupInfo;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.QueryMemberGroupRelationDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.MemberExtendInfoDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.rebate.RebateCashConfigDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.rebate.RebateLimitQuotaVO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.*;
import cn.htd.s2bplus.nongzi.pojo.vo.MemberGroupRelationVO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * User:wangrungeng
 * Date:2020/3/14
 * Description:
 **/
@Component
@FeignClient(value="s2bplus-user-service",configuration = FeignConfiguration.class)
public interface UserService {

    @PostMapping("/member/batchMemberNameByCodeList")
    @ApiOperation(value = "根据会员编码批量查询会员名称")
    Result<List<BatchMemberVO>> batchMemberNameByCodeList(@RequestBody List<String> memberCodeList);

    /**
     * 根据会员编码查询会员信息
     * @param memberCode
     * @return
     */
    @PostMapping("/member/memberInfo")
    Result<MemberBaseInfoDTO> memberBaseInfo(@RequestParam(value = "memberCode") String memberCode);

    /**
     * 查询返利渠道限额
     *
     * @return
     */
    @GetMapping("/rebate/queryChannelLimit")
    Result<List<RebateLimitQuotaVO>> queryRebateChannelLimitQuota();

    /**
     * 批量新增返利额度配置
     *
     * @param list
     * @return
     */
    @PostMapping("/rebate/batchOSSRebateQuotaInfo")
    Result<Boolean> batchOSSRebateQuotaInfo(@RequestBody List<RebateCashConfigDTO> list);

    /**
     * 获取分组详情
     * @param groupId
     * @return
     */
    @GetMapping("/memberGroup/selectCusBuyerGroupById")
    Result<MemberGroupDTO> selectCusBuyerGroupById(@RequestParam("groupId") String groupId);


    @ApiOperation(value = "查询分组会员信息",notes = "查询分组会员信息")
    @PostMapping("/memberGroup/selectMemberGroupListInfo")
    PageResult<List<MemberGroupResponseDTO>> selectMemberGroupListInfo(@RequestBody MemberGroupRequest memberGroupDTO);

    /**
     *根据loginId获取用户信息
     * @param
     * @return
     */
    @PostMapping("/oss/seller/getUserByLoginId")
    Result<LoginUserDetail> getUserByLoginId(@RequestParam(value = "loginId", required = true) String loginId);

    @PostMapping("/member_extend/selectInfoAnotherPay")
    Result<Map<String, MemberExtendInfoDTO>> selectInfoAnotherPay(@RequestBody List<String> memberCodes);

    /**
     * 获取收货地址详情
     * @param addressId
     * @return
     */
    @PostMapping("/member/addressDetail")
    Result<RuConsigAddressDTO> addressInfo(@RequestParam(value = "addressId", required = true) Long addressId);

    @PostMapping("/userApi/queryNoOrMemberInfoList")
    Result<List<MemberBaseInfoDTO>> queryNoOrMemberInfoList(@RequestBody MemberBaseInfoDTO memberBaseInfoDTO);

    @GetMapping("/userApi/getManagerName")
    Result<String> getManagerName(@RequestParam(value = "curBelongSellerId",required = true) Long curBelongSellerId,
                                  @RequestParam(value = "curBelongManagerId",required = true) String curBelongManagerId);

    @PostMapping("/userApi/selectNoMemberList")
    PageResult<List<MyNoMemberVo>> selectNoMemberList(@RequestBody MemberSearchDTO memberSearch,
                                                      @RequestParam(value = "sellerId",required = true)Long sellerId,
                                                      @RequestParam(value = "sellerType",required = true)String sellerType);

    /**
     * oop - 会员管理 - 查询我的会员、担保会员列表
     * @param memberSearch
     * @return
     */
    @PostMapping("/userApi/selectMemberList")
    PageResult<List<MyMemberVo>> selectMemberList(@RequestBody MemberSearchDTO memberSearch,
                                                  @RequestParam(value = "sellerId",required = true)Long sellerId,
                                                  @RequestParam(value = "sellerType",required = true)String sellerType);



    @PostMapping("/memberGroup/queryMemberGroupInfoList")
    Result<List<MemberGroupRelationVO>> queryMemberGroupInfoList(@RequestBody List<QueryMemberGroupRelationDTO> memberGroupDTOList);

    @PostMapping("/memberGroup/selectMemberGroup")
    PageResult<List<MemberGroupInfo>> memberGroupList(@RequestBody MemberGroupInfo memberGroupInfo);

    @PostMapping("/memberGroup/insertMemberGroupInfo")
    Result<Long> insertMemberGroupInfo(@RequestBody AddGroupDTO addGroupDTO);

    @PostMapping("/memberGroup/updateMemberGroupInfo")
    Result<Boolean> updateMemberGroupInfo(@RequestBody EditGroupDTO editGroupDTO);

    @GetMapping("/memberGroup/estimateMemberWhite")
    Result<Boolean> estimateMemberWhite(@RequestParam("memberCode") String memberCode);

    @PostMapping("/memberGroup/queryGroupBySellerIds")
    Result<List<MemberGroupInfo>> queryGroupBySellerIds(@RequestBody List<Long> sellerIds);
}
