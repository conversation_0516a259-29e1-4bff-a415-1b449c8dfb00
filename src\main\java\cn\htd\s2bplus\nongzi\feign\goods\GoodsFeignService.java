package cn.htd.s2bplus.nongzi.feign.goods;

import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.ItemDecimalCheckDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.MdmCategoryQueryDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.erp.PurchasingDepartmentDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ExportPublishFailureGoodsDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.*;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.EditGroupDTO;
import cn.htd.s2bplus.nongzi.pojo.vo.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * goods feignclent
 *
 * <AUTHOR>
 *
 */
@FeignClient(name = "s2bplus-goods-service")
public interface GoodsFeignService {

	@RequestMapping("/basedata/getBrandByName")
	Result<Brand> getBrandByName(@RequestParam(value = "brandName", required = true) String brandName);

	/**
	 * 商品明细报表生成
	 * @param record
	 * @return
	 */
	@PostMapping("/goods/insertSelective")
	Result insertSelective(@RequestBody ReportHistory record);


	/**
	 * 根据商品/sku编码、店铺id，批量查询商品/sku信息
	 * @param skuQueryDTOList
	 * @return
	 */
	@PostMapping("/item/batchQueryItemInfo")
	Result<List<SkuOutDTO>> batchQueryItemInfo(@RequestBody List<SkuQueryDTO> skuQueryDTOList);



	/**
	 * 查询提报商品池
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/assignment/getGoodsByAssignmentId",method = RequestMethod.POST)
	@ApiOperation(value = "查询提报商品池", notes = "查询提报商品池")
	Result<List<SubmitAssignmentGoodVO>> getGoodsByAssignmentId(@RequestParam(value = "submitAssignmentId") Long submitAssignmentId,@RequestParam(value = "employeeId") String employeeId);


	@RequestMapping(value = "/newItem/queryItemListByItemIds", method = RequestMethod.POST)
	@ApiOperation(value = "根据商品ids查询商品列表接口", notes = "根据商品ids查询商品列表接口")
	Result<List<ItemDTO>> queryItemListByItemIds(@RequestBody List<Long> itemIdList);


	@RequestMapping("/newItem/batchItemShelfStatus")
	@ApiOperation(value = "批量获取商品上下架状态", notes = "批量获取商品上下架状态")
	Result<List<ShelfData>> batchItemShelfStatus(@RequestBody ItemShelfStatusQryDTO itemShelfStatusQryDTO);

	/**
	 * 批量创建/更新虚拟库存
	 * @param goodsStockList 商品库存集合
	 * @return 批量创建/更新结果
	 */
	@PostMapping("/goods/stock/batchSaveVirtualInventory")
	Result<Long> batchSaveVirtualInventory(@RequestBody List<GoodsStockDTO> goodsStockList);

	/**
	 * 查询虚拟库存列表
	 * @param queryVirtualInventoryDTO 查询条件
	 * @return 虚拟库存列表
	 */
	@PostMapping("/goods/stock/queryVirtualInventoryListNoPage")
	Result<List<QueryVirtualInventoryVO>> queryVirtualInventoryList(@RequestBody QueryVirtualInventoryDTO queryVirtualInventoryDTO);

	/**
	 * 批量商品校验小数点规则
	 *
	 * @param itemDecimalCheckList
	 * @return
	 */
	@PostMapping("/newItem/checkIsItemDecimal")
	Result<List<ItemDecimalCheckDTO>> checkIsItemDecimal(@RequestBody List<ItemDecimalCheckDTO> itemDecimalCheckList);

	@PostMapping("/channel/selectAllChannel")
	Result<List<ExternalChannelManagementDTO>> selectAllChannel(@RequestBody ExternalChannelManagementDTO channelManagementDTO);

	@PostMapping("/channel/queryChannelName")
	@ApiOperation(value = "根据渠道编码查询渠道名称", notes = "根据渠道编码查询渠道名称")
	Result<Map<String,String>> queryChannelName(@RequestBody List<String> channelCodeList);

	@PostMapping("/intentionAddressWarehouseRecord/queryIntentionAddressByPage")
	@ApiOperation(value = "分页查询分销商地址对应关系", notes = "分页查询分销商地址对应关系")
	PageResult<List<IntentionAddressWarehouseDTO>> queryIntentionAddressByPage(@RequestBody QueryIntentionAddressPageDTO queryIntentionAddressPageDTO);

	@PostMapping("/intentionAddressRecord/queryIntentionAddressRecordByPage")
	@ApiOperation(value = "分页查询智能报价单地址", notes = "分页查询智能报价单地址")
	PageResult<List<IntentionAddressDTO>> queryIntentionAddressRecordByPage(@RequestBody QuerySalesInStorePageDTO querySalesInStorePageDTO);

	@PostMapping("/intentionAddressRecord/batchAdd")
	@ApiOperation(value = "批量新增到店销售数据", notes = "批量新增到店销售数据")
	Result<Boolean> batchAdd(@RequestBody List<IntentionAddressRecordAddDTO> batchAddList);

	@PostMapping("/intentionAddressRecord/batchAddSalesInStore")
	@ApiOperation(value = "批量新增到店销售计划", notes = "批量新增到店销售计划")
	Result<Boolean> batchAddSalesInStore(@RequestBody List<IntentionAddressRecordAddDTO> batchAddList);

	@PostMapping("/intentionAddressWarehouseRecord/updateConsigneeInfo")
	@ApiOperation(value = "更新地址收货人信息", notes = "更新地址收货人信息")
	Result<Boolean> updateConsigneeInfo(@RequestBody List<IntentionAddressRecordAddDTO> intentionAddressWarehouseRecords);

	@RequestMapping(value = "/upShelf/queryShopInfoBySellerId", method = RequestMethod.POST)
	Result<List<ShopInfoDTO>> queryShopInfoByCondition(@RequestBody ShopInfoDTO shopInfoDTO);

	@PostMapping("/goods/pageListUpShelfSku")
	Result<DataGrid<SkuOutDTO>> pageListUpShelfSku(@RequestBody SkuQueryDTO skuQueryDTO,
												   @RequestParam(value = "page") int page,
												   @RequestParam(value = "rows") int rows);

	@GetMapping(value = "/erp/queryPurchaseDepartment")
	@ApiOperation(value = "拆单前统一查询接口", notes = "拆单前统一查询接口")
	Result<List<PurchasingDepartmentDTO>> queryPurchaseDepartment(
			@RequestParam(value = "supplierCode") String supplierCode,
			@RequestParam(value = "productCode") String productCode);

	@GetMapping("/intentionAddressRecord/getIntentionAddressList")
	@ApiOperation(value = "根据意向单号查询到店地址", notes = "根据意向单号查询到店地址")
	Result<List<IntentionAddressDTO>> getIntentionAddressList(@RequestParam("intentionNo") String intentionNo);

	@GetMapping("/intentionAddressRecord/queryConsigneeIdByBuyerAndServiceProvider")
	@ApiOperation(value = "根据服务商和会员查询地址id", notes = "根据服务商和会员查询地址id")
	Result<Long> queryConsigneeIdByBuyerAndServiceProvider(@RequestParam("serviceProviderCode") String serviceProviderCode,
																  @RequestParam("appleId") String appleId,
														   @RequestParam("sellerCode") String sellerCode);

	/**
	 * 批量查询商品信息，不返回地区信息
	 *
	 * @return
	 */
	@PostMapping("/newItem/getItemListIgnoreSalesAreaBySkuCodes")
	Result<List<ItemVO>> getItemListIgnoreSalesAreaBySkuCodes(@RequestBody SalesAreaSkuQueryDTO salesAreaSkuQueryDTO);

	/**
	 * 批量新增分销商地址对应关系
	 */
	@PostMapping("/intentionAddressWarehouseRecord/batchAddServiceAddress")
    Result<List<BatchAddServiceAddressResp>> batchAddServiceAddress(List<BatchAddServiceAddressDTO> batchAddServiceAddressDTOS);

	@PostMapping("/intentionAddressWarehouseRecord/batchUpdateServiceShip")
	Result<List<BatchUpdateServiceShipResp>>batchUpdateServiceShip(@RequestBody List<BatchUpdateServiceShipDTO> batchUpdateServiceShipDTOS);

	@PostMapping("/goods/batchQuerySkuInfo")
	Result<List<SkuOutDTO>> batchQuerySkuInfo(@RequestBody List<ShopSkuQueryDTO> shopSkuQueryDTOList);

	@PostMapping("/shop/selectShopMemberList")
	@ApiOperation(value = "店铺商家列表", notes = "店铺商家列表")
	Result<DataGrid<MemberShopInfoResDTO>> selectShopMemberList(@RequestBody MemberShopInfoReqDTO memberShopInfoReqDTO);

	@PostMapping("/virtual/warehouse/batchPutWarehouse")
	@ApiOperation(value = "批量入库", notes = "批量入库")
	Result<String> batchPutWarehouse(@RequestBody BatchPutWarehouseApiDTO reqDTO);

	@PostMapping("/virtual/warehouse/batchOutWarehouse")
	@ApiOperation(value = "批量出库", notes = "批量出库")
	Result<String> batchOutWarehouse(@RequestBody BatchOutWarehouseApiDTO reqDTO);

	@PostMapping("/virtual/warehouse/pageWarehouseByParam")
	@ApiOperation(value = "分页查询仓库信息", notes = "分页查询仓库信息")
	PageResult<List<WarehouseVirtualVO>> pageWarehouseByParam(@RequestBody WarehouseVirtualReqDTO queryDTO);

	@PostMapping("/virtual/warehouse/checkSaleStockNum")
	@ApiOperation(value = "批量出库-批量校验库存扣减数量", notes = "批量出库-批量校验库存扣减数量")
	Result<List<CheckStockNumErrorVO>> checkSaleStockNum(@RequestBody BatchOutWarehouseReqDTO dto);

	@PostMapping("/virtual/warehouse/checkStockCode")
	@ApiOperation(value = "校验商品库存编码是否已被使用", notes = "校验商品库存编码是否已被使用")
	Result<Integer> checkStockCode(@RequestBody CheckStockCodeReqDTO reqDTO);
	@PostMapping("/goods/stock/selectAllBySkuCodesOop")
	Result<List<GoodsStockDTO>> selectAllBySkuCodesOop(@RequestBody QueryGoodsStockDTO queryGoodsStockDTO);

	@PostMapping("/platformItemPool/queryRaisePriceList")
	@ApiOperation(value = "加价设置列表查询", notes = "加价设置列表查询")
	Result<List<RaisePriceDTO>> queryRaisePriceList(@RequestBody List<RaisePriceQueryDTO> raisePriceQueryDTOList);

	@PostMapping("/newItem/updateItemMemberGroup")
	Result<Boolean> updateItemMemberGroup(@RequestBody EditGroupDTO editGroupDTO);


	@PostMapping("/importGoods/saveImportGoods")
	@ApiOperation(value = "保存导入商品", notes = "保存导入商品")
	Result<Boolean> saveImportGoods(@RequestBody List<ImportGoodsInfo> importGoodsInfoList);

	@GetMapping("/importGoods/queryPublishFailureGoods")
	@ApiOperation(value = "发布失败商品", notes = "发布失败商品")
	Result<List<ExportPublishFailureGoodsDTO>> queryPublishFailureGoods(@RequestParam(value = "businessType")String businessType,
																		@RequestParam(value = "sellerCode")String sellerCode);


	@PostMapping("/mdm/receiveMdm/queryMdmCategoryList")
	@ApiOperation(value = "分页查询管理类目", notes = "分页查询管理类目")
	PageResult<List<MdmCategoryVO>> queryMdmCategoryList(@RequestBody MdmCategoryQueryDTO mdmCategoryQueryDTO);

	@PostMapping(value = "/saleCategory/selectNewCategoryList")
	@ApiOperation(value = "查询销售列表集合", notes = "查询销售列表集合")
	Result<List<CategoryDTO>> selectNewCategoryList(@RequestBody CategoryDTO categoryDTO);

	@GetMapping("/salecategory/querySaleCategoryPath")
	@ApiOperation(value = "根据三级销售类目查询类目", notes = "根据三级销售类目查询类目")
	Result<SaleCategoryPathDTO> querySaleCategoryPath(@RequestParam(value = "thirdSaleCid")Long thirdSaleCid);

	@GetMapping("/basedata/getBrandList")
	@ApiOperation(value = "品牌列表查询",notes = "品牌列表查询")
	Result<List<Brand>> getBrandList(@RequestParam(value = "categoryId") Long categoryId);

    @PostMapping(value = "/specification/queryAllItemAttributeList")
    @ApiOperation(value = "查询商家类目下所有规格名称及属性信息", notes = "查询商家类目下所有规格名称及属性信息")
    Result<List<ItemAttributeDTO>> queryAllItemAttributeList(@RequestBody SpecificationInfoDTO dto);

	@PostMapping(value = "/specification/updateSalesSpecifications")
	@ApiOperation(value = "批量修改销售规格", notes = "批量修改销售规格")
	Result<Boolean> updateSalesSpecifications(@RequestBody EditItemSalesSpecificationsDTO dto);

	@PostMapping("/goodsImage/batchSaveGoodsImage")
	@ApiOperation(value = "批量保存商品图片", notes = "批量保存商品图片")
	Result<String> batchSaveGoodsImage(@RequestBody List<GoodsImagesEntity> goodsImagesEntities);
}
