package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.nongzi.pojo.dto.goods.SellerSku;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SellerSkuVO implements Serializable {

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Long total;


    /**
     * 总页数
     */
    @ApiModelProperty(value = "总页数")
    private Integer pages;

    /**
     * 商品详情信息
     */
    @ApiModelProperty(value = "商品详情信息")
    List<SellerSku> sellerSkuList;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"total\":")
                .append(total);
        sb.append(",\"pages\":")
                .append(pages);
        sb.append(",\"sellerSkuList\":")
                .append(sellerSkuList);
        sb.append('}');
        return sb.toString();
    }
}
