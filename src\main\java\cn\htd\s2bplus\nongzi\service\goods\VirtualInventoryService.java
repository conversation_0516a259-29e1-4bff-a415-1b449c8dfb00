package cn.htd.s2bplus.nongzi.service.goods;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.QueryVirtualInventoryDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Repository
public interface VirtualInventoryService {

    /**
     * 批量导入虚拟库存
     * @param file 导入文件
     * @param userDetail 登录人信息
     * @param shopId 店铺id
     * @return 导入结果
     */
    Result<Long> importVirtualInventoryList(MultipartFile file,LoginUserDetail userDetail,Long shopId);

    /**
     * 批量导出虚拟库存
     * @param queryVirtualInventoryDTO 查询条件
     * @param response 响应
     */
    void exportVirtualInventoryList(QueryVirtualInventoryDTO queryVirtualInventoryDTO, HttpServletResponse response);
}
