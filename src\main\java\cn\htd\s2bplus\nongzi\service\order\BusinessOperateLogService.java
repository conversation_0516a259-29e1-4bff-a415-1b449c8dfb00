package cn.htd.s2bplus.nongzi.service.order;


import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.dto.common.SaveOperationLogParamDTO;

public interface BusinessOperateLogService {

    /**
     * 记录商家操作日志
     * @param user
	 * @param businessKey
     * @param businessType
	 * @param operationType
	 * @param operationRecord
     */
    void recordBusinessOperateLog(LoginUserDetail user, String businessKey, Integer businessType, Integer operationType, String operationRecord);

	/**
	 * 组装日志记录数据
	 * @param businessKey
	 * @param operationRecord
	 * @param businessType
	 * @param operationType
	 */
	SaveOperationLogParamDTO getBusinessRecordLogData(String businessKey, String operationRecord, Integer businessType, Integer operationType);
}
