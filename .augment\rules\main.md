---
type: "always_apply"
---

项目概述
项目名称: s2bplus-nongzi-api
项目描述: S2BPLUS农资事业部开放运营API
技术栈: Spring Boot + Spring Cloud + MyBatis + Maven

技术架构
1. 基础架构
框架: Spring Boot 2.x + Spring Cloud
服务发现: Nacos (配置中心 + 注册中心)
数据访问: MyBatis
构建工具: Maven
Java版本: JDK 1.8
2. 核心依赖
基础框架: base-development-framework-cloud
通用组件: s2bplus-common (4.0.20-release)
服务熔断: Netflix Hystrix
Excel处理: EasyPOI + EasyExcel
文件存储: 阿里云OSS
身份认证: SSO + 网易验证码
PDF处理: iText
对象映射: Dozer
包结构详解
主包结构 (cn.htd.s2bplus.nongzi)
src/main/java/cn/htd/s2bplus
├── S2bplusNongziApiApplication.java    # 启动类
├── annotation/                         # 自定义注解
├── config/                            # 配置类
├── contants/                          # 常量定义
├── controller/                        # 控制器层
├── enums/                            # 枚举类
├── feign/                            # Feign客户端
├── handle/                           # 异常处理器
├── interceptor/                      # 拦截器
├── mapper/                           # 数据访问层

1. 控制器层 (Controller)
按业务模块划分：

goods/: 商品相关控制器
order/: 订单相关控制器
user/: 用户相关控制器
shop/: 店铺相关控制器
2. 服务层 (Service)
业务逻辑层，按模块组织：

goods/: 商品服务 (商品管理、导入导出等)
order/: 订单服务
member/: 会员服务
shop/: 店铺服务
distribute/: 分销服务
3. Feign客户端层
微服务间调用：

auth/: 认证服务调用
finance/: 财务服务调用
goods/: 商品服务调用
middleground/: 中台服务调用
order/: 订单服务调用
purchase/: 采购服务调用
user/: 用户服务调用
4. 数据对象层 (POJO)
dto/: 数据传输对象
common/: 通用DTO
excel/: Excel相关DTO
goods/: 商品相关DTO
order/: 订单相关DTO
user/: 用户相关DTO
vo/: 视图对象
5. 配置层 (Config)
NongZiNacosConfig: Nacos配置类
OssNacosConfig: OSS配置类
WebConfiguration: Web配置类
核心特性
1. 微服务架构
使用Spring Cloud构建微服务体系
通过Feign实现服务间调用
Nacos作为配置中心和注册中心
2. 数据访问
MyBatis作为ORM框架
支持分页查询
统一的数据访问层设计
3. 安全认证
集成SSO单点登录
自定义拦截器进行权限控制
支持网易验证码

4. 文件处理
阿里云OSS文件存储
Excel导入导出功能
PDF文档生成
5. 监控与日志
集成日志框架
支持业务监控
异常统一处理