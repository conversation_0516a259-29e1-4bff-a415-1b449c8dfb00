package cn.htd.s2bplus.nongzi.pojo.dto.goods;


import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class IntentionAddressWarehouseDTO implements Serializable {
    private static final long serialVersionUID = 5918189531947898840L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "卖家编号")
    private String sellerCode;

    @ApiModelProperty(value = "服务商编号")
    private String serviceProviderCode;

    @ApiModelProperty(value = "服务商姓名")
    private String serviceProviderName;

    @ApiModelProperty(value = "收货地址ID")
    private Long consigneeId;

    @ApiModelProperty(value = "买家编码")
    private String buyerCode;

    @ApiModelProperty(value = "买家姓名")
    private String buyerName;

    @ApiModelProperty(value = "apple_id")
    private String appleId;

    @ApiModelProperty(value = "收货人姓名")
    private String consigneeName;

    @ApiModelProperty(value = "收货人电话")
    private String consigneeMobile;

    @ApiModelProperty(value = "省名称")
    private String consigneeAddressProvinceStr;

    @ApiModelProperty(value = "市名称")
    private String consigneeAddressCityStr;

    @ApiModelProperty(value = "区/县/市名称")
    private String consigneeAddressDistrictStr;

    @ApiModelProperty(value = "收货地址-镇",example = "麒麟镇")
    private String consigneeAddressTownStr;

    @ApiModelProperty(value = "详细地址")
    private String consigneeAddressDetail;

    @ApiModelProperty(value = "地址数据类型 0:试导入 1:苹果宝尊")
    private Integer consigneeType;

    @ApiModelProperty(value = "0:待处理 1:成功")
    private Integer status;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
