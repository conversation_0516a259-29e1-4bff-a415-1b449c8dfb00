package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.ItemSkuDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商品VO
 *
 */
@Data
public class ItemVO implements Serializable {
    private static final long serialVersionUID = -297276607928007871L;

    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "卖家编码")
    private String sellerCode;

    @ApiModelProperty(value = "卖家名")
    private String companyName;

    @ApiModelProperty(value = "源卖家名")
    private String srcCompanyName;

    @ApiModelProperty(value = "商店名称")
    private String shopName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "租户ID")
    private Long tenementId;

    /**
     * 商品状态0:待审核 ,1：审核通过,2：审核驳回，3：待ERP上行库存及价格或外部商品品类价格待映射，4 未上架，5：已上架  6：已删除
     */
    @ApiModelProperty(value = "商品状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "卖家ID")
    private Long sellerId;


    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "类目ID")
    private Long cid;

    @ApiModelProperty(value = "店铺类目")
    private Long shopCid;


    /**
     * 品牌ID
     */
    @ApiModelProperty(value = "品牌")
    private Long brand;

    @ApiModelProperty(value = "品牌名")
    private String brandName;


    @ApiModelProperty(value = "型号")
    private String modelType;


    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 特征,按业务打标签
     */
    @ApiModelProperty(value = "特征")
    private String features;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "长")
    private BigDecimal length;

    @ApiModelProperty(value = "宽")
    private BigDecimal width;

    @ApiModelProperty(value = "高")
    private BigDecimal height;

    @ApiModelProperty(value = "广告词、商品卖点")
    private String ad;

    @ApiModelProperty(value = "产地")
    private String origin;

    @ApiModelProperty(value = "商品简称")
    private String itemAbbr;

    /**
     * 渠道编码: 10 内部供应商 20 外部供应商 3010 京东商品＋
     */
    @ApiModelProperty(value = "渠道编码")
    private String productChannelCode;

    /**
     * 是否来自产品 0 否 1 是
     */
    @ApiModelProperty(value = "是否来自模板")
    private Integer isSpu;

    @ApiModelProperty(value = "渠道id")
    private Long appId;


    @ApiModelProperty(value = "产品模板ID")
    private Long spuId;

    @ApiModelProperty(value = "产品编码")
    private String spuCode;

    @ApiModelProperty(value = "产品名称")
    private String spuName;

    @ApiModelProperty(value = "商品主图URL")
    private String itemPictureUrl;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "商城指导价")
    private BigDecimal guidePrice;

    @ApiModelProperty(value = "包装清单")
    private String packingList;


    @ApiModelProperty(value = "售后服务")
    private String afterService;

    @ApiModelProperty(value = "上架时间")
    private Date listtingTime;

    @ApiModelProperty(value = "下架时间")
    private Date delistingTime;


    @ApiModelProperty(value = "运费模版ID")
    private Long shopFreightTemplateId;

    /**
     * 运费金额（平台公司使用，外部供应商是根据运费模板计算出来的）
     */
    @ApiModelProperty(value = "运费金额")
    private BigDecimal freightAmount;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "是否自营商品")
    private Integer isSelfSell;

    @ApiModelProperty(value = "商品sku列表")
    private List<ItemSkuDTO> itemSkuDTOList;

    @ApiModelProperty(value = "库存量")
    private BigDecimal inventory;

    @ApiModelProperty(value = "删除标记:0、未删除，1、已删除")
    private Integer deleteFlag;

    @ApiModelProperty(value = "食品酒categoryAttrDTOList水>白酒>食品酒水>白酒 ")
    private String categoryPath;

    @ApiModelProperty(value = "类目名称 ")
    private String categoryName;

    @ApiModelProperty(value = "关键字/商品亮点")
    private String keywords;

    @ApiModelProperty(value = "建议零售价")
    private BigDecimal suggestedRetailPrice;

    @ApiModelProperty(value = "商品数量")
    private long shopCount;

    @ApiModelProperty(value = "外接商品编码")
    private String outerItemCode;

    @ApiModelProperty("是否是修改选品 1：是 0或null：否")
    private Integer chooseStatus = 0;

    @ApiModelProperty(value = "ERP一级类目编码")
    private String erpFirstCategoryCode;

    @ApiModelProperty(value = "ERP五级类目编码")
    private String erpFiveCategoryCode;

    @ApiModelProperty(value = "1:未下架执行编辑操作；0:已下架执行编辑操作")
    private String shelfStatus;

    @ApiModelProperty(value = "云链项目商品售价")
    private BigDecimal ylProjectSalePrice;

    private BigDecimal buyCount;

    private String buySkuCode;

    private BigDecimal buyPrice;

    @ApiModelProperty(value = "商品规格")
    private String itemAttributes;

    /**
     * 是否在销售区域内
     */
    private Boolean isInSalesArea;

    @ApiModelProperty(value = "购买快照数量")
    private BigDecimal goodsSnapCount;

    @ApiModelProperty(value = "埋点标志 1：es搜索")
    private String buryPointFlag;

    @ApiModelProperty(value = "推荐人,存到商品行")
    private String recommender;

    @ApiModelProperty(value = "是否支持小数点 0:不支持  1：支持")
    private String isSupportDecimal;

    /**
     * 云场订单子类型  当orderfrom为22时候  0：默认值 正常订单 1：撮合订单，20：直播订单
     */
    @ApiModelProperty(value="云场订单子类型:20：直播订单", example = "20")
    private String subOrderFrom ;

    @ApiModelProperty(value="渠道编码")
    private String appCode;
    @ApiModelProperty(value="渠道名称")
    private String appName;

    @ApiModelProperty(value = "商家资质证书")
    private Map<String, List<String>> itemQualificationMap;

    @ApiModelProperty(value = "仓库统一编码")
    private String warehouseCode;

    @ApiModelProperty(value = "采购部门编码")
    private String purchaseDepartmentCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
