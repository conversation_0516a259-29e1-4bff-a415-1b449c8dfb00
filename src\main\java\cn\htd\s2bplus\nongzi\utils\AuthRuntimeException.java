package cn.htd.s2bplus.nongzi.utils;

import cn.htd.rdc.base.development.framework.core.enums.IBaseEnums;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;

/**
 * 登录认证异常,当前服务的自定义异常处理
 * <AUTHOR>
 * date: 2021/1/22
 */
public class AuthRuntimeException extends RuntimeException {

    private Integer code;

    public AuthRuntimeException(Integer code, String msg) {
        super(msg);
        this.code = code;
    }

    /**
     * 抛出统一枚举抛出自定义异常
     *
     * @param resultEnum 枚举实例
     */
    public AuthRuntimeException(ResultEnum resultEnum) {
        super(resultEnum.getMsg());
        this.code = resultEnum.getCode();
    }

    public  AuthRuntimeException(IBaseEnums enums) {
        super(enums.getMessage());
        this.code = enums.getCode();
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

}
