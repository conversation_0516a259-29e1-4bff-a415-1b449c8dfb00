<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htd.s2bplus.nongzi.mapper.ReportHistoryMapper" >
  <resultMap id="BaseResultMap" type="cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="business_type" property="businessType" jdbcType="TINYINT" />
    <result column="report_status" property="reportStatus" jdbcType="TINYINT" />
    <result column="download_url" property="downloadUrl" jdbcType="VARCHAR" />
    <result column="finish_time" property="finishTime" jdbcType="TIMESTAMP" />
    <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="create_id" property="createId" jdbcType="BIGINT" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modify_id" property="modifyId" jdbcType="BIGINT" />
    <result column="modify_name" property="modifyName" jdbcType="VARCHAR" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, business_type, report_status, download_url, finish_time, begin_time, end_time,
    create_id, create_name, create_time, modify_id, modify_name, modify_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from s2bplus_report_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from s2bplus_report_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory" >
    insert into s2bplus_report_history (id, business_type, report_status,
      download_url, finish_time, begin_time,
      end_time, create_id, create_name,
      create_time, modify_id, modify_name,
      modify_time)
    values (#{id,jdbcType=BIGINT}, #{businessType,jdbcType=TINYINT}, #{reportStatus,jdbcType=TINYINT},
      #{downloadUrl,jdbcType=VARCHAR}, #{finishTime,jdbcType=TIMESTAMP}, #{beginTime,jdbcType=TIMESTAMP},
      #{endTime,jdbcType=TIMESTAMP}, #{createId,jdbcType=BIGINT}, #{createName,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{modifyId,jdbcType=BIGINT}, #{modifyName,jdbcType=VARCHAR},
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory" keyProperty="id" useGeneratedKeys="true">
    insert into s2bplus_report_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="businessType != null" >
        business_type,
      </if>
      <if test="reportStatus != null" >
        report_status,
      </if>
      <if test="downloadUrl != null" >
        download_url,
      </if>
      <if test="finishTime != null" >
        finish_time,
      </if>
      <if test="beginTime != null" >
        begin_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="createId != null" >
        create_id,
      </if>
      <if test="createName != null" >
        create_name,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="modifyId != null" >
        modify_id,
      </if>
      <if test="modifyName != null" >
        modify_name,
      </if>
      <if test="modifyTime != null" >
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="reportStatus != null" >
        #{reportStatus,jdbcType=TINYINT},
      </if>
      <if test="downloadUrl != null" >
        #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="finishTime != null" >
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="beginTime != null" >
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null" >
        #{createId,jdbcType=BIGINT},
      </if>
      <if test="createName != null" >
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyId != null" >
        #{modifyId,jdbcType=BIGINT},
      </if>
      <if test="modifyName != null" >
        #{modifyName,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null" >
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory" >
    update s2bplus_report_history
    <set >
      <if test="businessType != null" >
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="reportStatus != null" >
        report_status = #{reportStatus,jdbcType=TINYINT},
      </if>
      <if test="downloadUrl != null" >
        download_url = #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="finishTime != null" >
        finish_time = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="beginTime != null" >
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null" >
        create_id = #{createId,jdbcType=BIGINT},
      </if>
      <if test="createName != null" >
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyId != null" >
        modify_id = #{modifyId,jdbcType=BIGINT},
      </if>
      <if test="modifyName != null" >
        modify_name = #{modifyName,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null" >
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.htd.s2bplus.nongzi.pojo.dto.excel.ReportHistory" >
    update s2bplus_report_history
    set business_type = #{businessType,jdbcType=TINYINT},
      report_status = #{reportStatus,jdbcType=TINYINT},
      download_url = #{downloadUrl,jdbcType=VARCHAR},
      finish_time = #{finishTime,jdbcType=TIMESTAMP},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=BIGINT},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_id = #{modifyId,jdbcType=BIGINT},
      modify_name = #{modifyName,jdbcType=VARCHAR},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>



  <select id="queryReportHistoryPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from s2bplus_report_history
    where 1=1
    <if test="memberId != null" >
      and  create_id = #{memberId,jdbcType=BIGINT}
    </if>
    <if test="finishBeginTime != null" >
      and finish_time <![CDATA[>= ]]> #{finishBeginTime}
    </if>
    <if test="finishEndTime != null" >
      and  finish_time <![CDATA[<= ]]> #{finishEndTime}
    </if>
    <if test="businessType != null" >
      and business_type = #{businessType,jdbcType=TINYINT}
    </if>
    ORDER BY finish_time DESC
    <include refid="pagination_tail" />
  </select>

  <select id="queryReportHistoryCount" resultType="java.lang.Integer" >
    select
    count(1)
    from s2bplus_report_history
    where 1=1
    <if test="memberId != null" >
      and  create_id = #{memberId,jdbcType=BIGINT}
    </if>
    <if test="finishBeginTime != null" >
      and finish_time <![CDATA[>= ]]> #{finishBeginTime}
    </if>
    <if test="finishEndTime != null" >
      and  finish_time <![CDATA[<= ]]> #{finishEndTime}
    </if>
    <if test="businessType != null" >
      and business_type = #{businessType,jdbcType=TINYINT}
    </if>
  </select>



  <select id="queryReportHistoryListCount" resultType="java.lang.Integer" >
    select
    count(1)
    from s2bplus_report_history
    where 1=1
    <if test="businessType != null">
      and business_type = #{businessType,jdbcType=TINYINT}
    </if>
    <if test="createId != null" >
      and  create_id = #{createId,jdbcType=BIGINT}
    </if>
    <if test="beginTime != null" >
      and finish_time <![CDATA[>= ]]> #{beginTime}
    </if>
    <if test="endTime != null" >
      and  finish_time <![CDATA[<= ]]> #{endTime}
    </if>
  </select>




  <!-- mysql 分页尾 -->
  <sql id="pagination_tail">
    <if test="page != null">
      limit #{page.pageOffset} , #{page.rows}
    </if>
  </sql>

</mapper>
