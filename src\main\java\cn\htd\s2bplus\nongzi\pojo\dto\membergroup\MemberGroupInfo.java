package cn.htd.s2bplus.nongzi.pojo.dto.membergroup;

import cn.htd.rdc.base.development.framework.core.mp.support.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MemberGroupInfo extends Query implements Serializable {

    @ApiModelProperty(value = "分组ID", example = "1")
    private Long groupId;

    @ApiModelProperty(value = "分组名称", example = "美的组")
    private String name;

    @ApiModelProperty(value = "分组类型", example = "空默认、1指定人、2按行业")
    private Integer groupType;

    @ApiModelProperty(value = "备注", example = "备注")
    private String comment;

    @ApiModelProperty(value = "商家Id", example = "926388")
    private String sellerId;


    @ApiModelProperty(value = "展示会员数量", example = "11")
    private Integer memberCount;

    @ApiModelProperty(value = "展示行业编码", example = "11")
    private String industryCode;


    @ApiModelProperty(value = "会员编码", example = "926388")
    private String memberCode;

    @ApiModelProperty(value = "创建时间", example = "2015-12-18 14:16:51")
    private Date createTime;

    @ApiModelProperty(value = "分组名称模糊查询")
    private String nameLike;
    /**
     * 运营登录id
     */
    @ApiModelProperty(value = "登录id",hidden = true)
    private String loginId;

    @ApiModelProperty(value = "批量分组id集合")
    private List<Long> groupIds;
    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"groupId\":")
                .append(groupId);
        sb.append(",\"name\":\"")
                .append(name).append('\"');
        sb.append(",\"groupType\":")
                .append(groupType);
        sb.append(",\"comment\":\"")
                .append(comment).append('\"');
        sb.append(",\"sellerId\":\"")
                .append(sellerId).append('\"');
        sb.append(",\"memberCount\":")
                .append(memberCount);
        sb.append(",\"industryCode\":\"")
                .append(industryCode).append('\"');
        sb.append(",\"memberCode\":\"")
                .append(memberCode).append('\"');
        sb.append(",\"nameLike\":\"")
                .append(nameLike).append('\"');
        sb.append(",\"loginId\":\"")
                .append(loginId).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
