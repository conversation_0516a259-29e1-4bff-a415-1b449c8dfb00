package cn.htd.s2bplus.nongzi.pojo.dto.Express;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("UpdatePriceExcelDTO")
public class UpdatePriceExcelDTO implements Serializable {

    /**
     * sku编码
     */
    @Excel(name = "sku编码", height = 10, width = 30)
    private String skuCode;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺id", height = 10, width = 30)
    private String shopId;

    /**
     * 零售价
     */
    @Excel(name = "销售价", height = 10, width = 30)
    private String retailPrice;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
