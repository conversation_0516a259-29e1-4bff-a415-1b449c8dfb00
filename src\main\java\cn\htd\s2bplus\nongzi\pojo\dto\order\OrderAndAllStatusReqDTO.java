package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.nongzi.utils.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订单信息(各种订单状态)
 */
@Data
@ApiModel
public class OrderAndAllStatusReqDTO implements Serializable  {

    private static final long serialVersionUID = -1192806489806027873L;
    /**
     * tab页时间标记,0表示最近三个月,1表示三个月以前的数据
     */
    @ApiModelProperty(value = "tab页时间标记,0表示最近三个月,1表示三个月以前的数据")
    private Integer tabTime;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 买家编号
     */
    @ApiModelProperty(value = "买家编号")
    private String buyerCode;

    /**
     * 买家名称
     */
    @ApiModelProperty(value = "买家名称")
    private String buyerName;

    /**
     * 卖家编号
     */
    @ApiModelProperty(value = "卖家编号")
    private String sellerCode;

    /**
     * 卖家名称
     */
    @ApiModelProperty(value = "卖家名称")
    private String sellerName;

    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "店铺编号")
    private Long shopId;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private String orderFrom;

    /**
     * 云场订单子类型
     */
    @ApiModelProperty(value = "云场订单子类型")
    private String subOrderFrom;

    /**
     * 销售类型
     */
    @ApiModelProperty(value = "销售类型")
    private String salesType;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * 买家确认订单状态
     */
    @ApiModelProperty(value = "买家确认订单状态")
    private String buyerCheckStatus;

    /**
     * 卖家确认订单状态
     */
    @ApiModelProperty(value = "卖家确认订单状态")
    private String sellerCheckStatus;

    /**
     * 云原生 订单类型
     */
    @ApiModelProperty(value = "云原生 订单类型")
    private String  cloudOrderType;

    /**
     * 区间 开始时间 取下单时间
     */
    @ApiModelProperty(value = "区间 开始时间 取下单时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 区间 结束时间 取下单时间
     */
    @ApiModelProperty(value = "区间 结束时间 取下单时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;



    // 如上数据 是存在于 trade_orders 表 , 如下 字段是曾在于 tra_perform_stage_order 表中


    /**
     * 阶段单号
     */
    @ApiModelProperty(value = "阶段单号")
    private String orderStageNo;
    /**
     * 阶段单状态
     */
    @ApiModelProperty(value = "阶段单状态")
    private String stageStatus;

    /**
     * 支付状态
     */
    @ApiModelProperty(value = "支付状态")
    private String payStatus;

    /**
     * 物流状态
     */
    @ApiModelProperty(value = "物流状态 ")
    private String logisticsStatus ;

    /**
     * 收付款下行 erp 状态
     */
    @ApiModelProperty(value = "收付款下行 erp 状态")
    private String paymentDownErpStatus;

    /**
     * 结算状态 000:空值 2101:未结算 2102:已结算
     *
     */
    @ApiModelProperty(value = "结算状态 000:空值 2101:未结算 2102:已结算")
    private String settlementStatus;

    /**
     * 拆单状态
     */
    @ApiModelProperty(value = "拆单状态")
    private String spillStatus;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String consigneePhoneNum;

    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    @ApiModelProperty(value = "配送方式1:供应商配送  2:自提")
    private String deliveryType;

    @ApiModelProperty(value = "结算方式0:未结算 1:已结算")
    private String settlementType;

    @ApiModelProperty(value = "支付时间开始时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date startPayTime;

    @ApiModelProperty(value = "支付时间结束时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endPayTime;

    @ApiModelProperty(
            value = "店铺ID集合",
            notes = "逆向订单过滤条件",
            example = "",hidden = true
    )
    private List<Long> shopIdFilterList;

    private List<String> subOrderFromList;

    /**
     * 卖家编号
     */
    @ApiModelProperty(value = "授权卖家编号",hidden = true)
    private List<String> sellerCodes;

    /**
     * 支付系统流水号
     */
    @ApiModelProperty("支付系统流水号")
    private String paySerialNo;

    @ApiModelProperty(value = "用于区分已支付-待审核状态,1-待审核 2-通过，3-驳回")
    private String orderAuthorizationStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
