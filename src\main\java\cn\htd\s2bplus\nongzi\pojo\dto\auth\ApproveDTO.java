package cn.htd.s2bplus.nongzi.pojo.dto.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 检查权限请求对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "ApproveDTO", description = "检查权限请求对象")
public class ApproveDTO implements Serializable {

	@NotBlank(message = "JWT不能为空")
	@ApiModelProperty(value="JWT值" ,required = true)
	private String authorization;

	@NotBlank(message = "请求URL不能为空")
	@ApiModelProperty(value="请求URL" ,required = true)
	private String url;

	@NotBlank(message = "请求类型不能为空")
	@ApiModelProperty(value="请求类型" ,required = true)
	private String method;



	@Override
	public String toString() {
		final StringBuilder sb = new StringBuilder("{");
		sb.append("\"authorization\":\"")
				.append(authorization).append('\"');
		sb.append(",\"method\":\"")
				.append(method).append('\"');
		sb.append(",\"url\":\"")
				.append(url).append('\"');
		sb.append('}');
		return sb.toString();
	}
}
