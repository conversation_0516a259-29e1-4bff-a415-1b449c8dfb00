package cn.htd.s2bplus.nongzi.pojo.dto.coupon;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class BalanceInformationDTO implements Serializable {


    private static final long serialVersionUID = 8396026415008450676L;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "会员名称")
    private String memberName;

    @ApiModelProperty(value = "活动id")
    private Long promotionId;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "发券商家编码",hidden = true)
    private String sellerCode;

    @ApiModelProperty(value = "结余金额清算状态 0:未清算 1:已清算",hidden = true)
    private Integer remainStatus;

    @ApiModelProperty(value = "结余类型（区分导出文件） 0:活动或全量 1:会员",hidden = true)
    private Integer remainType;

    @ApiModelProperty(value = "是否查询会员历史 0||空:不是 1:是",hidden = true)
    private Integer isMemberHistoryType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
