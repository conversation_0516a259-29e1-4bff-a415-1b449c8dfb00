package cn.htd.s2bplus.nongzi.pojo.dto.user;


import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * 组中被关联对象
 */
@Data
public class MemberGroupInfoDTO implements Serializable {

    private static final long serialVersionUID = 265182789805821781L;


    /**
     * 组中被关联对象的类型（标签ID）
     */
    @ApiModelProperty(value = "组中被关联对象的类型")
    private String refObjType;

    /**
     * 组中被关联对象的类型名称（标签值ID）
     */
    @ApiModelProperty(value = "组中被关联对象的类型名称")
    private String refObjName;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}


