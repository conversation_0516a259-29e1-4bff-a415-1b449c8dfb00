package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title DecimalPointDTO
 * @Date: 2023/11/15 9:37
 */
@Data
public class DecimalPointDTO implements Serializable {
    @ApiModelProperty(value = "规则主键id",hidden = true)
    private Long id;

    @ApiModelProperty(value = "末级类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "商品Id")
    private Long itemId;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "规则类型 0-不允许 1-允许填写任意小数 2-允许填写小数的规格")
    private Integer ruleType;

    @ApiModelProperty(value = "支持输入小数位数")
    private Integer decimalPlaces;

    @ApiModelProperty(value = "支持输入小数点最小起订数量")
    private BigDecimal decimalNumber;

    @ApiModelProperty(value = "小数规格基数，逗号分隔")
    private String decimalSpecs;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
