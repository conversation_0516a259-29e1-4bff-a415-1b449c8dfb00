package cn.htd.s2bplus.nongzi.pojo.dto.distribute;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
public class CommissionRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "会员Id")
    private Long memberId;
    @ApiModelProperty("推客编码")
    private String memberCode;
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "经营人姓名")
    private String businessPersonName;
    @ApiModelProperty(value = "推客类型", example = "1：推客/分销员，2：合伙人")
    private Integer distributeType;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "变动类型 1:全部 2:增加 3:减少")
    private String changeType;

    @ApiModelProperty(value = "变动属性 0:全部 11:分销佣金 12:分销奖励 2:退款 3:提现")
    private String changeAttr;

    @ApiModelProperty(value = "关联订单号")
    private String orderNo;

    @ApiModelProperty(value = "结算状态 0 未发放 1 已发放")
    private String provideStatus;
    /**
     * 品牌分销:分销佣金等于累计佣金
     */
    @ApiModelProperty(value = "分销佣金")
    private BigDecimal distributeCommission;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
