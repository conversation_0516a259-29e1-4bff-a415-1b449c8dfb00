package cn.htd.s2bplus.nongzi.service.finance.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.BusinessTypeEnum;
import cn.htd.s2bplus.nongzi.enums.ExportSettlementEnum;
import cn.htd.s2bplus.nongzi.enums.SettleStatusEnum;
import cn.htd.s2bplus.nongzi.feign.finance.TradeOrderFeignService;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.feign.middleground.MiddleGroundAPI;
import cn.htd.s2bplus.nongzi.feign.order.OrderFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.finance.*;
import cn.htd.s2bplus.nongzi.pojo.dto.order.CloudOrderItemsRepDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.CloudTradeOrderItemsDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.FinanceReportformEntity;
import cn.htd.s2bplus.nongzi.pojo.dto.order.ReportHistoryDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.service.finance.StatementService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import cn.htd.s2bplus.nongzi.utils.XssFilter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class StatementServiceImpl implements StatementService {

    @Autowired
    private MiddleGroundAPI middleGroundAPI;

    @Autowired
    private TradeOrderFeignService tradeOrderFeignService;
    @Autowired
    private OrderFeignService orderFeignService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private GoodsFeignService goodsFeignService;

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${settlementOrder.exportRows}")
    private int exportRows;

    @Value("${settlementOrder.timeout}")
    private int timeout;

    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;
    private static final String EXPORT_REPEATED_SUBMIT = "exportRepeatedSubmit";

    private static final String EXPORTING = "exporting";

    @Override
    @Async
    public Result<Boolean> settlementReportGeneration(SettlementOrderDTO settlementOrderDTO,
                                                      HttpServletRequest request,
                                                      HttpServletResponse response) {
        //redis控制
        String redisKey = this.getRedisKey(settlementOrderDTO, request);
        try {
            if (!this.checkRepeatedSubmit(redisKey)) {
                return ResultUtil.error(cn.htd.s2bplus.nongzi.enums.ResultEnum.REPORT_IS_BEING_GENERATED_DO_NOT_RESUBMIT.getCode(),
                        cn.htd.s2bplus.nongzi.enums.ResultEnum.REPORT_IS_BEING_GENERATED_DO_NOT_RESUBMIT.getMsg());
            }
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName("结算单列表");
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", ExportSettlementOrderData.class);
            // sheet中要填充得数据
            List<ExportSettlementOrderData> exportDataList = new ArrayList<>();
            List<SettlementOrderVO> data = this.getListData(settlementOrderDTO);
            if (CollectionUtils.isEmpty(data)) {
                return ResultUtil.error(ResultEnum.FAILURE.getCode(), "查询结算单列表异常");
            }
            // 渠道名称赋值
            this.setReportStatementChannelName(data);
            for(SettlementOrderVO settlementOrderVO : data){
                ExportSettlementOrderData export = new ExportSettlementOrderData();
                //组装主要数据
                this.getMainData(settlementOrderVO, export);
                if (CollectionUtils.isNotEmpty(settlementOrderVO.getReportOrderDTOList())) {
                    for (ReportSettlementOrderItemDTO reportSettlementOrderItemDTO : settlementOrderVO.getReportOrderDTOList()) {
                        ExportSettlementOrderData exportLine = new ExportSettlementOrderData();
                        exportLine.setOrderNo(reportSettlementOrderItemDTO.getOrderNo());
                        exportLine.setMemberName(reportSettlementOrderItemDTO.getMemberName());
                        exportLine.setMemberCode(reportSettlementOrderItemDTO.getMemberCode());
                        //佣金金额
                        if(reportSettlementOrderItemDTO.getStatementAmount() != null){
                            exportLine.setStatementAmount(reportSettlementOrderItemDTO.getStatementAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        //实际支付金额
                        if(reportSettlementOrderItemDTO.getRealAmount() != null){
                            exportLine.setRealAmount(reportSettlementOrderItemDTO.getRealAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        //实际支付金额
                        if(reportSettlementOrderItemDTO.getChargeAmount() != null){
                            exportLine.setChargeAmount(reportSettlementOrderItemDTO.getChargeAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        //实际结算金额
//                        if(reportSettlementOrderItemDTO.getSettlementAmount() != null){
//                            exportLine.setSettlementAmount(reportSettlementOrderItemDTO.getWithdrawAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
//                        }
                        //渠道手续费金额
                        if(reportSettlementOrderItemDTO.getChargeAmount() != null){
                            exportLine.setChargeAmount(reportSettlementOrderItemDTO.getChargeAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        // 平台佣金
                        if(reportSettlementOrderItemDTO.getPlatformAmount() != null){
                            exportLine.setPlatformAmount(reportSettlementOrderItemDTO.getPlatformAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        // 商家分佣金额
                        if(reportSettlementOrderItemDTO.getSellerAmount() != null){
                            exportLine.setSellerAmount(reportSettlementOrderItemDTO.getSellerAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        // 运营商分佣金额
                        if(reportSettlementOrderItemDTO.getOperatorAmount() != null){
                            exportLine.setOperatorAmount(reportSettlementOrderItemDTO.getOperatorAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        //支付方式转换
                        this.conversionPayType(reportSettlementOrderItemDTO,exportLine);
                        exportDataList.add(this.getFinallyData(export,exportLine));
                    }
                } else {
                    exportDataList.add(export);
                }
            }
            // sheet中要填充得数据
            sheet1DataMap.put("data", exportDataList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType("applicationnd.ms-excel"); // 改成输出excel文件
            String fileName = java.net.URLEncoder.encode("结算单列表", "UTF-8");
            response.setHeader("Content-disposition",
                    "attachment; filename=" + fileName + ".xls");// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] barray = bos.toByteArray();
            return saveGeneration(settlementOrderDTO.getUser().getUserId() + "", settlementOrderDTO.getUser().getUserName(), settlementOrderDTO.getType(), new ByteArrayInputStream(barray));
        } catch (Exception e) {
            log.info("生成结算单列表异常,error:", e);
            return ResultUtil.error(ResultEnum.FAILURE.getCode(), "生成结算单列表异常");
        } finally {
            //删除redis控制
            redisTemplate.delete(redisKey);
        }
    }

    private void setReportStatementChannelName(List<SettlementOrderVO> data) {
        try {
            if (CollectionUtils.isEmpty(data)) {
                return;
            }
            // 从返回data获取渠道编码列表
            List<String> channelCodeList = data.stream().filter(Objects::nonNull).map(SettlementOrderVO::getChannelCode).collect(Collectors.toList());
            Map<String, String> channelNameMap = this.getChannelNameMap(channelCodeList);
            //  赋值渠道名称
            if (null != channelNameMap) {
                data.forEach(item -> item.setChannelName(channelNameMap.get(item.getChannelCode())));
            }
        } catch (Exception e) {
            log.error("结算单列表获取渠道名称异常", e);
        }
    }

    /**
     * 根据渠道编码查询渠道名称
     * @param channelCodeList
     * @return
     */
    private Map<String, String> getChannelNameMap(List<String> channelCodeList) {
        if (CollectionUtils.isEmpty(channelCodeList)) {
            return null;
        }
        channelCodeList = channelCodeList.stream().distinct().collect(Collectors.toList());
        //  查询渠道名称
        log.info("根据渠道编码查询渠道名称 入参{}", channelCodeList);
        Result<Map<String, String>> channelNameResult = goodsFeignService.queryChannelName(channelCodeList);
        log.info("根据渠道编码查询渠道名称 出参{}", channelNameResult);
        if (null == channelNameResult||!channelNameResult.isSuccess() || channelNameResult.getData().isEmpty()) {
            return null;
        }
        return channelNameResult.getData();
    }

    /**
     * redis控制
     * @param redisKey
     * @return
     */
    private Boolean checkRepeatedSubmit(String redisKey) {
        if (redisTemplate.hasKey(redisKey)) {
            return Boolean.FALSE;
        }
        redisTemplate.opsForValue().setIfPresent(redisKey,EXPORTING,timeout,TimeUnit.MINUTES);
        return Boolean.TRUE;
    }

    /**
     * 获取redis的key
     * @param settlementOrderDTO
     * @param request
     * @return
     */
    private String getRedisKey(SettlementOrderDTO settlementOrderDTO,HttpServletRequest request) {
        String loginId = settlementOrderDTO.getSellerId();
        if (ObjectUtils.isEmpty(loginId)) {
            loginId = request.getParameter("loginId");
        }
        return EXPORT_REPEATED_SUBMIT + loginId;
    }

    private List<SettlementOrderVO> getListData(SettlementOrderDTO settlementOrderDTO) {
        settlementOrderDTO.setOrderFrom("B2B_PLUS");
        List<SettlementOrderVO> list = new ArrayList<>();
        log.info("结算单管理-查询结算单列表-查询总数量,req:{}",settlementOrderDTO);
        PageResult<List<SettlementOrderVO>> totalResult = middleGroundAPI.exportSettlementOrder(settlementOrderDTO,1,1);
        log.info("结算单管理-查询结算单列表-查询总数量,resp:{}",JSON.toJSONString(totalResult));
        if (!totalResult.isSuccess() || CollectionUtils.isEmpty(totalResult.getData())) {
            return new ArrayList<>();
        }
        long total = ObjectUtils.isNotEmpty(totalResult.getPage()) ? totalResult.getPage().getTotal() : 0L;
        int pages = (int) Math.ceil((double) total / exportRows);
        for (int i = 1;i <= pages;i++) {
            log.info("结算单管理-查询结算单列表,page:{},rows:{},req:{}",i,exportRows,settlementOrderDTO);
            PageResult<List<SettlementOrderVO>> listPageResult = middleGroundAPI.exportSettlementOrder(settlementOrderDTO,i,exportRows);
            log.info("结算单管理-查询结算单列表,resp:{}",listPageResult.toString());
            if (!listPageResult.isSuccess() || null == listPageResult.getData()) {
                continue;
            }
            String settlementList = JSON.toJSONString(listPageResult.getData());
            List<SettlementOrderVO> data = JSON.parseArray(settlementList,SettlementOrderVO.class);
            list.addAll(data);
        }
        return list;
    }

    /**
     * 组装主要数据
     * @param settlementOrderVO
     * @param export
     */
    private void getMainData(SettlementOrderVO settlementOrderVO, ExportSettlementOrderData export) {
        BeanUtil.copy(settlementOrderVO,export);
        //结算状态转换
        this.conversionSettleStatus(settlementOrderVO,export);
        //结算金额
        if(settlementOrderVO.getSettleAmount() != null){
            export.setSettleAmount(settlementOrderVO.getSettleAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
        }
    }

    /**
     * 组装最终导出数据
     * @param export
     * @param exportLine
     * @return
     */
    private ExportSettlementOrderData getFinallyData(ExportSettlementOrderData export,ExportSettlementOrderData exportLine) {
        ExportSettlementOrderData exportFinallyData = new ExportSettlementOrderData();
        BeanUtil.copy(export,exportFinallyData);
        exportFinallyData.setOrderNo(exportLine.getOrderNo());
        exportFinallyData.setMemberName(exportLine.getMemberName());
        exportFinallyData.setMemberCode(exportLine.getMemberCode());
        //佣金金额
        exportFinallyData.setStatementAmount(exportLine.getStatementAmount());
        //实际支付金额
        exportFinallyData.setRealAmount(exportLine.getRealAmount());
        //实际支付金额
        exportFinallyData.setChargeAmount(exportLine.getChargeAmount());
        //实际结算金额
        // exportFinallyData.setSettlementAmount(exportLine.getSettlementAmount());
        //渠道手续费金额
        exportFinallyData.setChargeAmount(exportLine.getChargeAmount());
        // 平台分佣金额
        exportFinallyData.setPlatformAmount(exportLine.getPlatformAmount());
        // 商家分佣金额
        exportFinallyData.setSellerAmount(exportLine.getSellerAmount());
        // 运营商分佣金额
        exportFinallyData.setOperatorAmount(exportLine.getOperatorAmount());
        //支付方式
        exportFinallyData.setPayType(exportLine.getPayType());
        return exportFinallyData;
    }

    /**
     * 结算状态转换
     * @param settlementOrderVO
     * @param export
     */
    private void conversionSettleStatus(SettlementOrderVO settlementOrderVO,ExportSettlementOrderData export) {
        if(ExportSettlementEnum.SETTLE_STATUS_PENDING_FINANCIAL_CONFIRMATION.getCode().equals(settlementOrderVO.getSettleStatus())){
            export.setSettleStatus(ExportSettlementEnum.SETTLE_STATUS_PENDING_FINANCIAL_CONFIRMATION.getMsg());
        }else if(ExportSettlementEnum.SETTLE_STATUS_AWAITING_MERCHANT_WITHDRAWAL.getCode().equals(settlementOrderVO.getSettleStatus())){
            export.setSettleStatus(ExportSettlementEnum.SETTLE_STATUS_AWAITING_MERCHANT_WITHDRAWAL.getMsg());
        }else if(ExportSettlementEnum.SETTLE_STATUS_MERCHANT_WITHDRAWAL_PROCESSING.getCode().equals(settlementOrderVO.getSettleStatus())){
            export.setSettleStatus(ExportSettlementEnum.SETTLE_STATUS_MERCHANT_WITHDRAWAL_PROCESSING.getMsg());
        }else if(ExportSettlementEnum.SETTLE_STATUS_SETTLEMENT_COMPLETED.getCode().equals(settlementOrderVO.getSettleStatus())){
            export.setSettleStatus(ExportSettlementEnum.SETTLE_STATUS_SETTLEMENT_COMPLETED.getMsg());
        }else if(ExportSettlementEnum.SETTLE_STATUS_SETTLEMENT_FAILED.getCode().equals(settlementOrderVO.getSettleStatus())){
            export.setSettleStatus(ExportSettlementEnum.SETTLE_STATUS_SETTLEMENT_FAILED.getMsg());
        }
    }

    /**
     * 支付方式转换
     * @param reportSettlementOrderItemDTO
     * @param exportLine
     */
    private void conversionPayType(ReportSettlementOrderItemDTO reportSettlementOrderItemDTO, ExportSettlementOrderData exportLine) {
        if(ExportSettlementEnum.PAY_TYPE_CASH.getCode().equals(reportSettlementOrderItemDTO.getPayType())){
            exportLine.setPayType(ExportSettlementEnum.PAY_TYPE_CASH.getMsg());
        }else if(ExportSettlementEnum.PAY_TYPE_WECHAT.getCode().equals(reportSettlementOrderItemDTO.getPayType())){
            exportLine.setPayType(ExportSettlementEnum.PAY_TYPE_WECHAT.getMsg());
        }else if(ExportSettlementEnum.PAY_TYPE_ALIPAY.getCode().equals(reportSettlementOrderItemDTO.getPayType())){
            exportLine.setPayType(ExportSettlementEnum.PAY_TYPE_ALIPAY.getMsg());
        }else if(ExportSettlementEnum.PAY_TYPE_BALANCE.getCode().equals(reportSettlementOrderItemDTO.getPayType())){
            exportLine.setPayType(ExportSettlementEnum.PAY_TYPE_BALANCE.getMsg());
        }else if(ExportSettlementEnum.PAY_TYPE_LARGE_TRANSFER.getCode().equals(reportSettlementOrderItemDTO.getPayType())){
            exportLine.setPayType(ExportSettlementEnum.PAY_TYPE_LARGE_TRANSFER.getMsg());
        }else if(ExportSettlementEnum.PAY_TYPE_OTHER.getCode().equals(reportSettlementOrderItemDTO.getPayType())){
            exportLine.setPayType(ExportSettlementEnum.PAY_TYPE_OTHER.getMsg());
        }
    }

    /**
     * 生成报表上传OSS并且保存
     * @param createId
     * @param createName
     * @param type       0：云场结算单，1：OSS结算单，2oss佣金单 3: OSS商家佣金
     * @param
     * @return
     */
    public Result<Boolean> saveGeneration(String createId, String createName, String type, InputStream excelStream) {
        try {
            OssUtils ossUtils = new OssUtils();
            String fileName = getfileName("xls");
            String downloadUrl = ossUtils.upload(excelStream, fileName, bucket, endpoint, accessKeyId, accessKeySecret);
            if (StringUtils.isEmpty(downloadUrl)) {
                return ResultUtil.error(ResultEnum.FAILURE.getCode(), "OSS上传失败");
            }
            // 保存报表url
            FinanceReportformEntity financeReportformEntity = new FinanceReportformEntity();
            financeReportformEntity.setType(type);
            financeReportformEntity.setCreateId(createId);
            financeReportformEntity.setCreateName(createName);
            financeReportformEntity.setCreateDate(new Date());
            financeReportformEntity.setDownUrl(downloadUrl);
            financeReportformEntity.setFileName(fileName);
            Result<Boolean> saveFinanceReportForm = tradeOrderFeignService.saveFinanceReportForm(financeReportformEntity);
            if (saveFinanceReportForm.isSuccess() && saveFinanceReportForm.getData()) {
                return ResultUtil.success(ResultEnum.SUCCESS.getCode());
            } else {
                return ResultUtil.error(ResultEnum.FAILURE.getCode(), "保存报表地址失败");
            }
        } catch (Exception e) {
            log.error("保存报表地址异常",e);
            return ResultUtil.error(ResultEnum.FAILURE.getCode(), "保存报表地址异常");
        }
    }

    @Override
    public Result<String> batchModify(MultipartFile file, LoginUserDetail userDetail) {
        try {
            //文件非空校验
            if (file == null || file.isEmpty()) {
                log.info("上传文件为空");
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(),"上传文件为空!");
            }
            String originalFilename = file.getOriginalFilename();
            //判断文件格式
            String substring = originalFilename.substring(originalFilename.lastIndexOf(".") + 1,originalFilename.length());
            if (StringUtils.isEmpty(substring) && (!"xls".equals(substring) || !"xlsx".equals(substring))) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(),"仅支持Excel格式文件!");
            }
            //XSS拦截
            new XssFilter().importFilter(file,null, OfflineSettlementInfoModifyDTO.class.getCanonicalName());
            //获取excel内容
            List<OfflineSettlementInfoModifyDTO> fileListImport = this.getExcelInfo(file);
            if (CollectionUtils.isEmpty(fileListImport)) {
                throw new BusinessException(ResultEnum.FAILURE.getCode(), "导入的模板错误或内容为空");
            }
            if (nongZiNacosConfig.getSettlementLimit() < fileListImport.size()) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), String.format("一次最多导入%s条数据", nongZiNacosConfig.getSettlementLimit()));
            }
            //去空格、赋值修改人信息
            fileListImport.stream().forEach(dto -> {
                dto.setOrderNo(StringUtils.trimToEmpty(dto.getOrderNo()));
                dto.setModifyName(userDetail.getUserName());
            });

            //错误信息集合
            StringBuilder errorMsgBuilder = new StringBuilder();
            //校验数据是否为空,校验结算金额是否大于最大值
            this.checkImportDataNull(fileListImport, errorMsgBuilder);
            if (StringUtils.isNotBlank(errorMsgBuilder)) {
                throw new BusinessException(ResultEnum.PARAM_ERROR.getCode(), errorMsgBuilder.toString());
            }

            //根据订单号进行数据去重
            List<OfflineSettlementInfoModifyDTO> list = fileListImport.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(OfflineSettlementInfoModifyDTO::getOrderNo))),ArrayList::new));
            log.info("原始数据记录数:{}, 去重后数据记录数:{}", fileListImport.size(), list.size());
            if (list.size() < fileListImport.size()) {
                throw new BusinessException(ResultEnum.FAILURE.getCode(),"存在相同数据，请检查导入文件");
            }

            //校验订单号是否存在、订单是否已结算、结算金额不能大于订单实付金额
            this.checkImportData(list, errorMsgBuilder);
            if (StringUtils.isNotBlank(errorMsgBuilder)) {
                throw new BusinessException(ResultEnum.FAILURE.getCode(), errorMsgBuilder.toString());
            }

            Result<Boolean> result = middleGroundAPI.batchModifyOfflinePaySettlement(fileListImport);
            log.info("批量修改线下付款结算-resp:{}",result);
            if (!result.isSuccess()) {
                return ResultUtil.error(ResultEnum.FAILURE.getCode(), result.getMsg());
            }
            return CommonResultUtil.success(String.valueOf(ResultEnum.SUCCESS.getCode()));
        } catch (BusinessException b) {
            log.info("批量修改线下付款结算失败:{}", b);
            return CommonResultUtil.error(b.getCode(), b.getMessage());
        } catch (Exception e) {
            log.error("批量修改线下付款结算异常:",e);
            return CommonResultUtil.error(ResultEnum.SYSTEM_ERROR.getCode(), ResultEnum.SYSTEM_ERROR.getMsg());
        }
    }

    @Override
    public Result<String> exportData(OfflineSettlementInfoQueryDTO req, HttpServletResponse response) {
        log.info("导出线下付款结算列表-req:{}", req);
        //当前页数
        int page = 1;
        //每页记录数
        int rows = 200;
        //导出数据对象集合
        List<OfflineSettlementInfoDTO> allExportList = new ArrayList<>();
        //循环读取数据
        do {
            //根据条件分批查询数据
            PageResult<List<OfflineSettlementInfoDTO>> pageResult = middleGroundAPI.pageOfflineStatementList(req, page, rows);
            if (!pageResult.isSuccess() || CollectionUtils.isEmpty(pageResult.getData())) {
                break;
            }
            //处理数据
            allExportList.addAll(pageResult.getData());
            //判断是否继续循环查询
            if (pageResult.getPage().getTotal().intValue() > page * rows) {
                page++;
            } else {
                break;
            }
        } while (true);
        if (CollectionUtils.isEmpty(allExportList)) {
            return CommonResultUtil.error(ResultEnum.FAILURE.getCode(), "没有可导出的数据");
        }
        log.info("读取到数据总条数:{}", allExportList.size());
        List<OfflineSettlementInfoExportDTO> exportList = new ArrayList<>();
        //转换数据状态
        allExportList.stream().forEach(data -> {
            OfflineSettlementInfoExportDTO exportDTO = BeanUtil.copy(data, OfflineSettlementInfoExportDTO.class);
            //支付方式转换
//            exportDTO.setPayType(PayTypeEnum.getMsgByCode(data.getPayType()));
            //结算状态转换
            exportDTO.setSettleStatus(SettleStatusEnum.getMsgByType(data.getSettleStatus()));
            //订单时间、结算时间 转换
            exportDTO.setCreateOrderTime(DateUtil.dateToString(data.getCreateOrderTime()));
            exportDTO.setSettleCompleteTime(null != data.getSettleCompleteTime() ? DateUtil.dateToString(data.getSettleCompleteTime()) : "");
            //放入集合中
            exportList.add(exportDTO);
        });
        //生成excel文件，并上传的阿里云服务器返回文件地址路径
        String downloadUrl = this.getDownloadUrl(exportList, response, CommonConstants.OFFLINE_SETTLEMENT_ORDER_REPORT);
        if (!"".equals(downloadUrl)) {
            //保存报表生成下载历史
            this.saveReportHistory(downloadUrl);
        }
        return CommonResultUtil.success("");
    }

    /**
     * 保存报表生成下载历史
     * @param downloadUrl
     */
    private void saveReportHistory(String downloadUrl) {
        try {
            LoginUserDetail loginUser = BaseContextHandler.getLoginUser();
            ReportHistoryDTO reportHistoryDTO = new ReportHistoryDTO();
            reportHistoryDTO.setBusinessType((BusinessTypeEnum.OFFLINE_SETTLEMENT_ORDER.getCode().byteValue()));
            reportHistoryDTO.setReportStatus(CommonConstants.REPORT_STATUS_GENERATED);
            reportHistoryDTO.setDownloadUrl(downloadUrl);
            reportHistoryDTO.setFinishTime(new Date());
            reportHistoryDTO.setBeginTime(DateUtil.getMonthTime(CommonConstants.THREEMONTH));
            reportHistoryDTO.setEndTime(new Date());
            reportHistoryDTO.setCreateId(loginUser.getUserId());
            reportHistoryDTO.setCreateName(loginUser.getUserName());
            reportHistoryDTO.setCreateTime(new Date());
            reportHistoryDTO.setModifyId(loginUser.getUserId());
            reportHistoryDTO.setModifyName(loginUser.getUserName());
            reportHistoryDTO.setModifyTime(new Date());
            // 保存报表生成下载历史
            log.info("保存报表生成下载历史-req:{}", reportHistoryDTO);
            Result<ReportHistoryDTO> result = orderFeignService.createReportHistory(reportHistoryDTO);
            log.info("保存报表生成下载历史-resp:{}", result);
        }catch (Exception e) {
            log.error("保存报表生成下载历史异常:",e);
        }
    }


    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     */
    private String getDownloadUrl(List<OfflineSettlementInfoExportDTO> exportList, HttpServletResponse response, String sheetNameStart){

        String downloadUrl = "";
        try{
            //文件名样式
            SimpleDateFormat simpleDateFormatName = new SimpleDateFormat(DateUtil.YYYY_MM_DD_HH_MM_SS_FORMAT);
            String sheetName = simpleDateFormatName.format(new Date());
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", OfflineSettlementInfoExportDTO.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(exportList) ? new ArrayList<OfflineSettlementInfoExportDTO>() : exportList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            // 改成输出excel文件
            response.setContentType(CommonConstants.CONTENT_TYPE_MS_EXCEL);
            String fileName = sheetNameStart +"_"+ sheetName;
            // 拼接header头部Content-disposition属性值；03版本后缀xls，之后的xlsx
            response.setHeader(CommonConstants.CONTENT_DISPOSITION, String.format("%s=%s%s",CommonConstants.EXCEL_ATTACHMENT , fileName, CommonConstants.EXCEL_XLS));
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            OssUtils ossUtils = new OssUtils();
            String ossFileName = UUID.randomUUID().toString().replace(StrConstant.STR_LINE, StrConstant.STR_EMPTY_STRING).toLowerCase()+ CommonConstants.EXCEL_XLS;
            downloadUrl = ossUtils.upload(excelStream,ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        }catch (Exception e){
            log.error("生成Excel文件 error:{}",e);
        }
        return downloadUrl;
    }

    /**
     * 获取excel内容
     * @param file 导入文件
     * @return 导入数据
     */
    private List<OfflineSettlementInfoModifyDTO> getExcelInfo(MultipartFile file) {
        List<OfflineSettlementInfoModifyDTO> importCommodityDTOList;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setHeadRows(1);
            importCommodityDTOList = ExcelImportUtil.importExcel(inputStream,OfflineSettlementInfoModifyDTO.class,params);
            if (!CollectionUtils.isEmpty(importCommodityDTOList)) {
                return importCommodityDTOList;
            }
        } catch (Exception e) {
            log.error("线下结算-解析批量结算Excel数据异常:", e);
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("线下结算-解析批量结算Excel数据异常:",e);
            }
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 数据校验（非空校验、金额最大值校验）
     * @param list 导入数据
     * @param errorMsgBuilder 错误信息
     */
    private void checkImportDataNull(List<OfflineSettlementInfoModifyDTO> list, StringBuilder errorMsgBuilder) {
        for (int i = 0; i < list.size(); i++) {
            int index = i + 2;
            OfflineSettlementInfoModifyDTO excelDTO = list.get(i);
            //必填字段非空校验
            if (StringUtils.isBlank(excelDTO.getOrderNo())) {
                errorMsgBuilder.append(String.format("第%s行，订单号为空", index)).append(StrConstant.SEMICOLON);
            }
            if (ObjectUtils.isEmpty(excelDTO.getSettleAmount())) {
                errorMsgBuilder.append(String.format("第%s行，结算金额为空", index)).append(StrConstant.SEMICOLON);
            }
            //判断金额不能大于 9999999.99
            if (ObjectUtils.isNotEmpty(excelDTO.getSettleAmount())
                    && excelDTO.getSettleAmount().compareTo(CommonConstants.SETTLE_AMOUNT_MAX) > 0) {
                errorMsgBuilder.append(String.format("第%s行，结算金额不能大于%s", index, CommonConstants.SETTLE_AMOUNT_MAX)).append(StrConstant.SEMICOLON);
            }

        }
    }

    /**
     * 校验订单号是否存在（校验订单号是否存在、判断订单是否已结算、结算金额不能大于订单实付金额）
     * @param list
     * @param errorMsgBuilder
     */
    private void checkImportData(List<OfflineSettlementInfoModifyDTO> list, StringBuilder errorMsgBuilder) {
        //批量查询订单信息
        CloudOrderItemsRepDTO orderItemsRepDTO = new CloudOrderItemsRepDTO();
        orderItemsRepDTO.setOrderNoList(list.stream().map(OfflineSettlementInfoModifyDTO::getOrderNo).collect(Collectors.toList()));
        log.info("根据订单号批量查询订单req:{}", orderItemsRepDTO);
        //存储查询结果，key为订单号，value为订单明细
        Map<String, List<CloudTradeOrderItemsDTO>> orderMap = new HashMap<>();
        Result<Map<String, List<CloudTradeOrderItemsDTO>>> cloudResult = middleGroundAPI.queryOrderItemsListByOrderNoList(orderItemsRepDTO);
        if (cloudResult.isSuccess() && CollectionUtils.isNotEmpty(cloudResult.getData())) {
            orderMap = cloudResult.getData();
        }

        //存储线下付款订单结算单查询结果，key为订单号，value为结算单
        Map<String, OfflineSettlementInfoDTO> offlineOrderMap = new HashMap<>();
        Result<List<OfflineSettlementInfoDTO>> result = middleGroundAPI.batchQueryOfflineSettlement(orderItemsRepDTO.getOrderNoList());
        if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            offlineOrderMap = result.getData().stream().collect(Collectors.toMap(OfflineSettlementInfoDTO::getOrderNo, Function.identity(),(key1, key2) -> key2));
        }

        //校验订单
        for (int i = 0; i < list.size(); i++) {
            int index = i + 2;
            OfflineSettlementInfoModifyDTO excelDTO = list.get(i);
            //校验订单号是否存在
            if (CollectionUtils.isEmpty(orderMap.get(excelDTO.getOrderNo()))) {
                errorMsgBuilder.append(String.format("第%s行，订单号%s不存在", index, excelDTO.getOrderNo())).append(StrConstant.SEMICOLON);
            }
            //判断订单是否已结算
            OfflineSettlementInfoDTO offlineOrderDTO = offlineOrderMap.get(excelDTO.getOrderNo());
            if (null == offlineOrderDTO) {
                errorMsgBuilder.append(String.format("第%s行，线下付款订单%s不存在", index, excelDTO.getOrderNo())).append(StrConstant.SEMICOLON);
            }
            if (offlineOrderDTO.getSettleStatus() == 1) {
                errorMsgBuilder.append(String.format("第%s行，订单号%s已结算，不支持重复结算", index, excelDTO.getOrderNo())).append(StrConstant.SEMICOLON);
            }
            //结算金额不能大于订单实付金额
            if (null != excelDTO.getSettleAmount() && excelDTO.getSettleAmount().compareTo(offlineOrderDTO.getOrderPayAmount()) > 0) {
                errorMsgBuilder.append(String.format("第%s行，订单号%s，结算金额不能大于订单实付金额", index, excelDTO.getOrderNo()));
            }

        }
    }

    /**
     * 获取文件名称
     * @return
     */
    private String getfileName(String suffix) {
        SimpleDateFormat datetime = new SimpleDateFormat("yyyyMMddHHmmss");
        //根据时间获取文件名
        return datetime.format(new Date()) + "." + suffix;
    }
}
