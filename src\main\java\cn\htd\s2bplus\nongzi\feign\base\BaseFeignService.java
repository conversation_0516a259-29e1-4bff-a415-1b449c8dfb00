package cn.htd.s2bplus.nongzi.feign.base;

import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.common.Pager;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.CommissionRecordDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.CommissionRecordReqDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.DistributorCommissionDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.distribute.DistributorCommissionReqDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@FeignClient(name = "s2bplus-base-service")
public interface BaseFeignService {
	@ApiOperation(value = "查询分销员佣金汇总")
	@PostMapping(value = "/commission/distributorCommissionList")
	PageResult<List<DistributorCommissionDTO>> queryDistributorCommission(@RequestBody DistributorCommissionReqDTO requestDTO);

	@ApiOperation(value = "查询分销员佣金明细")
	@PostMapping(value = "/commission/queryCommissionRecord")
	PageResult<List<CommissionRecordDTO>> queryCommissionRecord(CommissionRecordReqDTO requestDTO);
}
