package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wangxuan
 * @description: 批量线下开单列表导出excel-DTO
 * @date: 2023/5/25 13:48
 */
@ExcelTarget("GuestOrderExportDto")
@Data
public class GuestOrderExportDto {

    @ApiModelProperty(value = "批次号")
    @Excel(name = "批次号", height = 10, width = 20)
    private String batchNo;

    @ApiModelProperty(value = "订单号")
    @Excel(name = "订单号", height = 10, width = 20)
    private String orderNo;

    @ApiModelProperty(value = "下单时间")
    @Excel(name = "下单时间", height = 10, width = 20)
    private String createTime;

    @ApiModelProperty(value = "供货商名称")
    @Excel(name = "供货商名称", height = 10, width = 20)
    private String supplierName;

    @ApiModelProperty(value = "仓库名称")
    @Excel(name = "仓库名称", height = 10, width = 20)
    private String warehouseName;

    @ApiModelProperty(value = "sku编码")
    @Excel(name = "sku编码", height = 10, width = 20)
    private String skuCode;

    @ApiModelProperty(value = "会员编号")
    @Excel(name = "客户编码", height = 10, width = 20)
    private String buyerCode;

    @ApiModelProperty(value = "商品单价")
    @Excel(name = "商品单价", height = 10, width = 20)
    private BigDecimal goodPrice;

    @ApiModelProperty(value = "商品数量")
    @Excel(name = "商品数量", height = 10, width = 20)
    private BigDecimal goodCount;

    @ApiModelProperty(value = "第三方单号")
    @Excel(name = "第三方单号", height = 10, width = 20)
    private String thirdOrderNo;

    @ApiModelProperty(value = "提交结果信息: 0 订单创建中,1 创单成功,2 创单失败")
    @Excel(name = "订单状态", height = 10, width = 20)
    private String orderStatus;

    @ApiModelProperty(value = "提交结果信息")
    @Excel(name = "失败原因", height = 10, width = 20)
    private String resultMsg;

    @ApiModelProperty(value = "删除标志 0:未删除 1：删除")
    @Excel(name = "是否删除", height = 10, width = 20)
    private String deleteFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
