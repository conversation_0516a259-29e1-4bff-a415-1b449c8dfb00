package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class GoodsStockVO implements Serializable {

    /**
     * 虚拟库存id
     */
    private Long goodsStockId;

    /**
     * 卖家编码
     */
    private String sellerCode;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 库存变动数量
     */
    private Long operateStockNum;

    /**
     * 回滚标识,回滚后该条记录不可操作 0：未回滚； 1：已回滚
     */
    private Integer rollbackFlag;

    /**
     * 操作类型 -1：扣减；1：增加
     */
    private Integer operateType;

    /**
     * 删除标记  0 未删除 1 已删除
     */
    private Integer deleteFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
