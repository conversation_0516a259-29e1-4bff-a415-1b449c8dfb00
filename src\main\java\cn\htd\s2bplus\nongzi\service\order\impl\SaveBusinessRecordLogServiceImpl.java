package cn.htd.s2bplus.nongzi.service.order.impl;

import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.mapper.MerchantOperationLogDTOMapper;
import cn.htd.s2bplus.nongzi.pojo.dto.common.MerchantOperationLogDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.common.SaveOperationLogParamDTO;
import cn.htd.s2bplus.nongzi.service.order.SaveBusinessRecordLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SaveBusinessRecordLogServiceImpl implements SaveBusinessRecordLogService {
    /**
     * 日志
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MerchantOperationLogDTOMapper merchantOperationLogDTOMapper;

    /**
     * 保存操作日志记录
     *
     * @param saveOperationLogParamDTO  入参
     * @param loginUser 操作人信息
     * @return 响应
     */
    @Override
    public void saveRecord(SaveOperationLogParamDTO saveOperationLogParamDTO, LoginUserDetail loginUser) throws Exception {
        try {
            merchantOperationLogDTOMapper.
                    insert(buildOperationLogDto(saveOperationLogParamDTO,loginUser));
        } catch (Exception e) {
            logger.error("保存操作日志记录异常 入参：{}",saveOperationLogParamDTO);
            logger.error("saveRecord error", e);
            throw new Exception("saveRecord error", e);
        }
    }

    /**
     * 构建MerchantOperationLogDTO
     *
     * @param saveOperationLogParamDTO 入参
     * @param loginUser 登录信息
     * @return 响应
     */
    private MerchantOperationLogDTO buildOperationLogDto(SaveOperationLogParamDTO saveOperationLogParamDTO,
                                                         LoginUserDetail loginUser) {
        // 账号信息
        MerchantOperationLogDTO logDTO = new MerchantOperationLogDTO();
        BeanUtils.copyProperties(saveOperationLogParamDTO,logDTO);
        logDTO.setSellerCode(loginUser.getMemberCode());
        // 子账号
        if (loginUser.getParentAccount() != null) {
            logDTO.setOperatorCode(loginUser.getSubAccountLoginId());
        } else {
            logDTO.setOperatorCode(loginUser.getLoginId());
        }
        logDTO.setOperatorName(loginUser.getUserName());
        return logDTO;
    }

    /**
     * 获取关键日志信息
     *
     * @param businessKey   业务关键字
     * @param businessType  业务类型
     * @param operationType 操作类型
     * @return 响应
     */
    @Override
    public String getPrintLog(String businessKey, Integer businessType, Integer operationType) {
        StringBuffer buffer = new StringBuffer();
        return buffer.append(businessKey).append("-").append(businessType).append("-").append(operationType).toString();
    }
}
