package cn.htd.s2bplus.nongzi.pojo.dto.order;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @author: xqw
 * @date: 2020/10/14
 * @time: 17:15
 */
public class InShipmentDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "发货单集合"
    )
    private List<ShipmentDTO> shipmentList;
    @ApiModelProperty(value = "操作信息"
    )
    private OperatorDTO operator;
    @ApiModelProperty(value = "租户id"
    )
    private Long tenementId;

    public String toString() {
        return "InShipment{shipmentList=" + this.shipmentList + ", operator=" + this.operator + ", tenementId=" + this.tenementId + '}';
    }

    public InShipmentDTO() {
    }

    public List<ShipmentDTO> getShipmentList() {
        return this.shipmentList;
    }

    public OperatorDTO getOperator() {
        return this.operator;
    }

    public Long getTenementId() {
        return this.tenementId;
    }

    public void setShipmentList(List<ShipmentDTO> shipmentList) {
        this.shipmentList = shipmentList;
    }

    public void setOperator(OperatorDTO operator) {
        this.operator = operator;
    }

    public void setTenementId(Long tenementId) {
        this.tenementId = tenementId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof InShipmentDTO)) {
            return false;
        } else {
            InShipmentDTO other = (InShipmentDTO)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label47: {
                    Object this$shipmentList = this.getShipmentList();
                    Object other$shipmentList = other.getShipmentList();
                    if (this$shipmentList == null) {
                        if (other$shipmentList == null) {
                            break label47;
                        }
                    } else if (this$shipmentList.equals(other$shipmentList)) {
                        break label47;
                    }

                    return false;
                }

                Object this$operator = this.getOperator();
                Object other$operator = other.getOperator();
                if (this$operator == null) {
                    if (other$operator != null) {
                        return false;
                    }
                } else if (!this$operator.equals(other$operator)) {
                    return false;
                }

                Object this$tenementId = this.getTenementId();
                Object other$tenementId = other.getTenementId();
                if (this$tenementId == null) {
                    if (other$tenementId != null) {
                        return false;
                    }
                } else if (!this$tenementId.equals(other$tenementId)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof InShipmentDTO;
    }

    public int hashCode() {
        int result = 1;
        Object $shipmentList = this.getShipmentList();
        result = result * 59 + ($shipmentList == null ? 43 : $shipmentList.hashCode());
        Object $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        Object $tenementId = this.getTenementId();
        result = result * 59 + ($tenementId == null ? 43 : $tenementId.hashCode());
        return result;
    }
}

