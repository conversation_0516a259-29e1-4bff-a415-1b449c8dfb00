package cn.htd.s2bplus.nongzi.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StreamUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

/**
 * request请求安全过滤包装类
 * <AUTHOR>
 */
public class XssSqlHttpServletRequestWrapper extends HttpServletRequestWrapper {


    private static final Logger logger = LoggerFactory.getLogger(XssSqlHttpServletRequestWrapper.class);

    private byte[] requestBody;
    private static Charset charSet;

    public XssSqlHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
        String method = request.getMethod();
        logger.info("request:{}",request.toString());
        if (method.equals("POST") || method.equals("post")){
            try {
                String requestBodyStr = getRequestPostStr(request);
                if (StringUtils.isNotBlank(requestBodyStr)) {
                    requestBodyStr=this.filterParamString(requestBodyStr);
                    Object parse = JSON.parse(requestBodyStr);
                    requestBody = parse.toString().getBytes(charSet);
                } else {
                    requestBody = new byte[0];
                }
            } catch (IOException e) {
                logger.error("XssSqlHttpServletRequestWrapper.获取body数据异常：{}",e);
            }
        }
    }


    public String getRequestPostStr(HttpServletRequest request)
            throws IOException {
        String charSetStr = request.getCharacterEncoding();
        if (charSetStr == null) {
            charSetStr = "UTF-8";
        }
        charSet = Charset.forName(charSetStr);

        return StreamUtils.copyToString(request.getInputStream(), charSet);
    }

    public ServletInputStream getInputStream() {
        if (requestBody == null) {
            requestBody = new byte[0];
        }

        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(requestBody);

        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }
            @Override
            public boolean isReady() {
                return false;
            }
            @Override
            public void setReadListener(ReadListener readListener) {
            }
            @Override
            public int read() {
                return byteArrayInputStream.read();
            }
        };
    }

    /**
     * description 重写  数组参数过滤
     *
     * @param parameter 1
     * @return java.lang.String[]
     */
    @Override
    public String[] getParameterValues(String parameter) {
        String[] values = super.getParameterValues(parameter);
        if (values == null) {
            return new String[0];
        }
        int count = values.length;
        String[] encodedValues = new String[count];
        for (int i = 0 ; i < count ; i++ ) {
            encodedValues[i] = filterParamString(values[i]);
        }
        return encodedValues;
    }

    @Override
    public Map<String,String[]> getParameterMap() {
        Map<String,String[]> primary = super.getParameterMap();
        Map<String,String[]> result = new HashMap<>(16);
        for (Map.Entry<String,String[]> entry : primary.entrySet()) {
            result.put(entry.getKey(),filterEntryString(entry.getValue()));
        }
        return result;
    }

    @Override
    public String getParameter(String parameter) {
        return filterParamString(super.getParameter(parameter));
    }

    @Override
    public String getHeader(String name) {
        return filterParamString(super.getHeader(name));
    }

    @Override
    public Cookie[] getCookies() {
        Cookie[] cookies = super.getCookies();
        if (cookies != null) {
            for (int i = 0 ; i < cookies.length; i++) {
                Cookie cookie = cookies[i];
                cookie.setValue(filterParamString(cookie.getValue()));
            }
        }
        return cookies;
    }

    /**
     * description 过滤字符串数组不安全内容
     *
     * @param value 1
     * @return java.lang.String[]
     */
    private String[] filterEntryString(String[] value) {
        for (int i = 0; i < value.length; i++) {
            value[i] = filterParamString(value[i]);
        }
        return value;
    }

    /**
     * description 过滤字符串不安全内容
     *
     * @param value 1
     * @return java.lang.String
     */
    private String filterParamString(String value) {
        if (null == value) {
            return null;
        }
        // 过滤XSS 和 SQL 注入
        return XssUtil.stripSqlXss(value);
    }

}
