package cn.htd.s2bplus.nongzi.enums;

/**
 * 货运方式 1-快递 2-汽配 3-火车 4-轮船 枚举类
 */
public enum FreightMethodEnum {
    /**
     * 快递
     */
    express("1","快递"),
    /**
     * 汽配
     */
    expressCar("2","汽配"),
    /**
     * 火车
     */
    train("3","火车"),
    /**
     * 轮船
     */
    ship("4","轮船"),
    ;
    private String type;
    private String name;

    FreightMethodEnum(String type, String name){
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return this.type;
    }

    public String getName() {
        return this.name;
    }

    public static FreightMethodEnum getByType(String type) {
        for (FreightMethodEnum value : FreightMethodEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
