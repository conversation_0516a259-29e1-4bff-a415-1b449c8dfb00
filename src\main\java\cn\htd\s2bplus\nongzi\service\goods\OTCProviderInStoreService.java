package cn.htd.s2bplus.nongzi.service.goods;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.QueryIntentionAddressPageDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.QuerySalesInStoreDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface OTCProviderInStoreService {

    /**
     * 导出分销商地址关系
     * @param queryIntentionAddressPageDTO
     * @param response
     * @return
     */
    Result<Boolean> exportIntentionAddress(QueryIntentionAddressPageDTO queryIntentionAddressPageDTO,
                                           HttpServletResponse response,LoginUserDetail loginUser);

    /**
     * 导出销售计划
     * @param querySalesInStoreDTO
     * @param response
     * @return
     */
    Result<String> exportSalesInStore(QuerySalesInStoreDTO querySalesInStoreDTO, HttpServletResponse response,LoginUserDetail loginUser);

    /**
     * 导出到店地址
     * @param intentionNo
     * @param response
     * @return
     */
    Result<String> exportIntentionInStore(String intentionNo,HttpServletResponse response);

    /**
     * 批量导入销售计划
     * @param file
     * @return
     */
    Result<Boolean> batchImportSalesInStore(MultipartFile file, LoginUserDetail loginUser);

    Result<String> batchImportIntentionAddressWarehouseRecord(MultipartFile file, LoginUserDetail loginUser);

    Result<String> batchImportUpdateServiceShip(MultipartFile file, LoginUserDetail loginUser);

    Result<Boolean> batchImportSalesInStoreNew(MultipartFile file, LoginUserDetail loginUser);
}
