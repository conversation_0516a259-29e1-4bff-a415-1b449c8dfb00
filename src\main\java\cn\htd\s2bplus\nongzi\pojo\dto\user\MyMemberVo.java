package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: s2bplus-oop-api
 * @Package: cn.htd.s2bplus.oopapi.dto.user.valet
 * @ClassName: MyMemberVo
 * @Author: 80333
 * @Description:
 * @Date: 2021/1/14 9:39
 */
@Data
public class MyMemberVo implements Serializable {
    @ApiModelProperty(value = " 会员ID", notes = "会员ID", example = "1")
    private Long memberId;
    @ApiModelProperty(value = " 会员编码", notes = "会员编码", example = "926XXX")
    private String memberCode;
    @ApiModelProperty(value = " 会员名称", notes = "会员名称", example = "XX投资管理有限公司")
    private String memberName;
    @ApiModelProperty(value = " 公司名称", notes = "公司名称", example = "XX投资管理有限公司")
    private String companyName;
    @ApiModelProperty(value = " 法人姓名", notes = "法人姓名", example = "范XX")
    private String artificialPersonName;
    @ApiModelProperty(value = " 法人电话", notes = "法人电话", example = "13899999XXX")
    private String artificialPersonMobile;
    @ApiModelProperty(value = " 客户经理", notes = "客户经理", example = "刘X")
    private String belongManagerName;
    @ApiModelProperty(value = "会员类型 1.非会员、2.产业会员、3.标准会员",
            notes = "会员类型 1.非会员、2.产业会员、3.标准会员",
            example = "1",
            hidden = true)
    private String memberFlag;
    @ApiModelProperty(value = " 注册时间", notes = "注册时间", example = "2019-04-25 21:52:20")
    private String registTime;
    @ApiModelProperty(
            value = " 会员审核状态",
            notes = "审核状态：1为待审核，2为通过，3为驳回",
            example = "1"
    )
    private String verifyStatus;
    @ApiModelProperty(
            value = " 无营业执照转有营业执照审核状态",
            notes = "1：待审核，2：已通过，3：被驳回",
            example = "1"
    )
    private String transeBusinessLicenseStatus;
    @ApiModelProperty(
            value = " 非会员转正状态",
            notes = "非会员转正状态：1为待审核，2为通过，3为驳回",
            example = "1"
    )
    private String verifyStatusTranse;

    @ApiModelProperty(
            value = " 名称修改状态",
            notes = "1：待审核，2：已通过，3：被驳回",
            example = "1"
    )
    private String companyNameModifyStatus;

    private Integer hasBusinessLicense;

    @ApiModelProperty(
            value = " 是否展示修改按钮",
            notes = "true展示，false不展示",
            example = "1"
    )
    private Boolean showModifyButton;

    @ApiModelProperty(
            value = "认证状态",
            notes = "-1-所有，0-未认证，1-待审核，2-已认证，3-认证失效，4-认证失败，5-人脸识别中"
    )
    private String attestationStatus;

    @ApiModelProperty(
            value = "认证失败/失效原因",
            notes = "认证失败/失效原因",
            example = "变更法人姓名"
    )
    private String attestationRemark;

    @ApiModelProperty(
            value = "平台公司名称",
            notes = "平台公司名称",
            example = "浙江汇鸿有限公司"
    )
    private String platformCompanyName;

    @ApiModelProperty(value = "认证失效时间",notes = "认证失效时间",example = "2019-04-25 21:53:23")
    private Date attestationUneffectTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
