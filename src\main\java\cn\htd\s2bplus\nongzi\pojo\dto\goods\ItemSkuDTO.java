package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("商品sku信息")
public class ItemSkuDTO implements Serializable {
	private static final long serialVersionUID = -1906838845616500575L;
	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	private Long itemSkuId;

	/**
	 * 商品id
	 */

	@ApiModelProperty(value = "商品id")
	private Long itemId;

	/**
	 * 产品skuId
	 */

	@ApiModelProperty(value = "产品skuId")
	private Long spuSkuId;

	/**
	 * 产品sku编码
	 */

	@ApiModelProperty(value = "产品sku编码")
	private String spuSkuCode;

	/**
	 * 租户ID
	 */

	@ApiModelProperty(value = "租户ID")
	private Long tenementId;

	/**
	 * 商品sku编码，保存当前hybris系统中的HTDH_******类似的编码
	 */

	@ApiModelProperty(value = "商品sku编码，保存当前hybris系统中的HTDH_******类似的编码")
	private String itemSkuCode;

	/**
	 * 卖家id
	 */

	@ApiModelProperty(value = "卖家id")
	private Long sellerId;

	/**
	 * 店铺id
	 */

	@ApiModelProperty(value = "店铺id")
	private Long shopId;

	/**
	 * 商品副标题
	 */

	@ApiModelProperty(value = "商品副标题")
	private String subTitle;

	/**
	 * sku 状态:0 无效 1 有效
	 */

	@ApiModelProperty(value = "sku 状态:0 无效 1 有效")
	private Integer skuStatus;

	/**
	 * 是否是标品,0:标品，1:非标品
	 */

	@ApiModelProperty(value = "是否是标品,0:标品，1:非标品")
	private Integer isStandard;

	/**
	 * sku 类型 1:主sku,2:非主sku
	 */

	@ApiModelProperty(value = "sku 类型 1:主sku,2:非主sku")
	private Integer skuType;

	/**
	 * 广告语,未使用！！
	 */

	@ApiModelProperty(value = "广告语,未使用！！")
	private String ad;

	/**
	 * 销售属性集合：keyId:valueId
	 */

	@ApiModelProperty(value = "销售属性集合：keyId:valueId")
	private String attributes;

	/**
	 * 商品SKU在ERP编码
	 */

	@ApiModelProperty(value = "商品SKU在ERP编码")
	private String skuErpCode;

	/**
	 * 外接商品sku_id或者erp上行过来的sku
	 */

	@ApiModelProperty(value = "外接商品sku_id或者erp上行过来的sku")
	private String outerSkuId;

	/**
	 * EAN编码
	 */

	@ApiModelProperty(value = "EAN编码")
	private String eanCode;


	/**
	 * 产品id
	 */

	@ApiModelProperty(value = "产品id")
	private Long spuId;


	/**
	 * 起订数
	 */
	@ApiModelProperty(value = "起订数")
	private BigDecimal mimQuantity;

	/**
	 * 是否限购
	 */
	@ApiModelProperty(value = "是否限购")
	private Integer isPurchaseLimit;

	/**
	 *最大限购数
	 */
	@ApiModelProperty(value = "最大限购数")
	private BigDecimal maxPurchaseQuantity;

	/**
	 * 是否预售 默认 0否
	 */
	@ApiModelProperty(value = "是否预售 默认 0否")
	private Integer preSaleFlag;

	/**
	 * 成本价/外部供货价
	 */

	@ApiModelProperty(value = "成本价/外部供货价")
	private BigDecimal costPrice;

	/**
	 * 分销限价/外部销售价
	 */

	@ApiModelProperty(value = "分销限价/外部销售价")
	private BigDecimal saleLimitedPrice;

	/**
	 * 零售价
	 */

	@ApiModelProperty(value = "零售价")
	private BigDecimal retailPrice;


	/**
	 * 组合价
	 */

	@ApiModelProperty(value = "组合价")
	private BigDecimal price;


	@ApiModelProperty(value = "商品库存")
	private String skuCount;

	@ApiModelProperty(value = "唯一标识")
	private Integer flag;

	@ApiModelProperty(value = "商品名称+属性组合")
	private String goodsNameAttr;

	@ApiModelProperty(value = "价格标志 1-区域 2-阶梯 3-活动")
	private String priceMark;


	@ApiModelProperty(value = "销售属性集合名称 颜色:白色;内存:16G;")
	private String attributesName;

	@ApiModelProperty("精准库存")
	private BigDecimal inventory;

	@ApiModelProperty(value = "库存模式 1-手动 2-库存管理")
	private String inventorySource;

	@ApiModelProperty(value = "商品sku图片DTO")
	private List<ItemSkuPictureDTO> itemSkuPictureDTOList;

	@ApiModelProperty(value = "sku图片名称：文件夹名-图片名")
	private String skuPicName;

	@ApiModelProperty(value = "商品货号")
	private String productNo;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
	}
}
