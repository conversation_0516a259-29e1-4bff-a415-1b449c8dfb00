package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ExportGoodsInfoReqVO implements Serializable {
    private static final long serialVersionUID = 6266817088396731758L;

    @ApiModelProperty(value = "条形码")
    private String eanCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "上下架状态 0:下架，1：上架")
    private String status;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "页码",required = true)
    private Integer page;

    @NotNull(message = "条数不能为空")
    @ApiModelProperty(value = "条数",required = true)
    private Integer rows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
