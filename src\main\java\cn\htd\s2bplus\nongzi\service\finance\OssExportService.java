package cn.htd.s2bplus.nongzi.service.finance;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.MemberShopInfoReqDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


public interface OssExportService {

    Result<Boolean> exportShopInfo(MemberShopInfoReqDTO memberShopInfoReqDTO,
                                   HttpServletRequest request,
                                   HttpServletResponse response);


}
