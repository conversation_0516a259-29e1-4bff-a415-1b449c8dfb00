package cn.htd.s2bplus.nongzi.service.recordlog;


import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;

/**
 * <AUTHOR>
 * @title BusinessRecordLogService
 * @description 业务日志记录接口
 * @Date: 2022/4/21 10:02
 */
public interface BusinessRecordLogService {
    /**
     * 操作类型
     *
     * @return Integer
     */
    Integer operationType();

    /**
     * 保存操作日志
     *
     * @param object param
     * @param loginUser 操作人信息
     * @return 响应
     */
    void saveBusinessRecordLog(Object[] object, LoginUserDetail loginUser);
}
