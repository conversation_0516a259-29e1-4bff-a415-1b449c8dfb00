package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class SettlementOrderItemDTO implements Serializable {
    private static final long serialVersionUID = 1443949397684268985L;

    @ApiModelProperty(value = "结算单号",position = 1)
    private String settlementNo;

    @ApiModelProperty(value = "单据类型编号",position = 2)
    private String typeCode;

    @ApiModelProperty(value = "清算单号",position = 3)
    private String statementNo;

    @ApiModelProperty(value = "清算单行id",position = 4)
    private Long statementItemId;

    @ApiModelProperty(value = "单据编号",position = 5)
    private String finOrderNo;

    @ApiModelProperty(value = "付款方式 1现金 2微信 3支付宝 4余额 5大额转账 6其他",position = 6)
    private String payType;

    @ApiModelProperty(value = "付款渠道编码",position = 7)
    private String payCode;

    @ApiModelProperty(value = "交易单号",position = 8)
    private String tradeNo;

    @ApiModelProperty(value = "订单号",position = 9)
    private String orderNo;

    @ApiModelProperty(value = "渠道编码",position = 10)
    private String channelCode;

    @ApiModelProperty(value = "金额",position = 11)
    private BigDecimal amount;

    @ApiModelProperty(value = "订单类型 1-正向 2-逆向",position = 12)
    private String orderType;

    @ApiModelProperty(value = "商户编码",position = 13)
    private String memberCode;

    @ApiModelProperty(value = "结算单场景",position = 14)
    private String sence;

    @ApiModelProperty(value = "结算类型 00:默认，10:业务方结算，20自动结算，30手动结算",position = 15)
    private String settlementType;

    @ApiModelProperty(value = "结算单状态 00初始化 10 待结算 30已结算",position = 16)
    private String settlementStatus;

    @ApiModelProperty(value = "账单生成类型：00:默认，10:业务方生成账单，20自动生成账单，30手动生成账单",position = 17)
    private String execType;

    @ApiModelProperty(value = "账单是否生成 00未生成 01已生成",position = 18)
    private String execGenStatus;

    @ApiModelProperty(value = "凭证是否生成 00未生成 01已生成 02无需生成",position = 19)
    private String certificateGenStatus;

    @ApiModelProperty(value = "账单时间",position = 20)
    private Date execTime;

    @ApiModelProperty(value = "是否删除 0-否 1-是",position = 21)
    private Integer deleteFlag;

    @ApiModelProperty(value = "创建时间",position = 22)
    private Date createTime;

    @ApiModelProperty(value = "支付单据创建时间",position = 23)
    private Date payOrderTime;

    @ApiModelProperty(value = "支付单据结算时间",position = 24)
    private Date payOrderExecTime;

    @ApiModelProperty(value = "支付单据号",position = 25)
    private String payOrderNo;

    @ApiModelProperty(value = "单据类型编号列表",position = 26)
    private List<String> typeCodeList;

    @ApiModelProperty(value = "角色编码",position = 27)
    private String finRoleCode;

    @ApiModelProperty(value = "凭证id",position = 28)
    private String finCertificateId;
    private Long ftoItemId;
    private String certificateTaskId;
    private StatementModelItemDTO statementModelItemDTO;

    public SettlementOrderItemDTO() {
    }

    public String getSettlementNo() {
        return this.settlementNo;
    }

    public String getTypeCode() {
        return this.typeCode;
    }

    public String getStatementNo() {
        return this.statementNo;
    }

    public Long getStatementItemId() {
        return this.statementItemId;
    }

    public String getFinOrderNo() {
        return this.finOrderNo;
    }

    public String getPayType() {
        return this.payType;
    }

    public String getPayCode() {
        return this.payCode;
    }

    public String getTradeNo() {
        return this.tradeNo;
    }

    public String getOrderNo() {
        return this.orderNo;
    }

    public String getChannelCode() {
        return this.channelCode;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public String getOrderType() {
        return this.orderType;
    }

    public String getMemberCode() {
        return this.memberCode;
    }

    public String getSence() {
        return this.sence;
    }

    public String getSettlementType() {
        return this.settlementType;
    }

    public String getSettlementStatus() {
        return this.settlementStatus;
    }

    public String getExecType() {
        return this.execType;
    }

    public String getExecGenStatus() {
        return this.execGenStatus;
    }

    public String getCertificateGenStatus() {
        return this.certificateGenStatus;
    }

    public Date getExecTime() {
        return this.execTime;
    }

    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public Date getPayOrderTime() {
        return this.payOrderTime;
    }

    public Date getPayOrderExecTime() {
        return this.payOrderExecTime;
    }

    public String getPayOrderNo() {
        return this.payOrderNo;
    }

    public List<String> getTypeCodeList() {
        return this.typeCodeList;
    }

    public String getFinRoleCode() {
        return this.finRoleCode;
    }

    public String getFinCertificateId() {
        return this.finCertificateId;
    }

    public Long getFtoItemId() {
        return this.ftoItemId;
    }

    public String getCertificateTaskId() {
        return this.certificateTaskId;
    }

    public StatementModelItemDTO getStatementModelItemDTO() {
        return this.statementModelItemDTO;
    }

    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public void setStatementNo(String statementNo) {
        this.statementNo = statementNo;
    }

    public void setStatementItemId(Long statementItemId) {
        this.statementItemId = statementItemId;
    }

    public void setFinOrderNo(String finOrderNo) {
        this.finOrderNo = finOrderNo;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public void setPayCode(String payCode) {
        this.payCode = payCode;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode;
    }

    public void setSence(String sence) {
        this.sence = sence;
    }

    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    public void setSettlementStatus(String settlementStatus) {
        this.settlementStatus = settlementStatus;
    }

    public void setExecType(String execType) {
        this.execType = execType;
    }

    public void setExecGenStatus(String execGenStatus) {
        this.execGenStatus = execGenStatus;
    }

    public void setCertificateGenStatus(String certificateGenStatus) {
        this.certificateGenStatus = certificateGenStatus;
    }

    public void setExecTime(Date execTime) {
        this.execTime = execTime;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setPayOrderTime(Date payOrderTime) {
        this.payOrderTime = payOrderTime;
    }

    public void setPayOrderExecTime(Date payOrderExecTime) {
        this.payOrderExecTime = payOrderExecTime;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public void setTypeCodeList(List<String> typeCodeList) {
        this.typeCodeList = typeCodeList;
    }

    public void setFinRoleCode(String finRoleCode) {
        this.finRoleCode = finRoleCode;
    }

    public void setFinCertificateId(String finCertificateId) {
        this.finCertificateId = finCertificateId;
    }

    public void setFtoItemId(Long ftoItemId) {
        this.ftoItemId = ftoItemId;
    }

    public void setCertificateTaskId(String certificateTaskId) {
        this.certificateTaskId = certificateTaskId;
    }

    public void setStatementModelItemDTO(StatementModelItemDTO statementModelItemDTO) {
        this.statementModelItemDTO = statementModelItemDTO;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SettlementOrderItemDTO)) {
            return false;
        } else {
            SettlementOrderItemDTO other = (SettlementOrderItemDTO)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label383: {
                    Object this$settlementNo = this.getSettlementNo();
                    Object other$settlementNo = other.getSettlementNo();
                    if (this$settlementNo == null) {
                        if (other$settlementNo == null) {
                            break label383;
                        }
                    } else if (this$settlementNo.equals(other$settlementNo)) {
                        break label383;
                    }

                    return false;
                }

                Object this$typeCode = this.getTypeCode();
                Object other$typeCode = other.getTypeCode();
                if (this$typeCode == null) {
                    if (other$typeCode != null) {
                        return false;
                    }
                } else if (!this$typeCode.equals(other$typeCode)) {
                    return false;
                }

                Object this$statementNo = this.getStatementNo();
                Object other$statementNo = other.getStatementNo();
                if (this$statementNo == null) {
                    if (other$statementNo != null) {
                        return false;
                    }
                } else if (!this$statementNo.equals(other$statementNo)) {
                    return false;
                }

                label362: {
                    Object this$statementItemId = this.getStatementItemId();
                    Object other$statementItemId = other.getStatementItemId();
                    if (this$statementItemId == null) {
                        if (other$statementItemId == null) {
                            break label362;
                        }
                    } else if (this$statementItemId.equals(other$statementItemId)) {
                        break label362;
                    }

                    return false;
                }

                label355: {
                    Object this$finOrderNo = this.getFinOrderNo();
                    Object other$finOrderNo = other.getFinOrderNo();
                    if (this$finOrderNo == null) {
                        if (other$finOrderNo == null) {
                            break label355;
                        }
                    } else if (this$finOrderNo.equals(other$finOrderNo)) {
                        break label355;
                    }

                    return false;
                }

                Object this$payType = this.getPayType();
                Object other$payType = other.getPayType();
                if (this$payType == null) {
                    if (other$payType != null) {
                        return false;
                    }
                } else if (!this$payType.equals(other$payType)) {
                    return false;
                }

                Object this$payCode = this.getPayCode();
                Object other$payCode = other.getPayCode();
                if (this$payCode == null) {
                    if (other$payCode != null) {
                        return false;
                    }
                } else if (!this$payCode.equals(other$payCode)) {
                    return false;
                }

                label334: {
                    Object this$tradeNo = this.getTradeNo();
                    Object other$tradeNo = other.getTradeNo();
                    if (this$tradeNo == null) {
                        if (other$tradeNo == null) {
                            break label334;
                        }
                    } else if (this$tradeNo.equals(other$tradeNo)) {
                        break label334;
                    }

                    return false;
                }

                label327: {
                    Object this$orderNo = this.getOrderNo();
                    Object other$orderNo = other.getOrderNo();
                    if (this$orderNo == null) {
                        if (other$orderNo == null) {
                            break label327;
                        }
                    } else if (this$orderNo.equals(other$orderNo)) {
                        break label327;
                    }

                    return false;
                }

                Object this$channelCode = this.getChannelCode();
                Object other$channelCode = other.getChannelCode();
                if (this$channelCode == null) {
                    if (other$channelCode != null) {
                        return false;
                    }
                } else if (!this$channelCode.equals(other$channelCode)) {
                    return false;
                }

                label313: {
                    Object this$amount = this.getAmount();
                    Object other$amount = other.getAmount();
                    if (this$amount == null) {
                        if (other$amount == null) {
                            break label313;
                        }
                    } else if (this$amount.equals(other$amount)) {
                        break label313;
                    }

                    return false;
                }

                Object this$orderType = this.getOrderType();
                Object other$orderType = other.getOrderType();
                if (this$orderType == null) {
                    if (other$orderType != null) {
                        return false;
                    }
                } else if (!this$orderType.equals(other$orderType)) {
                    return false;
                }

                label299: {
                    Object this$memberCode = this.getMemberCode();
                    Object other$memberCode = other.getMemberCode();
                    if (this$memberCode == null) {
                        if (other$memberCode == null) {
                            break label299;
                        }
                    } else if (this$memberCode.equals(other$memberCode)) {
                        break label299;
                    }

                    return false;
                }

                Object this$sence = this.getSence();
                Object other$sence = other.getSence();
                if (this$sence == null) {
                    if (other$sence != null) {
                        return false;
                    }
                } else if (!this$sence.equals(other$sence)) {
                    return false;
                }

                Object this$settlementType = this.getSettlementType();
                Object other$settlementType = other.getSettlementType();
                if (this$settlementType == null) {
                    if (other$settlementType != null) {
                        return false;
                    }
                } else if (!this$settlementType.equals(other$settlementType)) {
                    return false;
                }

                label278: {
                    Object this$settlementStatus = this.getSettlementStatus();
                    Object other$settlementStatus = other.getSettlementStatus();
                    if (this$settlementStatus == null) {
                        if (other$settlementStatus == null) {
                            break label278;
                        }
                    } else if (this$settlementStatus.equals(other$settlementStatus)) {
                        break label278;
                    }

                    return false;
                }

                label271: {
                    Object this$execType = this.getExecType();
                    Object other$execType = other.getExecType();
                    if (this$execType == null) {
                        if (other$execType == null) {
                            break label271;
                        }
                    } else if (this$execType.equals(other$execType)) {
                        break label271;
                    }

                    return false;
                }

                Object this$execGenStatus = this.getExecGenStatus();
                Object other$execGenStatus = other.getExecGenStatus();
                if (this$execGenStatus == null) {
                    if (other$execGenStatus != null) {
                        return false;
                    }
                } else if (!this$execGenStatus.equals(other$execGenStatus)) {
                    return false;
                }

                Object this$certificateGenStatus = this.getCertificateGenStatus();
                Object other$certificateGenStatus = other.getCertificateGenStatus();
                if (this$certificateGenStatus == null) {
                    if (other$certificateGenStatus != null) {
                        return false;
                    }
                } else if (!this$certificateGenStatus.equals(other$certificateGenStatus)) {
                    return false;
                }

                label250: {
                    Object this$execTime = this.getExecTime();
                    Object other$execTime = other.getExecTime();
                    if (this$execTime == null) {
                        if (other$execTime == null) {
                            break label250;
                        }
                    } else if (this$execTime.equals(other$execTime)) {
                        break label250;
                    }

                    return false;
                }

                label243: {
                    Object this$deleteFlag = this.getDeleteFlag();
                    Object other$deleteFlag = other.getDeleteFlag();
                    if (this$deleteFlag == null) {
                        if (other$deleteFlag == null) {
                            break label243;
                        }
                    } else if (this$deleteFlag.equals(other$deleteFlag)) {
                        break label243;
                    }

                    return false;
                }

                Object this$createTime = this.getCreateTime();
                Object other$createTime = other.getCreateTime();
                if (this$createTime == null) {
                    if (other$createTime != null) {
                        return false;
                    }
                } else if (!this$createTime.equals(other$createTime)) {
                    return false;
                }

                Object this$payOrderTime = this.getPayOrderTime();
                Object other$payOrderTime = other.getPayOrderTime();
                if (this$payOrderTime == null) {
                    if (other$payOrderTime != null) {
                        return false;
                    }
                } else if (!this$payOrderTime.equals(other$payOrderTime)) {
                    return false;
                }

                label222: {
                    Object this$payOrderExecTime = this.getPayOrderExecTime();
                    Object other$payOrderExecTime = other.getPayOrderExecTime();
                    if (this$payOrderExecTime == null) {
                        if (other$payOrderExecTime == null) {
                            break label222;
                        }
                    } else if (this$payOrderExecTime.equals(other$payOrderExecTime)) {
                        break label222;
                    }

                    return false;
                }

                label215: {
                    Object this$payOrderNo = this.getPayOrderNo();
                    Object other$payOrderNo = other.getPayOrderNo();
                    if (this$payOrderNo == null) {
                        if (other$payOrderNo == null) {
                            break label215;
                        }
                    } else if (this$payOrderNo.equals(other$payOrderNo)) {
                        break label215;
                    }

                    return false;
                }

                Object this$typeCodeList = this.getTypeCodeList();
                Object other$typeCodeList = other.getTypeCodeList();
                if (this$typeCodeList == null) {
                    if (other$typeCodeList != null) {
                        return false;
                    }
                } else if (!this$typeCodeList.equals(other$typeCodeList)) {
                    return false;
                }

                label201: {
                    Object this$finRoleCode = this.getFinRoleCode();
                    Object other$finRoleCode = other.getFinRoleCode();
                    if (this$finRoleCode == null) {
                        if (other$finRoleCode == null) {
                            break label201;
                        }
                    } else if (this$finRoleCode.equals(other$finRoleCode)) {
                        break label201;
                    }

                    return false;
                }

                Object this$finCertificateId = this.getFinCertificateId();
                Object other$finCertificateId = other.getFinCertificateId();
                if (this$finCertificateId == null) {
                    if (other$finCertificateId != null) {
                        return false;
                    }
                } else if (!this$finCertificateId.equals(other$finCertificateId)) {
                    return false;
                }

                label187: {
                    Object this$ftoItemId = this.getFtoItemId();
                    Object other$ftoItemId = other.getFtoItemId();
                    if (this$ftoItemId == null) {
                        if (other$ftoItemId == null) {
                            break label187;
                        }
                    } else if (this$ftoItemId.equals(other$ftoItemId)) {
                        break label187;
                    }

                    return false;
                }

                Object this$certificateTaskId = this.getCertificateTaskId();
                Object other$certificateTaskId = other.getCertificateTaskId();
                if (this$certificateTaskId == null) {
                    if (other$certificateTaskId != null) {
                        return false;
                    }
                } else if (!this$certificateTaskId.equals(other$certificateTaskId)) {
                    return false;
                }

                Object this$statementModelItemDTO = this.getStatementModelItemDTO();
                Object other$statementModelItemDTO = other.getStatementModelItemDTO();
                if (this$statementModelItemDTO == null) {
                    if (other$statementModelItemDTO != null) {
                        return false;
                    }
                } else if (!this$statementModelItemDTO.equals(other$statementModelItemDTO)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SettlementOrderItemDTO;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $settlementNo = this.getSettlementNo();
        result = result * 59 + ($settlementNo == null ? 43 : $settlementNo.hashCode());
        Object $typeCode = this.getTypeCode();
        result = result * 59 + ($typeCode == null ? 43 : $typeCode.hashCode());
        Object $statementNo = this.getStatementNo();
        result = result * 59 + ($statementNo == null ? 43 : $statementNo.hashCode());
        Object $statementItemId = this.getStatementItemId();
        result = result * 59 + ($statementItemId == null ? 43 : $statementItemId.hashCode());
        Object $finOrderNo = this.getFinOrderNo();
        result = result * 59 + ($finOrderNo == null ? 43 : $finOrderNo.hashCode());
        Object $payType = this.getPayType();
        result = result * 59 + ($payType == null ? 43 : $payType.hashCode());
        Object $payCode = this.getPayCode();
        result = result * 59 + ($payCode == null ? 43 : $payCode.hashCode());
        Object $tradeNo = this.getTradeNo();
        result = result * 59 + ($tradeNo == null ? 43 : $tradeNo.hashCode());
        Object $orderNo = this.getOrderNo();
        result = result * 59 + ($orderNo == null ? 43 : $orderNo.hashCode());
        Object $channelCode = this.getChannelCode();
        result = result * 59 + ($channelCode == null ? 43 : $channelCode.hashCode());
        Object $amount = this.getAmount();
        result = result * 59 + ($amount == null ? 43 : $amount.hashCode());
        Object $orderType = this.getOrderType();
        result = result * 59 + ($orderType == null ? 43 : $orderType.hashCode());
        Object $memberCode = this.getMemberCode();
        result = result * 59 + ($memberCode == null ? 43 : $memberCode.hashCode());
        Object $sence = this.getSence();
        result = result * 59 + ($sence == null ? 43 : $sence.hashCode());
        Object $settlementType = this.getSettlementType();
        result = result * 59 + ($settlementType == null ? 43 : $settlementType.hashCode());
        Object $settlementStatus = this.getSettlementStatus();
        result = result * 59 + ($settlementStatus == null ? 43 : $settlementStatus.hashCode());
        Object $execType = this.getExecType();
        result = result * 59 + ($execType == null ? 43 : $execType.hashCode());
        Object $execGenStatus = this.getExecGenStatus();
        result = result * 59 + ($execGenStatus == null ? 43 : $execGenStatus.hashCode());
        Object $certificateGenStatus = this.getCertificateGenStatus();
        result = result * 59 + ($certificateGenStatus == null ? 43 : $certificateGenStatus.hashCode());
        Object $execTime = this.getExecTime();
        result = result * 59 + ($execTime == null ? 43 : $execTime.hashCode());
        Object $deleteFlag = this.getDeleteFlag();
        result = result * 59 + ($deleteFlag == null ? 43 : $deleteFlag.hashCode());
        Object $createTime = this.getCreateTime();
        result = result * 59 + ($createTime == null ? 43 : $createTime.hashCode());
        Object $payOrderTime = this.getPayOrderTime();
        result = result * 59 + ($payOrderTime == null ? 43 : $payOrderTime.hashCode());
        Object $payOrderExecTime = this.getPayOrderExecTime();
        result = result * 59 + ($payOrderExecTime == null ? 43 : $payOrderExecTime.hashCode());
        Object $payOrderNo = this.getPayOrderNo();
        result = result * 59 + ($payOrderNo == null ? 43 : $payOrderNo.hashCode());
        Object $typeCodeList = this.getTypeCodeList();
        result = result * 59 + ($typeCodeList == null ? 43 : $typeCodeList.hashCode());
        Object $finRoleCode = this.getFinRoleCode();
        result = result * 59 + ($finRoleCode == null ? 43 : $finRoleCode.hashCode());
        Object $finCertificateId = this.getFinCertificateId();
        result = result * 59 + ($finCertificateId == null ? 43 : $finCertificateId.hashCode());
        Object $ftoItemId = this.getFtoItemId();
        result = result * 59 + ($ftoItemId == null ? 43 : $ftoItemId.hashCode());
        Object $certificateTaskId = this.getCertificateTaskId();
        result = result * 59 + ($certificateTaskId == null ? 43 : $certificateTaskId.hashCode());
        Object $statementModelItemDTO = this.getStatementModelItemDTO();
        result = result * 59 + ($statementModelItemDTO == null ? 43 : $statementModelItemDTO.hashCode());
        return result;
    }

    public String toString() {
        return "SettlementOrderItemDTO(settlementNo=" + this.getSettlementNo() + ", typeCode=" + this.getTypeCode() + ", statementNo=" + this.getStatementNo() + ", statementItemId=" + this.getStatementItemId() + ", finOrderNo=" + this.getFinOrderNo() + ", payType=" + this.getPayType() + ", payCode=" + this.getPayCode() + ", tradeNo=" + this.getTradeNo() + ", orderNo=" + this.getOrderNo() + ", channelCode=" + this.getChannelCode() + ", amount=" + this.getAmount() + ", orderType=" + this.getOrderType() + ", memberCode=" + this.getMemberCode() + ", sence=" + this.getSence() + ", settlementType=" + this.getSettlementType() + ", settlementStatus=" + this.getSettlementStatus() + ", execType=" + this.getExecType() + ", execGenStatus=" + this.getExecGenStatus() + ", certificateGenStatus=" + this.getCertificateGenStatus() + ", execTime=" + this.getExecTime() + ", deleteFlag=" + this.getDeleteFlag() + ", createTime=" + this.getCreateTime() + ", payOrderTime=" + this.getPayOrderTime() + ", payOrderExecTime=" + this.getPayOrderExecTime() + ", payOrderNo=" + this.getPayOrderNo() + ", typeCodeList=" + this.getTypeCodeList() + ", finRoleCode=" + this.getFinRoleCode() + ", finCertificateId=" + this.getFinCertificateId() + ", ftoItemId=" + this.getFtoItemId() + ", certificateTaskId=" + this.getCertificateTaskId() + ", statementModelItemDTO=" + this.getStatementModelItemDTO() + ")";
    }
}

