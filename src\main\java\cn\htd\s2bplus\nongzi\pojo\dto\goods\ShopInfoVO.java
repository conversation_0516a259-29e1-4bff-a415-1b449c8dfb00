package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.nongzi.pojo.dto.finance.ReportSettlementOrderItemDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.finance.SettlementOrderItemDTO;
import cn.htd.s2bplus.nongzi.utils.MyJsonStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ShopInfoVO implements Serializable {

    @ApiModelProperty(
            value = "结算单编号",
            notes = "结算单编号"
    )
    @Excel(name = "结算单编号", width = 20,numFormat = "1")
    private String settlementNo;

    @ApiModelProperty(
            value = "账单日",
            notes = "账单日"
    )
    @Excel(name = "账单日", width = 20,numFormat = "2")
    private Date periodTime;

    @ApiModelProperty(
            value = "结算单完成时间",
            notes = "结算单完成时间"
    )
    @Excel(name = "结算单完成时间", width = 20,numFormat = "3")
    private Date settleCompleteTime;

    @ApiModelProperty(
            value = "结算单生成时间",
            notes = "结算单生成时间"
    )
    @Excel(name = "结算单生成时间", width = 20,numFormat = "4")
    private Date settleCreateTime;

    @ApiModelProperty(
            value = "结算单状态",
            notes = "结算单状态"
    )
    private String settleStatus;

    @ApiModelProperty(
            value = "结算金额",
            notes = "结算金额"
    )
    private BigDecimal settleAmount;

    @ApiModelProperty(
            value = "订单数",
            notes = "订单数"
    )
    private Integer settleOrderNum;

    @ApiModelProperty(
            value = "订单来源",
            notes = "订单来源"
    )
    private String orderFrom;

    @ApiModelProperty(
            value = "订单编号",
            notes = "订单编号"
    )
    private String orderNo;

    @ApiModelProperty(
            value = "店铺id",
            notes = "店铺id"
    )
    private Long shopId;

    @ApiModelProperty(
            value = "店铺名称",
            notes = "店铺名称"
    )
    private String shopName;

    @ApiModelProperty(
            value = "卖家名称",
            notes = "卖家名称"
    )
    private String sellerName;

    @ApiModelProperty(
            value = "卖家编号",
            notes = "卖家编号"
    )
    private String sellerId;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal realAmount;

    @ApiModelProperty(
            value = "佣金总额",
            notes = "佣金总额"
    )
    private BigDecimal statementAmount;

    @ApiModelProperty(value = "结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;


    private List<SettlementOrderItemDTO> settlementOrderItemDTOList;

    private List<ReportSettlementOrderItemDTO> reportOrderDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
