package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ItemAttrDTO implements Serializable {
    private static final long serialVersionUID = -1715655413761934153L;

    @ApiModelProperty(value = "商家属性ID")
    private Long sellerAttrid;
    /**
     * 商家ID
     */
    @ApiModelProperty(value = "商家ID")
    private Long sellerId;


    @ApiModelProperty(value = "类目ID")
    private Long categoryId;
    /**
     * 类目属性ID
     */
    @ApiModelProperty(value = "类目属性ID")
    private Long attrId;
    /**
     * 属性类型  1：类目属性，2：销售属性
     */
    @ApiModelProperty(value = "属性类型  1：类目属性，2：销售属性")
    private String attrType;



    @ApiModelProperty(value = "属性名称")
    private String attrName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
