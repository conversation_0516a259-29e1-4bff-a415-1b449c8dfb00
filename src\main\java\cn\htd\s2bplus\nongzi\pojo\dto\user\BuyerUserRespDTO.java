package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
public class BuyerUserRespDTO implements Serializable {
    private static final long serialVersionUID = -6326390892558746630L;

    @ApiModelProperty(value = "会员ID")
    private Long memberId;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "联系人/姓名")
    private String businessPersonName;

    @ApiModelProperty(value = "注册手机号-脱敏")
    private String businessPersonMobile;

    @ApiModelProperty(value = "注册手机号-加密")
    private String dsBusinessPersonMobile;

    @ApiModelProperty(value = "一级分类 门店会员：MD、云店会员：YD、政企客户：ZQ",example = "MD")
    private String firstClassCode;

    @ApiModelProperty(value = "二级分类"+
            " 会员门店：A1、授权门店：A2、经销代理：A3" +
            "网店店主:B1、社群团长：B2、主播达人：B3"+
            "政府/中大型企业:C1、普通企业:C2",example = "C1")
    private String secondClassCode;

    @ApiModelProperty(value = "客户性质分类：" +
            "MD-A1：门店会员-会员门店，" +
            "MD-A2：门店会员-授权门店，" +
            "MD-A2：门店会员-经销代理，" +
            "YD-B1：云店会员-网店店主，" +
            "YD-B2：云店客户-社群团长，" +
            "YD-B3：云店客户-主播达人，" +
            "ZQ-C1：政企客户-政府/中大型企业，"+
            "ZQ-C2：政企客户-普通企业")
    private String customerClassCode;

    @ApiModelProperty(value = "会员认证状态：0-未认证，1-已认证")
    private String isRealNameAuthenticated;

    @ApiModelProperty(value = "实名认证状态：0-未实名，1-个人实名，2-企业单位实名")
    private String realNameStatus;

    @ApiModelProperty(value = "审核状态 1审核中、2审核通过、3审核驳回")
    private String verifyStatus;

    @ApiModelProperty(value = "注册时间")
    private Date registerTime;

    @ApiModelProperty(value = "分销员标识：0-不是，1-是")
    private Integer distributorRoleFlag;

    @ApiModelProperty(value = "客户分类是否确认(1-确认 2-未确认)")
    private Integer customerLabelSource;

    @ApiModelProperty(value = "实际经营地址-省市区镇详细地址 实际经营地址-省市区镇详细地址", example = "钟灵街50号")
    private String actualBusinessAddress;
    private String dsActualBusinessAddress;
    @ApiModelProperty(value = "营业执照电子版图片地址 营业执照电子版图片地址", example = "/8806838698014.jpg")
    private String businessLicensePicSrc;

    @ApiModelProperty(value = "推荐人编码",example = "123456")
    private String recommenderCode;
    @ApiModelProperty(value = "门头照片 门头照片", example = "xx.jpg")
    private String storeOutsidePicSrc;
    @ApiModelProperty(value = "店内照片 店内照片", example = "xx.jpg")
    private String storeInsidePicSrc;

    @ApiModelProperty(value = "法人姓名 法人姓名", example = "范宝华")
    private String artificialPersonName;

    @ApiModelProperty(value = "会员营业执照注册号 会员营业执照注册号", example = "14011602485045")
    private String buyerBusinessLicenseId;

    @ApiModelProperty(value = "工商企业名称")
    private String companyActualName;

    @ApiModelProperty(value = "会员注册终端来源 0=app、1=供应商入驻的会员身份、2=H5,3=小程序,4=公众号,5=其他、6=千橙H5", example = "0")
    private Integer memberFrom;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
