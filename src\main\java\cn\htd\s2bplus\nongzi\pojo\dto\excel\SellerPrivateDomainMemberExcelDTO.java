package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SellerPrivateDomainMemberExcelDTO implements Serializable {

    private static final long serialVersionUID = -4934948066808570176L;

    /**
     * 商家编码
     */
    @ApiModelProperty(value = "商家编码")
    @Excel(name = "商家编码",fixedIndex = 0,orderNum = "1")
    private String sellerCode;

    /**
     * 会员编码
     */
    @ApiModelProperty(value = "会员编码")
    @Excel(name = "会员编码",fixedIndex = 1,orderNum = "2")
    private String memberCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
