package cn.htd.s2bplus.nongzi.feign.middleground;

import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.common.dto.cipher.SingleDecryptInputDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.common.DataGrid;
import cn.htd.s2bplus.nongzi.pojo.dto.finance.*;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.*;
import cn.htd.s2bplus.nongzi.pojo.dto.membergroup.MemberCompanyInfoDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.CloudOrderItemsRepDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.order.CloudTradeOrderItemsDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.*;
import cn.htd.s2bplus.nongzi.pojo.vo.CheckStockNumErrorVO;
import cn.htd.s2bplus.nongzi.pojo.vo.SellerSkuVO;
import cn.htd.s2bplus.nongzi.pojo.vo.WarehouseVirtualApiDTO;
import cn.htd.s2bplus.nongzi.pojo.vo.WarehouseVirtualVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/4/2
 */
@Component
@FeignClient(value = "s2bplus-middleground-gateway")
public interface MiddleGroundAPI {

    /**
     * 查询结算单列表
     * @param settlementOrderDTO
     * @param page
     * @param rows
     * @return 分页查询结算单结果
     */
    @PostMapping("/settlementOrderApi/exportSettlementOrder")
    PageResult<List<SettlementOrderVO>> exportSettlementOrder(@RequestBody SettlementOrderDTO settlementOrderDTO,
                                                              @RequestParam(value = "page") int page,
                                                              @RequestParam(value = "rows") int rows);

    /**
     * 单记录单字段解密
     * @param inputDTO
     * @return
     */
    @PostMapping("/cipher/decryptSingle")
    Result<String> decryptSingle(@RequestBody SingleDecryptInputDTO inputDTO);

    /**
     * 商品明细查询
     * @param dto
     * @return
     */
    @RequestMapping("/goods/querySellerGoodsList")
    Result<SellerSkuVO> querySellerGoodsList(@RequestBody SellerSkuQueryDTO dto,
                                             @RequestParam(value = "page", required = true) int page,
                                             @RequestParam(value = "rows", required = true) int rows);

    /**
     * 查询会员信息
     * @param memberQueryDTO 查询会员信息条件入参
     * @return 会员信息
     */
    @GetMapping(value = "/userApi/selectMemberBaseInfo")
    Result<MemberBaseInfoDTO> selectMemberBaseInfo(@RequestBody MemberQueryDTO memberQueryDTO);

    @ApiOperation(value = "收货信息详情批量查询", notes = "收货信息详情批量查询")
    @PostMapping("/auth/member/batchQueryAddressInfo")
    Result<List<MemberConsigAddressDTO>> batchQueryAddressInfo(@RequestBody List<Long> addressIdList);

    @PostMapping("/virtual/warehouse/warehouseGoodsDetailByParam")
    Result<List<WarehouseGoodsDetailApiVO>> warehouseGoodsDetailByParam(@RequestBody WarehouseGoodsApiDTO queryDTO);

    /**
     * 批量校验库存扣减数量
     * @param queryDTO 批量校验库存扣减数量
     * @return 明细信息
     */
    @PostMapping("/virtual/warehouse/checkSaleStockNum")
    Result<List<CheckStockNumErrorVO>> checkSaleStockNum(BatchOutWarehouseApiDTO queryDTO);

    @PostMapping("/virtual/warehouse/pageWarehouseByParam")
    PageResult<List<WarehouseVirtualVO>>  pageWarehouseByParam(@RequestBody WarehouseVirtualApiDTO dto,
                                                               @RequestParam(value = "page") Integer page,
                                                               @RequestParam(value = "rows") Integer rows);

    @PostMapping("/virtual/warehouse/checkStockCode")
    Result<Integer>  checkStockCode(@RequestBody CheckStockCodeApiDTO dto);

    @PostMapping("/seller/batchQueryMemCompanyInfo")
    Result<List<MemberCompanyInfoDTO>> batchQueryMemCompanyInfo(@RequestBody List<MemberBaseInfoDTO> memberBaseInfoList);

    @PostMapping("/offlineSettlementApi/pageOfflineStatementList")
    @ApiOperation(value = "分页查询线下付款结算列表", notes = "分页查询线下付款结算列表")
    PageResult<List<OfflineSettlementInfoDTO>> pageOfflineStatementList(@RequestBody OfflineSettlementInfoQueryDTO req,
                                                                        @RequestParam(value = "page") int page,
                                                                        @RequestParam(value = "rows") int rows);
    @PostMapping("/offlineSettlementApi/batchModify")
    @ApiOperation(value = "批量修改线下付款结算", notes = "批量修改线下付款结算")
    Result<Boolean> batchModifyOfflinePaySettlement(@RequestBody List<OfflineSettlementInfoModifyDTO> reqList);

    @PostMapping("/offlineSettlementApi/batchQueryOfflineSettlement")
    @ApiOperation(value = "批量查询线下付款结算列表", notes = "批量查询线下付款结算列表")
    Result<List<OfflineSettlementInfoDTO>> batchQueryOfflineSettlement(@RequestBody List<String> orderNoList);

    @PostMapping(value = "/orderApi/queryOrderItemsListByOrderNoList")
    @ApiOperation(value = "根据订单号查询订单明细")
    Result<Map<String, List<CloudTradeOrderItemsDTO>>> queryOrderItemsListByOrderNoList(@RequestBody CloudOrderItemsRepDTO cloudOrderItemsRepDTO);

    @PostMapping("/goodsBaseData/queryBaseDictionaryListByCondition")
    PageResult<List<BaseDictionaryDTO>> queryBaseDictionaryListByCondition(@RequestBody BaseDictionaryDTO dictionaryDTO,
                                                                           @RequestParam(value = "page") Integer page,
                                                                           @RequestParam(value = "rows") Integer rows);

    @PostMapping("/platformUser/selectBuyerUserListPage")
    @ApiOperation(value = "查询用户列表", notes = "查询用户列表")
    Result<DataGrid<BuyerUserRespDTO>> selectBuyerUserListPage(@RequestBody PlatformUserQueryDTO buyerUserReqDTO);



    @PostMapping("/goodsBaseData/addSingleItemUnit")
    Result<String> addSingleItemUnit(@RequestParam(value = "name") String name,
                                     @RequestParam(value = "createId") Long createId,
                                     @RequestParam(value = "createName") String createName);

    @PostMapping("/goodsBaseData/queryBrandPageList")
    @ApiOperation(value = "查询品牌",notes = "查询品牌")
    Result<DataGrid<ItemBrandDTO>> queryBrandPageList(@RequestBody ItemBrandDTO requestParam);

    @PostMapping("/goodsTemplate/searchDelivery")
    @ApiOperation(value = "查询商家包邮模板",notes = "查询商家包邮模板")
    Result<DataGrid<TradeShopDeliveryDTO>> searchDelivery(@RequestBody TradeShopDeliveryDTO tradeShopDeliveryDTO);
}
