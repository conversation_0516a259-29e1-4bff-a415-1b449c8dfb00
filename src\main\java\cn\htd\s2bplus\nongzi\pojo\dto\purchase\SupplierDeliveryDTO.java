package cn.htd.s2bplus.nongzi.pojo.dto.purchase;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.contants.PatternRegexp;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 供应商发货请求体
 * <AUTHOR>
 * @Date 2021/9/26 14:27
 */
@Data
public class SupplierDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 6566645658757891642L;

    @ApiModelProperty(value = "请求链路ID 用于问题日志排查",hidden = true)
    private Long requestId = System.currentTimeMillis();

    @ApiModelProperty(value = "记录ID")
    private Long id;

    @ApiModelProperty(value = "委托单号")
    private String entrustedOrderNo;

    @ApiModelProperty(value = "收货或自提详细信息")
    private String receiverOrPickUpDetail;

    @ApiModelProperty(value = "采购单号")
    @NotBlank
    private String purchaseOrderNo;

    @ApiModelProperty(value = "采购单唯一编号,ERP采购单号+平台公司代码(4位)")
    @NotBlank
    private String purchaseOrderUniqueNo;

    @ApiModelProperty(value = "订单类型 0:囤货订单,1:直发订单")
    @NotBlank
    @Pattern(regexp = PatternRegexp.REGEXP_ORDER_TYPE)
    private String orderType;

    @ApiModelProperty(value = "发货方式 1:供应商配送,2:自提")
    @NotBlank
    @Pattern(regexp = PatternRegexp.REGEXP_DELIVERY_TYPE)
    private String deliveryType;

    @ApiModelProperty(value = "物流信息集合")
    private List<LogisticInfoDTO> logisticInfoList;

    @ApiModelProperty(value = "供应商实际发货方式 1:快递公司承运,2:派车配送 3:其他  4:线上发货（汇送达）")
    private String realDeliveryType;

    @ApiModelProperty(value = "销售单订单号或提货单单号")
    private String deliveryOrderNo;

    @ApiModelProperty(value = "发货商品行信息集合")
    @Valid
    private List<DeliveryOrderItemDTO> itemList;

    @ApiModelProperty(value = "发货时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    @ApiModelProperty(value = "附件URL集合")
    @Size(max = 10)
    private List<String> appendixUrl;

    @ApiModelProperty(value = "物流单号")
    private String logisticNo;

    @ApiModelProperty(value = "车牌号")
    private String carNo;

    @ApiModelProperty(value = "派车配送联系人信息,如姓名电话等")
    private String carSendInfo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
