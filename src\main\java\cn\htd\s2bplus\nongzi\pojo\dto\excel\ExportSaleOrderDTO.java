package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExportSaleOrderDTO implements Serializable {

    private static final long serialVersionUID = 6266817088396731758L;

    @Excel(name = "商品编码",width = 30,orderNum = "1")
    private String skuCode;

    @Excel(name = "商品名称",width = 30,orderNum = "2")
    private String goodsName;

    @Excel(name = "单价",width = 10,orderNum = "3")
    private BigDecimal unitPrice;

    @Excel(name = "数量",width = 10,orderNum = "4")
    private BigDecimal goodsNumber;

    @Excel(name = "合同编号",width = 10,orderNum = "6")
    private String contractCode;

    @Excel(name = "订单编号",width = 10,orderNum = "7")
    private String purchaserOrderNumber;

    @Excel(name = "订单状态",width = 10,orderNum = "8")
    private String purchaserOrderStatus;

    @Excel(name = "下单时间",width = 30,orderNum = "9")
    private String createPurchaserOrderTime;

    @Excel(name = "买家",width = 30,orderNum = "10")
    private String buyerName;

    @Excel(name = "订单类型",width = 30,orderNum = "11")
    private String orderType;

    @Excel(name = "委托单号/提货单号",width = 30,orderNum = "12")
    private String relationNo;

    @Excel(name = "收货人",width = 30,orderNum = "13")
    private String consigneeName;

    @Excel(name = "收货人电话",width = 30,orderNum = "14")
    private String consigneePhoneNum;

    @Excel(name = "收货人地址",width = 30,orderNum = "15")
    private String deliveryAddress;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
