package cn.htd.s2bplus.nongzi.feign.purchase;

import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.order.*;
import cn.htd.s2bplus.nongzi.pojo.dto.purchase.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;


/**
 * 供应商Feign调用类
 */
@FeignClient(name = "s2bplus-purchase-service")
public interface PurchaseFeignService {

    /**
     * 发货
     * @param supplierDeliveryDTO
     * @return
     */
    @PostMapping(value = "/logistic/supplierDelivery")
    Result<Boolean> supplierDelivery(@RequestBody @Valid SupplierDeliveryDTO supplierDeliveryDTO);

    /**
     * 分页查询待发货明细列表
     * @param purchaseSaleWaitDeliveryDTO
     * @return
     */
    @PostMapping("/purchaseSaleOrder/selectPurchaseSaleWaitDeliveryList")
    PageResult<List<PurchaseSaleWaitDeliveryVO>> selectPurchaseSaleWaitDeliveryList(@RequestBody PurchaseSaleWaitDeliveryDTO purchaseSaleWaitDeliveryDTO);

    @GetMapping("/deliveryAddress/queryDeliveryAddressDetail")
    @ApiOperation(value = "查询发货地址详情", notes = "查询发货地址详情")
    Result<DeliveryAddressInfo> queryDeliveryAddressDetail(@RequestParam(value = "id") Long id,
                                                           @RequestParam(value = "sellerId") Long sellerId);

    @GetMapping("/company/queryLogisticCompanyInfo")
    @ApiOperation(value = "查询采购商物流公司详情", notes = "查询采购商物流公司详情")
    Result<LogisticCompanyInfo> queryLogisticCompanyInfo(@RequestParam(value = "logisticCode") String logisticCode,
                                                         @RequestParam(value = "sellerId") Long sellerId);

    /**
     * 查询采购单列表
     * @param dto
     * @return
     */
    @PostMapping(value = "/purchaseOrder/queryPurchaserOrderList")
    PageResult<List<PurchaserOrderListVO>> queryPurchaserOrderList(@RequestBody QueryPurchaserOrderListDTO dto);

    /**
     * 分页查询已发货记录列表
     */
    @PostMapping(value = "/purchaseOrderDeliveryRecord/selectPurchaseOrderDeliveryRecordPage")
    PageResult<List<PurchaseOrderDeliveryRecordVO>> selectPurchaseOrderDeliveryRecordPage(@RequestBody PurchaseOrderDeliveryRecordDTO purchaseOrderDeliveryRecordDTO);

}
