package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ItemSkuPublishStatusDTO implements Serializable {
	private static final long serialVersionUID = 326806347952680245L;
	// "商品skuCode", desc = "商品skuCode", example = "htd100001")
	@ApiModelProperty(value = "商品skuCode")
	private String skuCode;
	// "商品编码", desc = "商品编码", example = "1l")
	@ApiModelProperty(value = "商品编码")
	private String itemCode;
	// "商品上架状态", desc = "0-未上架 1-已上架", example = "0")
	@ApiModelProperty(value = "商品上架状态\", desc = \"0-未上架 1-已上架")
	private Integer shelfStatus;

	/**
	 * 店铺ID
	 */
	@ApiModelProperty(value = "店铺ID")
	private Long shopId;


}
