package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * @Author: 80333
 * @Description:
 * @Date: 2021/5/17 17:49
 */
@Data
@ApiModel("会员搜索")
public class MemberSearchDTO implements Serializable {

    private static final long serialVersionUID = -7639049979140727433L;
    @ApiModelProperty(
            value = "起始时间",
            example = "2017-07-31 17:18:59",
            hidden = true
    )
    private Date startTime;
    @ApiModelProperty(
            value = "截止时间",
            notes = "截止时间",
            example = "2017-07-31 17:18:59",
            hidden = true
    )
    private Date endTime;
    @ApiModelProperty(
            value = "有效",
            notes = "状态 空白：审核中未成为正式会员，0：无效，1：有效",
            example = "1",
            hidden = true
    )
    private String status;
    @ApiModelProperty(
            value = "会员属性",
            notes = "客商属性(vms的会员属性字段)：11、乡镇零售服务商，12、工程客户13、代理批发，14、商超客户，15、零售客户",
            example = "11",
            hidden = true
    )
    private String buyerFeature;
    @ApiModelProperty(
            value = "是否异业",
            notes = "是否异业 1是，0否",
            example = "1",
            hidden = true
    )
    private String industryCategory;
    @ApiModelProperty(
            value = "是否有手机验证",
            notes = "是否有手机验证 1是",
            example = "1",
            hidden = true
    )
    private Integer isPhoneAuthenticated;
    @ApiModelProperty(
            value = "等级",
            notes = "会员店等级(1:一星会员,2:二星会员,3:三星会员,4:四星会员,5:五星会员,6:VIP会员)",
            example = "1",
            hidden = true
    )
    private String buyerGrade;
    @ApiModelProperty(
            value = "是否登录商城",
            notes = "是否登录商城 1是，0否",
            example = "1",
            hidden = true
    )
    private String canMallLogin;
    @ApiModelProperty(
            value = "是否有担保证明",
            notes = "是否有担保证明 1是，0否",
            example = "1",
            hidden = true
    )
    private String hasGuaranteeLicense;
    @ApiModelProperty(
            value = "是否有营业执照",
            notes = "1是0否",
            example = "1",
            hidden = true
    )
    private String hasBusinessLicense;
    @ApiModelProperty(
            value = "公司名称",
            notes = "公司名称",
            example = "汇通达公司"
    )
    private String companyName;
    @ApiModelProperty(
            value = "法人姓名",
            notes = "法人姓名",
            example = "张三"
    )
    private String artificialPersonName;
    @ApiModelProperty(
            value = "当前归属客户经理code",
            notes = "当前归属客户经理code",
            example = "21311"
    )
    private String curBelongManagerId;
    @ApiModelProperty(
            value = "会员账号",
            notes = "会员账号",
            example = "htd1298119"
    )
    private String memberCode;
    @ApiModelProperty(
            value = "区域 - 省",
            notes = "区域 - 省",
            example = "32"
    )
    private String locationProvince;
    @ApiModelProperty(
            value = "区域 - 市",
            notes = "区域 - 市",
            example = "3202"
    )
    private String locationCity;
    @ApiModelProperty(
            value = "区域 - 区/县",
            notes = "区域 - 区/县",
            example = "320201"
    )
    private String locationCounty;
    @ApiModelProperty(
            value = "VMS系统标志 - 用于区分新系统和老系统 1：新系统；其他：老系统",
            notes = "VMS系统标志 - 用于区分新系统和老系统 1：新系统；其他：老系统",
            example = "1",hidden = true
    )
    private String sysFlag;
    @ApiModelProperty(
            value = "会员类型 1：非会员；2：会员；3：担保会员；",
            notes = "会员类型 1：非会员；2：会员；3：担保会员",
            example = "1",
            required = true
    )
    private String memberType;
    @ApiModelProperty(
            value = "开始时间 - 字符串格式",
            notes = "开始时间",
            example = "2016-03-11 14:56:07"
    )
    private String beginTime;
    @ApiModelProperty(
            value = "结束时间 - 字符串格式",
            notes = "结束时间",
            example = "2016-03-11 14:56:07"
    )
    private String finishTime;
    @ApiModelProperty(
            value = "页参数 - 当前页",
            notes = "分页参数 - 当前页",
            example = "1"
    )
    private Integer start;
    @ApiModelProperty(
            value = "分页参数 - 每页显示记录数",
            notes = "分页参数 - 每页显示记录数",
            example = "10"
    )
    private Integer length;
    @ApiModelProperty(
            value = "审核状态：1为待审核，2为审核通过，3为审核不通过，4为中止",
            notes = "审核状态：1为待审核，2为通过，3为驳回",
            example = "1",
            hidden = true
    )
    private String auditStatus;
    @ApiModelProperty(
            value = "分销等级 1：普通会员店 2：二代；3：三代；4：四代；5：五代",
            notes = "分销等级 1：普通会员店 2：二代；3：三代；4：四代；5：五代",
            example = "1",
            hidden = true
    )
    private String distributorGrade;
    @ApiModelProperty(
            value = "商家名称",
            notes = "商家名称",
            example = "汇通达有限公司",
            hidden = true
    )
    private String sellerCompanyName;

    @ApiModelProperty(
            value = "认证状态",
            notes = "-1-所有，0-未认证，1-待审核，2-已认证，3-认证失效，4-认证失败，5-人脸识别中"
    )
    private String attestationStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
