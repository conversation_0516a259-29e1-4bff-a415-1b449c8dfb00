package cn.htd.s2bplus.nongzi.service.purchase;


import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.s2bplus.nongzi.enums.BusinessTypeEnum;
import cn.htd.s2bplus.nongzi.enums.DeliveryTypeEnum;
import cn.htd.s2bplus.nongzi.enums.FreightMethodEnum;
import cn.htd.s2bplus.nongzi.enums.PurchaseOrderTypeEnum;
import cn.htd.s2bplus.nongzi.feign.purchase.PurchaseFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.DirectOrderBatchShipmentDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.DirectOrderBatchShipmentRecordDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.StockUpOrderBatchShipmentDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.StockUpOrderBatchShipmentRecordDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.purchase.*;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.service.history.ReportHistoryService;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class AsyncOrderDelivery {

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;

    @Autowired
    private PurchaseFeignService purchaseFeignService;

    @Autowired
    private ReportHistoryService reportHistoryService;

    /**
     * 附件在OSS上的存放目录
     */
    private static final String UPLOAD_FILE_PREFIX = "/batchShipment" ;

    /**
     * 附件有效的扩展名
     */
    private static final Set<String> APPENDIX_VALID_EXTENSIONS = new HashSet<>(Arrays.asList(
            "jpeg", "jpg", "bmp", "pdf", "png", "xls", "xlsx", "doc", "docx"
    ));

    /**
     *  囤货模式的发货处理
     * @param batchShipmentDTOList
     * @param errList
     */
    @Async
    public void stockUpOrderDelivery(List<StockUpOrderBatchShipmentDTO> batchShipmentDTOList, List<StockUpOrderBatchShipmentRecordDTO> errList, LoginUserDetail loginUser){

        // 业务参数校验
        this.stockUpOrderDeliveryCheck(batchShipmentDTOList, errList);

        // 发货
        if(CollectionUtils.isNotEmpty(batchShipmentDTOList)){
            for(StockUpOrderBatchShipmentDTO batchShipmentDTO : batchShipmentDTOList){
                // 查询提货单信息 - 金力ERP控制一个提货单只能提一个商品
                String purchaseOrderNo = "";
                String purchaseOrderUniqueNo = "";
                List<DeliveryOrderItemDTO> itemList = new ArrayList<>();

                PurchaseSaleWaitDeliveryDTO purchaseSaleWaitDeliveryDTO = new PurchaseSaleWaitDeliveryDTO();
                purchaseSaleWaitDeliveryDTO.setTenderNo(batchShipmentDTO.getDeliveryOrderNo());
                purchaseSaleWaitDeliveryDTO.setOrderType("0");

                PageResult<List<PurchaseSaleWaitDeliveryVO>> result = purchaseFeignService.selectPurchaseSaleWaitDeliveryList(purchaseSaleWaitDeliveryDTO);

                log.info("call selectPurchaseSaleWaitDeliveryList req:{} resp:{}", purchaseSaleWaitDeliveryDTO, result);

                if(null != result && CollectionUtils.isNotEmpty(result.getData())
                        && CollectionUtils.isNotEmpty(result.getData().get(0).getPurchaseSaleWaitDeliveryItemList())){
                    PurchaseSaleWaitDeliveryItemVO purchaseSaleWaitDeliveryItemVO = result.getData().get(0).getPurchaseSaleWaitDeliveryItemList().get(0);

                    purchaseOrderNo = purchaseSaleWaitDeliveryItemVO.getPurchaseOrderNo();

                    purchaseOrderUniqueNo = purchaseSaleWaitDeliveryItemVO.getPurchaseOrderUniqueNo();

                    // 待发货数量
                    BigDecimal waitDeliveryNumber = purchaseSaleWaitDeliveryItemVO.getSaleItemWaitDeliveryNumber();
                    // 发货数量比较
                    if(new BigDecimal(batchShipmentDTO.getDeliveryNumber()).compareTo(waitDeliveryNumber) > 0){
                        log.info("提货单:{}，发货失败，发送数量不能大于待发货数量",  batchShipmentDTO.getDeliveryOrderNo());
                        StockUpOrderBatchShipmentRecordDTO errDto = new StockUpOrderBatchShipmentRecordDTO();
                        BeanUtil.copy(batchShipmentDTO, errDto);
                        errDto.setErrorReason("发送数量不能大于待发货数量");
                        errList.add(errDto);

                        continue;
                    }

                    // 发货商品封装
                    DeliveryOrderItemDTO itemDTO = new DeliveryOrderItemDTO();
                    itemDTO.setDeliveryNumber(batchShipmentDTO.getDeliveryNumber());
                    itemDTO.setItemNo(purchaseSaleWaitDeliveryItemVO.getSubOrderItemNo());
                    itemDTO.setSkuCode(purchaseSaleWaitDeliveryItemVO.getSkuCode());

                    itemList.add(itemDTO);
                }else{
                    log.info("提货单:{}，发货失败，没有找到待提货记录",  batchShipmentDTO.getDeliveryOrderNo());
                    StockUpOrderBatchShipmentRecordDTO errDto = new StockUpOrderBatchShipmentRecordDTO();
                    BeanUtil.copy(batchShipmentDTO, errDto);
                    errDto.setErrorReason("未找到待发货记录");
                    errList.add(errDto);

                    continue;
                }

                // 构成发货数据
                if(itemList.size() > 0){
                    SupplierDeliveryDTO dto = new SupplierDeliveryDTO();
                    dto.setDeliveryOrderNo(batchShipmentDTO.getDeliveryOrderNo());
                    dto.setDeliveryTime(DateUtil.strToDateLong(batchShipmentDTO.getDeliveryTime() + " 00:00:00"));
                    dto.setDeliveryType("1"); // 供应商配送
                    dto.setRealDeliveryType("1"); // 1:快递公司承运
                    dto.setLogisticNo(batchShipmentDTO.getLogisticNo());
                    dto.setOrderType("0"); // 0:囤货订单
                    dto.setPurchaseOrderNo(purchaseOrderNo);
                    dto.setPurchaseOrderUniqueNo(purchaseOrderUniqueNo);
                    dto.setItemList(itemList);
                    dto.setAppendixUrl(Arrays.asList(batchShipmentDTO.getAppendixUrl()));

                    Result<Boolean> booleanResult = purchaseFeignService.supplierDelivery(dto);
                    if(booleanResult.isSuccess() && booleanResult.getData() == false){
                        StockUpOrderBatchShipmentRecordDTO errDto = new StockUpOrderBatchShipmentRecordDTO();
                        BeanUtil.copy(batchShipmentDTO, errDto);
                        errDto.setErrorReason(booleanResult.getMsg());
                        errList.add(errDto);
                        log.info("提货单:{} 发货失败, 原因:{}", batchShipmentDTO.getDeliveryOrderNo(), booleanResult.getMsg());
                    }else{
                        log.info("提货单:{} 发货成功.", batchShipmentDTO.getDeliveryOrderNo());
                    }
                }else{
                    StockUpOrderBatchShipmentRecordDTO errDto = new StockUpOrderBatchShipmentRecordDTO();
                    BeanUtil.copy(batchShipmentDTO, errDto);
                    errDto.setErrorReason("没有找到可发货的商品信息");
                    errList.add(errDto);
                    log.info("提货单:{} 发货失败, 原因:没有找到可发货的商品信息", batchShipmentDTO.getDeliveryOrderNo());
                }
            }
        }

        // 记录错误数据
        if(CollectionUtils.isNotEmpty(errList)){
            // 将错误信息生成excel文件，上传至阿里云服务器
            String fileName = BusinessTypeEnum.BATCH_SHIPMENT_ERR_LIST.getMsg();
            OssUtils ossUtils = new OssUtils();
            String downloadUrl = ossUtils.getDownloadUrl(UPLOAD_FILE_PREFIX, errList, StockUpOrderBatchShipmentRecordDTO.class, fileName, bucket, endpoint, accessKeyId, accessKeySecret);
            if (StringUtils.isEmpty(downloadUrl)) {
                log.info("发货失败列表-生成Excel出错 downUrl is null.");
            }else{
                log.info("发货错误列表-生成Excel成功，url={}", downloadUrl);
            }

            //保存-批量发货-错误列表, 生成下载历史
            reportHistoryService.saveReportHistory(downloadUrl, BusinessTypeEnum.BATCH_SHIPMENT_ERR_LIST.getCode(), loginUser);
        }

    }

    /**
     *
     * Excel解析后的各列业务逻辑校验 , 发货方式、发货时间 etc. 将错误的数据从待处理数据列表中移除
     * @param batchShipmentDTOList
     * @param errList
     */
    private void stockUpOrderDeliveryCheck(List<StockUpOrderBatchShipmentDTO> batchShipmentDTOList, List<StockUpOrderBatchShipmentRecordDTO> errList){
        if(CollectionUtils.isNotEmpty(batchShipmentDTOList)) {
            Iterator<StockUpOrderBatchShipmentDTO> iterator = batchShipmentDTOList.iterator();
            while (iterator.hasNext()) {
                StockUpOrderBatchShipmentDTO batchShipmentDTO = iterator.next(); // 获取当前元素
                String errorReason = "";
                // 参数为空校验 start
                if(StringUtils.isEmpty(batchShipmentDTO.getDeliveryOrderNo())){
                    errorReason = errorReason + "提单号不能为空;";
                }else{
                    batchShipmentDTO.setDeliveryOrderNo(batchShipmentDTO.getDeliveryOrderNo().trim());
                }

                if(StringUtils.isEmpty(batchShipmentDTO.getDeliveryNumber())){
                    errorReason = errorReason + "发货数量不能为空;";
                }else{
                    batchShipmentDTO.setDeliveryNumber(batchShipmentDTO.getDeliveryNumber().trim());
                }

                if(StringUtils.isNotEmpty(batchShipmentDTO.getDeliveryNumber())
                        && new BigDecimal(batchShipmentDTO.getDeliveryNumber()).compareTo(BigDecimal.ZERO) <= 0){
                    errorReason = errorReason + "发货数量必须大于0;";
                }

                if(StringUtils.isEmpty(batchShipmentDTO.getRealDeliveryType())){
                    errorReason = errorReason + "发货方式不能为空;";
                }else{
                    batchShipmentDTO.setRealDeliveryType(batchShipmentDTO.getRealDeliveryType().trim());
                }

                if(StringUtils.isEmpty(batchShipmentDTO.getLogisticNo())){
                    errorReason = errorReason + "物流单号不能为空;";
                }else{
                    batchShipmentDTO.setLogisticNo(batchShipmentDTO.getLogisticNo().trim());
                }

                if(StringUtils.isEmpty(batchShipmentDTO.getDeliveryTime())){
                    errorReason = errorReason + "发货时间不能为空;";
                }else{
                    batchShipmentDTO.setDeliveryTime(batchShipmentDTO.getDeliveryTime().trim());
                }

                if(StringUtils.isEmpty(batchShipmentDTO.getAppendixName())){
                    errorReason = errorReason + "附件文件名称不能为空;";
                }else{
                    batchShipmentDTO.setAppendixName(batchShipmentDTO.getAppendixName().trim());
                }
                // 参数为空校验 end

                // 附件格式判断
                if(!isValidExtension(batchShipmentDTO.getAppendixName())){
                    errorReason = errorReason + "不支持的附件格式;";
                }
                // 发货方式判断
                if(!"1".equals(batchShipmentDTO.getRealDeliveryType())){
                    errorReason = errorReason + "发货方式不正确;";
                }
                // 发货时间判断
                if(StringUtils.isNotEmpty(batchShipmentDTO.getDeliveryTime())
                        && !DateUtil.isValidDate(batchShipmentDTO.getDeliveryTime(),"yyyy-MM-dd")){
                    errorReason = errorReason + "发货时间不正确;";
                }
                // 从待处理列表中移除不合规数据
                if(!"".equals(errorReason)){
                    // 添加到错误列表，并从原始列表中移除
                    StockUpOrderBatchShipmentRecordDTO errDto = new StockUpOrderBatchShipmentRecordDTO();
                    BeanUtil.copy(batchShipmentDTO, errDto);
                    errDto.setErrorReason(errorReason);
                    errList.add(errDto);
                    iterator.remove(); // 从原始列表中移除当前元素
                }
            }
        }
    }


    /**
     * 判断文件扩展名是否有效。
     *
     * @param fileName 文件名
     * @return 如果文件扩展名在预定义的有效扩展名集合中，则返回 true；否则返回 false。
     */
    private static boolean isValidExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        String fileExtension = getFileExtension(fileName);
        return APPENDIX_VALID_EXTENSIONS.contains(fileExtension.toLowerCase());
    }

    /**
     * 获取文件扩展名。
     *
     * @param fileName 文件名
     * @return 文件的扩展名，如果文件没有扩展名，则返回空字符串。
     */
    private static String getFileExtension(String fileName) {
        int lastIndexOfDot = fileName.lastIndexOf('.');
        if (lastIndexOfDot == -1 || lastIndexOfDot == fileName.length() - 1) {
            // 没有扩展名或者最后一个字符是 '.' 的情况
            return "";
        }
        return fileName.substring(lastIndexOfDot + 1);
    }

    /**
     * 直发订单发货 入参校验
     * @param batchShipmentDTOList
     * @param errList
     */
    private void directOrderDeliveryCheck(List<DirectOrderBatchShipmentDTO> batchShipmentDTOList, List<DirectOrderBatchShipmentRecordDTO> errList){
        if(CollectionUtils.isEmpty(batchShipmentDTOList)) {
            return;
        }

        batchShipmentDTOList.removeIf(dto -> {
            StringBuilder errorReason = new StringBuilder();
            //校验入参
            validateField(dto.getDeliveryOrderNo(), "提单号", errorReason, dto::setDeliveryOrderNo);
            validateDeliveryType(dto.getDeliveryType(), errorReason, dto::setDeliveryType);
            validateRealDeliveryType(dto.getRealDeliveryType(), errorReason, dto::setRealDeliveryType);
            validateFreightMethod(dto.getFreightMethod(), errorReason, dto::setFreightMethod);
            validateField(dto.getLogisticsCompanyCode(), "物流公司编码", errorReason, dto::setLogisticsCompanyCode);
            validateField(dto.getLogisticNo(), "物流单号", errorReason, dto::setLogisticNo);
            validateDate(dto.getDeliveryTime(), errorReason, dto::setDeliveryTime);
            validateField(dto.getDeliveryAddressId(), "发货地址ID", errorReason, dto::setDeliveryAddressId);
            validateAppendixName(dto.getAppendixName(), errorReason, dto::setAppendixName);

            if (errorReason.length() > 0) {
                addToErrorList(dto, errList, errorReason.toString());
                return true;
            }
            return false;
        });
    }

    private void validateAppendixName(String value, StringBuilder errorReason, java.util.function.Consumer<String> setter) {
        if (StringUtils.isBlank(value)) {
            errorReason.append("附件文件名称不能为空;");
            return;
        }
        // 附件格式判断
        if(!isValidExtension(value)){
            errorReason.append("不支持的附件格式;");
            return;
        }
        // 去除空格
        setter.accept(value.trim());
    }

    private void validateField(String value, String fieldName, StringBuilder errorReason, java.util.function.Consumer<String> setter) {
        if (StringUtils.isBlank(value)) {
            errorReason.append(fieldName).append("不能为空;");
        } else {
            setter.accept(value.trim());
        }
    }

    private void validateDeliveryType(String value, StringBuilder errorReason, java.util.function.Consumer<String> setter) {
        if (StringUtils.isBlank(value)) {
            errorReason.append("配送方式不能为空;");
            return;
        }
        if (!Objects.equals(DeliveryTypeEnum.DISTRIBUTE.getType(), value)) {
            errorReason.append("配送方式不正确,仅支持供应商配送;");
        } else {
            setter.accept(value.trim());
        }
    }

    private void validateRealDeliveryType(String value, StringBuilder errorReason, java.util.function.Consumer<String> setter) {
        if (StringUtils.isBlank(value)) {
            errorReason.append("发货方式不能为空;");
            return;
        }
        if (!Objects.equals(DeliveryTypeEnum.REAL_DISTRIBUTE.getType(), value)) {
            errorReason.append("发货方式不正确,仅支持快递公司承运;");
        } else {
            setter.accept(value.trim());
        }
    }

    private void validateFreightMethod(String value, StringBuilder errorReason, java.util.function.Consumer<String> setter) {
        if (StringUtils.isBlank(value)) {
            errorReason.append("货运方式不能为空;");
            return;
        }
        FreightMethodEnum freightMethodEnum = FreightMethodEnum.getByType(value);
        if (freightMethodEnum == null) {
            errorReason.append("货运方式不正确;");
        } else {
            setter.accept(value.trim());
        }
    }

    private void validateDate(String value, StringBuilder errorReason, java.util.function.Consumer<String> setter) {
        if (StringUtils.isBlank(value)) {
            errorReason.append("发货时间不能为空;");
            return;
        }
        if (!DateUtil.isValidDate(value, DateUtil.YYYY_MM_DD_FORMAT)) {
            errorReason.append("发货时间格式不正确;");
        } else {
            setter.accept(value.trim());
        }
    }

    private void addToErrorList(DirectOrderBatchShipmentDTO dto, List<DirectOrderBatchShipmentRecordDTO> errList, String errorReason) {
        DirectOrderBatchShipmentRecordDTO errDto = new DirectOrderBatchShipmentRecordDTO();
        BeanUtil.copy(dto, errDto);
        errDto.setErrorReason(errorReason);
        errList.add(errDto);
    }

    /**
     * 直发模式的发货处理
     * @param batchShipmentDTOList
     * @param errList
     */
    @Async
    public void directOrderDelivery(List<DirectOrderBatchShipmentDTO> batchShipmentDTOList, List<DirectOrderBatchShipmentRecordDTO> errList, LoginUserDetail loginUser){
        // 业务参数校验
        this.directOrderDeliveryCheck(batchShipmentDTOList, errList);

        if(CollectionUtils.isEmpty(batchShipmentDTOList) && CollectionUtils.isNotEmpty(errList)){
            //错误文件处理
            this.processDirectOrderErrDataList(errList, loginUser);
            return;
        }

        //  登录用户
        Long sellerId = loginUser.getMemberId();

        // 直发订单-批量发货
        for(DirectOrderBatchShipmentDTO batchShipmentDTO : batchShipmentDTOList){
            // 查询提货单信息 - 金力ERP控制一个提货单只能提一个商品
            String purchaseOrderNo = "";
            String purchaseOrderUniqueNo = "";
            //委托单号
            String entrustedOrderNo = "";
            //收货或自提详细信息
            String receiverOrPickUpDetail = "";
            String deliveryOrderNo = batchShipmentDTO.getDeliveryOrderNo();
            //根据发货地址ID查询地址信息
            Result<DeliveryAddressInfo> addressResult = purchaseFeignService.queryDeliveryAddressDetail(Long.valueOf(batchShipmentDTO.getDeliveryAddressId()), sellerId);
            if (!addressResult.isSuccess() || null == addressResult.getData()) {
                log.info("提货单:{}，发货失败，未找到收货地址信息",  deliveryOrderNo);
                addToErrorList(batchShipmentDTO, errList, "发货失败，未找到收货地址信息");
                continue;
            }

            //根据物流编码查询物流信息
            Result<LogisticCompanyInfo> companyInfoResult = purchaseFeignService.queryLogisticCompanyInfo(batchShipmentDTO.getLogisticsCompanyCode(), sellerId);
            if (!companyInfoResult.isSuccess() || null == companyInfoResult.getData()) {
                log.info("提货单:{}，发货失败，根据物流公司编码未找到物流信息",  deliveryOrderNo);
                addToErrorList(batchShipmentDTO, errList, "发货失败，根据物流公司编码未找到物流信息");
                continue;
            }

            //发货商品集合
            List<DeliveryOrderItemDTO> itemList = new ArrayList<>();
            PurchaseSaleWaitDeliveryDTO purchaseSaleWaitDeliveryDTO = new PurchaseSaleWaitDeliveryDTO();
            purchaseSaleWaitDeliveryDTO.setTenderNo(deliveryOrderNo);
            purchaseSaleWaitDeliveryDTO.setOrderType(PurchaseOrderTypeEnum.DIRECT.getType()); //直发订单
            //查询直发订单-待发货列表
            PageResult<List<PurchaseSaleWaitDeliveryVO>> result = purchaseFeignService.selectPurchaseSaleWaitDeliveryList(purchaseSaleWaitDeliveryDTO);
            log.info("call selectPurchaseSaleWaitDeliveryList req:{} resp:{}", purchaseSaleWaitDeliveryDTO, result);
            if(null != result && CollectionUtils.isNotEmpty(result.getData())
                    && CollectionUtils.isNotEmpty(result.getData().get(0).getPurchaseSaleWaitDeliveryItemList())){
                PurchaseSaleWaitDeliveryVO purchaseSaleWaitDeliveryVO = result.getData().get(0);
                purchaseOrderNo = purchaseSaleWaitDeliveryVO.getPurchaseOrderNo();
                purchaseOrderUniqueNo = purchaseSaleWaitDeliveryVO.getPurchaseOrderUniqueNo();
                entrustedOrderNo = purchaseSaleWaitDeliveryVO.getEntrustedOrderNo();

                List<PurchaseSaleWaitDeliveryItemVO> waitDeliveryItemList = purchaseSaleWaitDeliveryVO.getPurchaseSaleWaitDeliveryItemList();
                // 赋值 收货或自提详细信息
                receiverOrPickUpDetail = waitDeliveryItemList.get(0).getReceiverOrPickUpDetail();

                //判断配送方式非供应商配送，给出报错提示
                if (!Objects.equals(waitDeliveryItemList.get(0).getDeliveryType(), DeliveryTypeEnum.DISTRIBUTE.getType())) {
                    log.info("提货单:{}，发货失败，订单发货方式与配送方式冲突",  deliveryOrderNo);
                    addToErrorList(batchShipmentDTO, errList, "发货失败，订单发货方式与配送方式冲突");
                    continue;
                }

                //遍历封装商品item信息
                waitDeliveryItemList.stream().forEach(purchaseSaleWaitDeliveryItemVO -> {
                    DeliveryOrderItemDTO itemDTO = new DeliveryOrderItemDTO();
                    itemDTO.setDeliveryOrderNo(purchaseSaleWaitDeliveryItemVO.getSubOrderNo());
                    itemDTO.setDeliveryNumber(String.valueOf(purchaseSaleWaitDeliveryItemVO.getSaleItemWaitDeliveryNumber()));
                    itemDTO.setItemNo(purchaseSaleWaitDeliveryItemVO.getSubOrderItemNo());
                    itemDTO.setSkuCode(purchaseSaleWaitDeliveryItemVO.getSkuCode());
                    itemList.add(itemDTO);
                });
            }else{
                log.info("提货单:{}，发货失败，没有找到待提货记录",  deliveryOrderNo);
                addToErrorList(batchShipmentDTO, errList, "发货失败，未找到待发货记录");
                continue;
            }

            // 构成发货数据
            if(itemList.size() > 0){
                SupplierDeliveryDTO dto = new SupplierDeliveryDTO();
                dto.setPurchaseOrderNo(purchaseOrderNo);
                dto.setPurchaseOrderUniqueNo(purchaseOrderUniqueNo);
                dto.setEntrustedOrderNo(entrustedOrderNo);
                dto.setDeliveryType(batchShipmentDTO.getDeliveryType()); // 供应商配送
                dto.setOrderType(PurchaseOrderTypeEnum.DIRECT.getType()); // 1:直发订单
                dto.setReceiverOrPickUpDetail(receiverOrPickUpDetail);
                dto.setItemList(itemList);

                //构建发货物流信息
                LogisticInfoDTO logisticInfoDTO = buildLogisticInfoDTO(batchShipmentDTO, addressResult.getData(), companyInfoResult.getData());
                //设置物流信息
                dto.setLogisticInfoList(Arrays.asList(logisticInfoDTO));

                //调用供应商发货
                log.info("[直发订单]调用供应商发货req:{}", dto);
                Result<Boolean> booleanResult = purchaseFeignService.supplierDelivery(dto);
                if(booleanResult.isSuccess() && booleanResult.getData() == false){
                    addToErrorList(batchShipmentDTO, errList, booleanResult.getMsg());
                    log.info("提货单:{} 发货失败, 原因:{}", deliveryOrderNo, booleanResult.getMsg());
                }else{
                    log.info("提货单:{} 发货成功.", deliveryOrderNo);
                }
            }else{
                addToErrorList(batchShipmentDTO, errList, "没有找到可发货的商品信息");
                log.info("提货单:{} 发货失败, 原因:没有找到可发货的商品信息", deliveryOrderNo);
            }
        }

        // 记录错误数据
        this.processDirectOrderErrDataList(errList, loginUser);
    }

    /**
     * 处理直发订单-错误数据
     * @param errList
     */
    private void processDirectOrderErrDataList(List<DirectOrderBatchShipmentRecordDTO> errList, LoginUserDetail loginUser) {
        if(CollectionUtils.isNotEmpty(errList)){
            // 将错误信息生成excel文件，上传至阿里云服务器
            String fileName = BusinessTypeEnum.BATCH_SHIPMENT_ERR_LIST.getMsg();
            OssUtils ossUtils = new OssUtils();
            String downloadUrl = ossUtils.getDownloadUrl(UPLOAD_FILE_PREFIX, errList, DirectOrderBatchShipmentRecordDTO.class, fileName, bucket, endpoint, accessKeyId, accessKeySecret);
            if (StringUtils.isEmpty(downloadUrl)) {
                log.info("发货失败列表-生成Excel出错 downUrl is null.");
            }else{
                log.info("发货错误列表-生成Excel成功，url={}", downloadUrl);
            }

            //保存-批量发货-错误列表, 生成下载历史
            reportHistoryService.saveReportHistory(downloadUrl, BusinessTypeEnum.BATCH_SHIPMENT_ERR_LIST.getCode(), loginUser);
        }
    }

    private LogisticInfoDTO buildLogisticInfoDTO(DirectOrderBatchShipmentDTO batchShipmentDTO, DeliveryAddressInfo deliveryAddressInfo, LogisticCompanyInfo logisticCompanyInfo) {
        LogisticInfoDTO logisticInfoDTO = new LogisticInfoDTO();
        logisticInfoDTO.setDeliveryTime(batchShipmentDTO.getDeliveryTime() + " 00:00:00");
        logisticInfoDTO.setAppendixUrl(Arrays.asList(batchShipmentDTO.getAppendixUrl()));
        logisticInfoDTO.setFreightMethod(batchShipmentDTO.getFreightMethod());
        logisticInfoDTO.setRealDeliveryType(batchShipmentDTO.getRealDeliveryType());
        logisticInfoDTO.setLogisticNo(batchShipmentDTO.getLogisticNo());
        //设置发货地址信息
        logisticInfoDTO.setProvinceCode(deliveryAddressInfo.getProvinceCode());
        logisticInfoDTO.setDeliveryProvince(deliveryAddressInfo.getDeliveryProvince());
        logisticInfoDTO.setCityCode(deliveryAddressInfo.getCityCode());
        logisticInfoDTO.setDeliveryCity(deliveryAddressInfo.getDeliveryCity());
        logisticInfoDTO.setDistrictCode(deliveryAddressInfo.getDistrictCode());
        logisticInfoDTO.setDeliveryDistrict(deliveryAddressInfo.getDeliveryDistrict());
        logisticInfoDTO.setTownCode(deliveryAddressInfo.getTownCode());
        logisticInfoDTO.setDeliveryTown(deliveryAddressInfo.getDeliveryTown());
        logisticInfoDTO.setDetailAddress(deliveryAddressInfo.getDetailAddress());
        logisticInfoDTO.setDsDetailAddress(deliveryAddressInfo.getDsDetailAddress());
        logisticInfoDTO.setLogisticCompanyName(logisticCompanyInfo.getLogisticName());
        return logisticInfoDTO;
    }

}
