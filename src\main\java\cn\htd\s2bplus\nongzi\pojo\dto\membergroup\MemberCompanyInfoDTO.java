package cn.htd.s2bplus.nongzi.pojo.dto.membergroup;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date: 2020/9/8
 */
@Data
public class MemberCompanyInfoDTO implements Serializable {

    /**
     * 会员ID
     */
    @ApiModelProperty(value="会员ID")
    private Long memberId;
    /**
     * 会员Code
     */
    @ApiModelProperty(value="会员Code")
    private String memberCode;
    /**
     * 当前归属商家ID
     */
    @ApiModelProperty(value="当前归属商家ID",hidden = true)
    private String curBelongSellerId;
    /**
     * 当前商家客户经理ID
     */
    @ApiModelProperty(value="当前商家客户经理ID",hidden = true)
    private String curBelongManagerId;
    /**
     * 会员/商家类型 1：会员，2：商家
     */
    @ApiModelProperty(value="会员/商家类型 1：会员，2：商家")
    private Integer buyerSellerType;

    @ApiModelProperty(value = "1:内部供应商，2:外部供应商 ,3:分销商(会员店升级)")
    private String sellerType;
    /**
     * 公司名称
     */
    @ApiModelProperty(value="公司名称")
    private String companyName;
    /**
     * 法人姓名
     */
    @ApiModelProperty(value="法人姓名",hidden = true)
    private String artificialPersonName;
    /**
     * 法人手机号码
     */
    @ApiModelProperty(value="法人手机号码",hidden = true)
    private String artificialPersonMobile;
    private String dsArtificialPersonMobile;
    /**
     * 法人身份证号
     */
    @ApiModelProperty(value="法人身份证号",hidden = true)
    private String artificialPersonIdcard;
    private String dsArtificialPersonIdcard;
    /**
     * 法人身份证电子版图片地址
     */
    @ApiModelProperty(value="法人身份证电子版图片地址")
    private String artificialPersonPicSrc;
    /**
     * 法人身份证电子版图片地址
     */
    @ApiModelProperty(value="法人身份证电子版图片地址",hidden = true)
    private String artificialPersonPicBackSrc;
    /**
     * 法人手持身份证电子版图片地址
     */
    @ApiModelProperty(value="法人手持身份证电子版图片地址",hidden = true)
    private String artificialPersonIdcardPicSrc;
    /**
     * 所在地
     */
    @ApiModelProperty(value="所在地",hidden = true)
    private String locationProvince;
    /**
     * 城市
     */
    @ApiModelProperty(value="城市",hidden = true)
    private String locationCity;
    /**
     * 区
     */
    @ApiModelProperty(value="区",hidden = true)
    private String locationCounty;
    /**
     * 镇
     */
    @ApiModelProperty(value="镇",hidden = true)
    private String locationTown;
    /**
     * 地址
     */
    @ApiModelProperty(value="地址",hidden = true)
    private String locationDetail;
    private String dsLocationDetail;
    /**
     * 创建人ID
     */
    @ApiModelProperty(value="创建人ID",hidden = true)
    private String createId;
    /**
     * 创建人名称
     */
    @ApiModelProperty(value="创建人名称",hidden = true)
    private String createName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间",hidden = true)
    private String createTime;
    /**
     * 修改人Id
     */
    @ApiModelProperty(value="修改人Id",hidden = true)
    private String modifyId;
    /**
     * 修改人名称
     */
    @ApiModelProperty(value="修改人名称",hidden = true)
    private String modifyName;
    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间",hidden = true)
    private String modifyTime;

    @ApiModelProperty(value="经营人名称",hidden = true)
    private String businessPersonName;

    @ApiModelProperty(value="经营人电话",hidden = true)
    private String businessPersonMobile;
    private String dsBusinessPersonMobile;

    /**
     * 数据标记
     */
    @ApiModelProperty(value="数据标记",hidden = true)
    private Integer dataTag;

    @ApiModelProperty(value="营业执照号",hidden = true)
    private String buyerBusinessLicenseId;

    @ApiModelProperty(value = "注册资本")
    private String registerCapitalVal;

    @ApiModelProperty(value = "实缴资本")
    private String paidCapitalVal;

    @ApiModelProperty(
            value = "实际经营地址-省'",
            notes = "实际经营地址-省'",
            example = "32"
    )
    private String actualBusinessProvince;
    @ApiModelProperty(
            value = "实际经营地址-市'",
            notes = "实际经营地址-市'",
            example = "3205"
    )
    private String actualBusinessCity;
    @ApiModelProperty(
            value = "实际经营地址-区'",
            notes = "实际经营地址-区'",
            example = "320565"
    )
    private String actualBusinessCounty;
    @ApiModelProperty(
            value = "实际经营地址-镇',",
            notes = "实际经营地址-镇',",
            example = "32063521"
    )
    private String actualBusinessTown;
    @ApiModelProperty(
            value = "实际经营地址-详细地址",
            notes = "实际经营地址-详细地址",
            example = "钟灵街50号"
    )
    private String actualBusinessDetail;
    private String dsActualBusinessDetail;
    @ApiModelProperty(
            value = "实际经营地址-详细地址",
            notes = "实际经营地址-详细地址",
            example = "江苏省南京市玄武区孝陵卫街道钟灵街50号"
    )
    private String actualBusinessAddress;
    @ApiModelProperty(
            value = "实际经营地址-省市区镇详细地址-加密",hidden = true
    )
    private String dsActualBusinessAddress;

    @ApiModelProperty(
            value = "所属行业"
    )
    private String businessIndustry;

    @ApiModelProperty(
            value = "开户许可证",
            notes = "开户许可证",
            example = "/********/img/8096a8585340425288c8570c6a1002a6.jpg"
    )
    private String accountLicence;

    @ApiModelProperty(
            value = "公司名称简称",
            notes = "公司名称简称",
            example = "汇通达"
    )
    private String shortCompanyName;


    @ApiModelProperty(value = "统一社会信用代码", example = "**********")
    private String unifiedSocialCreditCode;


    @ApiModelProperty(value = "评级")
    private Integer level;

    @ApiModelProperty(value = "企业类型")
    private String customerNature;

    @ApiModelProperty(
            value = "供应商编码",
            example = "123456"
    )
    private String supplierCode;

    @ApiModelProperty(
            value = "财务联系人姓名",
            example = "张三"
    )
    private String financePersonName;

    @ApiModelProperty(
            value = "财务联系人手机号码",
            example = "***********"
    )
    private String financePersonMobile;
    private String dsFinancePersonMobile;

    @ApiModelProperty(value = "邮编",example = "210000")
    private String postCode;

    @ApiModelProperty(
            value = "是否入驻云场(成功)",
            example = "false"
    )
    private Boolean fromCloudSuccess;

    @ApiModelProperty(value = "公司地址")
    private String locationAddr;
    private String dsLocationAddr;

    @ApiModelProperty(value = "工商企业名称")
    private String companyActualName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
