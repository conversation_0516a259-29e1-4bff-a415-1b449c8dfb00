
package cn.htd.s2bplus.nongzi.pojo.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class MemberConsigAddressDTO implements Serializable {
    private static final long serialVersionUID = 8737822560587874667L;

   @ApiModelProperty(value = "会员ID",example = "1")
    private Long memberId;

   @ApiModelProperty(value = "地址ID",example = "1")
    private Long addressId;

   @ApiModelProperty(value = "发票ID",example = "1")
    private Long invoiceId;

   @ApiModelProperty(value = "收货人姓名",example = "张三")
    private String consigneeName;

   @ApiModelProperty(value = "收货人手机号码",example = "***********")
    private String consigneeMobile;
    private String dsConsigneeMobile;

   @ApiModelProperty(value = "收货人邮箱",example = "<EMAIL>")
    private String consigneeEmail;

   @ApiModelProperty(value = "增值税发票公司名称",example = "汇通达网络股份有限公司")
    private String invoiceCompanyName;

   @ApiModelProperty(value = "发票抬头",example = "汇通达网络股份有限公司")
    private String invoiceNotify;

   @ApiModelProperty(value = "纳税人识别号",example = "***************")
    private String taxManId;

   @ApiModelProperty(value = "收票人",example = "李四")
    private String invoicePerson;

   @ApiModelProperty(value = "联系电话",example = "***********")
    private String contactPhone;
    private String dsContactPhone;

   @ApiModelProperty(value = "开户银行",example = "工商银行")
    private String bankName;

   @ApiModelProperty(value = "银行账号",example = "**************")
    private String bankAccount;
    private String dsBankAccount;

   @ApiModelProperty(value = "外接渠道编码",example = "10")
    private String channelCode;

   @ApiModelProperty(value = "外接渠道名称",example = "测试渠道")
    private String channelName;

    @ApiModelProperty(value = "收货地址-省编码",example = "2")
    private String consigneeAddressProvince;

    @ApiModelProperty(value = "收货地址-市编码",example = "12")
    private String consigneeAddressCity;

    @ApiModelProperty(value = "收货地址-区编码",example = "42")
    private String consigneeAddressDistrict;

    @ApiModelProperty(value = "收货地址-镇编码",example = "23")
    private String consigneeAddressTown;

    @ApiModelProperty(value = "收货地址-省",example = "江苏省")
    private String consigneeAddressProvinceStr;

    @ApiModelProperty(value = " 收货地址-市",example = "南京市")
    private String consigneeAddressCityStr;

    @ApiModelProperty(value = "收货地址-区",example = "玄武区")
    private String consigneeAddressDistrictStr;

    @ApiModelProperty(value = "收货地址-镇",example = "麒麟镇")
    private String consigneeAddressTownStr;

   @ApiModelProperty(value = "收货地址-详细",example = "柳营西路50号")
    private String consigneeAddressDetail;
    private String dsConsigneeAddressDetail;

   @ApiModelProperty(value = "收货地址",example = "江苏省南京市玄武区柳营西路50号")
    private String consigneeAddress;
    private String dsConsigneeAddress;

   @ApiModelProperty(value = "发票邮寄地址-省",example = "江苏省")
    private String invoiceAddressProvince;

   @ApiModelProperty(value = " 发票邮寄地址-市",example = "南京市")
    private String invoiceAddressCity;

   @ApiModelProperty(value = "发票邮寄地址-区",example = "玄武区")
    private String invoiceAddressCounty;

   @ApiModelProperty(value = " 发票邮寄地址-镇",example = "麒麟镇")
    private String invoiceAddressTown;

   @ApiModelProperty(value = "发票邮寄地址-详细",example = "柳营西路50号")
    private String invoiceAddressDetail;
    private String dsInvoiceAddressDetail;

   @ApiModelProperty(value = "发票地址",example = "江苏省南京市玄武区柳营西路50号")
    private String invoiceAddress;
    private String dsInvoiceAddress;

   @ApiModelProperty(value = "更新人ID",example = "1")
    private Long modifyId;

   @ApiModelProperty(value = "更新人名称",example = "张三")
    private String modifyName;

   @ApiModelProperty(value = "用于批量删除",example = "[\"1\",\"2\"]")
    private List<String> strList;

   @ApiModelProperty(value = "邮编",example = "210000")
    private String postCode;

   @ApiModelProperty(value = "设置默认地址标记",example = "0")
    private String defaultFlag;

   @ApiModelProperty(value = "收货人座机区号",example = "025")
    private String consigneeAreaCode;

   @ApiModelProperty(value = "收货人座机号码",example = "69696969")
    private String consigneeLandline;
    private String dsConsigneeLandline;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }

}
