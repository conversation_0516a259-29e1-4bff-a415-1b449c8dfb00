package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2024/09/06 17:08
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class BrandWarehouseOutPutDTO implements Serializable {

    private static final long serialVersionUID = -4934948066808570176L;


    @ApiModelProperty(value = "商品库存编码")
    private String stockCode;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "变动类型（增加/减少）")
    private String businessType;

    @ApiModelProperty(value = "变动数量")
    private String useNum;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

}
