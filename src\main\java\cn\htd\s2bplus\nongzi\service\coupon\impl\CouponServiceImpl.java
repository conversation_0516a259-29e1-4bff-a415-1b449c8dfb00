package cn.htd.s2bplus.nongzi.service.coupon.impl;

import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.config.NongZiNacosConfig;
import cn.htd.s2bplus.nongzi.config.OssNacosConfig;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.BusinessTypeEnum;
import cn.htd.s2bplus.nongzi.enums.ResultEnum;
import cn.htd.s2bplus.nongzi.feign.promotion.PromotionCouponFeignService;
import cn.htd.s2bplus.nongzi.feign.user.UserService;
import cn.htd.s2bplus.nongzi.pojo.dto.coupon.*;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportCouponRuleDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.service.coupon.CouponService;
import cn.htd.s2bplus.nongzi.service.history.ReportHistoryService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.DozerUtil;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/8 10:46
 */
@Slf4j
@Service
public class CouponServiceImpl implements CouponService {

    @Autowired
    private PromotionCouponFeignService promotionCouponFeignService;

    @Autowired
    private ReportHistoryService reportHistoryService;

    @Autowired
    private UserService userService;

    @Autowired
    private NongZiNacosConfig nongZiNacosConfig;

    @Autowired
    private OssNacosConfig ossNacosConfig;

    @Override
    public Result<String> exportBalanceInformationByMemberPageList(BalanceInformationDTO balanceInformationDTO, HttpServletResponse response) {
        try {
            Integer current = 1;
            Integer pageSize = 50;
            LoginUserDetail loginUser = BaseContextHandler.getLoginUser();
            balanceInformationDTO.setSellerCode(loginUser.getMemberCode());
            balanceInformationDTO.setIsMemberHistoryType(CommonConstants.MEMBER_HISTORY_TYPE_YES);
            log.info("会员-活动发券结余列表 入参:{}", balanceInformationDTO);
            PageResult<List<BalanceInformationVO>> listPageResult = promotionCouponFeignService.queryBalanceInformationPageList(balanceInformationDTO, current, pageSize);
            log.info("会员-活动发券结余列表 出参:{}", listPageResult.isSuccess());
            if (!listPageResult.isSuccess()) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_BALANCE_INFORMATION_ERROR.getMsg());
            }
            if (ObjectUtils.isEmpty(listPageResult) || ObjectUtils.isEmpty(listPageResult.getData())){
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_BALANCE_INFORMATION_NULL.getMsg());
            }
            //首次同步查询活动发券结余列表  异步补全会员分组内容
            this.getDownUrlAndSaveHistoryReport(current,pageSize,listPageResult,loginUser,balanceInformationDTO, response);
        } catch (BusinessException e) {
            log.info("会员-活动发券结余导出失败:{}", e.getMessage());
            return CommonResultUtil.error(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("会员-活动发券结余导出异常", e);
            return CommonResultUtil.error(ApiResultEnum.ERROR.getCode(),"导出会员结余历史信息失败");
        }
        return CommonResultUtil.success(ApiResultEnum.EXPORT_CURRENT_PLAYING_SONG.getMsg(),ApiResultEnum.EXPORT_CURRENT_PLAYING_SONG.getMsg());
    }


    @Override
    public Result<String> exportBalanceInformationPageList(BalanceInformationDTO balanceInformationDTO, HttpServletResponse response) {
        try {
            Integer current = 1;
            Integer pageSize = 50;
            LoginUserDetail loginUser = BaseContextHandler.getLoginUser();
            balanceInformationDTO.setSellerCode(loginUser.getMemberCode());
            log.info("活动发券结余列表 入参:{}", balanceInformationDTO);
            PageResult<List<BalanceInformationVO>> listPageResult = promotionCouponFeignService.queryBalanceInformationPageList(balanceInformationDTO, current, pageSize);
            log.info("活动发券结余列表 出参:{}", listPageResult.isSuccess());
            if (!listPageResult.isSuccess()) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_BALANCE_INFORMATION_ERROR.getMsg());
            }
            if (ObjectUtils.isEmpty(listPageResult) || ObjectUtils.isEmpty(listPageResult.getData())){
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_BALANCE_INFORMATION_NULL.getMsg());
            }
            //首次同步查询活动发券结余  异步补全会员分组内容
            this.getDownUrlAndSaveHistoryReport(current,pageSize,listPageResult,loginUser,balanceInformationDTO, response);
        } catch (BusinessException e) {
            log.info("活动发券结余导出失败:{}", e.getMessage());
            return CommonResultUtil.error(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("活动发券结余导出异常", e);
            return CommonResultUtil.error(ApiResultEnum.ERROR.getCode(),"导出结余历史信息失败");
        }
        return CommonResultUtil.success(ApiResultEnum.EXPORT_CURRENT_PLAYING_SONG.getMsg(),ApiResultEnum.EXPORT_CURRENT_PLAYING_SONG.getMsg());
    }

    @Async
    @Override
    public void getDownUrlAndSaveHistoryReport(Integer current, Integer pageSize, PageResult<List<BalanceInformationVO>> listPageResult, LoginUserDetail loginUser,BalanceInformationDTO balanceInformationDTO, HttpServletResponse response) {
        List<BalanceInformationVO> balanceInformationVOList = listPageResult.getData();
        Long total = listPageResult.getPage().getTotal();
        log.info("结余信息导出条数 :{}",total);
        if (total > pageSize){
            //批量查询结余信息
            this.queryBalanceInformationList(current, pageSize, listPageResult, balanceInformationDTO, balanceInformationVOList);
        }
        //处理0为0e-8的科学计数法问题，保留六位小数
        balanceInformationVOList.forEach(exportMemberBalanceInformationVO -> {
            if (ObjectUtils.isNotEmpty(exportMemberBalanceInformationVO.getThisTimeRemainAmount())){
                exportMemberBalanceInformationVO.setThisTimeRemainAmount(exportMemberBalanceInformationVO.getThisTimeRemainAmount().setScale(6, RoundingMode.HALF_UP));
            }
            if (ObjectUtils.isNotEmpty(exportMemberBalanceInformationVO.getLastRemainAmount())){
                exportMemberBalanceInformationVO.setLastRemainAmount(exportMemberBalanceInformationVO.getLastRemainAmount().setScale(6, RoundingMode.HALF_UP));
            }
            if (ObjectUtils.isNotEmpty(exportMemberBalanceInformationVO.getPlanSendAmount())){
                exportMemberBalanceInformationVO.setPlanSendAmount(exportMemberBalanceInformationVO.getPlanSendAmount().setScale(6, RoundingMode.HALF_UP));
            }
            if (ObjectUtils.isNotEmpty(exportMemberBalanceInformationVO.getRealSendAmount())){
                exportMemberBalanceInformationVO.setRealSendAmount(exportMemberBalanceInformationVO.getRealSendAmount().setScale(6, RoundingMode.HALF_UP));
            }
        });
        //会员分组内容导出
        String downloadUrl="";
        Integer businessType=0;
        OssUtils ossUtils = new OssUtils();
        String fileName =CommonConstants.COUPON_BALANCE_SHEET_NAME;
        if (CommonConstants.REMAIN_TYPE_MEMBER.equals(balanceInformationDTO.getIsMemberHistoryType())){
            List<ExportMemberBalanceInformationVO> exportMemberBalanceInformationList = DozerUtil.convertList(balanceInformationVOList, ExportMemberBalanceInformationVO.class);
            downloadUrl = ossUtils.getDownloadUrl(exportMemberBalanceInformationList,ExportMemberBalanceInformationVO.class,fileName,ossNacosConfig.getBucket(),ossNacosConfig.getEndpoint(),ossNacosConfig.getAccessKeyId(),ossNacosConfig.getAccessKeySecret(), response);
            businessType=BusinessTypeEnum.EXPORT_MEMBER_BALANCE_INFORMATION.getCode();
        }else {
            if (ObjectUtils.isNotEmpty(balanceInformationDTO.getPromotionId())){
                List<ExportPromotionBalanceInformationVO> exportPromotionBalanceInformationList = DozerUtil.convertList(balanceInformationVOList, ExportPromotionBalanceInformationVO.class);
                downloadUrl = ossUtils.getDownloadUrl(exportPromotionBalanceInformationList,ExportPromotionBalanceInformationVO.class,fileName,ossNacosConfig.getBucket(),ossNacosConfig.getEndpoint(),ossNacosConfig.getAccessKeyId(),ossNacosConfig.getAccessKeySecret(), response);
                businessType=BusinessTypeEnum.EXPORT_PROMOTION_BALANCE_INFORMATION.getCode();
            }else {
                List<ExportBalanceInformationVO> exportBalanceInformationList = DozerUtil.convertList(balanceInformationVOList, ExportBalanceInformationVO.class);
                downloadUrl = ossUtils.getDownloadUrl(exportBalanceInformationList,ExportBalanceInformationVO.class,fileName, ossNacosConfig.getBucket(),ossNacosConfig.getEndpoint(),ossNacosConfig.getAccessKeyId(),ossNacosConfig.getAccessKeySecret(),response);
                businessType=BusinessTypeEnum.EXPORT_BALANCE_INFORMATION.getCode();
            }
        }
        if (StringUtils.isEmpty(downloadUrl)) {
            log.error("获取上传文件地址出错:{}", downloadUrl);
            throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.UPLOAD_OSS_ERROR.getMsg());
        }
        //保存报表生成下载历史
        reportHistoryService.saveReportHistory(downloadUrl, businessType,loginUser);
    }

    /**
     * 批量查询结余信息
     * @param current
     * @param pageSize
     * @param listPageResult
     * @param balanceInformationDTO
     * @param balanceInformationVOList
     */
    private void queryBalanceInformationList(Integer current, Integer pageSize, PageResult<List<BalanceInformationVO>> listPageResult, BalanceInformationDTO balanceInformationDTO, List<BalanceInformationVO> balanceInformationVOList) {
        //调用过一次，所以从2页开始
        current++;
        //计算还需要调用几次接口 拼接所有分组内容
        double ceil = Math.ceil((float) listPageResult.getPage().getTotal() / pageSize);
        while (current <=ceil){
            log.info("活动发券结余列表 入参:{}", balanceInformationDTO);
            PageResult<List<BalanceInformationVO>> pageResult = promotionCouponFeignService.queryBalanceInformationPageList(balanceInformationDTO, current, pageSize);
            log.info("活动发券结余列表 出参:{}", pageResult.isSuccess());
            if (!pageResult.isSuccess()) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_BALANCE_INFORMATION_ERROR.getMsg());
            }
            if (ObjectUtils.isEmpty(pageResult) || ObjectUtils.isEmpty(pageResult.getData())){
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.GET_BALANCE_INFORMATION_NULL.getMsg());
            }
            balanceInformationVOList.addAll(pageResult.getData());
            current++;
        }
    }

    @Override
    public Result<List<AutoSendCouponRecordDTO>> importBatchCreateCouponRule(MultipartFile file) {
        log.info("解析发券明细开始");
        Result<List<AutoSendCouponRecordDTO>> result = new Result<>();
        List<ImportCouponRuleDTO> list = new ArrayList<>();
        List<String> codeList = new ArrayList<>();
        int importMaxCount = nongZiNacosConfig.getImportCouponRuleMaxCount();
        String sellerCode = BaseContextHandler.getLoginUser().getMemberCode();

        try {
            //校验文件类型
            if (ObjectUtils.isEmpty(file) || StringUtils.isEmpty(file.getOriginalFilename())) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }
            String originalFilename = file.getOriginalFilename();
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
            if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equals(substring) || StrConstant.XLSX.equals(substring))) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_FILE_ERROR.getMsg());
            }
            long start = System.currentTimeMillis();
            EasyExcel.read(file.getInputStream(), ImportCouponRuleDTO.class, new ReadListener<ImportCouponRuleDTO>() {
                public static final int BATCH_COUNT = 100;
                private final List<ImportCouponRuleDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

                @Override
                public void invoke(ImportCouponRuleDTO data, AnalysisContext context) {
                    BigDecimal zeroNum = BigDecimal.ZERO;
                    BigDecimal hundredNum = new BigDecimal("100") ;
                    int rowNumber = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
                    int rowIndex = context.readSheetHolder().getRowIndex() + 1;
                    if (rowNumber == 0) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
                    }
                    if (rowNumber > importMaxCount) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_MAX_ERROR.getMsg() + importMaxCount);
                    }
                    if (StringUtils.isEmpty(data.getMemberCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_ITEMCODE_NULL.getMsg());
                    }
                    data.setMemberCode(data.getMemberCode().trim());
                    if (codeList.contains(data.getMemberCode())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "编码:" + data.getMemberCode() + ApiResultEnum.IMPORT_MEMBER_REPEAT.getMsg());
                    }
                    if (ObjectUtils.isEmpty(data.getWaitRemainAmount())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_CONTENT_NULL.getMsg());
                    }
                    //正数 小于等于0抛
                    if (data.getWaitRemainAmount().compareTo(zeroNum) <=0 || data.getWaitRemainAmount().scale()>2) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_WAIT_REMAIN_AMOUNT_ERROR.getMsg());
                    }
                    if (ObjectUtils.isEmpty(data.getSingleOrderLimit())) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_CONTENT_NULL.getMsg());
                    }
                    //0-100正数
                    if (data.getSingleOrderLimit().compareTo(zeroNum) <=0 || data.getSingleOrderLimit().compareTo(hundredNum) >0 || data.getSingleOrderLimit().scale()>2) {
                        throw new BusinessException(ApiResultEnum.ERROR.getCode(), "第" + rowIndex + "行" + ApiResultEnum.IMPORT_SINGLE_ORDER_LIMIT_ERROR.getMsg());
                    }
                    codeList.add(data.getMemberCode());
                    cachedDataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (!CollectionUtils.isEmpty(cachedDataList)) {
                        list.addAll(cachedDataList);
                    }
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) {
                    log.info("解析发券明细失败 :{}", exception.getMessage());
                    if (exception instanceof BusinessException) {
                        throw new BusinessException(ResultEnum.ERROR.getCode(), exception.getMessage());
                    } else if (exception instanceof ExcelDataConvertException) {
                        ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                        String errorMsg = ApiResultEnum.IMPORT_ERROR_LINE_COLUMN.getMsg() + "解析异常";
                        Integer rowIndex = excelDataConvertException.getRowIndex() + 1;
                        Integer columnIndex = excelDataConvertException.getColumnIndex() + 1;
                        log.info("解析发券明细,第{}行，第{}列解析异常，数据为:{}", rowIndex, columnIndex, JSON.toJSONString(excelDataConvertException.getCellData()));
                        throw new BusinessException(ResultEnum.ERROR.getCode(), String.format(errorMsg, rowIndex, columnIndex));
                    }
                }
            }).sheet().doRead();
            log.info("解析发券明细出来的数据量：{}", list.size());
            log.info("解析发券明细excel时间占用 :{}ms", System.currentTimeMillis() - start);
            log.info("解析发券明细数据完毕数据:{}", list);
            if (list.size() == 0) {
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), ApiResultEnum.IMPORT_NULL.getMsg());
            }

            AutoSendCouponDTO autoSendCouponDTO = new AutoSendCouponDTO();
            autoSendCouponDTO.setSellerCode(sellerCode);
            autoSendCouponDTO.setImportRecordList(list);
            return promotionCouponFeignService.calculateAutoSendCouponAmount(autoSendCouponDTO);
        } catch (BusinessException e) {
            log.info("解析发券明细失败:{}", e.getMessage());
            result.setCode(e.getCode());
            result.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("解析发券明细异常", e);
            result.setCode(ApiResultEnum.ERROR.getCode());
            result.setMsg("解析发券明细异常");
        }
        return result;
    }


}
