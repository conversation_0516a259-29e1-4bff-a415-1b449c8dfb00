package cn.htd.s2bplus.nongzi.service.goods.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.util.BeanUtil;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.contants.StrConstant;
import cn.htd.s2bplus.nongzi.enums.InventoryStatusEnum;
import cn.htd.s2bplus.nongzi.enums.ResultEnum;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.ItemDecimalCheckDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.*;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.QueryVirtualInventoryVO;
import cn.htd.s2bplus.nongzi.pojo.vo.VirtualInventoryExportVO;
import cn.htd.s2bplus.nongzi.service.goods.VirtualInventoryService;
import cn.htd.s2bplus.nongzi.utils.DateUtil;
import cn.htd.s2bplus.nongzi.utils.XssFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class virtualInventoryServiceImpl implements VirtualInventoryService {

    @Autowired
    private GoodsFeignService goodsFeignService;

    @Override
    public Result<Long> importVirtualInventoryList(MultipartFile file, LoginUserDetail userDetail, Long shopId) {
        try {
            log.info("批量导入虚拟库存,shopId:{}",shopId);
            //文件非空校验
            if (file == null || file.isEmpty()) {
                return CommonResultUtil.error(ResultEnum.IMPORT_FILE_IS_NULL_ERROR.getCode(),ResultEnum.IMPORT_FILE_IS_NULL_ERROR.getMsg());
            }
            //文件格式校验
            String originalFilename = file.getOriginalFilename();
            if (StringUtils.isBlank(originalFilename)) {
                return CommonResultUtil.error(ResultEnum.SYSTEM_ERROR.getCode(),ResultEnum.SYSTEM_ERROR.getMsg());
            }
            String substring = originalFilename.substring(originalFilename.lastIndexOf(StrConstant.POINT) + 1);
            if (StringUtils.isEmpty(substring) || !(StrConstant.XLS.equalsIgnoreCase(substring) || StrConstant.XLSX.equalsIgnoreCase(substring))) {
                return CommonResultUtil.error(ResultEnum.ONLY_SUPPORT_EXCEL_FORMAT_ERROR.getCode(),ResultEnum.ONLY_SUPPORT_EXCEL_FORMAT_ERROR.getMsg());
            }
            //将excel解析成list
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setStartSheetIndex(0);
            ExcelImportResult<VirtualInventoryExcelDTO> resultExcel = ExcelImportUtil.importExcelMore(file.getInputStream(),VirtualInventoryExcelDTO.class,params);
            //XSS拦截
            new XssFilter().importFilter(file,null,VirtualInventoryExcelDTO.class.getCanonicalName());
            List<VirtualInventoryExcelDTO> list = resultExcel.getList();
            if (CollectionUtils.isEmpty(list)) {
                return CommonResultUtil.error(ResultEnum.ANALYZE_EXCEL_FILE_FAILED_ERROR.getCode(),ResultEnum.ANALYZE_EXCEL_FILE_FAILED_ERROR.getMsg());
            }
            //导入数据去重处理
            this.removeRepeatData(list);
            //根据商品SKU编码批量查询商品信息
            Map<String,SkuOutDTO> skuMap = this.getSkuMap(list,shopId);
            if (CollectionUtils.isEmpty(skuMap)) {
                return CommonResultUtil.error(ResultEnum.QUERY_BATCH_SKU_INFO_ERROR.getCode(),ResultEnum.QUERY_BATCH_SKU_INFO_ERROR.getMsg());
            }
            //导入数据校验，全部校验成功，返回excel信息集合信息（去重）
            Result<Map<String,VirtualInventoryExcelDTO>> excelMap = this.importDataVerifyAndGetExcelMap(list,skuMap);
            if (!excelMap.isSuccess() || CollectionUtils.isEmpty(excelMap.getData())) {
                return CommonResultUtil.error(excelMap.getCode(),excelMap.getMsg());
            }
            //组装导入数据
            List<GoodsStockDTO> goodsStockList = this.getVirtualInventoryList(excelMap.getData(),skuMap,shopId,userDetail);
            if (CollectionUtils.isEmpty(goodsStockList)) {
                return CommonResultUtil.error(ResultEnum.MO_DATA_IMPORT_ERROR.getCode(),ResultEnum.MO_DATA_IMPORT_ERROR.getMsg());
            }
            return goodsFeignService.batchSaveVirtualInventory(goodsStockList);
        } catch (BusinessException e) {
            log.error("批量导入虚拟库存,error:",e);
            return CommonResultUtil.errorPage(e.getCode(),e.getMessage());
        }catch (Exception e) {
            log.error("批量导入虚拟库存,error:",e);
            return CommonResultUtil.errorPage(ResultEnum.SYSTEM_ERROR.getCode(),ResultEnum.SYSTEM_ERROR.getMsg());
        }
    }

    @Override
    public void exportVirtualInventoryList(QueryVirtualInventoryDTO queryVirtualInventoryDTO, HttpServletResponse response) {
        Result<List<QueryVirtualInventoryVO>> result = goodsFeignService.queryVirtualInventoryList(queryVirtualInventoryDTO);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            return;
        }
        List<VirtualInventoryExportVO> virtualInventoryExportList = result.getData().stream().map(item -> {
            VirtualInventoryExportVO vo = new VirtualInventoryExportVO();
            BeanUtil.copy(item,vo);
            InventoryStatusEnum inventoryStatusEnum = Arrays.stream(InventoryStatusEnum.values())
                    .filter(status -> status.getStatus().equals(item.getInventoryStatus())).findFirst().orElse(null);
            vo.setInventoryStatus(ObjectUtils.isEmpty(inventoryStatusEnum) ? StrConstant.STR_EMPTY_STRING : inventoryStatusEnum.getMsg());
            return vo;
        }).collect(Collectors.toList());
        String sheetNameStart = CommonConstants.EXCEL_NAME_VIRTUAL_INVENTORY + StrConstant.STR_LINE + DateUtil.dateToString(new Date());
        this.exportReverseOrderHandle(virtualInventoryExportList,response,sheetNameStart);
    }

    /**
     * 导入数据去重处理
     * @param list 导入数据
     */
    private void removeRepeatData(List<VirtualInventoryExcelDTO> list) {
        list.forEach(i -> {
            i.setSkuCode(StringUtils.isNotBlank(i.getSkuCode()) ? i.getSkuCode().trim() : i.getSkuCode());
            i.setEffectiveStartTime(StringUtils.isNotBlank(i.getEffectiveStartTime()) ? i.getEffectiveStartTime().trim() : i.getEffectiveStartTime());
            i.setEffectiveEndTime(StringUtils.isNotBlank(i.getEffectiveEndTime()) ? i.getEffectiveEndTime().trim() : i.getEffectiveEndTime());
        });
    }

    /**
     * 根据商品SKU编码批量查询商品信息
     * @param list 解析数据
     * @param shopId 店铺id
     * @return sku信息集合
     */
    private Map<String,SkuOutDTO> getSkuMap(List<VirtualInventoryExcelDTO> list,Long shopId) {
        List<SkuQueryDTO> batchSkuQueryList = list.stream().map(item -> {
            SkuQueryDTO skuQueryDTO = new SkuQueryDTO();
            skuQueryDTO.setShopId(shopId);
            String skuCode = item.getSkuCode();
            if (StringUtils.isNotBlank(skuCode)) {
                skuCode = skuCode.trim();
            }
            skuQueryDTO.setSkuCode(skuCode);
            return skuQueryDTO;
        }).filter(i -> StringUtils.isNotBlank(i.getSkuCode()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SkuQueryDTO::getSkuCode))),ArrayList::new));
        log.info("根据商品SKU编码批量查询商品信息,req:{}",batchSkuQueryList);
        Result<List<SkuOutDTO>> listResult = goodsFeignService.batchQueryItemInfo(batchSkuQueryList);
        log.info("根据商品SKU编码批量查询商品信息,resp:{}",listResult);
        if (!listResult.isSuccess() || CollectionUtils.isEmpty(listResult.getData())) {
            return null;
        }
        return listResult.getData().stream().collect(Collectors.toMap(SkuOutDTO::getSkuCode, Function.identity(),(key1, key2) -> key2));
    }


    public void checkSkuDecimal(List<VirtualInventoryExcelDTO> list,Map<String,SkuOutDTO> skuMap){
        List<ItemDecimalCheckDTO> itemDecimalCheckList = new ArrayList<>();
        for (VirtualInventoryExcelDTO virtualInventoryExcelDTO : list) {
            ItemDecimalCheckDTO itemDecimalCheckDTO = new ItemDecimalCheckDTO();
            itemDecimalCheckDTO.setItemId(skuMap.get(virtualInventoryExcelDTO.getSkuCode()).getItemId());
            itemDecimalCheckDTO.setGoodsCount(virtualInventoryExcelDTO.getStockTotalNum());
            itemDecimalCheckList.add(itemDecimalCheckDTO);
        }
        log.info("校验商品小数规则 入参:{}", itemDecimalCheckList);
        Result<List<ItemDecimalCheckDTO>> result = goodsFeignService.checkIsItemDecimal(itemDecimalCheckList);
        log.info("校验商品小数规则 出参:{}", result);
        if (!result.isSuccess()) {
            log.info("校验商品小数规则 失败:{}", result);
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        List<ItemDecimalCheckDTO> itemDecimalCheckDTOS = result.getData();
        if (!CollectionUtils.isEmpty(itemDecimalCheckDTOS)){
            for (ItemDecimalCheckDTO itemDecimalCheckDTO : itemDecimalCheckDTOS) {
                if (CommonConstants.SUPPORT_DECIMAL_NO.equals(itemDecimalCheckDTO.getIsSupportDecimal())){
                    throw new BusinessException(ResultEnum.FAILURE.getCode(), itemDecimalCheckDTO.getItemName()+",仅支持整数购买");
                }
            }
        }
    }
    /**
     * 导入数据校验并获取excel信息集合信息（去重）
     * @param list 解析数据
     * @param skuMap sku信息集合
     * @return sku信息集合
     */
    private Result<Map<String,VirtualInventoryExcelDTO>> importDataVerifyAndGetExcelMap(List<VirtualInventoryExcelDTO> list,Map<String,SkuOutDTO> skuMap) {
        Map<Integer,String> errorMsgMap = new HashMap<>();
        Map<String,VirtualInventoryExcelDTO> excelMap = new HashMap<>();
        //导入总条数校验
        if (list.size() > CommonConstants.IMPORT_LIMIT) {
            return CommonResultUtil.error(ResultEnum.EXCEED_LIMIT_ERROR.getCode(),ResultEnum.EXCEED_LIMIT_ERROR.getMsg());
        }
//        //校验小数点
//        this.checkSkuDecimal(list,skuMap);
        for (int i = 0; i < list.size(); i++) {
            VirtualInventoryExcelDTO virtualInventory = list.get(i);
            SkuOutDTO skuOutDTO = skuMap.get(virtualInventory.getSkuCode());
            //数据非空校验
            if (!this.checkNull(virtualInventory,i,errorMsgMap)) {
                continue;
            }
            //导入sku校验
            if (ObjectUtils.isEmpty(skuOutDTO)) {
                errorMsgMap.put(i,virtualInventory.getSkuCode() + ResultEnum.QUERY_SKU_INFO_ERROR.getMsg());
                continue;
            }
            //导入数量限制校验
            if (virtualInventory.getStockTotalNum().compareTo(CommonConstants.STOCK_TOTAL_NUM_MIN_LIMIT) <= 0
                    || virtualInventory.getStockTotalNum().compareTo(CommonConstants.STOCK_TOTAL_NUM_MAX_LIMIT) >= 0) {
                errorMsgMap.put(i,ResultEnum.STOCK_TOTAL_NUM_ERROR.getMsg());
                continue;
            }
            //导入数量限制校验
            if (virtualInventory.getStockTotalNum().scale() > CommonConstants.STOCK_TOTAL_NUM_DECIMAL) {
                errorMsgMap.put(i,ResultEnum.STOCK_TOTAL_NUM_DECIMAL_ERROR.getMsg());
                continue;
            }
            //时间格式校验
            if (ObjectUtils.isEmpty(DateUtil.strToDateLong(virtualInventory.getEffectiveStartTime()))
                    || ObjectUtils.isEmpty(DateUtil.strToDateLong(virtualInventory.getEffectiveEndTime()))) {
                errorMsgMap.put(i,ResultEnum.TIME_FORMAT_WRONG_ERROR.getMsg());
                continue;
            }
            //导入生效开始时间不能大于生效结束时间校验
            if (DateUtil.strToDateLong(virtualInventory.getEffectiveStartTime()).after(DateUtil.strToDateLong(virtualInventory.getEffectiveEndTime()))) {
                errorMsgMap.put(i,ResultEnum.START_TIME_AFTER_END_TIME_ERROR.getMsg());
                continue;
            }
            //导入生效结束时间不能小于当前系统时间校验
            if (DateUtil.strToDateLong(virtualInventory.getEffectiveEndTime()).before(new Date())) {
                errorMsgMap.put(i,ResultEnum.END_TIME_BEFORE_NOW_ERROR.getMsg());
                continue;
            }
            excelMap.put(virtualInventory.getSkuCode(),virtualInventory);
        }
        if (!CollectionUtils.isEmpty(errorMsgMap)) {
            StringBuffer errorMsg = new StringBuffer();
            errorMsgMap.forEach((key,value) -> errorMsg.append(CommonConstants.ERROR_MSG_PREFIX).append(key + 2)
                    .append(CommonConstants.ERROR_MSG_PREFIX_LINE).append(value).append(StrConstant.SEMICOLON));
            return CommonResultUtil.error(ResultEnum.SYSTEM_ERROR.getCode(),errorMsg.toString());
        }
        return CommonResultUtil.success(excelMap);
    }

    /**
     * 数据非空校验
     * @param virtualInventory 解析数据
     * @param i 行数
     * @param errorMsgMap 错误集合
     * @return 非空校验结果
     */
    private Boolean checkNull(VirtualInventoryExcelDTO virtualInventory,int i,Map<Integer,String> errorMsgMap) {
        boolean checkNull = true;
        if (StringUtils.isBlank(virtualInventory.getSkuCode())) {
            errorMsgMap.put(i,ResultEnum.SKU_CODE_IS_NULL_ERROR.getMsg());
            checkNull = false;
        } else if (ObjectUtils.isEmpty(virtualInventory.getStockTotalNum())) {
            errorMsgMap.put(i,ResultEnum.STOCK_TOTAL_NUM_IS_NULL_ERROR.getMsg());
            checkNull = false;
        } else if (StringUtils.isBlank(virtualInventory.getEffectiveStartTime())) {
            errorMsgMap.put(i,ResultEnum.EFFECTIVE_START_TIME_IS_NULL_ERROR.getMsg());
            checkNull = false;
        } else if (StringUtils.isBlank(virtualInventory.getEffectiveEndTime())) {
            errorMsgMap.put(i,ResultEnum.EFFECTIVE_END_TIME_IS_NULL_ERROR.getMsg());
            checkNull = false;
        }
        return checkNull;
    }

    /**
     * 组装导入数据
     * @param excelMap 解析数据
     * @param skuMap sku信息集合
     * @param shopId 店铺id
     * @param userDetail 登录人信息
     * @return 导入数据
     */
    private List<GoodsStockDTO> getVirtualInventoryList(Map<String,VirtualInventoryExcelDTO> excelMap,
                                                        Map<String,SkuOutDTO> skuMap,
                                                        Long shopId,
                                                        LoginUserDetail userDetail) {
        return excelMap.entrySet().stream().map(item -> {
            SkuOutDTO skuOutDTO = skuMap.get(item.getKey());
            VirtualInventoryExcelDTO inventoryExcelDTO = item.getValue();
            GoodsStockDTO goodsStockDTO = new GoodsStockDTO();
            goodsStockDTO.setSellerCode(userDetail.getLoginId());
            goodsStockDTO.setItemCode(skuOutDTO.getItemCode());
            goodsStockDTO.setShopId(shopId);
            goodsStockDTO.setItemName(skuOutDTO.getItemName());
            goodsStockDTO.setItemId(skuOutDTO.getItemId());
            goodsStockDTO.setSkuCode(skuOutDTO.getSkuCode());
            goodsStockDTO.setSaleStockNum(inventoryExcelDTO.getStockTotalNum());
            goodsStockDTO.setDeleteFlag(0);
            goodsStockDTO.setEffectiveStartTime(DateUtil.strToDateLong(inventoryExcelDTO.getEffectiveStartTime()));
            goodsStockDTO.setEffectiveEndTime(DateUtil.strToDateLong(inventoryExcelDTO.getEffectiveEndTime()));
            goodsStockDTO.setCreateId(userDetail.getMemberId());
            goodsStockDTO.setModifyId(userDetail.getMemberId());
            return goodsStockDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 库存导出处理
     * @param virtualInventoryExportList 库存信息集合
     * @param response 响应
     * @param sheetNameStart 文件标题
     */
    private void exportReverseOrderHandle(List<VirtualInventoryExportVO> virtualInventoryExportList, HttpServletResponse response, String sheetNameStart) {
        try {
            String sheetName = DateUtil.dateToString(new Date());
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title",sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity",VirtualInventoryExportVO.class);
            // sheet中要填充得数据
            sheet1DataMap.put("data",CollectionUtils.isEmpty(virtualInventoryExportList) ? new ArrayList<VirtualInventoryExportVO>() : virtualInventoryExportList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> maps = new ArrayList<>();
            maps.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(maps, ExcelType.HSSF);
            // 改成输出excel文件
            response.setContentType("application/vnd.ms/-excel;charset=utf-8");
            String fileName = URLEncoder.encode(sheetNameStart + sheetName, "UTF-8");
            // 03版本后缀xls，之后的xlsx
            response.setHeader("Content-disposition","attachment; filename=" + fileName + StrConstant.POINT + StrConstant.XLS);
            OutputStream out = response.getOutputStream();
            workbook.write(out);
        } catch (Exception e) {
            log.error("批量导出虚拟库存异常,error:", e);
        }
    }
}
