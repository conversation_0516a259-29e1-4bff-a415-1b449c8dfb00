package cn.htd.s2bplus.nongzi.service.member;


import cn.htd.rdc.base.development.framework.core.result.PageResult;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.dto.user.MemberGroupResponseDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 会员分组服务
 *
 */
public interface MemberGroupService {

	/**
	 * 查询会员分组内容，保存历史报表
	 * @param groupId
	 * @param current
	 * @param pageSize
	 * @param pageResult
	 * @param loginUser
	 * @param response
	 */
	void saveGroupReportHistory(String groupId, Integer current, Integer pageSize, PageResult<List<MemberGroupResponseDTO>> pageResult, LoginUserDetail loginUser,HttpServletResponse response);

	/**
	 * 导入商家私域会员
	 * @param file 导入文件
	 * @param userDetail 用户信息
	 */
	Result<String> importSellerPrivateDomainMember(MultipartFile file,LoginUserDetail userDetail);
}

