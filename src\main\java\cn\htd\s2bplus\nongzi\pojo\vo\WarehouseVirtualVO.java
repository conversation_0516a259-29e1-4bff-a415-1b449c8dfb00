package cn.htd.s2bplus.nongzi.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/5
 */
@Data
public class WarehouseVirtualVO implements Serializable
{
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;
    @ApiModelProperty(value = "商家编码")
    private String sellerCode;
    @ApiModelProperty(value = "商家名称")
    private String sellerName;
    @ApiModelProperty(value = "仓库类型", example = "WarehouseTypeEnum枚举")
    private Integer warehouseType;

    @ApiModelProperty(value = "仓库类型", example = "WarehouseTypeEnum枚举")
    private String warehouseTypeStr;

    @ApiModelProperty(value = "仓库地址")
    private String warehouseAddress;

    @ApiModelProperty(value = "仓库联系人")
    private String warehouseContacts;

    @ApiModelProperty(value = "仓库联系电话", example = "手机格式")
    private String warehousePhone;

    @ApiModelProperty(value = "来源类型",  example = "1")
    private Integer sourceType;

    @ApiModelProperty(value = "状态仓库状态（0：启用 1：禁用）")
    private String status;

    @ApiModelProperty(value = "销售类型0=全国销售，1=区域销售", example = "1")
    private Integer nationalSales;
}
