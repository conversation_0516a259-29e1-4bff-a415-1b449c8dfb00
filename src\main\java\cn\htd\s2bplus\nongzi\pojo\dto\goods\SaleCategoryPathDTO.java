package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class SaleCategoryPathDTO implements Serializable {
    private static final long serialVersionUID = -8642010590416657958L;
    @ApiModelProperty(value = "一级类目ID")
    private Long firstLevelId;

    @ApiModelProperty(value = "一级类目名称")
    private String firstLevelName;

    @ApiModelProperty(value = "二级类目ID")
    private Long secondLevelId;

    @ApiModelProperty(value = "二级类目名称")
    private String secondLevelName;

    @ApiModelProperty(value = "三级类目ID")
    private Long thirdLevelId;

    @ApiModelProperty(value = "三级类目名称")
    private String thirdLevelName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
