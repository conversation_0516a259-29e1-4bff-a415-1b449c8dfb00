package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SellerSku implements Serializable {

    @ApiModelProperty(value = "应用渠道ID",example = "1L")
    private Long appId;

    @ApiModelProperty(value = "商品id",example = "xx",required = false)
    @Excel(name = "订单编号", height = 10, width = 20)
    private Long itemId;

    @ApiModelProperty(value = "商品编码",example = "xx",required = false)
    @Excel(name = "订单编号", height = 10, width = 20)
    private String itemCode;

    @ApiModelProperty(value = "skuId",example = "xx",required = false)
    @Excel(name = "订单编号", height = 10, width = 20)
    private Long skuId;

    @ApiModelProperty(value = "sku编码",example = "xx",required = false)
    @Excel(name = "订单编号", height = 10, width = 20)
    private String skuCode;

    @ApiModelProperty(value = "货品Code",example = "xx",required = false)
    @Excel(name = "订单编号", height = 10, width = 20)
    private String cargoCode;

    @ApiModelProperty(value = "ERP编码",example = "xx",required = false)
    private String erpCode;

    @ApiModelProperty(value = "商品名称",example = "xx",required = false)
    private String itemName;

    @ApiModelProperty(value = "规格",example = "xx",required = false)
    private String skuAttributesName;

    @ApiModelProperty(value = "品牌名称",example = "xx",required = false)
    private String brandName;

    @ApiModelProperty(value = "类目路径",example = "家用空调>家用空调>挂机>1.5P",required = false)
    private String categoryPath;

    @ApiModelProperty(value = "可用库存")
    private Long useNum;

    @ApiModelProperty(value = "可用库存 小数点")
    private BigDecimal useNumDecimal;

    @ApiModelProperty(value = "商家id",example = "xx",required = false)
    private Long sellerId;

    @ApiModelProperty(value = "店铺id",example = "xx",required = false)
    private Long shopId;

    @ApiModelProperty(value = "卖家编码",example = "xx",required = false)
    private String sellerCode;

    @ApiModelProperty(value = "卖家名称",example = "xx",required = false)
    private String sellerName;

    @ApiModelProperty(value = "商品单位",example = "xx",required = false)
    private String weightUnit;

    @ApiModelProperty(value = "商品单位名称",example = "xx",required = false)
    private String unitName;

    @ApiModelProperty(value = "卖家类型",example = "xx",required = false)
    private String sellerType;

    @ApiModelProperty(value = "店铺名称",example = "xx",required = false)
    private String shopName;

    @ApiModelProperty(value = "商品上下架状态",example = "0:下架1:上架",required = false)
    private Integer status;

    @ApiModelProperty(value = "发布时间",example = "xx",required = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "修改时间",example = "xx",required = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @ApiModelProperty(value = "上架操作时间",example = "xx",required = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visableTime;

    @ApiModelProperty(value = "下架操作时间",example = "xx",required = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date invisableTime;

    @ApiModelProperty(value = "销售价格",example = "xx",required = false)
    private BigDecimal price;

    @ApiModelProperty(value = "是否使用阶梯价",example = "xx",required = false)
    private Integer isUseLadderPrice;

    @ApiModelProperty(value = "是否使用区域价",example = "xx",required = false)
    private Integer isUseAreaPrice;

    @ApiModelProperty(value = "是否使用分组价",example = "xx",required = false)
    private Integer isUseGroupPrice;

    @ApiModelProperty(value = "分销限价",example = "xx",required = false)
    private BigDecimal saleLimitedPrice;

    @ApiModelProperty(value = "商品小数点规则")
    private DecimalPointDTO decimalPointDTO;

    @ApiModelProperty(value = "商品主图")
    private String pictureUrl;

    @ApiModelProperty(value = "包装规格比例")
    private String specificationRatio;

    @ApiModelProperty(value = "包装规格单位")
    private String specificationUnit;

    @ApiModelProperty(value = "包装规格单位(中文)")
    private String specificationUnitName;

    @ApiModelProperty( value =  "下架类型：1：商家下架 2：运营下架",example = "2")
    private Integer itemActionType;

    @ApiModelProperty(value = "下架枚举 1:经营资质证书未上传 2:价格异常 3:商品信息不符合标准 4:其他", example = "1")
    private Integer delistingType;

    @ApiModelProperty(value = "下架原因",example = "下架原因")
    private String delistingRemark;

    @ApiModelProperty(value = "驳回原因",example = "驳回原因")
    private String auditReason;

    @ApiModelProperty(value = "审核状态 0:待审核 ,1：审核通过,2：审核驳回",example = "1")
    private Integer qualificationAuditStatus;

    @ApiModelProperty(value = "同步时间",example = "1")
    private Date lastChannelModifyTime;

    @ApiModelProperty(value = "国条码", example = "3333")
    private String eanCode;

    @ApiModelProperty(
            value = "售卖类型 1：私域 2：公域",
            example = "1"
    )
    private String salesType;
    @ApiModelProperty(
            value = "平台佣金比例/佣金值",
            example = "0.1"
    )
    private String platformCommissionRate;
    @ApiModelProperty(
            value = "运营佣金比例/佣金值",
            example = "0.1"
    )
    private String operateCommissionRate;
    @ApiModelProperty(
            value = "推客佣金比例/佣金值",
            example = "0.1"
    )
    private String tweeterCommissionRate;
    @ApiModelProperty(
            value = "对应类目平台佣金比例",
            example = "0.1"
    )
    private BigDecimal categoryPlatformCommissionRate;
    @ApiModelProperty(
            value = "对应类目运营佣金比例",
            example = "0.1"
    )
    private BigDecimal categoryOperateCommissionRate;
    @ApiModelProperty(
            value = "对应类目推客佣金比例",
            example = "0.1"
    )
    private BigDecimal categoryTweeterCommissionRate;
    @ApiModelProperty(
            value = "分销开关 0：打开 1：关闭",
            example = "1"
    )
    private String distributionSwitch;
    @ApiModelProperty(
            value = "分销开关角色 0：商家中心 1：OSS",
            example = "1"
    )
    private String distributionSwitchRole;
    @ApiModelProperty(
            value = "推客佣金比例/佣金固定值角色 0：商家中心 1：OSS",
            example = "1"
    )
    private String tweeterCommissionRateRole;

    @ApiModelProperty(
            value = "外接商品sku_id或者erp上行过来的sku",
            example = "234"
    )
    private String outerSkuId;

    @ApiModelProperty(value = "公域状态 0未开始 1生效中 2已结束", example = "1")
    private String domainStatus;

    @ApiModelProperty(value = "老基础价格", example = "99.99")
    private BigDecimal oldPrice;

    @ApiModelProperty(value = "加价数值", example = "10.00")
    private BigDecimal markupValue;

    @ApiModelProperty(value = "加价方式 1比例 2绝对值", example = "1")
    private Integer markupMethod;

    @ApiModelProperty(value = "成本", example = "10.0000")
    private BigDecimal cost;

    @ApiModelProperty(value = "生效开始时间", example = "2023-01-01 00:00:00")
    private Date effectiveTime;

    @ApiModelProperty(value = "生效结束时间", example = "2023-12-31 23:59:59")
    private Date invalidTime;

    @ApiModelProperty(value = "生效标识 1长期有效 2指定时间", example = "1")
    private Integer effectiveMark;

    @ApiModelProperty(value = "商品扩展对象")
    private ItemExtendDTO itemExtendDTO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
