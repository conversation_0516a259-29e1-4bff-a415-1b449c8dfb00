package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ItemAttributeValueDTO implements Serializable {
    private static final long serialVersionUID = -8983543087736254765L;

    @ApiModelProperty(value = "属性值ID")
    private Long valueId;

    @ApiModelProperty(value = "属性ID")
    private Long attrId;

    @ApiModelProperty(value = "值名称 规格名称最大8个字符")
    private String valueName;

    @ApiModelProperty(value = "删除标记")
    private Integer status;

    @ApiModelProperty(value = "属性名称 规格属性名称最大8个字符")
    private String attrName;

    @ApiModelProperty(value = "商家id")
    private Long sellerId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }

}
