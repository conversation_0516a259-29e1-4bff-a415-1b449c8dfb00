package cn.htd.s2bplus.nongzi.service.goods;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.BatchImportGoodsDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.BatchImportGoodsRecordDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ExportGoodsInfoReqVO;
import cn.htd.s2bplus.nongzi.pojo.dto.excel.ImportGoodsInfoDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.IntentionAddressRecordAddDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.LoginUserDetail;
import cn.htd.s2bplus.nongzi.pojo.vo.ImportItemOrSkuListVO;
import cn.htd.s2bplus.nongzi.utils.FileInfoDTO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 商品服务
 *
 */
public interface GoodsService {
	Map<Long,String> getShopInfo(LoginUserDetail loginUser, String shopName);

	/**
	 * 导入商品/SKU接口-限购策略
	 * @param file
	 * @param shopId
	 * @param type
	 * @return
	 */
	Result<ImportItemOrSkuListVO> importItemOrSkuList(MultipartFile file, Long shopId, Integer type);

	/**
	 * sku上下架导入
	 * @param file
	 */
	Result<String> ImportSkuUpShelf(MultipartFile file);

	/**
	 * oss-批量导入下架商品
	 * @param file
	 * @return
	 */
	Result<String> importRemoveItem(MultipartFile file);

	/**
	 * 更新收货人信息
	 *
	 * @param addressRecordAddDTOS
	 */
	void updateConsigneeInfo(List<IntentionAddressRecordAddDTO> addressRecordAddDTOS);


	/**
	 * 	校验导入商品数据，将校验结果生成excel文件上传并保存，同时保存校验通过的货盘商品
	 *
	 */
	void importGoodsList(List<ImportGoodsInfoDTO> importGoodsInfoDTOS, LoginUserDetail user,String fileName,HttpServletResponse response);

	/**
	 * 保存报表生成下载历史(保存至goods-service)
	 */
	void saveReportHistoryToGoodsService(String downloadUrl,Integer businessType, LoginUserDetail loginUser);

	/**
	 * 校验并保存批导商品信息
	 */
	 void checkAndSaveImportGoods(List<BatchImportGoodsDTO> batchImportGoodsDTOS, List<FileInfoDTO> picFiles,String batchNo,LoginUserDetail user);

	/**
	 * 批量保存图片至商品图库
	 */
	void batchSaveGoodsImage(List<FileInfoDTO> picFiles, String batchNo,LoginUserDetail loginUser);

	/**
	 * 保存上传的excel数据
	 */
	void saveImportUploadRecordList(List<BatchImportGoodsDTO> errorGoodsRecordList, LoginUserDetail loginUser);
}

