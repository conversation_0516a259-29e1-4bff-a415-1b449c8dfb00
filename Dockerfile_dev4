FROM 171.16.55.100/b2b_p/centos6v5:latest

EXPOSE 8080


WORKDIR /home/<USER>

RUN rm -rf  tomcat7 tomcat8

ADD ./target/s2bplus-nongzi-api.jar ./

ARG JACOCO_AGENT_DOWNLOAD_URL=http://10.20.4.108:5003/static/jacocoagent.jar
RUN mkdir -p /opt/jacoco \
  && echo "Downloading jacoco agent" \
  && curl -fsSL -o /opt/jacoco/jacocoagent.jar ${JACOCO_AGENT_DOWNLOAD_URL}

ENV nacos_config_server_addr=***********:8848 \
    nacos_discovery_server_addr=***********:8848 \
    nacos_config_namespace=70924ec0-4584-42b2-bd3f-abf85eb30506


ENV JAVA_OPTS="-Xms1G -Xmx1G -XX:PermSize=256M -XX:MaxPermSize=256m  -Dfile.encoding=UTF8 -Dsun.jnu.encoding=UTF8  -Dapp.id=s2bplus-nongzi-api -javaagent:/opt/jacoco/jacocoagent.jar=includes=*,output=tcpserver,address=*,port=8001,hostname=s2bplus-nongzi-api-dev4"

ENTRYPOINT java ${JAVA_OPTS}   -Dmaven.test.skip=true  -jar  s2bplus-nongzi-api.jar