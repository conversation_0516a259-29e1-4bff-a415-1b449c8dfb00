package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.rdc.base.development.framework.core.mp.support.Query;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class QueryVirtualInventoryDTO extends Query implements Serializable {

    /**
     * 商家编码
     */
    @ApiModelProperty(value = "商家编码",notes = "商家编码",example = "1")
    private String sellerCode;

    /**
     * 店铺ID
     */
    @ApiModelProperty(value = "店铺ID",notes = "店铺ID",example = "1")
    private Long shopId;

    /**
     * sku编码
     */
    @ApiModelProperty(value = "sku编码",notes = "sku编码",example = "1")
    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称",notes = "商品名称",example = "1")
    private String itemName;

    /**
     * 库存状态 1:未开始,2:生效中,3:已过期
     */
    @ApiModelProperty(value = "库存状态 1:未开始,2:生效中,3:已过期",notes = "库存状态 1:未开始,2:生效中,3:已过期",example = "1")
    private String inventoryStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
