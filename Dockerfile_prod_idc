#FROM   171.16.55.100/b2b_p/centos6v5-tingyun-v2:latest
#FROM 171.16.55.100/b2b_p/centos6v5-ty-msah:latest
FROM test-dockimgws.htd.cn/b2b_p/centos6v5-ty364-msah:latest

RUN sed -i '0,/htdty/s//idc-nongzi-api/' /home/<USER>/tingyun/tingyun.properties
#RUN sed -i '0,/micloud-biz/s//idc-nongzi-api/' /home/<USER>/tingyun/tingyun.properties

EXPOSE 8080

WORKDIR /home/<USER>

RUN rm -rf  tomcat7 tomcat8

ADD ./target/s2bplus-nongzi-api.jar ./

ENV nacos_config_server_addr=171.16.46.200:8848 \
    nacos_discovery_server_addr=171.16.46.200:8848 \
    nacos_config_namespace=0b141e80-bf11-4d75-95e9-132c32fc67ac

ENV JAVA_OPTS="-Xms2G -Xmx2G -XX:PermSize=512M -XX:MaxPermSize=512M  -Dfile.encoding=UTF8 -Dsun.jnu.encoding=UTF8   -Dapp.id=s2bplus-nongzi-api \
-Dregion-id=idc \
-Dzone-id=idc-zone-a \
-Dvpc-id=empty \
-Downer-account-id=**************** \
-javaagent:/home/<USER>/msha-java-agent.jar \
-Dmsha.licence=a4d94903-d810-4f9c-b8e2-e2836d8f00e8 \
-Dmsha.namespaces=f84f6aea-8903-4a75-b10c-89ad7be3ff58 \
-Dmsha.app.name=idc-nongzi-api \
-Dmsha.nacos.namespace=3a2e97e3-27a6-4324-9ee7-4d52d3d874e0 \
-Dmsha.nacos.server.addr=*************:8848"

ENTRYPOINT java ${JAVA_OPTS}  -javaagent:/home/<USER>/tingyun/tingyun-agent-java.jar   -Dmaven.test.skip=true  -jar  s2bplus-nongzi-api.jar
