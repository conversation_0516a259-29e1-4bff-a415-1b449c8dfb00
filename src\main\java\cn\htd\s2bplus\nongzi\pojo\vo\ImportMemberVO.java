package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.user.BatchMemberVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class ImportMemberVO implements Serializable {

    private static final long serialVersionUID = -4988091322569225437L;
    /**
     * 文件url
     */
    @ApiModelProperty(value = "文件url")
    private String downloadUrl;


    /**
     * 会员上传信息
     */
    @ApiModelProperty(value = "会员上传信息")
    private List<BatchMemberVO> batchMemberVOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
