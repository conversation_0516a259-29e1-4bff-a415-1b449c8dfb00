package cn.htd.s2bplus.nongzi.pojo.dto.distribute;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BasePageRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;


@Data
public class DistributorCommissionReqDTO extends BasePageRequestDTO implements Serializable {
    @ApiModelProperty("推客编码")
    private String memberCode;

    @ApiModelProperty(value = "经营人姓名")
    private String businessPersonName;
    @ApiModelProperty(value = "推客类型", example = "1：推客/分销员，2：合伙人")
    private Integer distributeType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
