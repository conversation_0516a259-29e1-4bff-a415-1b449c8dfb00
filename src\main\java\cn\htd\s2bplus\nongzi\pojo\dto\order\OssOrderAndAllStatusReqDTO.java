package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.nongzi.utils.MyJsonStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * 订单信息(各种订单状态)
 */
@Data
@ApiModel
public class OssOrderAndAllStatusReqDTO implements Serializable  {

    @ApiModelProperty(
            value = "tab页时间标记,0-表示最近三个月,1-表示三个月以前的数据",
            notes = "tab页时间标记,0-表示最近三个月,1-表示三个月以前的数据",
            allowableValues = "4",
            dataType = "Integer",
            example = "0"
    )
    private Integer tabTime;

    @ApiModelProperty(
            value = "订单来源,2-代客下单,22-云场订单",
            notes = "订单来源,2-代客下单,22-云场订单",
            allowableValues = "4",
            dataType = "String",
            example = "22"
    )
    private String orderFrom;

    @ApiModelProperty(
            value = "云场订单子类型,1-字头表示撮合,10-来自商城的撮合订单,11-来自经理人的撮合订单",
            notes = "云场订单子类型,1-字头表示撮合,10-来自商城的撮合订单,11-来自经理人的撮合订单",
            allowableValues = "4",
            dataType = "String",
            example = "1"
    )
    private String subOrderFrom;

    @ApiModelProperty(
            value = "订单编号",
            notes = "订单编号",
            allowableValues = "32",
            dataType = "String",
            example = "5316037125911209017"
    )
    private String orderNo;

    @ApiModelProperty(
            value = "买家名称",
            notes = "买家名称",
            allowableValues = "255",
            dataType = "String",
            example = "江苏炜赋集团建设开发有限公司"
    )
    private String buyerName;

    @ApiModelProperty(
            value = "商品名称",
            notes = "商品名称",
            allowableValues = "255",
            dataType = "String",
            example = "TCL直拨库商品"
    )
    private String goodsName;

    @ApiModelProperty(
            value = "手机号",
            notes = "手机号",
            allowableValues = "32",
            dataType = "String",
            example = "18346853536"
    )
    private String consigneePhoneNum;

    @ApiModelProperty(
            value = "订单状态,0-交易成功,1-未付款,-1-待审核,2-未发货,3-已发货,10-交易关闭",
            notes = "订单状态,0-交易成功,1-未付款,-1-待审核,2-未发货,3-已发货,10-交易关闭",
            allowableValues = "10",
            dataType = "String",
            example = "TCL直拨库商品"
    )
    private String orderStatus;

    @ApiModelProperty(
            value = "用于区分已支付-待审核状态,1-待审核 2-通过，3-驳回",
            notes = "用于区分已支付-待审核状态,1-待审核 2-通过，3-驳回",
            allowableValues = "10",
            dataType = "String",
            example = "1"
    )
    private String orderAuthorizationStatus;

    @ApiModelProperty(
            value = "支付系统流水号",
            notes = "支付系统流水号",
            allowableValues = "32",
            dataType = "String",
            example = "6A16823018989527811"
    )
    private String paySerialNo;

    @ApiModelProperty(
            value = "下单开始时间 格式:yyyy-MM-dd HH:mm:ss",
            notes = "区间 开始时间 取下单时间,格式:yyyy-MM-dd HH:mm:ss",
            allowableValues = "32",
            dataType = "String",
            example = "2023-04-01 00:00:00"
    )
    private String beginTime;

    @ApiModelProperty(
            value = "下单结束时间 格式:yyyy-MM-dd HH:mm:ss",
            notes = "区间 结束时间 取下单时间,格式:yyyy-MM-dd HH:mm:ss",
            allowableValues = "32",
            dataType = "String",
            example = "2023-04-25 00:00:00"
    )
    private String endTime;

    @ApiModelProperty(
            value = "支付开始时间 格式:yyyy-MM-dd HH:mm:ss",
            notes = "区间 开始时间 取支付时间,格式:yyyy-MM-dd HH:mm:ss",
            allowableValues = "32",
            dataType = "String",
            example = "2023-04-01 00:00:00"
    )
    private String startPayTime;

    @ApiModelProperty(
            value = "支付结束时间 格式:yyyy-MM-dd HH:mm:ss",
            notes = "区间 结束时间 取支付时间,格式:yyyy-MM-dd HH:mm:ss",
            allowableValues = "32",
            dataType = "String",
            example = "2023-04-25 00:00:00"
    )
    private String endPayTime;

    @ApiModelProperty(
            value = "店铺id",
            notes = "店铺id",
            allowableValues = "20",
            dataType = "Long",
            example = "5251"
    )
    private Long shopId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
