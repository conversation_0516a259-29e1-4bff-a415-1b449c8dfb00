package cn.htd.s2bplus.nongzi.service.goods;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.SubmitAssignmentExecutorVO;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface SubmitAssignmentService {

    /**
     * 导入任务执行人
     * @param file
     * @return
     */
    Result<List<SubmitAssignmentExecutorVO>> importBatchAssignmentExecutor(MultipartFile file);


    /**
     * 导出提报商品信息
     * @param submitAssignmentId
     * @return
     */
    Result<String> deriveSubmitAssignmentGood(Long submitAssignmentId,HttpServletResponse response);
}
