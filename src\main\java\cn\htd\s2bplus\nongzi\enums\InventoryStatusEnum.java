package cn.htd.s2bplus.nongzi.enums;

/**
 * <AUTHOR>
 */

public enum InventoryStatusEnum {

    INVENTORY_STATUS_NOT_START("1", "未开始"),
    INVENTORY_STATUS_IN_EFFECT("2","生效中"),
    INVENTORY_STATUS_EXPIRED("3","已过期"),
    ;

    private String status;
    private String msg;

    InventoryStatusEnum(String status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public String getStatus() {
        return status;
    }

    public String getMsg() {
        return msg;
    }
}
