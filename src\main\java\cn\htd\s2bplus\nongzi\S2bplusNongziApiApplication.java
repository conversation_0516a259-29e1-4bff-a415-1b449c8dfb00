package cn.htd.s2bplus.nongzi;

import cn.htd.s2bplus.nongzi.properties.ConfigProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "cn.htd.*")
@MapperScan("cn.htd.mapper")
@EnableDiscoveryClient
@EnableFeignClients
@EnableAsync
@EnableConfigurationProperties(ConfigProperties.class)
public class S2bplusNongziApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(S2bplusNongziApiApplication.class, args);
    }

}
