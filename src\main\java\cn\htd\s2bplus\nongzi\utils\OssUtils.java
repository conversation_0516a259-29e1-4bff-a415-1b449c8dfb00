package cn.htd.s2bplus.nongzi.utils;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.s2bplus.nongzi.contants.CommonConstants;
import cn.htd.s2bplus.nongzi.pojo.dto.guestorder.ApiResultEnum;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import lombok.Data;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.util.StringUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OSS公共方法
 */
@Data
public class OssUtils {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    //文件分隔符
    private static final String FILE_SPLIT = "/";

    //S2B产品线
    private static final String S2B = "b2b";


    /**
     * 连接 oss
     *
     * @return
     */
    public OSS initOssClient(String endpoint, String accessKeyId, String accessKeySecret) {
        logger.info("开始连接OSS存储服务器域名：endpoint={},accessKeyId={},accessKeySecret={}", endpoint, accessKeyId, accessKeySecret);
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

    /**
     * 文件上传
     *
     * @param bucketName
     * @param
     * @param fileName    文件名称
     * @param
     * @return
     */
    public String upload(InputStream inputStream, String fileName, String bucketName, String endpoint, String accessKeyId, String accessKeySecret) throws Exception {
        logger.info("OSS文件上传开始 bucketName={}", bucketName);
        //连接OSS
        OSS ossClient = null;
        try{
            ossClient = initOssClient(endpoint, accessKeyId, accessKeySecret);
            //判断bucketName是否存在
            if (!ossClient.doesBucketExist(bucketName)) {
                logger.info("Bucket不存在，创建Bucket={}", bucketName);
                ossClient.createBucket(bucketName);
            }
            changeMakeWorkingDir(S2B, bucketName, ossClient);
            String objectName = S2B + FILE_SPLIT + fileName;
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(inputStream.available());
            // 指定该Object被下载时的网页的缓存行为
            metadata.setCacheControl("no-cache");
            // 指定该Object下设置Header
            metadata.setHeader("Pragma", "no-cache");
            // 指定该Object被下载时的内容编码格式
            metadata.setContentEncoding("utf-8");
            //文件上传
            PutObjectResult result = ossClient.putObject(bucketName, objectName, inputStream);
            if (result != null) {
                logger.info("OSS文件上传成功");
                //文件上下载
                OSSObject ossObject = ossClient.getObject(bucketName, objectName);
                //获取url
                return ossObject.getResponse().getUri();
            }else{
                logger.error("OSS文件上传失败 bucketName={}, objectName={}", bucketName, objectName);
                return null;
            }
        } catch (Exception ex){
            logger.error("OSS文件上传异常 bucketName={}, fileName={}", bucketName, fileName, ex);
            return null;
        } finally {
            // 确保始终关闭OSS客户端
            if (ossClient != null) {
                ossClient.shutdown();
                logger.info("OSS客户端已关闭");
            }
        }
    }

    /**
     * 文件路径判断是否存在
     *
     * @param path
     * @param bucketName
     * @param oSSClient
     * @throws Exception
     */
    private void changeMakeWorkingDir(String path, String bucketName, OSS oSSClient) throws Exception {
        //path = path.substring(1, path.length());
        boolean exists = oSSClient.doesObjectExist(bucketName, path);
        if (!exists) {
            path = new String(path.getBytes("GBK"), "iso-8859-1");
            if (!"".equals(path)) {
                ObjectMetadata objectMeta = new ObjectMetadata();
                byte[] buffer = new byte[0];
                ByteArrayInputStream in = new ByteArrayInputStream(buffer);
                objectMeta.setContentLength(0L);
                oSSClient.putObject(bucketName, path, in, objectMeta);
            }
        }
    }


    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     * @param sourceList 数据集合
     * @param destinationClass 模版导出泛型类
     * @param sheetNameStart  sheet1名称
     * @param response
     * @return
     */
    public String  getDownloadUrl(List<?> sourceList, Class<?> destinationClass,String sheetNameStart,String bucket,String endpoint, String accessKeyId, String accessKeySecret, HttpServletResponse response){

        String downloadUrl = "";
        try{
            String time = DateUtil.getCurrentDateFull();
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", destinationClass);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(sourceList) ? new ArrayList<T>() : sourceList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            response.setContentType(CommonConstants.CONTENT_TYPE_MS_EXCEL); // 改成输出excel文件
            String fileName = sheetNameStart +CommonConstants.UNDERLINE+ time;
            response.setHeader(CommonConstants.CONTENT_DISPOSITION,
                    CommonConstants.EXCEL_ATTACHMENT + fileName + CommonConstants.EXCEL_XLS);// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            String ossFileName = fileName+CommonConstants.EXCEL_XLS;
            downloadUrl = upload(excelStream,ossFileName, bucket,endpoint , accessKeyId, accessKeySecret);
        }catch (Exception e){
            logger.error("生成excel文件上传的阿里云服务器异常:",e);
        }
        return downloadUrl;
    }


    /**
     * 动态生成excel文件，并上传的阿里云服务器返回文件地址路径
     * @param sourceList 数据集合
     * @param entityList 模版导出泛型类
     * @param sheetNameStart  sheet1名称
     * @param response
     * @return
     */
    public String getCommonDownloadUrl(List<?> sourceList, List<ExcelExportEntity> entityList, String sheetNameStart, String bucket, String endpoint, String accessKeyId, String accessKeySecret, HttpServletResponse response) {
        String downloadUrl = "";
        try {
            String time = DateUtil.getCurrentDateFull();
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建Workbook并写入表头信息
            Workbook workbook = new HSSFWorkbook();
            Sheet sheet = workbook.createSheet(sheet1Params.getSheetName());
            // 写入表头信息
            Row headerRow = sheet.createRow(0);
            int colIndex = 0;
            for (ExcelExportEntity excelExportEntity : entityList) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(excelExportEntity.getName());
            }
            response.setContentType(CommonConstants.CONTENT_TYPE_MS_EXCEL); // 改成输出excel文件
            String fileName = sheetNameStart + CommonConstants.UNDERLINE + time;
            response.setHeader(CommonConstants.CONTENT_DISPOSITION,
                    CommonConstants.EXCEL_ATTACHMENT + fileName + CommonConstants.EXCEL_XLS);// 03版本后缀xls，之后的xlsx
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            String ossFileName = fileName + CommonConstants.EXCEL_XLS;
            downloadUrl = upload(excelStream, ossFileName, bucket, endpoint, accessKeyId, accessKeySecret);
        } catch (Exception e) {
            logger.error("生成excel文件上传的阿里云服务器异常:", e);
        }
        return downloadUrl;
    }


    /**
     *
     * @param path
     * @param inputStream
     * @param fileName
     * @param bucketName
     * @param endpoint
     * @param accessKeyId
     * @param accessKeySecret
     * @return
     * @throws Exception
     */
    public String upload(String path, InputStream inputStream, String fileName, String bucketName, String endpoint, String accessKeyId, String accessKeySecret) throws Exception {
        logger.info("OSS文件上传开始 bucketName={}", bucketName);
        //连接OSS
        OSS ossClient = null;
        try{
            ossClient = initOssClient(endpoint, accessKeyId, accessKeySecret);
            //判断bucketName是否存在
            if (!ossClient.doesBucketExist(bucketName)) {
                logger.info("Bucket不存在，创建Bucket={}", bucketName);
                ossClient.createBucket(bucketName);
            }
            // path参数处理
            if(StringUtil.isEmpty(path)){
                throw new BusinessException(ApiResultEnum.ERROR.getCode(), "path参数不能为空");
            }
            if(!path.startsWith("/")){
                path = "/" + path;
            }
            if(!path.endsWith("/")){
                path = path + "/";
            }
            changeMakeWorkingDir(S2B + path, bucketName, ossClient);
            String objectName = S2B + path + fileName;
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(inputStream.available());
            // 指定该Object被下载时的网页的缓存行为
            metadata.setCacheControl("no-cache");
            // 指定该Object下设置Header
            metadata.setHeader("Pragma", "no-cache");
            // 指定该Object被下载时的内容编码格式
            metadata.setContentEncoding("utf-8");
            //文件上传
            PutObjectResult result = ossClient.putObject(bucketName, objectName, inputStream);
            if (result != null) {
                logger.info("OSS文件上传成功");
                //文件上下载
                OSSObject ossObject = ossClient.getObject(bucketName, objectName);
                //获取url
                return ossObject.getResponse().getUri();
            }else{
                logger.error("OSS文件上传失败 bucketName={}, objectName={}", bucketName, objectName);
                return null;
            }
        } catch (Exception ex){
            logger.error("OSS文件上传异常 bucketName={}, fileName={}", bucketName, fileName, ex);
            return null;
        } finally {
            // 确保始终关闭OSS客户端
            if (ossClient != null) {
                ossClient.shutdown();
                logger.info("OSS客户端已关闭");
            }
        }
    }

    /**
     * 生产excel文件，并上传的阿里云服务器返回文件地址路径
     * @param path 指定文件存储的路径
     * @param sourceList 数据集合
     * @param destinationClass 模版导出泛型类
     * @param sheetNameStart  sheet1名称
     * @param response
     * @return
     */
    public String  getDownloadUrl(String path, List<?> sourceList, Class<?> destinationClass,String sheetNameStart,String bucket,String endpoint, String accessKeyId, String accessKeySecret){

        String downloadUrl = "";
        try{
            String time = DateUtil.getDaySS();
            ExportParams sheet1Params = new ExportParams();
            // 设置sheet1得名称
            sheet1Params.setSheetName(sheetNameStart);
            // 创建sheet1使用得map
            Map<String, Object> sheet1DataMap = new HashMap<>();
            // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
            sheet1DataMap.put("title", sheet1Params);
            // 模版导出对应得实体类型
            sheet1DataMap.put("entity", destinationClass);
            // sheet中要填充得数据
            sheet1DataMap.put("data", CollectionUtils.isEmpty(sourceList) ? new ArrayList<T>() : sourceList);
            // 将sheet1使用得map进行包装
            List<Map<String, Object>> sheetsList = new ArrayList<>();
            sheetsList.add(sheet1DataMap);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
            String fileName = sheetNameStart + CommonConstants.UNDERLINE + time;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            InputStream excelStream = new ByteArrayInputStream(out.toByteArray());
            out.close();
            String ossFileName = fileName+CommonConstants.EXCEL_XLS;
            downloadUrl = upload(path,excelStream,ossFileName, bucket,endpoint , accessKeyId, accessKeySecret);
        }catch (Exception e){
            logger.error("生成excel文件上传的阿里云服务器异常:",e);
        }
        return downloadUrl;
    }

}
