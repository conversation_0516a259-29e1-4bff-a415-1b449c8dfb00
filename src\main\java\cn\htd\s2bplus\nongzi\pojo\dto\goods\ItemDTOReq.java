package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ItemDTOReq implements Serializable {
    private static final long serialVersionUID = -69806729556642274L;

    @ApiModelProperty(value = "是否新增产品标识 ")
    private Boolean addSpuFlag;

    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "卖家ID")
    private Long sellerId;

    @ApiModelProperty(value = "卖家编码")
    private String sellerCode;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "商店名称")
    private String shopName;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品状态 0:待审核 ,1：审核通过,2：审核驳回，3：待ERP上行库存及价格或外部商品品类价格待映射，4 未上架，5：已上架  6：已删除")
    private Integer itemStatus;

    @ApiModelProperty(value = "末级类目ID")
    private Long cid;

    @ApiModelProperty(value = "品牌")
    private Long brand;

    @ApiModelProperty(value = "品牌名")
    private String brandName;

    @ApiModelProperty(value = "型号")
    private String modelType;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "计量单位名称")
    private String unitName;

    @ApiModelProperty(value = "商品主图URL")
    private String itemPictureUrl;

    @ApiModelProperty(value = "商品图片")
    private List<ItemPictureDTO> itemPictureDTOList;

    @ApiModelProperty(value = "销售区域集合")
    private List<ItemSalesAreaDTO> itemSalesAreaDTOList;

    @ApiModelProperty(value = "销售属性勾选集合")
    private List<SimpleCategoryAttrDTO> chekAttrList;

    @ApiModelProperty(value = "类目属性集合 ")
    private List<SimpleCategoryAttrDetailDTO> categoryAttrDetailDTOS;

    @ApiModelProperty(value = "商品sku列表")
    private List<ItemSkuDTO> itemSkuDTOList;

    @ApiModelProperty(value = "包装清单")
    private String packingList;

    @ApiModelProperty(value = "商品描述")
    private ItemDescribeDTO itemDescribeDTO;

    @ApiModelProperty(value = "商品详情图片名称： 文件夹_图片名")
    private List<String> itemDescribePicNameList;

    @ApiModelProperty(value = "商品和销售类目的关系集合")
    private List<ItemSaleCategoryDTO> itemSaleCategoryList;

    @ApiModelProperty(value = "倍数标记(0:不使用倍数,1:使用倍数)")
    private Integer multipleFlag;

    @ApiModelProperty(value = "倍数")
    private Integer multipleNumber;

    @ApiModelProperty(value = "运费模版ID")
    private Long shopFreightTemplateId;

    @ApiModelProperty(value = "店铺信息集合")
    private List<ShopDTO> shopInfoList;

    @ApiModelProperty("是否选品 1：是 0或null：否")
    private Integer chooseStatus = 0;

    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "商品图片名称：文件夹名_图片名")
    private List<String> itemPicNameList;
}
