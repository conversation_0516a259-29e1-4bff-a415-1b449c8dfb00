package cn.htd.s2bplus.nongzi.enums;

/**
 * 批量导入下架原因枚举
 */
public enum DelistingTypeEnum {

    CER_NOT_UPLOAD(1, "经营资质证书未上传"),

    PRICE_EXCEPTION(2, "价格异常"),

    ITEM_NOT_MATCH_STANDARD(3, "商品信息不符合标准"),

    OTHER(4, "其他"),
    ;


    private Integer code;
    private String msg;

    DelistingTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    // 添加此静态方法根据msg查找对应的code
    public static Integer getCodeByMsg(String msg) {
        for (DelistingTypeEnum type : DelistingTypeEnum.values()) {
            if (type.getMsg().equals(msg)) {
                return type.getCode();
            }
        }
       return null;
    }

}
