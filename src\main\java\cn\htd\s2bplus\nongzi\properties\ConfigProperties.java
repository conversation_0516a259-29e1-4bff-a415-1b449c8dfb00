package cn.htd.s2bplus.nongzi.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc ConfigProperties
 * @data 2020/9/18
 */
@Data
@ConfigurationProperties(prefix = "api")
@RefreshScope
public class ConfigProperties implements Serializable {
    private static final long serialVersionUID = -8231568156802035509L;


    private String xssSwitch = "1";

}
