package cn.htd.s2bplus.nongzi.pojo.dto.finance;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class OfflineSettlementInfoDTO implements Serializable {
    private static final long serialVersionUID = 5288706208071698268L;

    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value ="id", example = "")
    private Long id;

    /**
     * 订单编号
     */
    @ApiModelProperty(value ="订单编号", example = "")
    private String orderNo;

    /**
     * 买家编码
     */
    @ApiModelProperty(value ="买家编码", example = "")
    private String buyerCode;

    /**
     * 买家名称
     */
    @ApiModelProperty(value ="买家名称", example = "")
    private String buyerName;

    /**
     * 卖家编码
     */
    @ApiModelProperty(value ="卖家编码", example = "")
    private String sellerCode;

    /**
     * 卖家名称
     */
    @ApiModelProperty(value ="卖家名称", example = "")
    private String sellerName;

    /**
     * 店铺ID
     */
    @ApiModelProperty(value ="店铺ID", example = "")
    private Long shopId;

    /**
     * 支付方式
     */
    @ApiModelProperty(value ="支付方式: 7-线下汇款支付", example = "7")
    private String payType;

    /**
     * 订单创建时间
     */
    @ApiModelProperty(value ="订单创建时间", example = "")
    private Date createOrderTime;

    /**
     * 订单实付金额
     */
    @ApiModelProperty(value ="订单实付金额", example = "")
    private BigDecimal orderPayAmount;

    /**
     * 平台佣金
     */
    @ApiModelProperty(value ="平台佣金", example = "")
    private BigDecimal platformAmount;

    /**
     * 结算佣金
     */
    @ApiModelProperty(value ="结算佣金", example = "")
    private BigDecimal settleAmount;

    /**
     * 结算时间
     */
    @ApiModelProperty(value ="结算时间", example = "")
    private Date settleCompleteTime;

    /**
     * 结算状态：0-未结算，1-已结算
     */
    @ApiModelProperty(value ="结算状态：0-未结算，1-已结算", example = "")
    private Integer settleStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value ="创建时间", example = "")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value ="创建人", example = "")
    private String createName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value ="更新时间", example = "")
    private Date modifyTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value ="更新人", example = "")
    private String modifyName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
