package cn.htd.s2bplus.nongzi.pojo.vo;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CouponRuleVO implements Serializable {


    private static final long serialVersionUID = 5545162918123933625L;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "会员名称")
    private String memberName;

    @ApiModelProperty(value = "待发券面额")
    private BigDecimal waitRemainAmount;

    @ApiModelProperty(value = "结余金额")
    private BigDecimal thisTimeRemainAmount=BigDecimal.ZERO;

    @ApiModelProperty(value = "单笔订单用券比例")
    private BigDecimal singleOrderLimit;

    @ApiModelProperty(value = "实际待发券面额")
    private BigDecimal actualAaitRemainAmount;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
