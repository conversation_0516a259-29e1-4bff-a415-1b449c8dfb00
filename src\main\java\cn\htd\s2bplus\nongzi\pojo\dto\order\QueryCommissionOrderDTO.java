package cn.htd.s2bplus.nongzi.pojo.dto.order;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> gm
 * @create 2024/2/29
 */
@Data
@ApiModel
public class QueryCommissionOrderDTO extends BaseDTO implements Serializable {


    @ApiModelProperty(value = "应用渠道编码")
    private String appCode;

    @ApiModelProperty(value = "商家名称")
    private String sellerName;

    @ApiModelProperty(value = "客户名称")
    private String buyerName;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单创建时间开始")
    private Date orderCreateTimeStart;

    @ApiModelProperty(value = "订单创建时间结束")
    private Date orderCreateTimeEnd;

    @ApiModelProperty(value = "商家类型")
    private Integer sellerType;

    @ApiModelProperty(value = "线下结算状态")
    private Integer offlineSettlementStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
