package cn.htd.s2bplus.nongzi.pojo.vo;
import cn.htd.rdc.base.development.framework.core.mp.support.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WarehouseVirtualApiDTO extends Query implements Serializable {

	 @ApiModelProperty(name =  "仓库编码")
	private String warehouseCode;

	 @ApiModelProperty(name =  "仓库名称")
	private String warehouseName;

	 @ApiModelProperty(name =  "品牌方编码")
	private String sellerCode;

	 @ApiModelProperty(name =  "来源 （1：自建 2：外部）")
	private Integer sourceType;

	 @ApiModelProperty(name =  "仓库状态（0：启用 1：禁用）")
	private Integer status;
}
