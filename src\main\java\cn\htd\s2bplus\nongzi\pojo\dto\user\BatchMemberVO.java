package cn.htd.s2bplus.nongzi.pojo.dto.user;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * 批量查询会员vo
 */
@Data
public class BatchMemberVO implements Serializable {

    private static final long serialVersionUID = 4124390838739398440L;
    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "会员名称")
    private String companyName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
