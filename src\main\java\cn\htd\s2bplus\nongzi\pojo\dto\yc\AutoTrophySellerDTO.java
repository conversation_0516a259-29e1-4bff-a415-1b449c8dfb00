package cn.htd.s2bplus.nongzi.pojo.dto.yc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 20/9/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AutoTrophySellerDTO",description = "自营商家入驻请求对象")
public class AutoTrophySellerDTO implements Serializable {
    private static final long serialVersionUID = 4855141015228247948L;

    @ApiModelProperty(value = "公司名称",required = true,hidden = true)
    private String companyName;

    @ApiModelProperty(
            value = "公司电话 区号",required = false,hidden = true
    )
    private String areaCode;

    @ApiModelProperty(value = "会员ID",hidden = true)
    private Long memberId;

    @ApiModelProperty(value = "公司电话 座机号",required = false,hidden = true)
    private String landline;

    @ApiModelProperty(value = "公司地址",required = true,hidden = true)
    private String locationAddr;

    @ApiModelProperty(value = "详细地址",required = true,hidden = true)
    private String locationDetail;

    @ApiModelProperty(value = "省",required = true,hidden = true)
    private String locationProvince;

    @ApiModelProperty(value = "市",required = true,hidden = true)
    private String locationCity;

    @ApiModelProperty(value = "区",required = true,hidden = true)
    private String locationCounty;

    @ApiModelProperty(value = "镇",required = true,hidden = true)
    private String locationTown;

    @ApiModelProperty(
            value = "实际经营地址-省",hidden = true
    )
    private String actualBusinessProvince;
    @ApiModelProperty(
            value = "实际经营地址-市",hidden = true
    )
    private String actualBusinessCity;
    @ApiModelProperty(
            value = "实际经营地址-区",hidden = true
    )
    private String actualBusinessCounty;
    @ApiModelProperty(
            value = "实际经营地址-镇",hidden = true
    )
    private String actualBusinessTown;
    @ApiModelProperty(
            value = "实际经营地址-详细地址",hidden = true
    )
    private String actualBusinessDetail;
    @ApiModelProperty(
            value = "实际经营地址-省市区镇详细地址",hidden = true
    )
    private String actualBusinessAddress;

    @ApiModelProperty(value = "是否三证合一 1：是，2：否",required = true,hidden = true)
    private String certificateType;

    @ApiModelProperty(value = "统一社会信用代码",required = true,hidden = true)
    private String unifiedSocialCreditCode;

    @ApiModelProperty(value = "税务人识别号",required = true,hidden = true)
    private String taxManId;

    @ApiModelProperty(value = "纳税人类型 1：一般纳税人，2：小规模纳税人，3：非增值税纳税人",required = true,hidden = true)
    private String taxpayerType;

    @ApiModelProperty(value = "经营范围",required = true,hidden = true)
    private String businessScope;

    @ApiModelProperty(value = "营业执照副本",required = true,hidden = true)
    private String businessLicensePicSrc;

    @ApiModelProperty(value = "纳税人资格证",required = true,hidden = true)
    private String taxpayerCertificatePicSrc;

    @ApiModelProperty(value = "法人姓名",required = true,hidden = true)
    private String artificialPersonName;

    @ApiModelProperty(value = "法人身份证号",required = true,hidden = true)
    private String artificialPersonIdcard;

    @ApiModelProperty(value = "法人手机号",required = true,hidden = true)
    private String artificialPersonMobile;

    @ApiModelProperty(value = "法人身份证人头像",required = true,hidden = true)
    private String artificialPersonPicSrc;

    @ApiModelProperty(value = "法人身份证国徽面",required = true,hidden = true)
    private String artificialPersonPicBackSrc;

    @ApiModelProperty(value = "公司类型 1：品牌商，2：经销商",required = true)
    private String companyType;

    @ApiModelProperty(value = "官网地址",required = false)
    private String homePage;

    @ApiModelProperty(value = "最近一年销售额",required = false)
    private String salesVolumn;

    @ApiModelProperty(value = "同类电商网站经验",required = false)
    private String isHaveEbusiness;

    @ApiModelProperty(value = "网站运营人数",required = false)
    private String websiteOpertionNumber;

    @ApiModelProperty(value = "ERP类型 0：无,1：自有ERP,2：第三方ERP代运营",required = false)
    private String hasErp;

    @NotBlank(message = "经营人不能为空")
    @ApiModelProperty(value = "经营人",required = true)
    private String businessPersonName;

    @NotBlank(message = "经营人电话不能为空")
    @ApiModelProperty(value = "经营人电话",required = true)
    private String businessPersonMobile;

    @ApiModelProperty(value = "商家类型",required = true)
    private String sellerType;

    @ApiModelProperty(value = "营业执照注册号",required = true)
    private String businessLicenseId;

    @ApiModelProperty(
            value = "组织机构代码",required = true
    )
    private String organizationPicSrc;
    @ApiModelProperty(
            value = "税务登记证",required = true
    )
    private String taxRegistrationCertificatePicSrc;

    @ApiModelProperty(
            value = "创建人id"
    )
    private Long createId;
    @ApiModelProperty(
            value = "创建人名称"
    )
    private String createName;

    @ApiModelProperty(
            value = "商家来源（1直接入驻，2会员店升级，3自营）"
    )
    private Integer sellerFrom;

    @ApiModelProperty(
            value = "会员Code"
    )
    private String memberCode;

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("AutoTrophySellerDTO{");
        sb.append("companyName='").append(companyName).append('\'');
        sb.append(", areaCode='").append(areaCode).append('\'');
        sb.append(", memberId=").append(memberId);
        sb.append(", landline='").append(landline).append('\'');
        sb.append(", locationAddr='").append(locationAddr).append('\'');
        sb.append(", locationDetail='").append(locationDetail).append('\'');
        sb.append(", locationProvince='").append(locationProvince).append('\'');
        sb.append(", locationCity='").append(locationCity).append('\'');
        sb.append(", locationCounty='").append(locationCounty).append('\'');
        sb.append(", locationTown='").append(locationTown).append('\'');
        sb.append(", actualBusinessProvince='").append(actualBusinessProvince).append('\'');
        sb.append(", actualBusinessCity='").append(actualBusinessCity).append('\'');
        sb.append(", actualBusinessCounty='").append(actualBusinessCounty).append('\'');
        sb.append(", actualBusinessTown='").append(actualBusinessTown).append('\'');
        sb.append(", actualBusinessDetail='").append(actualBusinessDetail).append('\'');
        sb.append(", actualBusinessAddress='").append(actualBusinessAddress).append('\'');
        sb.append(", certificateType='").append(certificateType).append('\'');
        sb.append(", unifiedSocialCreditCode='").append(unifiedSocialCreditCode).append('\'');
        sb.append(", taxManId='").append(taxManId).append('\'');
        sb.append(", taxpayerType='").append(taxpayerType).append('\'');
        sb.append(", businessScope='").append(businessScope).append('\'');
        sb.append(", businessLicensePicSrc='").append(businessLicensePicSrc).append('\'');
        sb.append(", taxpayerCertificatePicSrc='").append(taxpayerCertificatePicSrc).append('\'');
        sb.append(", artificialPersonName='").append(artificialPersonName).append('\'');
        sb.append(", artificialPersonIdcard='").append(artificialPersonIdcard).append('\'');
        sb.append(", artificialPersonMobile='").append(artificialPersonMobile).append('\'');
        sb.append(", artificialPersonPicSrc='").append(artificialPersonPicSrc).append('\'');
        sb.append(", artificialPersonPicBackSrc='").append(artificialPersonPicBackSrc).append('\'');
        sb.append(", companyType='").append(companyType).append('\'');
        sb.append(", homePage='").append(homePage).append('\'');
        sb.append(", salesVolumn='").append(salesVolumn).append('\'');
        sb.append(", isHaveEbusiness='").append(isHaveEbusiness).append('\'');
        sb.append(", websiteOpertionNumber='").append(websiteOpertionNumber).append('\'');
        sb.append(", hasErp='").append(hasErp).append('\'');
        sb.append(", businessPersonName='").append(businessPersonName).append('\'');
        sb.append(", businessPersonMobile='").append(businessPersonMobile).append('\'');
        sb.append(", sellerType='").append(sellerType).append('\'');
        sb.append(", businessLicenseId='").append(businessLicenseId).append('\'');
        sb.append(", organizationPicSrc='").append(organizationPicSrc).append('\'');
        sb.append(", taxRegistrationCertificatePicSrc='").append(taxRegistrationCertificatePicSrc).append('\'');
        sb.append(", createId=").append(createId);
        sb.append(", createName='").append(createName).append('\'');
        sb.append(", sellerFrom=").append(sellerFrom);
        sb.append('}');
        return sb.toString();
    }
}
