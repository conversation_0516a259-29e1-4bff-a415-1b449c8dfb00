package cn.htd.s2bplus.nongzi.pojo.dto.guestorder;

/**
 * <AUTHOR>
 * @desc ApiResultEnum
 * @data 2021/1/15
 */
public enum ApiResultEnum {

    /**
     * 0-成功代码
     */
    SUCCESS(0, "成功"),
    ERROR(500, "error"),
    LOGIN_FAIL(1001,"登录失败"),
    SKU_NOT_EXITS(1101,"未查询到SKU"),
    ORDER_NOT_EXITS(1201,"未查询到订单"),
    ORDER_TRIAL_ERROR(1202,"订单试算异常"),
    CHANNEL_NOT_SUPPORT_ERROR(1203, "终端来源未开放"),
    JSON_DATA_FORMAT_ERROR(4001,"JSON数据格式错误"),
    CHECK_FLOOR_PRICE(4002,"分销限价查询异常"),
    CHECK_FLOOR_PRICE_RESULT(4003,"商品价格必须大于分销限价"),
    BATCH_ORDER_CHECK_FLOOR_PRICE_RESULT(4004,"商品价格必须大于等于分销限价"),
    IMPORT_ITEM_REPEAT(4005,"存在相同的商品，请检查"),
    IMPORT_ITEMCODE_NULL(4006,"编码不能为空"),
    PURCHASE_COUNT_ITEMCODE_ERROR(4007,"限购数量只可填写1-999的整数"),
    IMPORT_ERROR_LINE_COLUMN(4008,"第%s行，第%s列,"),
    IMPORT_MAX_ERROR(4009,"批量导入数量不能大于"),
    IMPORT_NULL(4010,"批量导入模板不能为空"),
    IMPORT_FILE_ERROR(4010,"导入的文件格式有误,非Excel!"),
    IMPORT_ITEM_ERROR(4011,",商品不在店铺内，请检查"),
    IMPORT_MEMBER_REPEAT(4012,"会员重复，请检查"),
    IMPORT_MEMBER_ERROR(4013,"会员不存在"),
    GET_MEMBER_GROUP_ERROR(4014,"查询会员分组详情异常"),
    GET_MEMBER_GROUP_NULL(4015,"会员分组信息不存在"),
    SUPPORT_THE_SPECIFIED_TYPE_ERROR(4016,"会员分组导出只支持指定类型"),
    GET_MEMBER_GROUP_MEMBER_ERROR(4017,"查询分组会员信息失败"),
    GET_MEMBER_GROUP_MEMBER_NULL(4017,"查询分组会员信息为空，无需导出"),
    EXPORT_CURRENT_PLAYING_SONG(4018,"正在导出，请在5分分钟后点击导出列表查看"),
    UPLOAD_OSS_ERROR(4019,"获取上传文件地址出错！"),
    REPORT_HISTORY_ERROR(4020,"保存报表生成下载历史异常！"),
    BATCH_QUERY_ITEM_INFO_ERROR(4021,"批量查询商品/sku信息失败！"),
    BATCH_QUERY_MEMBER_INFO_ERROR(4022,"批量查询会员信息失败！"),
    EMPLOYEE_ID_NULL(4023,"员工账号不能为空"),
    EMPLOYEE_NAME_NULL(4024,"员工名称不能为空"),
    EMPLOYEE_NAME_REPEAT(4025,"员工账号重复，请检查"),
    GET_BALANCE_INFORMATION_ERROR(4026,"导出结余信息失败"),
    GET_BALANCE_INFORMATION_NULL(4027,"导出结余信息为空，无需导出"),
    IMPORT_CONTENT_NULL(4028,"存在为空信息"),
    IMPORT_WAIT_REMAIN_AMOUNT_ERROR(4029,"待发券面额必须为正数，最多支持2位小数"),
    IMPORT_SINGLE_ORDER_LIMIT_ERROR(4030,"单笔订单用券比例必须为0~100的正数，最多支持2位小数"),
    QUERY_LATEST_REMAINAMOUNT_ERROR(4031,"批量查询结余信息失败"),
    IMPORT_MAX_NUM_ERROR(4032,"导入模板数量不能超过"),
    IMPORT_SKU_CODE_NULL(4033,"sku编码不能为空"),
    IMPORT_SHOP_ID_NULL(4034,"店铺编码不能为空"),
    IMPORT_OPERATION_NULL(4035,"上下架操作不能为空"),
    IMPORT_OPERATION_ERROR(4036,"上下架操作状态错误"),
    IMPORT_SKU_CODE_ERROR(4037,"sku编码数据类型错误"),
    IMPORT_SHOP_ID_ERROR(4038,"店铺编码数据类型错误"),

    IMPORT_ITEM_CODE_NULL(4039,"商品编码不能为空"),
    IMPORT_DELISTING_TYPE_NULL(4040,"下架原因不能为空"),
    IMPORT_DELISTING_TYPE_ERROR(4041,"下架原因选择错误"),

    IMPORT_DELISTING_REMARK_ERROR(4042,"备注长度不能超过50个字符"),
    MEMBER_CODE_ERROR(4043,"会员编码不能为空"),
    SELLER_CODE_ERROR(4044,"商家编码不能为空"),
    QUOTA_ERROR(4045,"配置的额度不能小于等于渠道限额"),
    QUOTA_IS_NULL(4046,"输入渠道额度数据有误"),

    IMPORT_ITEM_NAME_NULL(4047,"商品名称不能为空"),
    IMPORT_UNIT_NULL(4048,"单位不能为空"),
    IMPORT_COST_NULL(4049,"成本价不能为空"),
    IMPORT_RETAIL_PRICE_NULL(4050,"销售价不能为空"),
    UNIT_FORMAT_ERROR(4051,"单位请输入中文文本"),
    EAN_CODE_FORMAT_ERROR(4052,"条形码请输入纯数字"),
    IMPORT_ITEM_NAME_EXISTS(4053,"商品已存在，商品编码为"),
    IMPORT_COST_BIGGER_ZERO(4054,"成本价需为大于0的正数"),
    IMPORT_ITEM_NAME_REPEAT(4055,"导入的商品名称存在重复"),
    IMPORT_COST_ERROR(4056,"成本价不超过两位小数"),
    IMPORT_RETAIL_PRICE_ERROR(4057,"销售价不超过两位小数"),
    IMPORT_RETAIL_PRICE_BIGGER_ZERO(4058,"销售价需为大于0的正数"),

    IMPORT_FOUR_MANAGER_ID_NOT_NULL(4059,"四级管理类目ID不能为空"),
    IMPORT_FOUR_MANAGER_ID_NOT_VALID(4060,"四级管理类目ID不存在或无效"),
    IMPORT_THIRD_SALE_CATEGORY_ID_NOT_NULL(4061,"三级销售类目ID不能为空"),
    IMPORT_THIRD_SALE_CATEGORY_ID_NOT_VALID(4062,"三级销售类目ID不存在"),
    IMPORT_BRAND_ID_NOT_NULL(4063,"品牌ID不能为空"),
    IMPORT_BRAND_NOT_BOUND_CATEGORY_ID(4064,"品牌ID与四级管理类目ID未绑定"),
    IMPORT_BRAND_ID_NOT_VALID(4065,"品牌ID不存在"),
    IMPORT_MODEL_TYPE_NOT_NULL(4066,"型号不能为空"),
    IMPORT_UNIT_NOT_NULL(4067,"单位不能为空"),
    IMPORT_ATTR_NAME_OR_VALUE_NOT_NULL(4068,"规格名称1或规格值1不能为空"),
    IMPORT_INVENTORY_NOT_NULL(4069,"库存数量不能为空"),
    IMPORT_INVENTORY_BIGGER_ZERO(4070,"库存数量需要大于0"),
    IMPORT_ITEM_PIC_FILE_NOT_NULL(4071,"商品图片文件夹或商品图片名称不能为空"),
    IMPORT_ITEM_DESCRIBE_NOT_NULL(4072,"商品详情不能为空"),
    IMPORT_SHOP_NOT_NULL(4073,"发布店铺名称不能为空"),
    IMPORT_SHOP_NOT_EXIST(4074,"发布店铺名称不存在"),
    IMPORT_RETAIL_PRICE_NOT_NULL(4075,"基础售价不能为空"),
    IMPORT_ITEM_NAME_NOT_NULL(4076,"商品名称不能为空"),
    IMPORT_SAME_ITEM_ATTR_LAGER_THAN_LIMIT(4077,"商品规格数量已超出最大限制，请重新导入"),
    IMPORT_ATTR_NAME_1_AND_2_REPEAT(4078,"规格名称1与规格名称2不能重复，请重新导入"),
    IMPORT_ATTR_NAME_1_AND_3_REPEAT(4079,"规格名称1与规格名称3不能重复，请重新导入"),
    IMPORT_ATTR_NAME_2_AND_3_REPEAT(4080,"规格名称2与规格名称3不能重复，请重新导入"),
    IMPORT_ATTR_NAME_1_NOT_CONSISTENT(4081,"同一个商品名称的规格名称1必须相同，请重新导入"),
    IMPORT_ATTR_NAME_2_NOT_CONSISTENT(4082,"同一个商品名称的规格名称2必须相同，请重新导入"),
    IMPORT_ATTR_NAME_3_NOT_CONSISTENT(4083,"同一个商品名称的规格名称3必须相同，请重新导入"),
    IMPORT_SAME_ATTR_VALUE_1_NO_MORE_THAN_3(4084,"同一个商品名称的规格值1不能超过3种"),
    IMPORT_SAME_ATTR_VALUE_2_NO_MORE_THAN_3(4085,"同一个商品名称的规格值2不能超过3种"),
    IMPORT_SAME_ATTR_VALUE_3_NO_MORE_THAN_3(4086,"同一个商品名称的规格值3不能超过3种"),
    IMPORT_UPDATE_ATTR_FAIL(4087,"更新规格失败"),
    IMPORT_SHOP_NAME_NOT_CONSISTENT(4088,"同一个商品名称的发布店铺必须相同，请重新导入"),
    IMPORT_FOURTH_CID_NOT_CONSISTENT(4089,"同一个商品名称的四级管理类目ID必须相同，请重新导入"),
    IMPORT_SALE_CID_NOT_CONSISTENT(4090,"同一个商品名称的三级销售类目ID必须相同，请重新导入"),
    IMPORT_BRAND_ID_NOT_CONSISTENT(4091,"同一个商品名称的品牌ID必须相同，请重新导入"),
    IMPORT_UNIT_NOT_CONSISTENT(4092,"同一个商品名称的单位必须相同，请重新导入"),
    IMPORT_MODEL_TYPE_NOT_CONSISTENT(4093,"同一个商品名称的型号必须相同，请重新导入"),
    IMPORT_ITEM_DESCRIBE_NOT_CONSISTENT(4094,"同一个商品名称的商品详情必须相同，请重新导入"),
    IMPORT_ATTR_VALUE_EXIST_REPEAT(4095,"规格值1存在重复数据，请重新导入"),
    IMPORT_ATTR_VALUE_2_LARGE_THAN_LIMIT(4096,"规格值2数量超过限制，请重新导入"),
    IMPORT_ATTR_VALUE_3_LARGE_THAN_LIMIT(4097,"规格值3数量超过限制，请重新导入"),
    IMPORT_ITEM_PIC_NOT_MORE_THAN_6(4098,"商品图片最多6张，请重新导入"),
    IMPORT_ATTR_NAME_2_NOT_NULL(4099,"规格名称2不能为空"),
    IMPORT_ATTR_VALUE_2_NOT_NULL(4101,"规格值2不能为空"),
    IMPORT_ATTR_NAME_3_NOT_NULL(4102,"规格名3不能为空"),
    IMPORT_ATTR_VALUE_3_NOT_NULL(4103,"规格值3不能为空"),
    IMPORT_ITEM_DESCRIBE_PIC_NOT_CONSISTENT(4104,"同一个商品名称的商品详情图片必须相同，请重新导入"),
    IMPORT_ITEM_FILE_NOT_CONSISTENT(4105,"同一个商品名称的商品图片文件夹必须相同，请重新导入"),
    IMPORT_ITEM_PIC_NAME_NOT_CONSISTENT(4106,"同一个商品名称的商品图片名称必须相同，请重新导入"),
    IMPORT_ITEM_PIC_FILE_NOT_EXIST(4107,"未找到商品图片文件夹，请核实"),
    IMPORT_ITEM_PIC_NOT_EXIST(4108,"文件夹中的商品图片未找到，请核实"),
    IMPORT_RETAIL_PRICE_MAX_4_DECIMAL(4109,"销售价最多保留4位小数"),
    IMPORT_INVENTORY_NOT_EXCEEDED_999999999(4110,"库存数量最多支持999999999"),
    IMPORT_INVENTORY_NOT_DECIMAL(4111,"库存数量不支持小数"),
    IMPORT_ITEM_DESCRIBE_NOT_EXCEEDED_500(4112,"商品详情最多支持500字"),
    IMPORT_IMPORT_ATTR_EXCEEDED_3(4113,"四级管理类目下现有规格加本次导入规格超出了数量限制，请重新导入"),
    IMPORT_QUERY_ATTR_LIST_ERROR(4114,"查询类目规格失败"),
    IMPORT_UNIT_NOT_EXIST(4115,"单位不存在，请重新导入"),
    IMPORT_ATTR_NAME_1_TOO_LONG(4116,"规格名称1不能超过8位"),
    IMPORT_ATTR_VALUE_1_TOO_LONG(4117,"规格值1不能超过15位"),
    IMPORT_ATTR_NAME_2_TOO_LONG(4118,"规格名称2不能超过8位"),
    IMPORT_ATTR_VALUE_2_TOO_LONG(4119,"规格值2不能超过15位"),
    IMPORT_ATTR_NAME_3_TOO_LONG(4120,"规格名称3不能超过8位"),
    IMPORT_ATTR_VALUE_3_TOO_LONG(4121,"规格值3不能超过15位"),
    IMPORT_MODEL_TYPE_TOO_LONG(4122,"型号不能超过32位"),
    IMPORT_ITEM_NAME_TOO_LONG(4123,"商品名称不能超过100位"),
    IMPORT_EAN_CODE_TOO_LONG(4124,"条形码不能超过20位"),
    IMPORT_EAN_CODE_MUST_PURE_DIGITAL(4125,"条形码只支持纯数字"),
    IMPORT_EAN_CODE_NOT_DECIMAL(4126,"条形码不支持小数"),
    IMPORT_SKU_PIC_TOO_MUCH(4127,"每个规格图片最多一张图片"),
    IMPORT_SKU_PIC_NOT_COMPLETE(4128,"文件夹中的规格图片未找到，请核实"),
    IMPORT_ITEM_DESC_PIC_NOT_EXIST(4129,"文件夹中的详情图片未找到，请核实"),
    ;
    private Integer code;
    private String msg;

    ApiResultEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
