package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title BatchUpdateServiceShipDTO
 * @Date: 2025/4/14 15:59
 */
@Data
public class BatchUpdateServiceShipDTO implements Serializable {
    private static final long serialVersionUID = 3988136558980661598L;

    @ApiModelProperty(value = "服务商编码")
    private String serviceProviderCode;

    @ApiModelProperty(value = "代收客户编码")
    private String buyerCode;

    @ApiModelProperty(value = "代收客户代收客户Apple ID")
    private String appleId;

    @ApiModelProperty(value = "创建人ID")
    private Long createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Long modifyId;

    @ApiModelProperty(value = "更新人名称")
    private String modifyName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
