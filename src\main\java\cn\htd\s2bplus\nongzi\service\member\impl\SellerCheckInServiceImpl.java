package cn.htd.s2bplus.nongzi.service.member.impl;

import cn.htd.rdc.base.development.framework.core.exception.BusinessException;
import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.common.util.CommonResultUtil;
import cn.htd.s2bplus.nongzi.enums.BusinessExceptionEnum;
import cn.htd.s2bplus.nongzi.feign.goods.GoodsFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.ExternalChannelManagementDTO;
import cn.htd.s2bplus.nongzi.pojo.dto.user.AuthChannelDTO;
import cn.htd.s2bplus.nongzi.service.member.SellerCheckInService;
import cn.htd.s2bplus.nongzi.utils.BaseContextHandler;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;


@Service("sellerCheckInService")
@Slf4j
@RefreshScope
public class SellerCheckInServiceImpl implements SellerCheckInService {


    @Autowired
    private GoodsFeignService goodsFeignService;

    @Override
    public Result<List<AuthChannelDTO>> queryAuthChannelList() {
        List<AuthChannelDTO> authChannelDTOList= new ArrayList<>();
        String loginId = BaseContextHandler.getLoginId();
        ExternalChannelManagementDTO externalChannelManagementDTO = new ExternalChannelManagementDTO();
        externalChannelManagementDTO.setLoginId(loginId);
        log.info("查询授权渠道列表,req:{}",externalChannelManagementDTO);
        Result<List<ExternalChannelManagementDTO>> result = goodsFeignService.selectAllChannel(externalChannelManagementDTO);
        log.info("查询授权渠道列表,resp:{}",result);
        if (!result.isSuccess()) {
            throw new BusinessException(BusinessExceptionEnum.AUTH_CHANNEL_QUERY_ERROR);
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            return CommonResultUtil.success(authChannelDTOList);
        }
        for (ExternalChannelManagementDTO channelManagementDTO : result.getData()){
            AuthChannelDTO authChannelDTO = new AuthChannelDTO();
            authChannelDTO.setAppId(channelManagementDTO.getAppId());
            authChannelDTO.setChannelCode(channelManagementDTO.getChannelCode());
            authChannelDTO.setExternalChannelName(channelManagementDTO.getExternalChannelName());
            authChannelDTO.setAppType(channelManagementDTO.getAppType());
            authChannelDTOList.add(authChannelDTO);
        }
        log.info("查询授权渠道列表,authChannelDTOList:{}",JSON.toJSONString(authChannelDTOList));
        return CommonResultUtil.success(authChannelDTOList);
    }

}
