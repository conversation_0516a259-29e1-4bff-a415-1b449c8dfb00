package cn.htd.s2bplus.nongzi.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title ShopAuthSubAccountVO
 * @description 子账号店铺关联表
 * @Date: 2022/5/9 17:20
 */
@Data
public class ShopAuthSubAccountVO implements Serializable {
    private static final long serialVersionUID = 7649601318901135850L;

    @ApiModelProperty(value = "登录ID")
    private String loginId;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "是否分配 0-未分配 1-已分配")
    private Integer allocateFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
