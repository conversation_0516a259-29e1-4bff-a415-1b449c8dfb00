package cn.htd.s2bplus.nongzi.service.search;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.common.BaseDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @author: wangxuan
 * @description: 搜索分词库配置服务
 * @date: 2023/2/22 15:15
 */
public interface SearchParticipleService {

    Result<Boolean> importBatchParticiple(MultipartFile file,
                                          BaseDTO baseDto,
                                          HttpServletResponse response) throws Exception;

}
