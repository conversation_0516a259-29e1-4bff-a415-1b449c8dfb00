package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchOutWarehouseApiDTO implements Serializable {



	@ApiModelProperty(name = "品牌方编码")
	@NotBlank(message ="品牌方编码 不可为空")
	private String sellerCode;


	@ApiModelProperty(name = "批量出库信息")
	private List<BatchOutWarehouseApiEntity> outInfo;

	@ApiModelProperty(name = "操作类型 1：出库，2：入库，3=订单扣减，4=退货入库")
	@NotNull(message = "操作类型 不可为空")
	private int businessType;

	private Long modifyId;

	private String modifyName;

}
