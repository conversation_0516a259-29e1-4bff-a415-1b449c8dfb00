package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class EditItemAttributeValueDTO implements Serializable {
    private static final long serialVersionUID = -2475781671395275375L;

    @ApiModelProperty(value = "属性值ID")
    private Long valueId;

    @ApiModelProperty(value = "属性ID")
    private Long attrId;

    @ApiModelProperty(value = "值名称")
    private String valueName;

    @ApiModelProperty(value = "属性名称")
    private String attrName;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
