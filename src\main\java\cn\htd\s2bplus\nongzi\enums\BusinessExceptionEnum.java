package cn.htd.s2bplus.nongzi.enums;

import cn.htd.rdc.base.development.framework.core.enums.IBaseEnums;

public enum BusinessExceptionEnum implements IBaseEnums {

    AUTH_CHANNEL_QUERY_ERROR(306490,"查询渠道信息失败!"),
    ;

    private Integer code;
    private String msg;

    BusinessExceptionEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return msg;
    }

    public String getMsg() {
        return msg;
    }
}
