package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import cn.htd.s2bplus.common.util.MyJsonStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

@Data
public class EidtItemAttributeDTO implements Serializable {

    private static final long serialVersionUID = -3655847602085654831L;

    @ApiModelProperty(value = "属性ID")
    private Long attrId;

    @ApiModelProperty(value = "属性名称")
    private String attrName;

    @ApiModelProperty(value = "属性值列表")
    private List<EditItemAttributeValueDTO> itemAttributeValueList;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
