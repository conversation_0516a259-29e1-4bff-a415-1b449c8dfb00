package cn.htd.s2bplus.nongzi.pojo.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.htd.s2bplus.common.util.MyJsonStyle;
import cn.htd.s2bplus.nongzi.pojo.dto.goods.BatchAddServiceAddressDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class ExportBatchAddServiceAddressResp implements Serializable {
    @ApiModelProperty(value = "服务商编码")
    @Excel(name = "服务商编码", width = 15,orderNum = "1")
    private String serviceProviderCode;

    @ApiModelProperty(value = "服务商名称")
    @Excel(name = "服务商名称", width = 20,orderNum = "2")
    private String serviceProviderName;

    @ApiModelProperty(value = "服务商会员id")
    @Excel(name = "服务商会员id", width = 15,orderNum = "3")
    private Long serviceProviderMemberId;

    @ApiModelProperty(value = "代收客户编码")
    @Excel(name = "代收客户编码", width = 15,orderNum = "4")
    private String buyerCode;

    @ApiModelProperty(value = "代收客户名称")
    @Excel(name = "代收客户名称", width = 20,orderNum = "5")
    private String buyerName;

    @ApiModelProperty(value = "代收客户Apple Id")
    @Excel(name = "代收客户Apple Id", width = 20,orderNum = "6")
    private String appleId;

    @ApiModelProperty(value = "收货人姓名")
    @Excel(name = "收货人姓名", width = 10,orderNum = "7")
    private String receiverName;

    @ApiModelProperty(value = "收货人电话")
    @Excel(name = "收货人电话", width = 15,orderNum = "8")
    private String receiverPhone;

    @ApiModelProperty(value = "省")
    @Excel(name = "省", width = 5,orderNum = "9")
    private String provinceName;

    @ApiModelProperty(value = "市")
    @Excel(name = "市", width = 10,orderNum = "10")
    private String cityName;

    @Excel(name = "区/县/市",width = 10, orderNum = "11")
    @ApiModelProperty(value = "区/县/市")
    private String areaName;

    @Excel(name = "镇/街道/乡",width = 10, orderNum = "12")
    @ApiModelProperty(value = "镇/街道/乡")
    private String townName;

    @Excel(name = "详细地址",width = 30, orderNum = "13")
    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "商家编码")
    @Excel(name = "商家编码", width = 15,orderNum = "14")
    private String sellerCode;

    @ApiModelProperty(value = "导入结果")
    @Excel(name = "导入结果", width = 5,orderNum = "15")
    private String importResult;

    @ApiModelProperty(value = "失败原因")
    @Excel(name = "失败原因", width = 80,orderNum = "16")
    private String failedReason;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, MyJsonStyle.JSON_STYLE);
    }
}
