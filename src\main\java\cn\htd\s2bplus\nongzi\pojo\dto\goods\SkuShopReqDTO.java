package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SkuShopReqDTO implements Serializable {
    private static final long serialVersionUID = -1454895995176101748L;


    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "上下架操作 1：上架 0下架")
    private Integer isVisable;

    @ApiModelProperty(value = "卖家id")
    private Long sellerId;

    @ApiModelProperty(value = "操作人id")
    private Long modifyId;

    @ApiModelProperty(value = "操作人名称")
    private String modifyName;
}
