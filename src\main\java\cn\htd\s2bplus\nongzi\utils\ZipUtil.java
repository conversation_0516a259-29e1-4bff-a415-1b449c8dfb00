package cn.htd.s2bplus.nongzi.utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Enumeration;

@Slf4j
public class ZipUtil {

    public static void unzip(String zipFilePath, String destDir) throws IOException {
        Path destinationPath = Paths.get(destDir);
        Files.createDirectories(destinationPath);

        // 使用 Paths 处理可能包含非 ASCII 字符的路径
        Path zipPath = Paths.get(zipFilePath);

        try (ZipFile zipFile = new ZipFile(zipPath.toFile(), "UTF-8")) {
            Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();
            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();

                // 尝试使用UTF-8解码文件名，若失败则使用系统默认编码或其他编码（例如GBK）
                String fileName = null;
                try {
                    fileName = new String(entry.getRawName(), StandardCharsets.UTF_8);
                } catch (Exception e) {
                    fileName = new String(entry.getRawName()); // 使用平台默认编码
                }

                Path newPath = destinationPath.resolve(fileName);

                // 确保路径安全
                if (!newPath.normalize().startsWith(destinationPath.normalize())) {
                    throw new IOException("非法文件路径：" + newPath.toString());
                }

                if (entry.isDirectory()) {
                    Files.createDirectories(newPath);
                } else {
                    Files.createDirectories(newPath.getParent());
                    try (InputStream is = zipFile.getInputStream(entry);
                         OutputStream os = Files.newOutputStream(newPath)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = is.read(buffer)) > -1) {
                            os.write(buffer, 0, len);
                        }
                    }
                }
            }
        }

        deleteFile(zipFilePath);
    }

    public static void unzipAndFixEncoding(String zipPath, String destDir, String encoding) throws Exception {
        try (ZipFile zipFile = new ZipFile(new File(zipPath))) {
            Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();
            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();

                // 获取原始文件名的字节数组
                byte[] rawName = entry.getRawName();
                // 使用 GBK 解码获得正确文件名
                String decodedName = new String(rawName, Charset.forName(encoding));

                File targetFile = new File(destDir, decodedName);

                if (entry.isDirectory()) {
                    targetFile.mkdirs();
                } else {
                    targetFile.getParentFile().mkdirs();
                    try (InputStream is = zipFile.getInputStream(entry);
                         FileOutputStream fos = new FileOutputStream(targetFile)) {
                        byte[] buffer = new byte[4096];
                        int len;
                        while ((len = is.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
        }
    }

    public static void unzipWithCharsetGBK(String zipFilePath, String destDirPath) throws Exception {
        try (ZipFile zipFile = new ZipFile(zipFilePath, "GBK")) {
            Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();
            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();
                File entryDestination = new File(destDirPath, entry.getName());
                if (entry.isDirectory()) {
                    entryDestination.mkdirs();
                } else {
                    entryDestination.getParentFile().mkdirs();
                    try (InputStream in = zipFile.getInputStream(entry)) {
                        // Copy the content of the input stream to the destination file.
                        Files.copy(in, entryDestination.toPath());
                    }
                }
            }
        }

//        deleteFile(zipFilePath);
    }

    public static void unzipWithAutoDetectCharset(String zipFilePath, String destDirPath) throws Exception {
        String[] charsets = {"GBK", "UTF-8", "ISO-8859-1"};
        Exception lastException = null;

        for (String charset : charsets) {
            try (ZipFile zipFile = new ZipFile(zipFilePath, charset)) {
                Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();
                while (entries.hasMoreElements()) {
                    ZipArchiveEntry entry = entries.nextElement();
                    // 使用Paths处理路径，兼容性更好
                    Path entryPath = Paths.get(destDirPath, entry.getName());
                    if (entry.isDirectory()) {
                        Files.createDirectories(entryPath);
                    } else {
                        Files.createDirectories(entryPath.getParent());
                        try (InputStream in = zipFile.getInputStream(entry)) {
                            Files.copy(in, entryPath);
                        }
                    }
                }
                // 成功则删除原文件
                deleteFile(zipFilePath);
                return;
            } catch (Exception e) {
                lastException = e;
            }
        }
        throw new IOException("Failed to unzip with all charsets tried", lastException);
    }

    private static void deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            boolean deleted = file.delete();
            if (deleted) {
                log.info("压缩文件删除成功：" + filePath);
            } else {
                log.info("压缩文件删除失败：" + filePath);
            }
        } else {
            log.info("压缩文件不存在：" + filePath);
        }
    }
}
