package cn.htd.s2bplus.nongzi.pojo.dto.guestorder;

import java.math.BigDecimal;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/15
 * @Version 1.0
 */
public class Constant {

    /**
     * 租户ID
     */
    public static final Long TENEMENT_ID = 1L;

    /**
     * 阶段单模板ID
     */
    public static final Long PERFORM_PROCESS_TEMPLATE_ID = 1L;

    /**
     * 销售全国
     */
    public static final int SALES_WHOLE_COUNTRY = 1;

    /**
     * 江苏移动订单渠道-中台使用
     */
    public static final String ORDER_FROM = "20";

    /**
     * 云链渠道
     */
    public static final String ORDER_FROM_CLOUD_CHAIN = "21";

    /**
     * 云场PC渠道
     */
    public static final String ORDER_FROM_CLOUD_MARKET_PC = "22";

    /**
     * 商品上下架状态，1=上架
     */
    public static final String GOODS_STATUS_UP = "1";

    /**
     * 支付渠道
     */
    public static final String PAY_CHANNEL_TYPE = "1";

    /**
     * 是否需要发票，1=需要
     */
    public static final Integer NEED_INVOICE = 1;

    /**
     * 发票类型 1：普通发票
     */
    public static final String INVOICE_TYPE_COMMON = "1";

    /**
     * 2：增值税发票
     */
    public static final String INVOICE_TYPE_APPRECIATION = "2";

    /**
     * 会员注册来源，江苏移动
     */
    public static final String USER_SOURCE_CHANNEL = "JSYD";

    /**
     * 支付成功标识,SUCCESS
     */
    public static final String PAY_STATUS_SUCCESS = "SUCCESS";

    /**
     * 支付失败标识,FAIL
     */
    public static final String PAY_STATUS_FAIL = "FAIL";


    /**
     * PAY_CODE,SUCCESS
     */
    public static final String PAY_CODE = "bankPayOnline";


    public static final Long PLATFORM_USER_ID = -1L;

    public static final String PLATFORM_USER_NAME = "B2B交易引擎";

    /**
     * 买家已确认
     */
    public static final String CONFIRM_BUYER_ORDER_CODE = "602";

    /**
     * 卖家已确认
     */
    public static final String CONFIRM_SELLER_ORDER_CODE = "604";

    /**
     * 自提
     */
    public static final String DELIVER_TYPE_SELF_PICKUP = "2";

    /**
     * 物流配送
     */
    public static final String DELIVER_TYPE_LOGISTICS = "1";

    /**
     * 直拨仓类型
     */
    public static final int WAREHOUSE_DIRECT = 1;

    /**
     * 库存来源(B2B/S2B),ERP
     */
    public  static final String INVENTORY_SOURCE_S2B = "S2B";

    /**
     * 库存来源(B2B/S2B),ERP
     */
    public  static final String INVENTORY_SOURCE_ERP = "ERP";

    /**
     * 专项仓
     */
    public static final String WAREHOUSE_TYPE_SPECIAL = "2";

    /**
     * 0实际库存 1共享库存 2专项库存
     */
    public static final String WAREHOUSE_TYPE_SHARE = "1";


    /**
     * 普通专项
     */
    public static final String WAREHOUSE_TYPE_SPECIAL_NORMAL = "1";

    /**
     * 云链专项
     */
    public static final String WAREHOUSE_TYPE_SPECIAL_30 = "30";

    /**
     * 要货单加库存
     */
    public static final Integer REQUEST_ORDER_BUY_COUNT = 0;

    /**
     * 仓库，操作类型,1加库存
     */
    public static final int SPECIAL_INVENTORY_TYPE_ADD = 1;

    /**
     * 仓库，操作类型,扣减库存，-1减库存
     */
    public static final int SPECIAL_INVENTORY_TYPE_DEDUCTION = -1;

    /**
     * 云链订单
     */
    public static final String CLOUD_ORDER_TYPE = "4";

    /**
     * 云原生普通订单
     */
    public static final String CLOUD_NORMAL_ORDER_TYPE = "1";

    /**
     * 3 活动订单
     */
    public static final String CLOUD_PROMOTION_ORDER_TYPE = "3";

    /**
     * 普通订单
     */
    public static final String CLOUD_ORDER_TYPE_NORMAL = "1";
    /**
     * 返利订单
     */
    public static final String REBATE_ORDER = "5";
    /**
     * 总部促销资源订单
     */
    public static final String HTD_PROMOTION_ORDER = "6";
    /**
     * 混合使用返利和总部促销资源订单
     */
    public static final String BOTH_REBATE_AND_HTD_PROMOTION_ORDER = "7";

    /**
     * 支付类型-ERP账存支付
     */
    public static final String TRADE_TYPE_ERP_PAY = "ERP_PAY";

    /**
     * 支付类型-大额转账
     */
    public static final String TRADE_TYPE_BANK_PAY = "BANK_PAY";

    /**
     * 支付类型-网商余额支付
     */
    public static final String TRADE_TYPE_MIX_BALANCE_PAY = "MIX_BALANCE_PAY";

    public static final String CALLER ="云场交易商城";

    public static final String METHOD_TYPE = "POST";

    public static final String TRADE_ORDER_REASON = "交易单创建失败，订单回滚";

    public static final String AUDIT_ORDER_REASON = "批量审核订单失败，订单回滚";

    /**
     * 商家类型是为外部供应商
     */
    public static final String MERCHANT_TYPE_EXTERNAL_SUPPLIER = "2";

    /**
     * 会员/商家类型 1：会员，2：商家
     */
    public static final String BUYER_SELLER_TYPE = "2";


    /**
     * 渠道编码: 10 内部供应商 20 外部供应商
     */
    public static final String PRODUCT_CHANNEL_CODE_INSIDE = "10";
    /**
     * 渠道编码: 10 内部供应商 20 外部供应商
     */
    public static final String PRODUCT_CHANNEL_CODE_OUTSIDE = "20";

    /**
     *  清分类型为MANUAL:手动
     */
    public static final String DISTRIBUTION_TYPE_IS_MANUAL = "MANUAL";

    /**
     *  清分类型为AUTO:自动
     */
    public static final String DISTRIBUTION_TYPE_IS_AUTO = "AUTO";

    /**
     *  不创建交易流水createSerialNo
     */
    public static final String NOT_CREATE_SERIALNO = "0";

    /**
     *  返回判断金额
     */
    public static final int RETURN_JUDGMENT_AMOUNT = 0;

    /**
     * 议价提醒短信
     */
    public static final String PRICE_NEAOTIATION_REMINDER = "尊敬的汇通达商家，您有一笔订单客户发起议价申请，请及时登录商家中心进行处理，订单编号：";

    /**
     * 分润角色，卖家不需要分润
     */
    public static final String ROLE_SUPPLYER = "ROLE_SUPPLYER";

    /**
     * 限时购类型，启用
     */
    public static final String PROMOTION_TYPE_CLOUD_LIMITED_DISCOUNT = "42";

    /**
     * 1:优惠券
     */
    public static final String PROMOTION_TYPE_COUPON = "1";

    /**
     * 2:秒杀
     */
    public static final String PROMOTION_TYPE_PROMOTION = "2";

    /**
     * 秒杀订单
     */
    public static final int ORDER_LIMIT_TYPE = 1;

    /**
     * 1 普通类型库存专项
     */
    public static final String SPECIAL_CATEGORY_TYPE_ONE = "1";

    /**
     * 2 活动专项库存
     */
    public static final String SPECIAL_CATEGORY_TYPE_TWO = "2";

    /**
     * 去支付 PC同步跳转
     */
    public static final int PC_CHANNEL = 0 ;
    /**
     * 去支付 APP同步跳转
     */
    public static final int APP_CHANNEL = 1 ;
    /**
     * 去支付 H5同步跳转
     */
    public static final int H5_CHANNEL = 2 ;
    /**
     * 去支付 小程序同步跳转
     */
    public static final int MINI_PROGRAM_CHANNEL = 3 ;
    /**
     * 去支付 公众号同步跳转
     */
    public static final int PUBLIC_CHANNEL = 4 ;
    /**
     * 去支付 其他同步跳转
     */
    public static final int OTHER_CHANNEL = 5 ;

    /**
     * 手机号正则校验
     */
    public static final String TEL_NUM = "^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\\d{8})$";

    /**
     *
     */
    public static final int DOCUMENT_NO_SET = 0;
    /**
     *
     */
    public static final int DOCUMENT_SET = 1;

    /*
    * 代客下单订单来源
    */
    public static final String VALET_ORDER_FROM = "2";

    /*
     * 代客下单店铺渠道类型
     */
    public static final String VALET_CHANNEL_TYPE = "0";

    /*
     * 撮合订单服务订单已支付
     */
    public static final String MATCH_SERVICE_PAID = "803";

    /**
     * 订单子来源 1:撮合订单
     */
    public static final int SUB_ORDER_FROM_MATCH = 1;


    public static final String EXECUTE_SUCCESS = "EXECUTE_SUCCESS";

    /**
     * 会员
     */
    public static final String MEMBER_TYPE = "1";

    /**
     * 商家
     */
    public static final String SELLER_TYPE = "2";

    /**
     * 服务商
     */
    public static final String SERVICE_PROVIDER_TYPE = "3";

    public static final String PHONE_REGEX = "^1[1-9]\\d{9}$";

    public static final Integer TRY_EXPORT = 0;

    public static final Integer APPLE_DATA = 1;

    public static final Integer WAIT_DEAL = 1;

}
