package cn.htd.s2bplus.nongzi.pojo.dto.rebate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@Data
@ApiModel(value = "返利配置限额出参")
public class RebateLimitQuotaVO {

    /**
     * 额度
     */
    @ApiModelProperty(value = "额度")
    private String quota;

    /**
     * 渠道类型 1:云场自主下单 2:商家中心报价单 3:商家中心线下开单 4:VMS报价单 5:VMS线下开单
     */
    @ApiModelProperty(value = "渠道类型 1:云场自主下单 2:商家中心报价单 3:商家中心线下开单 4:VMS报价单 5:VMS线下开单")
    private Integer channelType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}