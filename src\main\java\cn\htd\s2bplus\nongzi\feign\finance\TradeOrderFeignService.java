package cn.htd.s2bplus.nongzi.feign.finance;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.s2bplus.nongzi.pojo.dto.order.FinanceReportformEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(name = "s2bplus-order-service")
public interface TradeOrderFeignService {

    /**
     * 保存结算单列表
     * @return
     */
    @PostMapping("/oss/statement/saveFinanceReportForm")
    Result<Boolean> saveFinanceReportForm(@RequestBody FinanceReportformEntity financeReportformEntity);
}
