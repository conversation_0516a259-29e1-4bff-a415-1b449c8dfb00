package cn.htd.s2bplus.nongzi.pojo.dto.goods;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 商品单位是否需要转换dto
 * <AUTHOR>
 * date: 2019年6月20日 下午2:39:05
 */
@Data
public class StockNeedConvertOutDTO implements Serializable {

    private static final long serialVersionUID = 4218993169798902156L;

    @ApiModelProperty(
            value = "是否需要转换",
            notes = "是否需要转换",
            example = "true"
    )
    private boolean needConvert;
    @ApiModelProperty(
            value = "转换系数",
            notes = "转换系数",
            example = "10000"
    )
    private Integer ratio;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"needConvert\":")
                .append(needConvert);
        sb.append(",\"ratio\":")
                .append(ratio);
        sb.append('}');
        return sb.toString();
    }
}
