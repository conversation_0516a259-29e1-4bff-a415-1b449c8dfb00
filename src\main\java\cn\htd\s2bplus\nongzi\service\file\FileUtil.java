package cn.htd.s2bplus.nongzi.service.file;

import cn.htd.rdc.base.development.framework.core.result.Result;
import cn.htd.rdc.base.development.framework.core.result.ResultEnum;
import cn.htd.rdc.base.development.framework.core.util.ResultUtil;
import cn.htd.s2bplus.nongzi.feign.finance.TradeOrderFeignService;
import cn.htd.s2bplus.nongzi.pojo.dto.order.FinanceReportformEntity;
import cn.htd.s2bplus.nongzi.utils.OssUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

@Slf4j
@Component
public class FileUtil {

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.AccessKeyId}")
    private String accessKeyId;

    @Value("${oss.AccessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Autowired
    private TradeOrderFeignService tradeOrderFeignService;

    /**
     * 生成文件上传OSS并且保存
     * @param createId
     * @param createName
     * @param type 参见ExportFileTypeEnum
     * @param
     * @return
     */
    public Result<Boolean> saveFileGeneration(String createId, String createName, String type, InputStream excelStream) {
        try {
            OssUtils ossUtils = new OssUtils();
            String fileName = this.getFileName("xls");
            String downloadUrl = ossUtils.upload(excelStream, fileName, bucket, endpoint, accessKeyId, accessKeySecret);
            if (StringUtils.isEmpty(downloadUrl)) {
                return ResultUtil.error(ResultEnum.FAILURE.getCode(), "OSS上传失败");
            }
            // 保存报表url
            FinanceReportformEntity financeReportformEntity = new FinanceReportformEntity();
            financeReportformEntity.setType(type);
            financeReportformEntity.setCreateId(createId);
            financeReportformEntity.setCreateName(createName);
            financeReportformEntity.setCreateDate(new Date());
            financeReportformEntity.setDownUrl(downloadUrl);
            financeReportformEntity.setFileName(fileName);
            Result<Boolean> saveFinanceReportForm = tradeOrderFeignService.saveFinanceReportForm(financeReportformEntity);
            if (saveFinanceReportForm.isSuccess() && saveFinanceReportForm.getData()) {
                return ResultUtil.success(ResultEnum.SUCCESS.getCode());
            } else {
                return ResultUtil.error(ResultEnum.FAILURE.getCode(), "保存报表地址失败");
            }
        } catch (Exception e) {
            log.error("保存报表地址异常",e);
            return ResultUtil.error(ResultEnum.FAILURE.getCode(), "保存报表地址异常");
        }
    }

    private String getFileName(String suffix) {
        SimpleDateFormat datetime = new SimpleDateFormat("yyyyMMddHHmmss");
        //根据时间获取文件名
        return datetime.format(new Date()) + "." + suffix;
    }
}
